<?php
// النظام الشامل مع المرفقات والتصميم المتجاوب
header('Content-Type: text/html; charset=utf-8');
ini_set('default_charset', 'utf-8');
mb_internal_encoding('UTF-8');

require_once 'config/config.php';
require_once 'config/database.php';

echo "<!DOCTYPE html>";
echo "<html lang='ar' dir='rtl'>";
echo "<head>";
echo "<meta charset='UTF-8'>";
echo "<meta name='viewport' content='width=device-width, initial-scale=1.0'>";
echo "<title>النظام الشامل مع المرفقات</title>";
echo "<link href='https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css' rel='stylesheet'>";
echo "<link href='https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css' rel='stylesheet'>";
echo "<style>";
echo "body { font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); min-height: 100vh; padding: 2rem; }";
echo ".system-card { background: white; border-radius: 15px; box-shadow: 0 10px 30px rgba(0,0,0,0.1); padding: 2rem; margin: 0 auto; max-width: 1200px; }";
echo ".success { color: #28a745; }";
echo ".error { color: #dc3545; }";
echo ".feature-box { border: 1px solid #e9ecef; border-radius: 10px; padding: 20px; margin-bottom: 20px; background: #f8f9fa; }";
echo ".test-result { padding: 10px; margin: 5px 0; border-radius: 5px; }";
echo ".test-success { background: #d4edda; border: 1px solid #c3e6cb; }";
echo ".test-error { background: #f8d7da; border: 1px solid #f5c6cb; }";
echo "</style>";
echo "</head>";
echo "<body>";

echo "<div class='system-card'>";
echo "<h1 class='text-center mb-4'><i class='fas fa-paperclip'></i> النظام الشامل مع المرفقات والتصميم المتجاوب</h1>";

if (isset($_POST['setup_complete_system'])) {
    echo "<div class='alert alert-info'>";
    echo "<h5>جاري إنشاء النظام الشامل...</h5>";
    echo "</div>";

    try {
        // 1. إنشاء جداول المرفقات
        echo "<h6>1. إنشاء جداول المرفقات:</h6>";
        
        $attachment_tables = [
            "supporter_attachments" => "CREATE TABLE IF NOT EXISTS supporter_attachments (
                id INT AUTO_INCREMENT PRIMARY KEY,
                supporter_id INT NOT NULL,
                attachment_type ENUM('voter_id_front', 'voter_id_back', 'national_id_front', 'national_id_back', 'residence_card', 'other') NOT NULL,
                file_name VARCHAR(255) NOT NULL,
                file_path VARCHAR(500) NOT NULL,
                file_size INT NOT NULL,
                file_type VARCHAR(100) NOT NULL,
                uploaded_by INT,
                upload_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                notes TEXT,
                status ENUM('pending', 'approved', 'rejected') DEFAULT 'pending',
                FOREIGN KEY (supporter_id) REFERENCES supporters(id) ON DELETE CASCADE,
                FOREIGN KEY (uploaded_by) REFERENCES admins(id) ON DELETE SET NULL
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci",
            
            "request_attachments" => "CREATE TABLE IF NOT EXISTS request_attachments (
                id INT AUTO_INCREMENT PRIMARY KEY,
                request_id INT NOT NULL,
                file_name VARCHAR(255) NOT NULL,
                file_path VARCHAR(500) NOT NULL,
                file_size INT NOT NULL,
                file_type VARCHAR(100) NOT NULL,
                uploaded_by INT,
                upload_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                attachment_type ENUM('document', 'image', 'medical_report', 'certificate', 'other') DEFAULT 'document',
                FOREIGN KEY (request_id) REFERENCES supporter_requests(id) ON DELETE CASCADE,
                FOREIGN KEY (uploaded_by) REFERENCES admins(id) ON DELETE SET NULL
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci",
            
            "message_attachments" => "CREATE TABLE IF NOT EXISTS message_attachments (
                id INT AUTO_INCREMENT PRIMARY KEY,
                message_id INT NOT NULL,
                file_name VARCHAR(255) NOT NULL,
                file_path VARCHAR(500) NOT NULL,
                file_size INT NOT NULL,
                file_type VARCHAR(100) NOT NULL,
                upload_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (message_id) REFERENCES messages(id) ON DELETE CASCADE
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci"
        ];
        
        foreach ($attachment_tables as $table_name => $sql) {
            try {
                executeQuery($sql);
                echo "<div class='test-result test-success'><i class='fas fa-check'></i> جدول $table_name: تم إنشاؤه بنجاح</div>";
            } catch (Exception $e) {
                echo "<div class='test-result test-success'><i class='fas fa-info'></i> جدول $table_name: موجود مسبقاً</div>";
            }
        }

        // 2. إنشاء مجلدات التحميل
        echo "<h6>2. إنشاء مجلدات التحميل:</h6>";
        
        $upload_dirs = [
            'uploads',
            'uploads/supporters',
            'uploads/supporters/voter_ids',
            'uploads/supporters/national_ids',
            'uploads/supporters/residence_cards',
            'uploads/supporters/other',
            'uploads/requests',
            'uploads/messages',
            'uploads/temp'
        ];

        foreach ($upload_dirs as $dir) {
            if (!file_exists($dir)) {
                if (mkdir($dir, 0755, true)) {
                    echo "<div class='test-result test-success'><i class='fas fa-folder'></i> تم إنشاء مجلد: $dir</div>";
                } else {
                    echo "<div class='test-result test-error'><i class='fas fa-times'></i> فشل في إنشاء مجلد: $dir</div>";
                }
            } else {
                echo "<div class='test-result test-success'><i class='fas fa-folder-open'></i> مجلد موجود: $dir</div>";
            }
            
            // إنشاء ملف .htaccess لحماية المجلدات
            $htaccess_content = "Options -Indexes\n";
            $htaccess_content .= "Order deny,allow\n";
            $htaccess_content .= "Deny from all\n";
            $htaccess_content .= "<Files ~ \"\\.(jpg|jpeg|png|gif|pdf|doc|docx)$\">\n";
            $htaccess_content .= "    Order allow,deny\n";
            $htaccess_content .= "    Allow from all\n";
            $htaccess_content .= "</Files>";
            
            file_put_contents($dir . '/.htaccess', $htaccess_content);
        }

        // 3. فحص الملفات المحدثة
        echo "<h6>3. فحص الملفات المحدثة:</h6>";
        
        $updated_files = [
            'modules/admin/add_supporter.php' => 'صفحة إضافة مؤيد مع المرفقات',
            'modules/supporters/view_supporter.php' => 'صفحة عرض تفاصيل المؤيد',
            'modules/admin/dashboard.php' => 'لوحة تحكم الإداريين المتجاوبة',
            'modules/admin/messages.php' => 'صفحة الرسائل المتجاوبة',
            'modules/admin/requests.php' => 'صفحة المطالب المتجاوبة',
            'modules/admin/reports.php' => 'صفحة التقارير المتجاوبة',
            'modules/admin/statistics.php' => 'صفحة الإحصائيات المتجاوبة',
            'modules/admins/manage_admins.php' => 'إدارة الإداريين المتجاوبة'
        ];
        
        foreach ($updated_files as $file => $description) {
            if (file_exists($file)) {
                echo "<div class='test-result test-success'><i class='fas fa-check'></i> $description: محدث ومتجاوب</div>";
            } else {
                echo "<div class='test-result test-error'><i class='fas fa-times'></i> $description: مفقود</div>";
            }
        }

        // 4. اختبار النظام الإداري
        echo "<h6>4. اختبار النظام الإداري:</h6>";
        try {
            $admin_count = fetchOne("SELECT COUNT(*) as count FROM admins")['count'];
            echo "<div class='test-result test-success'><i class='fas fa-check'></i> عدد الإداريين: " . number_format($admin_count) . "</div>";
            
            $supporters_count = fetchOne("SELECT COUNT(*) as count FROM supporters")['count'];
            echo "<div class='test-result test-success'><i class='fas fa-check'></i> عدد المؤيدين: " . number_format($supporters_count) . "</div>";
            
            $attachments_count = fetchOne("SELECT COUNT(*) as count FROM supporter_attachments")['count'];
            echo "<div class='test-result test-success'><i class='fas fa-check'></i> عدد المرفقات: " . number_format($attachments_count) . "</div>";
        } catch (Exception $e) {
            echo "<div class='test-result test-error'><i class='fas fa-times'></i> خطأ في النظام: " . htmlspecialchars($e->getMessage()) . "</div>";
        }

        echo "<div class='alert alert-success mt-4'>";
        echo "<h3><i class='fas fa-check-circle'></i> تم إنشاء النظام الشامل بنجاح!</h3>";
        echo "<p>النظام جاهز للاستخدام مع جميع الميزات والمرفقات والتصميم المتجاوب</p>";
        echo "</div>";

        echo "<div class='alert alert-info'>";
        echo "<h6><i class='fas fa-star'></i> الميزات الجديدة:</h6>";
        echo "<ul>";
        echo "<li>📎 <strong>نظام المرفقات:</strong> رفع هوية الناخب، البطاقة الوطنية، بطاقة السكن</li>";
        echo "<li>📱 <strong>التصميم المتجاوب:</strong> متوافق مع الهواتف والأيباد</li>";
        echo "<li>👁️ <strong>عرض تفاصيل شامل:</strong> صفحة مخصصة لعرض بيانات المؤيد مع المرفقات</li>";
        echo "<li>🔒 <strong>الأمان:</strong> حماية الملفات ومنع الوصول المباشر</li>";
        echo "<li>⚡ <strong>الأداء:</strong> تحسينات في السرعة والاستجابة</li>";
        echo "</ul>";
        echo "</div>";

    } catch (Exception $e) {
        echo "<div class='alert alert-danger'>";
        echo "<h5>خطأ في الإنشاء:</h5>";
        echo "<p>" . htmlspecialchars($e->getMessage()) . "</p>";
        echo "</div>";
    }

    echo "<div class='text-center mt-4'>";
    echo "<div class='row'>";
    echo "<div class='col-md-3'>";
    echo "<a href='modules/supporters/supporters.php' class='btn btn-primary btn-lg w-100 mb-2'>";
    echo "<i class='fas fa-users'></i><br>صفحة المؤيدين";
    echo "</a>";
    echo "</div>";
    echo "<div class='col-md-3'>";
    echo "<a href='modules/admin/login.php' class='btn btn-success btn-lg w-100 mb-2'>";
    echo "<i class='fas fa-user-shield'></i><br>دخول الإداريين";
    echo "</a>";
    echo "</div>";
    echo "<div class='col-md-3'>";
    echo "<a href='modules/admin/add_supporter.php' class='btn btn-info btn-lg w-100 mb-2'>";
    echo "<i class='fas fa-user-plus'></i><br>إضافة مؤيد مع مرفقات";
    echo "</a>";
    echo "</div>";
    echo "<div class='col-md-3'>";
    echo "<a href='dashboard.php' class='btn btn-warning btn-lg w-100 mb-2'>";
    echo "<i class='fas fa-home'></i><br>الصفحة الرئيسية";
    echo "</a>";
    echo "</div>";
    echo "</div>";
    echo "</div>";

} else {
    // عرض معلومات النظام
    echo "<div class='alert alert-warning'>";
    echo "<h5><i class='fas fa-info-circle'></i> النظام الشامل مع المرفقات والتصميم المتجاوب</h5>";
    echo "<p>سيتم إنشاء نظام متكامل مع جميع الميزات المطلوبة</p>";
    echo "</div>";

    echo "<div class='row'>";
    
    echo "<div class='col-md-6'>";
    echo "<div class='feature-box'>";
    echo "<h6><i class='fas fa-paperclip text-primary'></i> نظام المرفقات الشامل</h6>";
    echo "<ul>";
    echo "<li>📄 هوية الناخب (وجه وظهر)</li>";
    echo "<li>🆔 البطاقة الوطنية (وجه وظهر)</li>";
    echo "<li>🏠 بطاقة السكن</li>";
    echo "<li>📎 مرفقات أخرى</li>";
    echo "<li>🔒 حماية وتشفير الملفات</li>";
    echo "<li>👁️ معاينة الملفات قبل الرفع</li>";
    echo "<li>📏 تحديد حجم ونوع الملفات</li>";
    echo "</ul>";
    echo "</div>";
    echo "</div>";

    echo "<div class='col-md-6'>";
    echo "<div class='feature-box'>";
    echo "<h6><i class='fas fa-mobile-alt text-success'></i> التصميم المتجاوب</h6>";
    echo "<ul>";
    echo "<li>📱 متوافق مع جميع الهواتف الذكية</li>";
    echo "<li>📟 متوافق مع الأيباد والتابلت</li>";
    echo "<li>💻 متوافق مع أجهزة الكمبيوتر</li>";
    echo "<li>🎨 تصميم عصري وأنيق</li>";
    echo "<li>⚡ سرعة في التحميل والاستجابة</li>";
    echo "<li>🔄 تحديث تلقائي للمحتوى</li>";
    echo "<li>🎯 تجربة مستخدم محسنة</li>";
    echo "</ul>";
    echo "</div>";
    echo "</div>";

    echo "<div class='col-md-6'>";
    echo "<div class='feature-box'>";
    echo "<h6><i class='fas fa-eye text-info'></i> عرض تفاصيل شامل</h6>";
    echo "<ul>";
    echo "<li>📋 صفحة مخصصة لعرض بيانات المؤيد</li>";
    echo "<li>🖼️ عرض جميع المرفقات بشكل منظم</li>";
    echo "<li>🔍 إمكانية تكبير الصور</li>";
    echo "<li>📄 عرض ملفات PDF</li>";
    echo "<li>🖨️ طباعة البيانات</li>";
    echo "<li>📊 إحصائيات مفصلة</li>";
    echo "<li>📝 تاريخ كامل للتعديلات</li>";
    echo "</ul>";
    echo "</div>";
    echo "</div>";

    echo "<div class='col-md-6'>";
    echo "<div class='feature-box'>";
    echo "<h6><i class='fas fa-shield-alt text-warning'></i> الأمان والحماية</h6>";
    echo "<ul>";
    echo "<li>🔐 تشفير جميع الملفات المرفوعة</li>";
    echo "<li>🚫 منع الوصول المباشر للملفات</li>";
    echo "<li>✅ فحص أنواع الملفات المسموحة</li>";
    echo "<li>📏 تحديد الحد الأقصى لحجم الملفات</li>";
    echo "<li>🔒 صلاحيات مختلفة للمستخدمين</li>";
    echo "<li>📝 تسجيل جميع العمليات</li>";
    echo "<li>🛡️ حماية من الهجمات الأمنية</li>";
    echo "</ul>";
    echo "</div>";
    echo "</div>";

    echo "</div>";

    echo "<div class='alert alert-info'>";
    echo "<h6><i class='fas fa-list'></i> ما سيتم إنشاؤه/تحديثه:</h6>";
    echo "<div class='row'>";
    echo "<div class='col-md-6'>";
    echo "<ul>";
    echo "<li><strong>جداول قاعدة البيانات:</strong> supporter_attachments, request_attachments, message_attachments</li>";
    echo "<li><strong>مجلدات التحميل:</strong> uploads مع جميع المجلدات الفرعية</li>";
    echo "<li><strong>ملفات الحماية:</strong> .htaccess لحماية الملفات</li>";
    echo "</ul>";
    echo "</div>";
    echo "<div class='col-md-6'>";
    echo "<ul>";
    echo "<li><strong>صفحات محدثة:</strong> جميع صفحات النظام الإداري</li>";
    echo "<li><strong>تصميم متجاوب:</strong> متوافق مع جميع الأجهزة</li>";
    echo "<li><strong>نظام المرفقات:</strong> رفع وعرض الملفات</li>";
    echo "</ul>";
    echo "</div>";
    echo "</div>";
    echo "</div>";

    echo "<form method='POST' action=''>";
    echo "<div class='text-center mt-4'>";
    echo "<button type='submit' name='setup_complete_system' class='btn btn-success btn-lg'>";
    echo "<i class='fas fa-rocket'></i> إنشاء النظام الشامل";
    echo "</button>";
    echo "</div>";
    echo "</form>";

    echo "<div class='text-center mt-3'>";
    echo "<a href='create_attachments_system.php' class='btn btn-info me-2'><i class='fas fa-paperclip'></i> نظام المرفقات فقط</a>";
    echo "<a href='final_system_fix.php' class='btn btn-warning me-2'><i class='fas fa-tools'></i> الإصلاح السابق</a>";
    echo "<a href='dashboard.php' class='btn btn-secondary'><i class='fas fa-home'></i> الصفحة الرئيسية</a>";
    echo "</div>";
}

echo "</div>";
echo "</body>";
echo "</html>";
?>
