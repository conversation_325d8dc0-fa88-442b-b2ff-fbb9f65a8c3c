<?php
// نظام البحث الشامل

header('Content-Type: text/html; charset=utf-8');
ini_set('default_charset', 'utf-8');
mb_internal_encoding('UTF-8');

require_once 'config/config.php';
require_once 'config/database.php';

// التحقق من تسجيل الدخول
if (!isLoggedIn()) {
    redirect('login.php');
}

$database = new Database();
$db = $database->getConnection();

$search_query = isset($_GET['q']) ? trim($_GET['q']) : '';
$search_results = [];

if (!empty($search_query)) {
    $search_term = '%' . $search_query . '%';
    
    // البحث في المؤيدين
    $supporters = $db->prepare("
        SELECT 'supporter' as type, id, full_name as title, phone as subtitle, 
               CONCAT('مؤيد من منطقة ', (SELECT name FROM regions WHERE id = region_id)) as description,
               'modules/supporters/supporters.php' as url
        FROM supporters 
        WHERE full_name LIKE ? OR phone LIKE ? OR address LIKE ? OR voter_number LIKE ?
        LIMIT 10
    ");
    $supporters->execute([$search_term, $search_term, $search_term, $search_term]);
    $search_results = array_merge($search_results, $supporters->fetchAll());
    
    // البحث في المناطق
    if (isCandidate()) {
        $regions = $db->prepare("
            SELECT 'region' as type, id, name as title, description as subtitle,
                   CONCAT('منطقة انتخابية - ', (SELECT COUNT(*) FROM supporters WHERE region_id = regions.id), ' مؤيد') as description,
                   'modules/regions/regions.php' as url
            FROM regions 
            WHERE name LIKE ? OR description LIKE ?
            LIMIT 10
        ");
        $regions->execute([$search_term, $search_term]);
        $search_results = array_merge($search_results, $regions->fetchAll());
    }
    
    // البحث في الإداريين
    if (isCandidate()) {
        $admins = $db->prepare("
            SELECT 'admin' as type, id, full_name as title, username as subtitle,
                   CONCAT('إداري - ', phone) as description,
                   'modules/admins/admins.php' as url
            FROM users 
            WHERE user_type = 'admin' AND (full_name LIKE ? OR username LIKE ? OR phone LIKE ?)
            LIMIT 10
        ");
        $admins->execute([$search_term, $search_term, $search_term]);
        $search_results = array_merge($search_results, $admins->fetchAll());
    }
    
    // البحث في المصروفات
    $expenses = $db->prepare("
        SELECT 'expense' as type, id, description as title, 
               CONCAT(amount, ' د.ع') as subtitle,
               CONCAT('مصروف من فئة ', category) as description,
               'modules/expenses/expenses.php' as url
        FROM expenses 
        WHERE description LIKE ? OR category LIKE ? OR notes LIKE ?
        LIMIT 10
    ");
    $expenses->execute([$search_term, $search_term, $search_term]);
    $search_results = array_merge($search_results, $expenses->fetchAll());
    
    // البحث في الفعاليات
    $events = $db->prepare("
        SELECT 'event' as type, id, name as title, location as subtitle,
               CONCAT('فعالية - ', status) as description,
               'modules/events/events.php' as url
        FROM events 
        WHERE name LIKE ? OR description LIKE ? OR location LIKE ?
        LIMIT 10
    ");
    $events->execute([$search_term, $search_term, $search_term]);
    $search_results = array_merge($search_results, $events->fetchAll());
    
    // البحث في المنافسين
    if (isCandidate()) {
        $competitors = $db->prepare("
            SELECT 'competitor' as type, id, name as title, party as subtitle,
                   'منافس انتخابي' as description,
                   'modules/competitors/competitors.php' as url
            FROM competitors 
            WHERE name LIKE ? OR party LIKE ? OR strengths LIKE ? OR weaknesses LIKE ?
            LIMIT 10
        ");
        $competitors->execute([$search_term, $search_term, $search_term, $search_term]);
        $search_results = array_merge($search_results, $competitors->fetchAll());
    }
}

// إرجاع النتائج كـ JSON للـ AJAX
if (isset($_GET['ajax'])) {
    header('Content-Type: application/json; charset=utf-8');
    echo json_encode($search_results);
    exit;
}
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>البحث الشامل - <?php echo SITE_NAME; ?></title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="assets/css/dashboard.css" rel="stylesheet">
    <style>
        .search-container {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 2rem 0;
        }
        .search-card {
            background: white;
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            padding: 2rem;
            margin: 0 auto;
            max-width: 800px;
        }
        .search-input {
            border-radius: 25px;
            border: 2px solid #e9ecef;
            padding: 15px 25px;
            font-size: 1.1rem;
            transition: all 0.3s ease;
        }
        .search-input:focus {
            border-color: #667eea;
            box-shadow: 0 0 0 0.2rem rgba(102,126,234,0.25);
        }
        .search-btn {
            border-radius: 25px;
            padding: 15px 30px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border: none;
            color: white;
            font-weight: 600;
            transition: all 0.3s ease;
        }
        .search-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0,0,0,0.2);
        }
        .result-item {
            border: 1px solid #e9ecef;
            border-radius: 10px;
            padding: 1rem;
            margin-bottom: 1rem;
            transition: all 0.3s ease;
            cursor: pointer;
        }
        .result-item:hover {
            border-color: #667eea;
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        }
        .result-type {
            display: inline-block;
            padding: 0.25rem 0.75rem;
            border-radius: 15px;
            font-size: 0.75rem;
            font-weight: 600;
            text-transform: uppercase;
        }
        .type-supporter { background-color: #e3f2fd; color: #1976d2; }
        .type-region { background-color: #e8f5e8; color: #388e3c; }
        .type-admin { background-color: #fff3e0; color: #f57c00; }
        .type-expense { background-color: #fce4ec; color: #c2185b; }
        .type-event { background-color: #f3e5f5; color: #7b1fa2; }
        .type-competitor { background-color: #ffebee; color: #d32f2f; }
        .no-results {
            text-align: center;
            padding: 3rem;
            color: #6c757d;
        }
        .search-suggestions {
            position: absolute;
            top: 100%;
            left: 0;
            right: 0;
            background: white;
            border: 1px solid #e9ecef;
            border-radius: 10px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
            z-index: 1000;
            max-height: 300px;
            overflow-y: auto;
        }
        .suggestion-item {
            padding: 0.75rem 1rem;
            border-bottom: 1px solid #f8f9fa;
            cursor: pointer;
            transition: background-color 0.2s ease;
        }
        .suggestion-item:hover {
            background-color: #f8f9fa;
        }
        .suggestion-item:last-child {
            border-bottom: none;
        }
    </style>
</head>
<body>
    <div class="search-container">
        <div class="container">
            <div class="search-card">
                <div class="text-center mb-4">
                    <h1 class="h2">
                        <i class="fas fa-search me-2"></i>
                        البحث الشامل في النظام
                    </h1>
                    <p class="text-muted">ابحث في جميع أقسام النظام: المؤيدين، المناطق، الإداريين، المصروفات، الفعاليات، والمنافسين</p>
                </div>

                <!-- نموذج البحث -->
                <form method="GET" action="" class="mb-4">
                    <div class="input-group position-relative">
                        <input type="text" 
                               class="form-control search-input" 
                               id="searchInput"
                               name="q" 
                               value="<?php echo htmlspecialchars($search_query); ?>" 
                               placeholder="ابحث عن أي شيء في النظام..."
                               autocomplete="off">
                        <button class="btn search-btn" type="submit">
                            <i class="fas fa-search me-2"></i>بحث
                        </button>
                        <div id="searchSuggestions" class="search-suggestions d-none"></div>
                    </div>
                </form>

                <!-- نتائج البحث -->
                <?php if (!empty($search_query)): ?>
                    <div class="search-results">
                        <h5 class="mb-3">
                            نتائج البحث عن: "<strong><?php echo htmlspecialchars($search_query); ?></strong>"
                            <span class="badge bg-primary"><?php echo count($search_results); ?> نتيجة</span>
                        </h5>

                        <?php if (empty($search_results)): ?>
                            <div class="no-results">
                                <i class="fas fa-search fa-3x mb-3 text-muted"></i>
                                <h4>لا توجد نتائج</h4>
                                <p>لم يتم العثور على أي نتائج تطابق بحثك. جرب كلمات مختلفة أو تأكد من الإملاء.</p>
                            </div>
                        <?php else: ?>
                            <?php foreach ($search_results as $result): ?>
                                <div class="result-item" onclick="goToResult('<?php echo $result['url']; ?>', <?php echo $result['id']; ?>)">
                                    <div class="d-flex justify-content-between align-items-start">
                                        <div class="flex-grow-1">
                                            <div class="d-flex align-items-center mb-2">
                                                <span class="result-type type-<?php echo $result['type']; ?> me-2">
                                                    <?php
                                                    $type_names = [
                                                        'supporter' => 'مؤيد',
                                                        'region' => 'منطقة',
                                                        'admin' => 'إداري',
                                                        'expense' => 'مصروف',
                                                        'event' => 'فعالية',
                                                        'competitor' => 'منافس'
                                                    ];
                                                    echo $type_names[$result['type']] ?? $result['type'];
                                                    ?>
                                                </span>
                                                <h6 class="mb-0"><?php echo htmlspecialchars($result['title']); ?></h6>
                                            </div>
                                            <?php if (!empty($result['subtitle'])): ?>
                                                <p class="text-muted mb-1"><?php echo htmlspecialchars($result['subtitle']); ?></p>
                                            <?php endif; ?>
                                            <small class="text-secondary"><?php echo htmlspecialchars($result['description']); ?></small>
                                        </div>
                                        <div class="text-end">
                                            <i class="fas fa-chevron-left text-muted"></i>
                                        </div>
                                    </div>
                                </div>
                            <?php endforeach; ?>
                        <?php endif; ?>
                    </div>
                <?php endif; ?>

                <!-- روابط سريعة -->
                <div class="text-center mt-4">
                    <h6>روابط سريعة:</h6>
                    <div class="btn-group flex-wrap" role="group">
                        <a href="dashboard.php" class="btn btn-outline-primary btn-sm">
                            <i class="fas fa-tachometer-alt me-1"></i>لوحة التحكم
                        </a>
                        <a href="modules/supporters/supporters.php" class="btn btn-outline-success btn-sm">
                            <i class="fas fa-users me-1"></i>المؤيدين
                        </a>
                        <?php if (isCandidate()): ?>
                        <a href="modules/regions/regions.php" class="btn btn-outline-info btn-sm">
                            <i class="fas fa-map me-1"></i>المناطق
                        </a>
                        <a href="modules/admins/admins.php" class="btn btn-outline-warning btn-sm">
                            <i class="fas fa-user-tie me-1"></i>الإداريين
                        </a>
                        <?php endif; ?>
                        <a href="modules/expenses/expenses.php" class="btn btn-outline-danger btn-sm">
                            <i class="fas fa-money-bill me-1"></i>المصروفات
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // البحث التلقائي أثناء الكتابة
        let searchTimeout;
        const searchInput = document.getElementById('searchInput');
        const searchSuggestions = document.getElementById('searchSuggestions');

        searchInput.addEventListener('input', function() {
            clearTimeout(searchTimeout);
            const query = this.value.trim();
            
            if (query.length < 2) {
                searchSuggestions.classList.add('d-none');
                return;
            }
            
            searchTimeout = setTimeout(() => {
                fetch(`search.php?ajax=1&q=${encodeURIComponent(query)}`)
                    .then(response => response.json())
                    .then(data => {
                        displaySuggestions(data.slice(0, 5)); // أول 5 نتائج فقط
                    })
                    .catch(error => {
                        console.error('خطأ في البحث:', error);
                    });
            }, 300);
        });

        function displaySuggestions(suggestions) {
            if (suggestions.length === 0) {
                searchSuggestions.classList.add('d-none');
                return;
            }
            
            const html = suggestions.map(item => `
                <div class="suggestion-item" onclick="selectSuggestion('${item.title}', '${item.url}', ${item.id})">
                    <div class="d-flex align-items-center">
                        <span class="result-type type-${item.type} me-2" style="font-size: 0.7rem;">
                            ${getTypeName(item.type)}
                        </span>
                        <div>
                            <div class="fw-bold">${item.title}</div>
                            ${item.subtitle ? `<small class="text-muted">${item.subtitle}</small>` : ''}
                        </div>
                    </div>
                </div>
            `).join('');
            
            searchSuggestions.innerHTML = html;
            searchSuggestions.classList.remove('d-none');
        }

        function selectSuggestion(title, url, id) {
            searchInput.value = title;
            searchSuggestions.classList.add('d-none');
            goToResult(url, id);
        }

        function goToResult(url, id) {
            window.location.href = url + (url.includes('?') ? '&' : '?') + 'highlight=' + id;
        }

        function getTypeName(type) {
            const names = {
                'supporter': 'مؤيد',
                'region': 'منطقة',
                'admin': 'إداري',
                'expense': 'مصروف',
                'event': 'فعالية',
                'competitor': 'منافس'
            };
            return names[type] || type;
        }

        // إخفاء الاقتراحات عند النقر خارجها
        document.addEventListener('click', function(e) {
            if (!searchInput.contains(e.target) && !searchSuggestions.contains(e.target)) {
                searchSuggestions.classList.add('d-none');
            }
        });

        // تركيز على حقل البحث عند تحميل الصفحة
        document.addEventListener('DOMContentLoaded', function() {
            searchInput.focus();
        });
    </script>
</body>
</html>
