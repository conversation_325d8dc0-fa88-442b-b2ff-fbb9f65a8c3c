// JavaScript لصفحة الفعاليات

$(document).ready(function() {
    // تهيئة جدول البيانات
    initializeDataTable();
    
    // تهيئة التحقق من النماذج
    initializeFormValidation();
    
    // تهيئة رفع الصور
    initializePhotoUpload();
});

// تهيئة جدول البيانات
function initializeDataTable() {
    $('#eventsTable').DataTable({
        language: {
            url: 'https://cdn.datatables.net/plug-ins/1.13.6/i18n/ar.json'
        },
        responsive: true,
        pageLength: 25,
        lengthMenu: [[10, 25, 50, 100, -1], [10, 25, 50, 100, "الكل"]],
        order: [[1, 'desc']], // ترتيب حسب التاريخ
        columnDefs: [
            { orderable: false, targets: [7] }, // عدم ترتيب الإجراءات
            { searchable: false, targets: [7] }, // عدم البحث في الإجراءات
            { className: "text-center", targets: "_all" }
        ]
    });
}

// تهيئة التحقق من النماذج
function initializeFormValidation() {
    const forms = document.querySelectorAll('.needs-validation');
    
    forms.forEach(function(form) {
        form.addEventListener('submit', function(event) {
            if (!form.checkValidity()) {
                event.preventDefault();
                event.stopPropagation();
            }
            
            form.classList.add('was-validated');
        });
    });
}

// تهيئة رفع الصور
function initializePhotoUpload() {
    const uploadAreas = document.querySelectorAll('.photo-upload-area');
    
    uploadAreas.forEach(area => {
        const input = area.querySelector('input[type="file"]');
        
        area.addEventListener('click', () => input.click());
        
        area.addEventListener('dragover', (e) => {
            e.preventDefault();
            area.classList.add('dragover');
        });
        
        area.addEventListener('dragleave', () => {
            area.classList.remove('dragover');
        });
        
        area.addEventListener('drop', (e) => {
            e.preventDefault();
            area.classList.remove('dragover');
            
            const files = e.dataTransfer.files;
            input.files = files;
            handleFileSelect(files, area);
        });
        
        input.addEventListener('change', (e) => {
            handleFileSelect(e.target.files, area);
        });
    });
}

// معالجة اختيار الملفات
function handleFileSelect(files, uploadArea) {
    const preview = uploadArea.querySelector('.photo-preview') || createPhotoPreview(uploadArea);
    preview.innerHTML = '';
    
    Array.from(files).forEach(file => {
        if (file.type.startsWith('image/')) {
            const reader = new FileReader();
            reader.onload = (e) => {
                const img = document.createElement('img');
                img.src = e.target.result;
                img.className = 'img-thumbnail me-2 mb-2';
                img.style.width = '100px';
                img.style.height = '100px';
                img.style.objectFit = 'cover';
                preview.appendChild(img);
            };
            reader.readAsDataURL(file);
        }
    });
}

// إنشاء منطقة معاينة الصور
function createPhotoPreview(uploadArea) {
    const preview = document.createElement('div');
    preview.className = 'photo-preview mt-3';
    uploadArea.appendChild(preview);
    return preview;
}

// عرض تفاصيل الفعالية
function viewEvent(id) {
    showLoading(true);
    
    fetch(`get_event.php?id=${id}`)
        .then(response => response.json())
        .then(data => {
            showLoading(false);
            if (data.success) {
                displayEventDetails(data.event);
            } else {
                showToast('حدث خطأ في جلب بيانات الفعالية', 'error');
            }
        })
        .catch(error => {
            showLoading(false);
            console.error('خطأ:', error);
            showToast('حدث خطأ في الاتصال', 'error');
        });
}

// عرض تفاصيل الفعالية في نافذة منبثقة
function displayEventDetails(event) {
    const modal = new bootstrap.Modal(document.getElementById('viewEventModal') || createViewEventModal());
    
    // ملء البيانات
    document.getElementById('viewEventName').textContent = event.name;
    document.getElementById('viewEventDescription').textContent = event.description || 'لا يوجد وصف';
    document.getElementById('viewEventDate').textContent = event.event_date;
    document.getElementById('viewEventTime').textContent = event.event_time;
    document.getElementById('viewEventLocation').textContent = event.location;
    document.getElementById('viewEventStatus').innerHTML = getStatusBadge(event.status);
    document.getElementById('viewEventAddedBy').textContent = event.added_by_name;
    
    // عرض الصور
    const photosContainer = document.getElementById('viewEventPhotos');
    photosContainer.innerHTML = '';
    
    if (event.photos) {
        const photos = JSON.parse(event.photos);
        photos.forEach(photo => {
            const img = document.createElement('img');
            img.src = '../../' + photo;
            img.className = 'img-thumbnail me-2 mb-2';
            img.style.width = '150px';
            img.style.height = '150px';
            img.style.objectFit = 'cover';
            img.style.cursor = 'pointer';
            img.onclick = () => openPhotoModal(img.src);
            photosContainer.appendChild(img);
        });
    } else {
        photosContainer.innerHTML = '<p class="text-muted">لا توجد صور</p>';
    }
    
    modal.show();
}

// إنشاء نافذة عرض الفعالية
function createViewEventModal() {
    const modalHTML = `
        <div class="modal fade" id="viewEventModal" tabindex="-1">
            <div class="modal-dialog modal-lg">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title">تفاصيل الفعالية</h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                    </div>
                    <div class="modal-body">
                        <div class="row">
                            <div class="col-md-6">
                                <p><strong>اسم الفعالية:</strong> <span id="viewEventName"></span></p>
                                <p><strong>التاريخ:</strong> <span id="viewEventDate"></span></p>
                                <p><strong>الوقت:</strong> <span id="viewEventTime"></span></p>
                                <p><strong>المكان:</strong> <span id="viewEventLocation"></span></p>
                            </div>
                            <div class="col-md-6">
                                <p><strong>الحالة:</strong> <span id="viewEventStatus"></span></p>
                                <p><strong>أضيف بواسطة:</strong> <span id="viewEventAddedBy"></span></p>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-12">
                                <p><strong>الوصف:</strong></p>
                                <p id="viewEventDescription"></p>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-12">
                                <p><strong>الصور:</strong></p>
                                <div id="viewEventPhotos"></div>
                            </div>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إغلاق</button>
                    </div>
                </div>
            </div>
        </div>
    `;
    document.body.insertAdjacentHTML('beforeend', modalHTML);
    return document.getElementById('viewEventModal');
}

// تعديل الفعالية
function editEvent(id) {
    showLoading(true);
    
    fetch(`get_event.php?id=${id}`)
        .then(response => response.json())
        .then(data => {
            showLoading(false);
            if (data.success) {
                fillEditForm(data.event);
                const modal = new bootstrap.Modal(document.getElementById('editEventModal'));
                modal.show();
            } else {
                showToast('حدث خطأ في جلب بيانات الفعالية', 'error');
            }
        })
        .catch(error => {
            showLoading(false);
            console.error('خطأ:', error);
            showToast('حدث خطأ في الاتصال', 'error');
        });
}

// ملء نموذج التعديل
function fillEditForm(event) {
    document.getElementById('editId').value = event.id;
    document.getElementById('editName').value = event.name;
    document.getElementById('editDescription').value = event.description || '';
    document.getElementById('editEventDate').value = event.event_date;
    document.getElementById('editEventTime').value = event.event_time;
    document.getElementById('editLocation').value = event.location;
    document.getElementById('editStatus').value = event.status;
}

// حذف الفعالية
function deleteEvent(id) {
    if (confirm('هل أنت متأكد من حذف هذه الفعالية؟ لا يمكن التراجع عن هذا الإجراء.')) {
        showLoading(true);
        
        const formData = new FormData();
        formData.append('action', 'delete');
        formData.append('id', id);
        
        fetch('events.php', {
            method: 'POST',
            body: formData
        })
        .then(response => response.text())
        .then(data => {
            showLoading(false);
            location.reload();
        })
        .catch(error => {
            showLoading(false);
            console.error('خطأ:', error);
            showToast('حدث خطأ أثناء الحذف', 'error');
        });
    }
}

// تصدير الفعاليات
function exportEvents() {
    window.location.href = 'export.php?type=excel';
}

// فتح نافذة عرض الصورة
function openPhotoModal(src) {
    const modal = document.getElementById('photoModal') || createPhotoModal();
    document.getElementById('modalPhoto').src = src;
    new bootstrap.Modal(modal).show();
}

// إنشاء نافذة عرض الصورة
function createPhotoModal() {
    const modalHTML = `
        <div class="modal fade" id="photoModal" tabindex="-1">
            <div class="modal-dialog modal-lg">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title">عرض الصورة</h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                    </div>
                    <div class="modal-body text-center">
                        <img id="modalPhoto" class="img-fluid" alt="صورة الفعالية">
                    </div>
                </div>
            </div>
        </div>
    `;
    document.body.insertAdjacentHTML('beforeend', modalHTML);
    return document.getElementById('photoModal');
}

// الحصول على شارة الحالة
function getStatusBadge(status) {
    const statusConfig = {
        'planned': { class: 'warning', text: 'مخططة' },
        'ongoing': { class: 'info', text: 'جارية' },
        'completed': { class: 'success', text: 'مكتملة' },
        'cancelled': { class: 'danger', text: 'ملغية' }
    };
    
    const config = statusConfig[status] || { class: 'secondary', text: status };
    return `<span class="badge bg-${config.class}">${config.text}</span>`;
}

// إظهار مؤشر التحميل
function showLoading(show) {
    const loader = document.getElementById('loadingOverlay') || createLoadingOverlay();
    loader.style.display = show ? 'flex' : 'none';
}

// إنشاء مؤشر التحميل
function createLoadingOverlay() {
    const loaderHTML = `
        <div id="loadingOverlay" class="loading-overlay">
            <div class="loading-spinner"></div>
        </div>
    `;
    document.body.insertAdjacentHTML('beforeend', loaderHTML);
    return document.getElementById('loadingOverlay');
}

// دالة عامة لإظهار الرسائل
function showToast(message, type = 'info') {
    const toastContainer = getOrCreateToastContainer();
    const toast = document.createElement('div');
    toast.className = `toast align-items-center text-white bg-${getBootstrapColor(type)} border-0`;
    toast.setAttribute('role', 'alert');
    toast.innerHTML = `
        <div class="d-flex">
            <div class="toast-body">
                <i class="fas fa-${getIcon(type)} me-2"></i>
                ${message}
            </div>
            <button type="button" class="btn-close btn-close-white me-2 m-auto" data-bs-dismiss="toast"></button>
        </div>
    `;
    
    toastContainer.appendChild(toast);
    const bsToast = new bootstrap.Toast(toast);
    bsToast.show();
    
    toast.addEventListener('hidden.bs.toast', function() {
        toast.remove();
    });
}

// دالة للحصول على حاوية التوست
function getOrCreateToastContainer() {
    let container = document.querySelector('.toast-container');
    if (!container) {
        container = document.createElement('div');
        container.className = 'toast-container position-fixed top-0 end-0 p-3';
        container.style.zIndex = '9999';
        document.body.appendChild(container);
    }
    return container;
}

// دالة للحصول على لون Bootstrap
function getBootstrapColor(type) {
    const colors = {
        'success': 'success',
        'error': 'danger',
        'warning': 'warning',
        'info': 'info'
    };
    return colors[type] || 'info';
}

// دالة للحصول على الأيقونة
function getIcon(type) {
    const icons = {
        'success': 'check-circle',
        'error': 'exclamation-triangle',
        'warning': 'exclamation-circle',
        'info': 'info-circle'
    };
    return icons[type] || 'info-circle';
}
