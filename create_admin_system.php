<?php
// إنشاء النظام الإداري الشامل
header('Content-Type: text/html; charset=utf-8');
ini_set('default_charset', 'utf-8');
mb_internal_encoding('UTF-8');

require_once 'config/config.php';
require_once 'config/database.php';

echo "<!DOCTYPE html>";
echo "<html lang='ar' dir='rtl'>";
echo "<head>";
echo "<meta charset='UTF-8'>";
echo "<meta name='viewport' content='width=device-width, initial-scale=1.0'>";
echo "<title>إنشاء النظام الإداري</title>";
echo "<link href='https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css' rel='stylesheet'>";
echo "<link href='https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css' rel='stylesheet'>";
echo "<style>";
echo "body { font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); min-height: 100vh; padding: 2rem; }";
echo ".admin-card { background: white; border-radius: 15px; box-shadow: 0 10px 30px rgba(0,0,0,0.1); padding: 2rem; margin: 0 auto; max-width: 1000px; }";
echo ".success { color: #28a745; }";
echo ".error { color: #dc3545; }";
echo ".sql-code { background: #f8f9fa; padding: 10px; border-radius: 5px; font-family: monospace; font-size: 12px; }";
echo "</style>";
echo "</head>";
echo "<body>";

echo "<div class='admin-card'>";
echo "<h1 class='text-center mb-4'><i class='fas fa-cogs'></i> إنشاء النظام الإداري الشامل</h1>";

if (isset($_POST['create_system'])) {
    echo "<div class='alert alert-info'>";
    echo "<h5>جاري إنشاء النظام الإداري...</h5>";
    echo "</div>";

    try {
        // 1. جدول الإداريين
        $sql_admins = "CREATE TABLE IF NOT EXISTS admins (
            id INT AUTO_INCREMENT PRIMARY KEY,
            username VARCHAR(50) UNIQUE NOT NULL,
            password VARCHAR(255) NOT NULL,
            full_name VARCHAR(100) NOT NULL,
            phone VARCHAR(15) NOT NULL,
            email VARCHAR(100),
            region_id INT,
            role ENUM('admin', 'supervisor') DEFAULT 'admin',
            status ENUM('active', 'inactive') DEFAULT 'active',
            last_login DATETIME,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            FOREIGN KEY (region_id) REFERENCES regions(id)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci";
        
        executeQuery($sql_admins);
        echo "<p class='success'><i class='fas fa-check'></i> تم إنشاء جدول الإداريين</p>";

        // 2. جدول الرسائل
        $sql_messages = "CREATE TABLE IF NOT EXISTS messages (
            id INT AUTO_INCREMENT PRIMARY KEY,
            sender_type ENUM('admin', 'management') NOT NULL,
            sender_id INT,
            receiver_type ENUM('admin', 'management') NOT NULL,
            receiver_id INT,
            subject VARCHAR(200) NOT NULL,
            message TEXT NOT NULL,
            attachment VARCHAR(255),
            status ENUM('unread', 'read') DEFAULT 'unread',
            priority ENUM('low', 'normal', 'high', 'urgent') DEFAULT 'normal',
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            read_at DATETIME
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci";
        
        executeQuery($sql_messages);
        echo "<p class='success'><i class='fas fa-check'></i> تم إنشاء جدول الرسائل</p>";

        // 3. جدول التقارير الأسبوعية
        $sql_reports = "CREATE TABLE IF NOT EXISTS weekly_reports (
            id INT AUTO_INCREMENT PRIMARY KEY,
            admin_id INT NOT NULL,
            week_start DATE NOT NULL,
            week_end DATE NOT NULL,
            supporters_added INT DEFAULT 0,
            events_attended INT DEFAULT 0,
            calls_made INT DEFAULT 0,
            meetings_held INT DEFAULT 0,
            challenges TEXT,
            achievements TEXT,
            next_week_plans TEXT,
            notes TEXT,
            status ENUM('draft', 'submitted', 'reviewed') DEFAULT 'draft',
            submitted_at DATETIME,
            reviewed_at DATETIME,
            reviewed_by INT,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            FOREIGN KEY (admin_id) REFERENCES admins(id) ON DELETE CASCADE
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci";
        
        executeQuery($sql_reports);
        echo "<p class='success'><i class='fas fa-check'></i> تم إنشاء جدول التقارير الأسبوعية</p>";

        // 4. جدول مطالب المؤيدين
        $sql_requests = "CREATE TABLE IF NOT EXISTS supporter_requests (
            id INT AUTO_INCREMENT PRIMARY KEY,
            admin_id INT NOT NULL,
            supporter_id INT,
            supporter_name VARCHAR(100) NOT NULL,
            supporter_phone VARCHAR(15) NOT NULL,
            request_type ENUM('financial', 'medical', 'educational', 'employment', 'housing', 'other') NOT NULL,
            title VARCHAR(200) NOT NULL,
            description TEXT NOT NULL,
            attachment VARCHAR(255),
            priority ENUM('low', 'normal', 'high', 'urgent') DEFAULT 'normal',
            status ENUM('pending', 'received', 'in_progress', 'completed', 'rejected') DEFAULT 'pending',
            estimated_cost DECIMAL(10,2),
            actual_cost DECIMAL(10,2),
            management_response TEXT,
            management_attachment VARCHAR(255),
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            completed_at DATETIME,
            FOREIGN KEY (admin_id) REFERENCES admins(id) ON DELETE CASCADE,
            FOREIGN KEY (supporter_id) REFERENCES supporters(id) ON DELETE SET NULL
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci";
        
        executeQuery($sql_requests);
        echo "<p class='success'><i class='fas fa-check'></i> تم إنشاء جدول مطالب المؤيدين</p>";

        // 5. جدول الإشعارات
        $sql_notifications = "CREATE TABLE IF NOT EXISTS notifications (
            id INT AUTO_INCREMENT PRIMARY KEY,
            user_type ENUM('admin', 'management') NOT NULL,
            user_id INT NOT NULL,
            title VARCHAR(200) NOT NULL,
            message TEXT NOT NULL,
            type ENUM('info', 'success', 'warning', 'error') DEFAULT 'info',
            is_read BOOLEAN DEFAULT FALSE,
            action_url VARCHAR(255),
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci";
        
        executeQuery($sql_notifications);
        echo "<p class='success'><i class='fas fa-check'></i> تم إنشاء جدول الإشعارات</p>";

        // 6. جدول جلسات الإداريين
        $sql_sessions = "CREATE TABLE IF NOT EXISTS admin_sessions (
            id INT AUTO_INCREMENT PRIMARY KEY,
            admin_id INT NOT NULL,
            session_token VARCHAR(255) NOT NULL,
            ip_address VARCHAR(45),
            user_agent TEXT,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            expires_at DATETIME NOT NULL,
            is_active BOOLEAN DEFAULT TRUE,
            FOREIGN KEY (admin_id) REFERENCES admins(id) ON DELETE CASCADE
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci";
        
        executeQuery($sql_sessions);
        echo "<p class='success'><i class='fas fa-check'></i> تم إنشاء جدول جلسات الإداريين</p>";

        // 7. جدول إحصائيات الإداريين
        $sql_stats = "CREATE TABLE IF NOT EXISTS admin_statistics (
            id INT AUTO_INCREMENT PRIMARY KEY,
            admin_id INT NOT NULL,
            date DATE NOT NULL,
            supporters_added INT DEFAULT 0,
            calls_made INT DEFAULT 0,
            meetings_held INT DEFAULT 0,
            requests_submitted INT DEFAULT 0,
            login_count INT DEFAULT 0,
            active_hours DECIMAL(4,2) DEFAULT 0,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            FOREIGN KEY (admin_id) REFERENCES admins(id) ON DELETE CASCADE,
            UNIQUE KEY unique_admin_date (admin_id, date)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci";
        
        executeQuery($sql_stats);
        echo "<p class='success'><i class='fas fa-check'></i> تم إنشاء جدول إحصائيات الإداريين</p>";

        // 8. تحديث جدول المؤيدين لإضافة مرجع الإداري
        $sql_update_supporters = "ALTER TABLE supporters 
            ADD COLUMN IF NOT EXISTS admin_id INT,
            ADD FOREIGN KEY (admin_id) REFERENCES admins(id) ON DELETE SET NULL";
        
        try {
            executeQuery($sql_update_supporters);
            echo "<p class='success'><i class='fas fa-check'></i> تم تحديث جدول المؤيدين</p>";
        } catch (Exception $e) {
            echo "<p class='success'><i class='fas fa-info'></i> جدول المؤيدين محدث مسبقاً</p>";
        }

        // إنشاء إداري تجريبي
        $admin_password = password_hash('admin123', PASSWORD_DEFAULT);
        $sql_demo_admin = "INSERT IGNORE INTO admins (username, password, full_name, phone, email, role, status) 
                          VALUES ('admin', ?, 'إداري تجريبي', '07701234567', '<EMAIL>', 'admin', 'active')";
        
        executeQuery($sql_demo_admin, [$admin_password]);
        echo "<p class='success'><i class='fas fa-check'></i> تم إنشاء إداري تجريبي (admin / admin123)</p>";

        echo "<div class='alert alert-success mt-4'>";
        echo "<h3><i class='fas fa-check-circle'></i> تم إنشاء النظام الإداري بنجاح!</h3>";
        echo "<p>تم إنشاء جميع الجداول والبيانات المطلوبة للنظام الإداري الشامل</p>";
        echo "</div>";

        echo "<div class='alert alert-info'>";
        echo "<h6>معلومات تسجيل الدخول التجريبي:</h6>";
        echo "<ul>";
        echo "<li><strong>اسم المستخدم:</strong> admin</li>";
        echo "<li><strong>كلمة المرور:</strong> admin123</li>";
        echo "</ul>";
        echo "</div>";

    } catch (Exception $e) {
        echo "<div class='alert alert-danger'>";
        echo "<h5>خطأ في إنشاء النظام:</h5>";
        echo "<p>" . htmlspecialchars($e->getMessage()) . "</p>";
        echo "</div>";
    }

    echo "<div class='text-center mt-4'>";
    echo "<a href='modules/admin/login.php' class='btn btn-primary btn-lg me-2'><i class='fas fa-sign-in-alt'></i> تسجيل دخول الإداريين</a>";
    echo "<a href='modules/admin/dashboard.php' class='btn btn-success btn-lg'><i class='fas fa-tachometer-alt'></i> لوحة تحكم الإداريين</a>";
    echo "</div>";

} else {
    // عرض معلومات النظام
    echo "<div class='alert alert-warning'>";
    echo "<h5><i class='fas fa-info-circle'></i> النظام الإداري الشامل</h5>";
    echo "<p>سيتم إنشاء نظام إداري متكامل يشمل:</p>";
    echo "</div>";

    echo "<div class='row'>";
    
    echo "<div class='col-md-6'>";
    echo "<div class='card mb-3'>";
    echo "<div class='card-header bg-primary text-white'>";
    echo "<h6><i class='fas fa-users-cog'></i> إدارة الإداريين</h6>";
    echo "</div>";
    echo "<div class='card-body'>";
    echo "<ul>";
    echo "<li>تسجيل دخول منفصل للإداريين</li>";
    echo "<li>صلاحيات مختلفة حسب المنطقة</li>";
    echo "<li>تتبع نشاط الإداريين</li>";
    echo "<li>إحصائيات شخصية لكل إداري</li>";
    echo "</ul>";
    echo "</div>";
    echo "</div>";
    echo "</div>";

    echo "<div class='col-md-6'>";
    echo "<div class='card mb-3'>";
    echo "<div class='card-header bg-success text-white'>";
    echo "<h6><i class='fas fa-comments'></i> نظام الرسائل</h6>";
    echo "</div>";
    echo "<div class='card-body'>";
    echo "<ul>";
    echo "<li>رسائل بين الإدارة والإداريين</li>";
    echo "<li>إشعارات فورية</li>";
    echo "<li>مرفقات في الرسائل</li>";
    echo "<li>أولويات مختلفة للرسائل</li>";
    echo "</ul>";
    echo "</div>";
    echo "</div>";
    echo "</div>";

    echo "<div class='col-md-6'>";
    echo "<div class='card mb-3'>";
    echo "<div class='card-header bg-info text-white'>";
    echo "<h6><i class='fas fa-file-alt'></i> التقارير الأسبوعية</h6>";
    echo "</div>";
    echo "<div class='card-body'>";
    echo "<ul>";
    echo "<li>تقارير أسبوعية من الإداريين</li>";
    echo "<li>إحصائيات الأنشطة</li>";
    echo "<li>التحديات والإنجازات</li>";
    echo "<li>خطط الأسبوع القادم</li>";
    echo "</ul>";
    echo "</div>";
    echo "</div>";
    echo "</div>";

    echo "<div class='col-md-6'>";
    echo "<div class='card mb-3'>";
    echo "<div class='card-header bg-warning text-white'>";
    echo "<h6><i class='fas fa-hand-holding-heart'></i> مطالب المؤيدين</h6>";
    echo "</div>";
    echo "<div class='card-body'>";
    echo "<ul>";
    echo "<li>تسجيل مطالب المؤيدين</li>";
    echo "<li>تتبع حالة المطالب</li>";
    echo "<li>رد الإدارة على المطالب</li>";
    echo "<li>مرفقات وصور</li>";
    echo "</ul>";
    echo "</div>";
    echo "</div>";
    echo "</div>";

    echo "</div>";

    echo "<form method='POST' action=''>";
    echo "<div class='text-center mt-4'>";
    echo "<button type='submit' name='create_system' class='btn btn-success btn-lg'>";
    echo "<i class='fas fa-cogs'></i> إنشاء النظام الإداري";
    echo "</button>";
    echo "</div>";
    echo "</form>";

    echo "<div class='text-center mt-3'>";
    echo "<a href='modules/supporters/supporters.php' class='btn btn-primary me-2'><i class='fas fa-users'></i> صفحة المؤيدين</a>";
    echo "<a href='dashboard.php' class='btn btn-secondary'><i class='fas fa-home'></i> الصفحة الرئيسية</a>";
    echo "</div>";
}

echo "</div>";
echo "</body>";
echo "</html>";
?>
