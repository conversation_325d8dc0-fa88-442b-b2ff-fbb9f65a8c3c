<?php
// اختبار الاتصال بقاعدة البيانات
error_reporting(E_ALL);
ini_set('display_errors', 1);
header('Content-Type: text/html; charset=utf-8');

echo "<!DOCTYPE html>";
echo "<html lang='ar' dir='rtl'>";
echo "<head>";
echo "<meta charset='UTF-8'>";
echo "<title>اختبار الاتصال</title>";
echo "<link href='https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css' rel='stylesheet'>";
echo "</head>";
echo "<body style='padding:20px;'>";

echo "<div class='container'>";
echo "<h1>اختبار الاتصال بقاعدة البيانات</h1>";

// اختبار مباشر
echo "<h3>1. اختبار مباشر:</h3>";

try {
    $pdo = new PDO("mysql:host=localhost;dbname=irjnpfzw_mr;charset=utf8mb4", "irjnpfzw_mr", "irjnpfzw_mr");
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    echo "<p class='text-success'>✅ الاتصال المباشر نجح</p>";
    
    // اختبار الجداول
    $tables = ['users', 'admins', 'supporters', 'regions'];
    foreach ($tables as $table) {
        try {
            $stmt = $pdo->query("SELECT COUNT(*) FROM $table");
            $count = $stmt->fetchColumn();
            echo "<p class='text-success'>✅ جدول $table: $count سجل</p>";
        } catch (Exception $e) {
            echo "<p class='text-warning'>⚠️ جدول $table: " . $e->getMessage() . "</p>";
        }
    }
    
} catch (Exception $e) {
    echo "<p class='text-danger'>❌ فشل الاتصال المباشر: " . $e->getMessage() . "</p>";
}

// اختبار عبر ملف database.php
echo "<h3>2. اختبار عبر ملف database.php:</h3>";

try {
    require_once 'config/database.php';
    
    $conn = getDBConnection();
    if ($conn) {
        echo "<p class='text-success'>✅ الاتصال عبر database.php نجح</p>";
        
        // اختبار الدوال
        $result = fetchOne("SELECT COUNT(*) as count FROM supporters");
        if ($result) {
            echo "<p class='text-success'>✅ دالة fetchOne تعمل: " . $result['count'] . " مؤيد</p>";
        } else {
            echo "<p class='text-warning'>⚠️ دالة fetchOne لا تعمل</p>";
        }
        
        $results = fetchAll("SELECT * FROM supporters LIMIT 3");
        if ($results) {
            echo "<p class='text-success'>✅ دالة fetchAll تعمل: " . count($results) . " نتيجة</p>";
        } else {
            echo "<p class='text-warning'>⚠️ دالة fetchAll لا تعمل</p>";
        }
        
    } else {
        echo "<p class='text-danger'>❌ فشل الاتصال عبر database.php</p>";
    }
    
} catch (Exception $e) {
    echo "<p class='text-danger'>❌ خطأ في database.php: " . $e->getMessage() . "</p>";
}

// روابط الاختبار
echo "<h3>3. اختبار الصفحات:</h3>";
echo "<a href='dashboard.php' class='btn btn-primary me-2' target='_blank'>لوحة تحكم المرشح</a>";
echo "<a href='modules/admin/dashboard.php' class='btn btn-success me-2' target='_blank'>لوحة تحكم الإداريين</a>";
echo "<a href='demo_system.php' class='btn btn-warning' target='_blank'>النظام التجريبي</a>";

echo "</div>";
echo "</body>";
echo "</html>";
?>
