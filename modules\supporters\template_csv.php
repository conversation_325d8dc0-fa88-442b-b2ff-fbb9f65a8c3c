<?php
// إنشاء نموذج CSV للاستيراد
header('Content-Type: text/csv; charset=utf-8');
header('Content-Disposition: attachment; filename="نموذج_المؤيدين.csv"');
header('Cache-Control: max-age=0');

$output = fopen('php://output', 'w');

// كتابة BOM للدعم العربي
fprintf($output, chr(0xEF).chr(0xBB).chr(0xBF));

// العناوين
$headers = [
    'الاسم الكامل',
    'الجنس',
    'رقم الهاتف',
    'العنوان',
    'المنطقة',
    'تاريخ الميلاد',
    'رقم الناخب',
    'المهنة',
    'ملاحظات'
];

fputcsv($output, $headers);

// بيانات تجريبية
$sample_data = [
    ['أحمد محمد علي', 'ذكر', '07701234567', 'بغداد - الكرادة', 'الكرادة', '1990-01-01', '123456789', 'مهندس', 'مؤيد نشط'],
    ['فاطمة أحمد حسن', 'أنثى', '07801234567', 'بغداد - الجادرية', 'الجادرية', '1985-05-15', '987654321', 'طبيبة', 'مؤيدة متحمسة'],
    ['محمد علي حسن', 'ذكر', '07901234567', 'بغداد - الكاظمية', 'الكاظمية', '1988-12-10', '456789123', 'معلم', ''],
    ['زينب حسين محمد', 'أنثى', '07501234567', 'بغداد - الأعظمية', 'الأعظمية', '1992-03-20', '', 'محاسبة', 'مؤيدة جديدة']
];

foreach ($sample_data as $row) {
    fputcsv($output, $row);
}

fclose($output);
exit;
?>
