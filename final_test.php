<?php
// اختبار نهائي للنظام
error_reporting(E_ALL);
ini_set('display_errors', 1);
header('Content-Type: text/html; charset=utf-8');

echo "<!DOCTYPE html>";
echo "<html lang='ar' dir='rtl'>";
echo "<head>";
echo "<meta charset='UTF-8'>";
echo "<meta name='viewport' content='width=device-width, initial-scale=1.0'>";
echo "<title>اختبار نهائي للنظام</title>";
echo "<link href='https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css' rel='stylesheet'>";
echo "<style>body { font-family: Arial, sans-serif; padding: 20px; background: #f8f9fa; }</style>";
echo "</head>";
echo "<body>";

echo "<div class='container'>";
echo "<div class='row justify-content-center'>";
echo "<div class='col-md-10'>";

echo "<div class='card'>";
echo "<div class='card-header bg-success text-white'>";
echo "<h3>🎯 اختبار نهائي للنظام</h3>";
echo "</div>";
echo "<div class='card-body'>";

// اختبار قاعدة البيانات مع كلمة المرور الجديدة
echo "<h5>1. اختبار قاعدة البيانات:</h5>";

$configs_to_test = [
    ['irjnpfzw_mr', 'irjnpfzw_mr', 'Zain@123456789'],
    ['irjnpfzw_mr', 'irjnpfzw_mr', 'irjnpfzw_mr'],
    ['irjnpfzw_mr', 'irjnpfzw_abd', 'Zain@123456789'],
    ['irjnpfzw_mr', 'irjnpfzw_abd', 'abd123'],
    ['irjnpfzw_mr', 'irjnpfzw_user', 'Zain@123456789']
];

$connection_success = false;
$working_config = null;

foreach ($configs_to_test as $index => $config) {
    echo "<div class='mb-3'>";
    echo "<h6>تجربة " . ($index + 1) . ": {$config[0]} / {$config[1]} / " . str_repeat('*', strlen($config[2])) . "</h6>";
    
    try {
        $pdo = new PDO("mysql:host=localhost;dbname={$config[0]};charset=utf8mb4", $config[1], $config[2]);
        $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
        
        echo "<p class='text-success'>✅ نجح الاتصال!</p>";
        $connection_success = true;
        $working_config = $config;
        break;
        
    } catch (Exception $e) {
        echo "<p class='text-danger'>❌ فشل: " . $e->getMessage() . "</p>";
    }
    echo "</div>";
}

if ($connection_success) {
    echo "<div class='alert alert-success'>";
    echo "<h5>🎉 تم العثور على الإعدادات الصحيحة!</h5>";
    echo "<p><strong>المعلومات الصحيحة:</strong></p>";
    echo "<ul>";
    echo "<li><strong>Database:</strong> {$working_config[0]}</li>";
    echo "<li><strong>Username:</strong> {$working_config[1]}</li>";
    echo "<li><strong>Password:</strong> " . str_repeat('*', strlen($working_config[2])) . "</li>";
    echo "</ul>";
    
    // تحديث ملف database.php
    $database_content = "<?php
// إعدادات قاعدة البيانات - صحيحة ومختبرة
define('DB_HOST', 'localhost');
define('DB_NAME', '{$working_config[0]}');
define('DB_USER', '{$working_config[1]}');
define('DB_PASS', '{$working_config[2]}');
define('DB_CHARSET', 'utf8mb4');

class Database {
    private \$host = DB_HOST;
    private \$db_name = DB_NAME;
    private \$username = DB_USER;
    private \$password = DB_PASS;
    private \$charset = DB_CHARSET;
    public \$conn;

    public function getConnection() {
        \$this->conn = null;
        try {
            \$dsn = \"mysql:host=\" . \$this->host . \";dbname=\" . \$this->db_name . \";charset=utf8mb4\";
            \$this->conn = new PDO(\$dsn, \$this->username, \$this->password, array(
                PDO::MYSQL_ATTR_INIT_COMMAND => \"SET NAMES utf8mb4 COLLATE utf8mb4_unicode_ci\",
                PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
                PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
                PDO::ATTR_EMULATE_PREPARES => false
            ));
        } catch(PDOException \$exception) {
            echo \"خطأ في الاتصال: \" . \$exception->getMessage();
        }
        return \$this->conn;
    }
}

// دالة للحصول على اتصال قاعدة البيانات
function getDBConnection() {
    \$database = new Database();
    return \$database->getConnection();
}

// دالة لتنفيذ استعلام آمن
function executeQuery(\$sql, \$params = []) {
    try {
        \$conn = getDBConnection();
        \$stmt = \$conn->prepare(\$sql);
        \$result = \$stmt->execute(\$params);
        return \$result;
    } catch(PDOException \$e) {
        error_log(\"Database Error: \" . \$e->getMessage());
        return false;
    }
}

// دالة للحصول على صف واحد
function fetchOne(\$sql, \$params = []) {
    try {
        \$conn = getDBConnection();
        \$stmt = \$conn->prepare(\$sql);
        \$stmt->execute(\$params);
        return \$stmt->fetch(PDO::FETCH_ASSOC);
    } catch(PDOException \$e) {
        error_log(\"Database Error: \" . \$e->getMessage());
        return false;
    }
}

// دالة للحصول على عدة صفوف
function fetchAll(\$sql, \$params = []) {
    try {
        \$conn = getDBConnection();
        \$stmt = \$conn->prepare(\$sql);
        \$stmt->execute(\$params);
        return \$stmt->fetchAll(PDO::FETCH_ASSOC);
    } catch(PDOException \$e) {
        error_log(\"Database Error: \" . \$e->getMessage());
        return [];
    }
}

// دالة للحصول على آخر ID مدرج
function getLastInsertId() {
    \$conn = getDBConnection();
    return \$conn->lastInsertId();
}

// دالة لعرض الرسائل
function showMessage(\$message, \$type = 'info') {
    if (\$type === 'error') {
        error_log(\$message);
    }
}

// متغير عام لاتصال قاعدة البيانات
\$GLOBALS['pdo'] = getDBConnection();
?>";
    
    if (file_put_contents('config/database.php', $database_content)) {
        echo "<p class='text-success'>✅ تم تحديث ملف database.php بالمعلومات الصحيحة</p>";
    }
    
    echo "</div>";
    
    // اختبار الجداول
    echo "<h5>2. اختبار الجداول:</h5>";
    
    $tables = ['users', 'admins', 'supporters', 'regions', 'messages', 'supporter_requests', 'weekly_reports', 'notifications'];
    
    foreach ($tables as $table) {
        try {
            $stmt = $pdo->query("SELECT COUNT(*) FROM $table");
            $count = $stmt->fetchColumn();
            echo "<p class='text-success'>✅ جدول $table - يحتوي على $count سجل</p>";
        } catch (Exception $e) {
            echo "<p class='text-warning'>⚠️ جدول $table - قد يحتاج إنشاء: " . $e->getMessage() . "</p>";
        }
    }
    
    // روابط الاختبار
    echo "<h5>3. اختبار النظام:</h5>";
    echo "<div class='row'>";
    
    $test_links = [
        ['لوحة تحكم المرشح', 'dashboard.php', 'primary'],
        ['لوحة تحكم الإداريين', 'modules/admin/dashboard.php', 'success'],
        ['تسجيل دخول الإداريين', 'modules/admin/login.php', 'info'],
        ['إضافة مؤيد', 'modules/admin/add_supporter.php', 'warning']
    ];
    
    foreach ($test_links as $link) {
        echo "<div class='col-md-6 mb-2'>";
        echo "<a href='{$link[1]}' class='btn btn-{$link[2]} w-100' target='_blank'>";
        echo "<i class='fas fa-external-link-alt me-2'></i>{$link[0]}";
        echo "</a>";
        echo "</div>";
    }
    
    echo "</div>";
    
} else {
    echo "<div class='alert alert-danger'>";
    echo "<h5>❌ لم يتم العثور على إعدادات صحيحة</h5>";
    echo "<p>جرب الحلول البديلة:</p>";
    echo "<div class='text-center'>";
    echo "<a href='demo_system.php' class='btn btn-warning me-2'>النظام التجريبي</a>";
    echo "<a href='simple_dashboard.php' class='btn btn-info me-2'>لوحة تحكم بسيطة</a>";
    echo "<a href='quick_fix.php' class='btn btn-danger'>إصلاح يدوي</a>";
    echo "</div>";
    echo "</div>";
}

echo "</div>";
echo "</div>";

echo "</div>";
echo "</div>";
echo "</div>";

echo "</body>";
echo "</html>";
?>
