<?php
// دوال المطالب
require_once __DIR__ . '/../config/database.php';
require_once __DIR__ . '/notification_functions.php';

/**
 * إضافة مطلب جديد
 */
function addSupporterRequest($data, $admin_id) {
    // توليد رقم المطلب
    $request_number = generateRequestNumber();
    
    $sql = "INSERT INTO supporter_requests (
        request_number, admin_id, supporter_id, supporter_name, supporter_phone,
        supporter_address, request_type, category, title, description,
        amount_requested, currency, priority, urgency_level,
        expected_completion_date, beneficiary_count, location_details,
        contact_person, contact_phone, follow_up_required, follow_up_date
    ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)";
    
    $params = [
        $request_number,
        $admin_id,
        $data['supporter_id'] ?? null,
        $data['supporter_name'],
        $data['supporter_phone'],
        $data['supporter_address'] ?? null,
        $data['request_type'],
        $data['category'] ?? 'normal',
        $data['title'],
        $data['description'],
        $data['amount_requested'] ?? null,
        $data['currency'] ?? 'IQD',
        $data['priority'] ?? 'normal',
        $data['urgency_level'] ?? 'medium',
        $data['expected_completion_date'] ?? null,
        $data['beneficiary_count'] ?? 1,
        $data['location_details'] ?? null,
        $data['contact_person'] ?? null,
        $data['contact_phone'] ?? null,
        $data['follow_up_required'] ?? false,
        $data['follow_up_date'] ?? null
    ];
    
    try {
        $result = executeQuery($sql, $params);
        $request_id = $GLOBALS['pdo']->lastInsertId();
        
        // تسجيل تغيير الحالة
        logRequestStatusChange($request_id, null, 'pending', $admin_id, 'تم إنشاء المطلب');
        
        // تسجيل النشاط
        logActivity([
            'user_type' => 'admin',
            'user_id' => $admin_id,
            'action_type' => 'create',
            'target_type' => 'request',
            'target_id' => $request_id,
            'target_name' => $data['title'],
            'description' => 'تم إنشاء مطلب جديد: ' . $data['title']
        ]);
        
        // إنشاء إشعار
        notifyNewRequest($request_id);
        
        // تحديث إحصائيات الإداري
        updateAdminDailyStats($admin_id, 'requests_submitted', 1);
        
        return $request_id;
    } catch (Exception $e) {
        error_log("خطأ في إضافة المطلب: " . $e->getMessage());
        return false;
    }
}

/**
 * توليد رقم مطلب فريد
 */
function generateRequestNumber() {
    $year = date('Y');
    $month = date('m');
    
    // جلب آخر رقم مطلب لهذا الشهر
    $last_request = fetchOne("
        SELECT request_number 
        FROM supporter_requests 
        WHERE request_number LIKE ? 
        ORDER BY id DESC 
        LIMIT 1
    ", ["REQ-$year$month-%"]);
    
    if ($last_request) {
        $last_number = intval(substr($last_request['request_number'], -4));
        $new_number = $last_number + 1;
    } else {
        $new_number = 1;
    }
    
    return sprintf("REQ-%s%s-%04d", $year, $month, $new_number);
}

/**
 * تحديث حالة المطلب
 */
function updateRequestStatus($request_id, $new_status, $user_id, $notes = null, $response = null) {
    // جلب الحالة الحالية
    $current = fetchOne("SELECT status, title FROM supporter_requests WHERE id = ?", [$request_id]);
    
    if (!$current) {
        return false;
    }
    
    $old_status = $current['status'];
    
    // تحديث الحالة
    $sql = "UPDATE supporter_requests SET status = ?, updated_at = NOW()";
    $params = [$new_status];
    
    if ($response) {
        $sql .= ", management_response = ?";
        $params[] = $response;
    }
    
    if ($new_status === 'completed') {
        $sql .= ", completed_at = NOW(), actual_completion_date = CURDATE()";
    }
    
    $sql .= " WHERE id = ?";
    $params[] = $request_id;
    
    try {
        executeQuery($sql, $params);
        
        // تسجيل تغيير الحالة
        logRequestStatusChange($request_id, $old_status, $new_status, $user_id, $notes);
        
        // تسجيل النشاط
        logActivity([
            'user_type' => 'management',
            'user_id' => $user_id,
            'action_type' => 'update',
            'target_type' => 'request',
            'target_id' => $request_id,
            'target_name' => $current['title'],
            'description' => "تم تغيير حالة المطلب من $old_status إلى $new_status"
        ]);
        
        // إنشاء إشعار للإداري المسؤول
        $request_data = fetchOne("SELECT admin_id, title FROM supporter_requests WHERE id = ?", [$request_id]);
        if ($request_data) {
            createNotification([
                'user_type' => 'admin',
                'user_id' => $request_data['admin_id'],
                'title' => 'تحديث حالة المطلب',
                'message' => "تم تحديث حالة المطلب '{$request_data['title']}' إلى: $new_status",
                'type' => 'info',
                'category' => 'request',
                'related_type' => 'request',
                'related_id' => $request_id,
                'action_url' => "modules/admin/requests.php?view=$request_id"
            ]);
        }
        
        return true;
    } catch (Exception $e) {
        error_log("خطأ في تحديث حالة المطلب: " . $e->getMessage());
        return false;
    }
}

/**
 * تسجيل تغيير حالة المطلب
 */
function logRequestStatusChange($request_id, $old_status, $new_status, $user_id, $notes = null) {
    $sql = "INSERT INTO request_status_history (
        request_id, old_status, new_status, changed_by, change_reason, notes
    ) VALUES (?, ?, ?, ?, ?, ?)";
    
    $params = [
        $request_id,
        $old_status,
        $new_status,
        $user_id,
        "تغيير الحالة من " . ($old_status ?? 'جديد') . " إلى $new_status",
        $notes
    ];
    
    return executeQuery($sql, $params);
}

/**
 * البحث في المطالب
 */
function searchRequests($search_term = '', $filters = [], $limit = 50, $offset = 0) {
    $sql = "SELECT sr.*, a.full_name as admin_name, r.name as region_name,
            (SELECT COUNT(*) FROM request_attachments WHERE request_id = sr.id) as attachment_count
            FROM supporter_requests sr 
            LEFT JOIN admins a ON sr.admin_id = a.id 
            LEFT JOIN regions r ON a.region_id = r.id 
            WHERE 1=1";
    $params = [];
    
    // البحث النصي
    if (!empty($search_term)) {
        $sql .= " AND (MATCH(sr.title, sr.description, sr.supporter_name) AGAINST(? IN NATURAL LANGUAGE MODE) 
                  OR sr.request_number LIKE ? OR sr.supporter_phone LIKE ?)";
        $params[] = $search_term;
        $params[] = "%$search_term%";
        $params[] = "%$search_term%";
    }
    
    // فلاتر
    if (!empty($filters['status'])) {
        $sql .= " AND sr.status = ?";
        $params[] = $filters['status'];
    }
    
    if (!empty($filters['request_type'])) {
        $sql .= " AND sr.request_type = ?";
        $params[] = $filters['request_type'];
    }
    
    if (!empty($filters['priority'])) {
        $sql .= " AND sr.priority = ?";
        $params[] = $filters['priority'];
    }
    
    if (!empty($filters['admin_id'])) {
        $sql .= " AND sr.admin_id = ?";
        $params[] = $filters['admin_id'];
    }
    
    if (!empty($filters['date_from'])) {
        $sql .= " AND sr.submitted_at >= ?";
        $params[] = $filters['date_from'];
    }
    
    if (!empty($filters['date_to'])) {
        $sql .= " AND sr.submitted_at <= ?";
        $params[] = $filters['date_to'] . ' 23:59:59';
    }
    
    $sql .= " ORDER BY sr.priority DESC, sr.submitted_at DESC LIMIT ? OFFSET ?";
    $params[] = $limit;
    $params[] = $offset;
    
    return fetchAll($sql, $params);
}

/**
 * عدد المطالب حسب الفلاتر
 */
function countRequests($search_term = '', $filters = []) {
    $sql = "SELECT COUNT(*) as count FROM supporter_requests sr 
            LEFT JOIN admins a ON sr.admin_id = a.id 
            WHERE 1=1";
    $params = [];
    
    // البحث النصي
    if (!empty($search_term)) {
        $sql .= " AND (MATCH(sr.title, sr.description, sr.supporter_name) AGAINST(? IN NATURAL LANGUAGE MODE) 
                  OR sr.request_number LIKE ? OR sr.supporter_phone LIKE ?)";
        $params[] = $search_term;
        $params[] = "%$search_term%";
        $params[] = "%$search_term%";
    }
    
    // فلاتر
    if (!empty($filters['status'])) {
        $sql .= " AND sr.status = ?";
        $params[] = $filters['status'];
    }
    
    if (!empty($filters['request_type'])) {
        $sql .= " AND sr.request_type = ?";
        $params[] = $filters['request_type'];
    }
    
    if (!empty($filters['priority'])) {
        $sql .= " AND sr.priority = ?";
        $params[] = $filters['priority'];
    }
    
    if (!empty($filters['admin_id'])) {
        $sql .= " AND sr.admin_id = ?";
        $params[] = $filters['admin_id'];
    }
    
    if (!empty($filters['date_from'])) {
        $sql .= " AND sr.submitted_at >= ?";
        $params[] = $filters['date_from'];
    }
    
    if (!empty($filters['date_to'])) {
        $sql .= " AND sr.submitted_at <= ?";
        $params[] = $filters['date_to'] . ' 23:59:59';
    }
    
    $result = fetchOne($sql, $params);
    return $result['count'] ?? 0;
}

/**
 * إضافة مرفق للمطلب
 */
function addRequestAttachment($request_id, $file_data, $admin_id) {
    $sql = "INSERT INTO request_attachments (
        request_id, file_name, original_name, file_path, file_size,
        file_type, mime_type, uploaded_by, attachment_type, description
    ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)";
    
    $params = [
        $request_id,
        $file_data['file_name'],
        $file_data['original_name'],
        $file_data['file_path'],
        $file_data['file_size'],
        $file_data['file_type'],
        $file_data['mime_type'],
        $admin_id,
        $file_data['attachment_type'] ?? 'document',
        $file_data['description'] ?? null
    ];
    
    try {
        $result = executeQuery($sql, $params);
        $attachment_id = $GLOBALS['pdo']->lastInsertId();
        
        // تسجيل النشاط
        logActivity([
            'user_type' => 'admin',
            'user_id' => $admin_id,
            'action_type' => 'create',
            'target_type' => 'file',
            'target_id' => $attachment_id,
            'description' => 'تم رفع مرفق للمطلب: ' . $file_data['original_name']
        ]);
        
        return $attachment_id;
    } catch (Exception $e) {
        error_log("خطأ في إضافة مرفق المطلب: " . $e->getMessage());
        return false;
    }
}

/**
 * جلب مرفقات المطلب
 */
function getRequestAttachments($request_id) {
    return fetchAll("
        SELECT ra.*, a.full_name as uploaded_by_name 
        FROM request_attachments ra 
        LEFT JOIN admins a ON ra.uploaded_by = a.id 
        WHERE ra.request_id = ? 
        ORDER BY ra.upload_date DESC
    ", [$request_id]);
}

/**
 * إضافة تعليق على المطلب
 */
function addRequestComment($request_id, $comment_data, $user_id, $user_type) {
    $sql = "INSERT INTO request_comments (
        request_id, commenter_type, commenter_id, comment, comment_type,
        is_internal, parent_comment_id
    ) VALUES (?, ?, ?, ?, ?, ?, ?)";
    
    $params = [
        $request_id,
        $user_type,
        $user_id,
        $comment_data['comment'],
        $comment_data['comment_type'] ?? 'note',
        $comment_data['is_internal'] ?? false,
        $comment_data['parent_comment_id'] ?? null
    ];
    
    try {
        $result = executeQuery($sql, $params);
        $comment_id = $GLOBALS['pdo']->lastInsertId();
        
        // تسجيل النشاط
        logActivity([
            'user_type' => $user_type,
            'user_id' => $user_id,
            'action_type' => 'create',
            'target_type' => 'request',
            'target_id' => $request_id,
            'description' => 'تم إضافة تعليق على المطلب'
        ]);
        
        return $comment_id;
    } catch (Exception $e) {
        error_log("خطأ في إضافة التعليق: " . $e->getMessage());
        return false;
    }
}

/**
 * جلب تعليقات المطلب
 */
function getRequestComments($request_id) {
    return fetchAll("
        SELECT rc.*, 
               CASE 
                   WHEN rc.commenter_type = 'admin' THEN a.full_name
                   WHEN rc.commenter_type = 'management' THEN u.full_name
                   ELSE 'النظام'
               END as commenter_name
        FROM request_comments rc 
        LEFT JOIN admins a ON rc.commenter_id = a.id AND rc.commenter_type = 'admin'
        LEFT JOIN users u ON rc.commenter_id = u.id AND rc.commenter_type = 'management'
        WHERE rc.request_id = ? 
        ORDER BY rc.created_at ASC
    ", [$request_id]);
}

/**
 * جلب تاريخ حالات المطلب
 */
function getRequestStatusHistory($request_id) {
    return fetchAll("
        SELECT rsh.*, u.full_name as changed_by_name 
        FROM request_status_history rsh 
        LEFT JOIN users u ON rsh.changed_by = u.id 
        WHERE rsh.request_id = ? 
        ORDER BY rsh.changed_at ASC
    ", [$request_id]);
}

/**
 * إحصائيات المطالب
 */
function getRequestsStatistics($filters = []) {
    $sql = "SELECT 
        COUNT(*) as total,
        SUM(CASE WHEN status = 'pending' THEN 1 ELSE 0 END) as pending_count,
        SUM(CASE WHEN status = 'received' THEN 1 ELSE 0 END) as received_count,
        SUM(CASE WHEN status = 'in_progress' THEN 1 ELSE 0 END) as in_progress_count,
        SUM(CASE WHEN status = 'completed' THEN 1 ELSE 0 END) as completed_count,
        SUM(CASE WHEN status = 'rejected' THEN 1 ELSE 0 END) as rejected_count,
        SUM(CASE WHEN priority = 'urgent' THEN 1 ELSE 0 END) as urgent_count,
        SUM(CASE WHEN request_type = 'financial' THEN 1 ELSE 0 END) as financial_count,
        SUM(CASE WHEN request_type = 'medical' THEN 1 ELSE 0 END) as medical_count,
        SUM(amount_requested) as total_amount_requested,
        AVG(DATEDIFF(COALESCE(actual_completion_date, CURDATE()), DATE(submitted_at))) as avg_processing_days
    FROM supporter_requests WHERE 1=1";
    
    $params = [];
    
    if (!empty($filters['admin_id'])) {
        $sql .= " AND admin_id = ?";
        $params[] = $filters['admin_id'];
    }
    
    if (!empty($filters['date_from'])) {
        $sql .= " AND submitted_at >= ?";
        $params[] = $filters['date_from'];
    }
    
    if (!empty($filters['date_to'])) {
        $sql .= " AND submitted_at <= ?";
        $params[] = $filters['date_to'] . ' 23:59:59';
    }
    
    return fetchOne($sql, $params);
}

/**
 * المطالب المتأخرة
 */
function getOverdueRequests($days = 7) {
    return fetchAll("
        SELECT sr.*, a.full_name as admin_name 
        FROM supporter_requests sr 
        JOIN admins a ON sr.admin_id = a.id 
        WHERE sr.status IN ('pending', 'received', 'in_progress') 
        AND sr.submitted_at < DATE_SUB(NOW(), INTERVAL ? DAY)
        ORDER BY sr.submitted_at ASC
    ", [$days]);
}

/**
 * المطالب العاجلة
 */
function getUrgentRequests() {
    return fetchAll("
        SELECT sr.*, a.full_name as admin_name 
        FROM supporter_requests sr 
        JOIN admins a ON sr.admin_id = a.id 
        WHERE sr.priority = 'urgent' 
        AND sr.status NOT IN ('completed', 'rejected', 'cancelled')
        ORDER BY sr.submitted_at ASC
    ");
}
?>
