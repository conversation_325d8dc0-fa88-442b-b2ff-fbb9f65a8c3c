<?php
// إنشاء جداول المستخدمين والإداريين
header('Content-Type: text/html; charset=utf-8');
ini_set('default_charset', 'utf-8');
mb_internal_encoding('UTF-8');

require_once '../config/config.php';
require_once '../config/database.php';

function createUsersTables() {
    $tables = [];
    
    // جدول المناطق
    $tables['regions'] = "CREATE TABLE IF NOT EXISTS regions (
        id INT AUTO_INCREMENT PRIMARY KEY,
        name VARCHAR(100) NOT NULL,
        description TEXT,
        admin_count INT DEFAULT 0,
        supporter_count INT DEFAULT 0,
        status ENUM('active', 'inactive') DEFAULT 'active',
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        INDEX idx_name (name),
        INDEX idx_status (status)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci";

    // جدول المستخدمين الرئيسيين
    $tables['users'] = "CREATE TABLE IF NOT EXISTS users (
        id INT AUTO_INCREMENT PRIMARY KEY,
        username VARCHAR(50) UNIQUE NOT NULL,
        password VARCHAR(255) NOT NULL,
        full_name VARCHAR(100) NOT NULL,
        email VARCHAR(100),
        phone VARCHAR(15),
        user_type ENUM('candidate', 'manager', 'supervisor') DEFAULT 'manager',
        status ENUM('active', 'inactive') DEFAULT 'active',
        last_login DATETIME,
        login_attempts INT DEFAULT 0,
        locked_until DATETIME NULL,
        profile_image VARCHAR(255),
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        INDEX idx_username (username),
        INDEX idx_user_type (user_type),
        INDEX idx_status (status)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci";

    // جدول الإداريين
    $tables['admins'] = "CREATE TABLE IF NOT EXISTS admins (
        id INT AUTO_INCREMENT PRIMARY KEY,
        username VARCHAR(50) UNIQUE NOT NULL,
        password VARCHAR(255) NOT NULL,
        full_name VARCHAR(100) NOT NULL,
        phone VARCHAR(15) NOT NULL,
        email VARCHAR(100),
        region_id INT,
        role ENUM('admin', 'supervisor', 'coordinator') DEFAULT 'admin',
        status ENUM('active', 'inactive', 'suspended') DEFAULT 'active',
        last_login DATETIME,
        login_attempts INT DEFAULT 0,
        locked_until DATETIME NULL,
        profile_image VARCHAR(255),
        hire_date DATE,
        salary DECIMAL(10,2),
        notes TEXT,
        created_by INT,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        FOREIGN KEY (region_id) REFERENCES regions(id) ON DELETE SET NULL,
        FOREIGN KEY (created_by) REFERENCES users(id) ON DELETE SET NULL,
        INDEX idx_username (username),
        INDEX idx_region_id (region_id),
        INDEX idx_role (role),
        INDEX idx_status (status),
        INDEX idx_phone (phone)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci";

    // جدول صلاحيات الإداريين
    $tables['admin_permissions'] = "CREATE TABLE IF NOT EXISTS admin_permissions (
        id INT AUTO_INCREMENT PRIMARY KEY,
        admin_id INT NOT NULL,
        permission_name VARCHAR(100) NOT NULL,
        permission_value BOOLEAN DEFAULT TRUE,
        granted_by INT,
        granted_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (admin_id) REFERENCES admins(id) ON DELETE CASCADE,
        FOREIGN KEY (granted_by) REFERENCES users(id) ON DELETE SET NULL,
        UNIQUE KEY unique_admin_permission (admin_id, permission_name),
        INDEX idx_admin_id (admin_id),
        INDEX idx_permission_name (permission_name)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci";

    // جدول جلسات الإداريين
    $tables['admin_sessions'] = "CREATE TABLE IF NOT EXISTS admin_sessions (
        id INT AUTO_INCREMENT PRIMARY KEY,
        admin_id INT NOT NULL,
        session_token VARCHAR(255) NOT NULL,
        ip_address VARCHAR(45),
        user_agent TEXT,
        device_info VARCHAR(255),
        location VARCHAR(100),
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        expires_at DATETIME NOT NULL,
        is_active BOOLEAN DEFAULT TRUE,
        last_activity TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        logout_at DATETIME NULL,
        FOREIGN KEY (admin_id) REFERENCES admins(id) ON DELETE CASCADE,
        INDEX idx_session_token (session_token),
        INDEX idx_admin_id (admin_id),
        INDEX idx_expires_at (expires_at),
        INDEX idx_is_active (is_active)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci";

    // جدول إحصائيات الإداريين اليومية
    $tables['admin_daily_stats'] = "CREATE TABLE IF NOT EXISTS admin_daily_stats (
        id INT AUTO_INCREMENT PRIMARY KEY,
        admin_id INT NOT NULL,
        date DATE NOT NULL,
        supporters_added INT DEFAULT 0,
        calls_made INT DEFAULT 0,
        meetings_held INT DEFAULT 0,
        requests_submitted INT DEFAULT 0,
        messages_sent INT DEFAULT 0,
        reports_submitted INT DEFAULT 0,
        login_count INT DEFAULT 0,
        active_hours DECIMAL(4,2) DEFAULT 0,
        first_login TIME,
        last_logout TIME,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        FOREIGN KEY (admin_id) REFERENCES admins(id) ON DELETE CASCADE,
        UNIQUE KEY unique_admin_date (admin_id, date),
        INDEX idx_date (date),
        INDEX idx_admin_id (admin_id)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci";

    return $tables;
}

function insertDefaultData() {
    $queries = [];
    
    // إدراج مناطق افتراضية
    $queries[] = "INSERT IGNORE INTO regions (name, description) VALUES 
        ('بغداد - الكرخ', 'منطقة الكرخ في بغداد'),
        ('بغداد - الرصافة', 'منطقة الرصافة في بغداد'),
        ('البصرة', 'محافظة البصرة'),
        ('أربيل', 'محافظة أربيل'),
        ('الموصل', 'محافظة نينوى - الموصل'),
        ('النجف', 'محافظة النجف'),
        ('كربلاء', 'محافظة كربلاء'),
        ('الحلة', 'محافظة بابل'),
        ('الناصرية', 'محافظة ذي قار'),
        ('العمارة', 'محافظة ميسان')";

    // إدراج مستخدم رئيسي (المرشح)
    $candidate_password = password_hash('candidate123', PASSWORD_DEFAULT);
    $queries[] = "INSERT IGNORE INTO users (username, password, full_name, phone, email, user_type) VALUES 
        ('candidate', '$candidate_password', 'المرشح الرئيسي', '07701234567', '<EMAIL>', 'candidate')";

    // إدراج إداري تجريبي
    $admin_password = password_hash('admin123', PASSWORD_DEFAULT);
    $queries[] = "INSERT IGNORE INTO admins (username, password, full_name, phone, email, region_id, role) VALUES 
        ('admin', '$admin_password', 'إداري تجريبي', '07701234568', '<EMAIL>', 1, 'admin')";

    // إدراج صلاحيات افتراضية للإداري
    $queries[] = "INSERT IGNORE INTO admin_permissions (admin_id, permission_name) VALUES 
        (1, 'add_supporters'),
        (1, 'edit_supporters'),
        (1, 'view_supporters'),
        (1, 'send_messages'),
        (1, 'submit_requests'),
        (1, 'create_reports'),
        (1, 'view_statistics')";

    return $queries;
}

// تشغيل الدوال إذا تم استدعاء الملف مباشرة
if (basename($_SERVER['PHP_SELF']) == 'create_users_tables.php') {
    echo "<!DOCTYPE html>";
    echo "<html lang='ar' dir='rtl'>";
    echo "<head>";
    echo "<meta charset='UTF-8'>";
    echo "<meta name='viewport' content='width=device-width, initial-scale=1.0'>";
    echo "<title>إنشاء جداول المستخدمين والإداريين</title>";
    echo "<link href='https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css' rel='stylesheet'>";
    echo "<link href='https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css' rel='stylesheet'>";
    echo "<style>";
    echo "body { font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); min-height: 100vh; padding: 2rem; }";
    echo ".system-card { background: white; border-radius: 15px; box-shadow: 0 10px 30px rgba(0,0,0,0.1); padding: 2rem; margin: 0 auto; max-width: 1000px; }";
    echo ".test-result { padding: 10px; margin: 5px 0; border-radius: 5px; }";
    echo ".test-success { background: #d4edda; border: 1px solid #c3e6cb; }";
    echo ".test-error { background: #f8d7da; border: 1px solid #f5c6cb; }";
    echo "</style>";
    echo "</head>";
    echo "<body>";

    echo "<div class='system-card'>";
    echo "<h1 class='text-center mb-4'><i class='fas fa-users'></i> إنشاء جداول المستخدمين والإداريين</h1>";

    if (isset($_POST['create_users_tables'])) {
        echo "<div class='alert alert-info'>";
        echo "<h5>جاري إنشاء جداول المستخدمين والإداريين...</h5>";
        echo "</div>";

        try {
            // إنشاء الجداول
            $tables = createUsersTables();
            foreach ($tables as $table_name => $sql) {
                try {
                    executeQuery($sql);
                    echo "<div class='test-result test-success'><i class='fas fa-check'></i> جدول $table_name: تم إنشاؤه بنجاح</div>";
                } catch (Exception $e) {
                    echo "<div class='test-result test-success'><i class='fas fa-info'></i> جدول $table_name: موجود مسبقاً</div>";
                }
            }

            // إدراج البيانات الافتراضية
            echo "<h6 class='mt-4'>إدراج البيانات الافتراضية:</h6>";
            $queries = insertDefaultData();
            foreach ($queries as $query) {
                try {
                    executeQuery($query);
                    echo "<div class='test-result test-success'><i class='fas fa-check'></i> تم إدراج البيانات الافتراضية</div>";
                } catch (Exception $e) {
                    echo "<div class='test-result test-success'><i class='fas fa-info'></i> البيانات موجودة مسبقاً</div>";
                }
            }

            echo "<div class='alert alert-success mt-4'>";
            echo "<h3><i class='fas fa-check-circle'></i> تم إنشاء جداول المستخدمين والإداريين بنجاح!</h3>";
            echo "</div>";

        } catch (Exception $e) {
            echo "<div class='alert alert-danger'>";
            echo "<h5>خطأ في الإنشاء:</h5>";
            echo "<p>" . htmlspecialchars($e->getMessage()) . "</p>";
            echo "</div>";
        }

        echo "<div class='text-center mt-4'>";
        echo "<a href='create_supporters_tables.php' class='btn btn-primary btn-lg me-2'><i class='fas fa-user-friends'></i> إنشاء جداول المؤيدين</a>";
        echo "<a href='../dashboard.php' class='btn btn-success btn-lg'><i class='fas fa-home'></i> الصفحة الرئيسية</a>";
        echo "</div>";

    } else {
        echo "<div class='alert alert-warning'>";
        echo "<h5><i class='fas fa-info-circle'></i> إنشاء جداول المستخدمين والإداريين</h5>";
        echo "<p>سيتم إنشاء جميع الجداول المتعلقة بالمستخدمين والإداريين</p>";
        echo "</div>";

        echo "<div class='card mb-4'>";
        echo "<div class='card-header bg-primary text-white'>";
        echo "<h6><i class='fas fa-table'></i> الجداول التي سيتم إنشاؤها</h6>";
        echo "</div>";
        echo "<div class='card-body'>";
        echo "<div class='row'>";
        echo "<div class='col-md-6'>";
        echo "<ul>";
        echo "<li><strong>regions:</strong> المناطق الجغرافية</li>";
        echo "<li><strong>users:</strong> المستخدمين الرئيسيين</li>";
        echo "<li><strong>admins:</strong> الإداريين</li>";
        echo "</ul>";
        echo "</div>";
        echo "<div class='col-md-6'>";
        echo "<ul>";
        echo "<li><strong>admin_permissions:</strong> صلاحيات الإداريين</li>";
        echo "<li><strong>admin_sessions:</strong> جلسات الإداريين</li>";
        echo "<li><strong>admin_daily_stats:</strong> إحصائيات يومية</li>";
        echo "</ul>";
        echo "</div>";
        echo "</div>";
        echo "</div>";
        echo "</div>";

        echo "<form method='POST' action=''>";
        echo "<div class='text-center'>";
        echo "<button type='submit' name='create_users_tables' class='btn btn-success btn-lg'>";
        echo "<i class='fas fa-database'></i> إنشاء جداول المستخدمين والإداريين";
        echo "</button>";
        echo "</div>";
        echo "</form>";

        echo "<div class='text-center mt-3'>";
        echo "<a href='../dashboard.php' class='btn btn-secondary'><i class='fas fa-home'></i> الصفحة الرئيسية</a>";
        echo "</div>";
    }

    echo "</div>";
    echo "</body>";
    echo "</html>";
}
?>
