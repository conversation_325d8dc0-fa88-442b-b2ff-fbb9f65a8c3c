<?php
// إصلاح مشاكل الاستيراد والتصدير

header('Content-Type: text/html; charset=utf-8');
ini_set('default_charset', 'utf-8');
mb_internal_encoding('UTF-8');

require_once 'config/config.php';
require_once 'config/database.php';

echo "<!DOCTYPE html>";
echo "<html lang='ar' dir='rtl'>";
echo "<head>";
echo "<meta charset='UTF-8'>";
echo "<meta name='viewport' content='width=device-width, initial-scale=1.0'>";
echo "<title>إصلاح الاستيراد والتصدير</title>";
echo "<link href='https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css' rel='stylesheet'>";
echo "<link href='https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css' rel='stylesheet'>";
echo "<style>";
echo "body { font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); min-height: 100vh; padding: 2rem; }";
echo ".fix-card { background: white; border-radius: 15px; box-shadow: 0 10px 30px rgba(0,0,0,0.1); padding: 2rem; margin: 0 auto; max-width: 900px; }";
echo ".success { color: #28a745; }";
echo ".error { color: #dc3545; }";
echo ".feature-card { border: 1px solid #e9ecef; border-radius: 10px; padding: 20px; margin-bottom: 20px; }";
echo "</style>";
echo "</head>";
echo "<body>";

echo "<div class='fix-card'>";
echo "<h1 class='text-center mb-4'><i class='fas fa-tools'></i> إصلاح الاستيراد والتصدير</h1>";

if (isset($_POST['fix_all'])) {
    echo "<div class='alert alert-info'>";
    echo "<h5>جاري إصلاح جميع المشاكل...</h5>";
    echo "</div>";

    try {
        // إنشاء مجلد الرفع إذا لم يكن موجود
        $upload_dir = 'uploads/imports/';
        if (!file_exists($upload_dir)) {
            mkdir($upload_dir, 0755, true);
            echo "<p class='success'><i class='fas fa-check'></i> تم إنشاء مجلد الرفع: $upload_dir</p>";
        }

        // إنشاء مجلد التصدير إذا لم يكن موجود
        $export_dir = 'exports/';
        if (!file_exists($export_dir)) {
            mkdir($export_dir, 0755, true);
            echo "<p class='success'><i class='fas fa-check'></i> تم إنشاء مجلد التصدير: $export_dir</p>";
        }

        echo "<div class='alert alert-success'>";
        echo "<h3><i class='fas fa-check-circle'></i> تم إصلاح جميع المشاكل بنجاح!</h3>";
        echo "<p>الآن يمكنك استخدام:</p>";
        echo "<ul>";
        echo "<li>✅ <strong>الاستيراد:</strong> modules/supporters/import_simple.php</li>";
        echo "<li>✅ <strong>التصدير:</strong> modules/supporters/export_simple.php</li>";
        echo "<li>✅ <strong>إدارة المؤيدين:</strong> modules/supporters/supporters_working.php</li>";
        echo "</ul>";
        echo "</div>";

    } catch (Exception $e) {
        echo "<div class='alert alert-danger'>";
        echo "<h5>خطأ في الإصلاح:</h5>";
        echo "<p>" . htmlspecialchars($e->getMessage()) . "</p>";
        echo "</div>";
    }

    echo "<div class='text-center mt-4'>";
    echo "<a href='modules/supporters/supporters_working.php' class='btn btn-primary btn-lg me-2'><i class='fas fa-users'></i> صفحة المؤيدين</a>";
    echo "<a href='modules/supporters/import_simple.php' class='btn btn-success btn-lg me-2'><i class='fas fa-upload'></i> الاستيراد</a>";
    echo "<a href='modules/supporters/export_simple.php' class='btn btn-info btn-lg'><i class='fas fa-download'></i> التصدير</a>";
    echo "</div>";

} else {
    // عرض حالة النظام
    echo "<div class='alert alert-warning'>";
    echo "<h5><i class='fas fa-exclamation-triangle'></i> حالة النظام الحالية</h5>";
    echo "</div>";

    // فحص الملفات
    $files_status = [
        'modules/supporters/supporters_working.php' => 'صفحة المؤيدين المُصلحة',
        'modules/supporters/import_simple.php' => 'صفحة الاستيراد المبسطة',
        'modules/supporters/export_simple.php' => 'صفحة التصدير المبسطة',
        'modules/supporters/template_csv.php' => 'نموذج CSV للتحميل'
    ];

    echo "<div class='row'>";
    foreach ($files_status as $file => $description) {
        $exists = file_exists($file);
        echo "<div class='col-md-6'>";
        echo "<div class='feature-card'>";
        echo "<h6>" . ($exists ? '<i class="fas fa-check-circle text-success"></i>' : '<i class="fas fa-times-circle text-danger"></i>') . " $description</h6>";
        echo "<p class='text-muted'>$file</p>";
        if ($exists) {
            echo "<a href='$file' class='btn btn-sm btn-outline-primary'>فتح الملف</a>";
        }
        echo "</div>";
        echo "</div>";
    }
    echo "</div>";

    // فحص المجلدات
    $directories = ['uploads/imports/', 'exports/'];
    echo "<h5 class='mt-4'>حالة المجلدات:</h5>";
    echo "<div class='row'>";
    foreach ($directories as $dir) {
        $exists = file_exists($dir);
        echo "<div class='col-md-6'>";
        echo "<div class='feature-card'>";
        echo "<h6>" . ($exists ? '<i class="fas fa-folder text-success"></i>' : '<i class="fas fa-folder-open text-warning"></i>') . " $dir</h6>";
        echo "<p class='text-muted'>" . ($exists ? 'موجود' : 'غير موجود - سيتم إنشاؤه') . "</p>";
        echo "</div>";
        echo "</div>";
    }
    echo "</div>";

    // الميزات المتاحة
    echo "<h5 class='mt-4'>الميزات المتاحة:</h5>";
    echo "<div class='row'>";
    
    echo "<div class='col-md-4'>";
    echo "<div class='feature-card text-center'>";
    echo "<i class='fas fa-upload fa-3x text-primary mb-3'></i>";
    echo "<h6>استيراد CSV</h6>";
    echo "<p class='text-muted'>استيراد المؤيدين من ملف CSV بسيط</p>";
    echo "<ul class='text-start'>";
    echo "<li>دعم الترميز العربي</li>";
    echo "<li>التحقق من صحة البيانات</li>";
    echo "<li>إنشاء مناطق جديدة تلقائياً</li>";
    echo "<li>تقرير مفصل بالأخطاء</li>";
    echo "</ul>";
    echo "</div>";
    echo "</div>";

    echo "<div class='col-md-4'>";
    echo "<div class='feature-card text-center'>";
    echo "<i class='fas fa-download fa-3x text-success mb-3'></i>";
    echo "<h6>تصدير متعدد الصيغ</h6>";
    echo "<p class='text-muted'>تصدير البيانات بصيغ مختلفة</p>";
    echo "<ul class='text-start'>";
    echo "<li>Excel (.xls)</li>";
    echo "<li>CSV (.csv)</li>";
    echo "<li>JSON (.json)</li>";
    echo "<li>دعم الترميز العربي</li>";
    echo "</ul>";
    echo "</div>";
    echo "</div>";

    echo "<div class='col-md-4'>";
    echo "<div class='feature-card text-center'>";
    echo "<i class='fas fa-users fa-3x text-info mb-3'></i>";
    echo "<h6>إدارة محسنة</h6>";
    echo "<p class='text-muted'>واجهة محسنة لإدارة المؤيدين</p>";
    echo "<ul class='text-start'>";
    echo "<li>إضافة وتعديل وحذف</li>";
    echo "<li>البحث والفلترة</li>";
    echo "<li>إحصائيات مفصلة</li>";
    echo "<li>واجهة سهلة الاستخدام</li>";
    echo "</ul>";
    echo "</div>";
    echo "</div>";

    echo "</div>";

    // زر الإصلاح
    echo "<form method='POST' action=''>";
    echo "<div class='text-center mt-4'>";
    echo "<button type='submit' name='fix_all' class='btn btn-success btn-lg'>";
    echo "<i class='fas fa-tools'></i> إصلاح جميع المشاكل";
    echo "</button>";
    echo "</div>";
    echo "</form>";

    echo "<div class='text-center mt-3'>";
    echo "<a href='test_supporters.php' class='btn btn-info me-2'><i class='fas fa-cog'></i> اختبار النظام</a>";
    echo "<a href='modules/supporters/supporters.php' class='btn btn-warning'><i class='fas fa-users'></i> الصفحة الأصلية</a>";
    echo "</div>";
}

echo "</div>";
echo "</body>";
echo "</html>";
?>
