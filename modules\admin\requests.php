<?php
// صفحة مطالب المؤيدين للإداريين
session_start();
header('Content-Type: text/html; charset=utf-8');
ini_set('default_charset', 'utf-8');
mb_internal_encoding('UTF-8');

require_once '../../config/config.php';
require_once '../../config/database.php';

// التحقق من تسجيل الدخول
if (!isset($_SESSION['admin_id'])) {
    redirect('login.php');
}

$admin_id = $_SESSION['admin_id'];
$admin_name = $_SESSION['admin_name'];

// معالجة إضافة مطلب جديد
if ($_SERVER['REQUEST_METHOD'] == 'POST' && isset($_POST['action']) && $_POST['action'] == 'add') {
    $supporter_name = sanitize($_POST['supporter_name']);
    $supporter_phone = sanitize($_POST['supporter_phone']);
    $request_type = sanitize($_POST['request_type']);
    $title = sanitize($_POST['title']);
    $description = sanitize($_POST['description']);
    $priority = sanitize($_POST['priority']);
    $estimated_cost = !empty($_POST['estimated_cost']) ? (float)$_POST['estimated_cost'] : null;
    
    if (!empty($supporter_name) && !empty($supporter_phone) && !empty($title) && !empty($description)) {
        // البحث عن المؤيد في قاعدة البيانات
        $supporter = fetchOne("SELECT id FROM supporters WHERE phone = ?", [$supporter_phone]);
        $supporter_id = $supporter ? $supporter['id'] : null;
        
        $sql = "INSERT INTO supporter_requests (admin_id, supporter_id, supporter_name, supporter_phone, request_type, title, description, priority, estimated_cost, created_at) 
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, NOW())";
        
        $result = executeQuery($sql, [$admin_id, $supporter_id, $supporter_name, $supporter_phone, $request_type, $title, $description, $priority, $estimated_cost]);
        
        if ($result) {
            showMessage('تم إضافة المطلب بنجاح', 'success');
        } else {
            showMessage('حدث خطأ أثناء إضافة المطلب', 'error');
        }
    } else {
        showMessage('يرجى ملء جميع الحقول المطلوبة', 'error');
    }
}

// جلب المطالب
$requests = fetchAll("SELECT * FROM supporter_requests WHERE admin_id = ? ORDER BY created_at DESC", [$admin_id]);

// إحصائيات المطالب
$pending_count = count(array_filter($requests, function($r) { return $r['status'] == 'pending'; }));
$completed_count = count(array_filter($requests, function($r) { return $r['status'] == 'completed'; }));
$in_progress_count = count(array_filter($requests, function($r) { return $r['status'] == 'in_progress'; }));
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>مطالب المؤيدين - لوحة تحكم الإداريين</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: #f8f9fa;
        }
        .main-content {
            margin-top: 80px;
        }
        .request-card {
            border-radius: 10px;
            border: none;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 15px;
            transition: transform 0.2s;
        }
        .request-card:hover {
            transform: translateY(-2px);
        }
        .status-pending { border-left: 4px solid #ffc107; }
        .status-received { border-left: 4px solid #17a2b8; }
        .status-in_progress { border-left: 4px solid #007bff; }
        .status-completed { border-left: 4px solid #28a745; }
        .status-rejected { border-left: 4px solid #dc3545; }
    </style>
</head>
<body>
    <!-- شريط التنقل العلوي -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-dark fixed-top">
        <div class="container-fluid">
            <a class="navbar-brand" href="dashboard.php">
                <i class="fas fa-user-shield me-2"></i>
                لوحة تحكم الإداريين
            </a>
            
            <div class="navbar-nav ms-auto">
                <div class="nav-item dropdown">
                    <a class="nav-link dropdown-toggle" href="#" id="userDropdown" role="button" data-bs-toggle="dropdown">
                        <i class="fas fa-user-circle me-1"></i>
                        <?php echo htmlspecialchars($admin_name); ?>
                    </a>
                    <ul class="dropdown-menu dropdown-menu-end">
                        <li><a class="dropdown-item" href="dashboard.php"><i class="fas fa-tachometer-alt me-2"></i>لوحة التحكم</a></li>
                        <li><a class="dropdown-item" href="logout.php"><i class="fas fa-sign-out-alt me-2"></i>تسجيل الخروج</a></li>
                    </ul>
                </div>
            </div>
        </div>
    </nav>

    <!-- المحتوى الرئيسي -->
    <div class="container-fluid main-content">
        <div class="row">
            <div class="col-md-12">
                <div class="d-flex justify-content-between align-items-center mb-4">
                    <h2><i class="fas fa-hand-holding-heart me-2"></i>مطالب المؤيدين</h2>
                    <div>
                        <button type="button" class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#addRequestModal">
                            <i class="fas fa-plus me-2"></i>مطلب جديد
                        </button>
                        <a href="dashboard.php" class="btn btn-secondary">
                            <i class="fas fa-arrow-left me-2"></i>العودة
                        </a>
                    </div>
                </div>

                <?php displayMessage(); ?>

                <!-- الإحصائيات -->
                <div class="row mb-4">
                    <div class="col-md-3">
                        <div class="card bg-warning text-white">
                            <div class="card-body text-center">
                                <h3><?php echo $pending_count; ?></h3>
                                <p class="mb-0">قيد الانتظار</p>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="card bg-info text-white">
                            <div class="card-body text-center">
                                <h3><?php echo $in_progress_count; ?></h3>
                                <p class="mb-0">جاري العمل</p>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="card bg-success text-white">
                            <div class="card-body text-center">
                                <h3><?php echo $completed_count; ?></h3>
                                <p class="mb-0">مكتمل</p>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="card bg-primary text-white">
                            <div class="card-body text-center">
                                <h3><?php echo count($requests); ?></h3>
                                <p class="mb-0">إجمالي المطالب</p>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- قائمة المطالب -->
                <div class="row">
                    <?php if (empty($requests)): ?>
                        <div class="col-12">
                            <div class="alert alert-info text-center">
                                <i class="fas fa-hand-holding-heart fa-3x mb-3"></i>
                                <h5>لا توجد مطالب</h5>
                                <p>لم تقم بإضافة أي مطالب للمؤيدين بعد</p>
                                <button type="button" class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#addRequestModal">
                                    <i class="fas fa-plus me-2"></i>إضافة مطلب جديد
                                </button>
                            </div>
                        </div>
                    <?php else: ?>
                        <?php foreach ($requests as $request): ?>
                        <div class="col-md-6">
                            <div class="card request-card status-<?php echo $request['status']; ?>">
                                <div class="card-body">
                                    <div class="d-flex justify-content-between align-items-start mb-2">
                                        <h6 class="card-title"><?php echo htmlspecialchars($request['title']); ?></h6>
                                        <span class="badge bg-<?php 
                                            echo $request['status'] == 'pending' ? 'warning' : 
                                                ($request['status'] == 'completed' ? 'success' : 
                                                ($request['status'] == 'in_progress' ? 'info' : 
                                                ($request['status'] == 'received' ? 'primary' : 'danger'))); 
                                        ?>">
                                            <?php 
                                            $status_map = [
                                                'pending' => 'قيد الانتظار',
                                                'received' => 'تم الاستلام',
                                                'in_progress' => 'جاري العمل',
                                                'completed' => 'مكتمل',
                                                'rejected' => 'مرفوض'
                                            ];
                                            echo $status_map[$request['status']] ?? $request['status'];
                                            ?>
                                        </span>
                                    </div>
                                    
                                    <p class="card-text">
                                        <strong>المؤيد:</strong> <?php echo htmlspecialchars($request['supporter_name']); ?><br>
                                        <strong>الهاتف:</strong> <?php echo htmlspecialchars($request['supporter_phone']); ?><br>
                                        <strong>النوع:</strong> 
                                        <?php 
                                        $type_map = [
                                            'financial' => 'مالي',
                                            'medical' => 'طبي',
                                            'educational' => 'تعليمي',
                                            'employment' => 'توظيف',
                                            'housing' => 'سكن',
                                            'other' => 'أخرى'
                                        ];
                                        echo $type_map[$request['request_type']] ?? $request['request_type'];
                                        ?>
                                    </p>
                                    
                                    <p class="card-text"><?php echo nl2br(htmlspecialchars($request['description'])); ?></p>
                                    
                                    <?php if ($request['estimated_cost']): ?>
                                    <p class="card-text">
                                        <strong>التكلفة المقدرة:</strong> <?php echo number_format($request['estimated_cost']); ?> دينار
                                    </p>
                                    <?php endif; ?>
                                    
                                    <?php if ($request['management_response']): ?>
                                    <div class="alert alert-info">
                                        <strong>رد الإدارة:</strong><br>
                                        <?php echo nl2br(htmlspecialchars($request['management_response'])); ?>
                                    </div>
                                    <?php endif; ?>
                                    
                                    <small class="text-muted">
                                        <i class="fas fa-clock me-1"></i>
                                        <?php echo date('Y-m-d H:i', strtotime($request['created_at'])); ?>
                                    </small>
                                </div>
                            </div>
                        </div>
                        <?php endforeach; ?>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>

    <!-- نافذة إضافة مطلب -->
    <div class="modal fade" id="addRequestModal" tabindex="-1">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">إضافة مطلب مؤيد</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <form method="POST" action="">
                    <input type="hidden" name="action" value="add">
                    <div class="modal-body">
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="supporter_name" class="form-label">اسم المؤيد *</label>
                                    <input type="text" class="form-control" id="supporter_name" name="supporter_name" required>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="supporter_phone" class="form-label">رقم هاتف المؤيد *</label>
                                    <input type="tel" class="form-control" id="supporter_phone" name="supporter_phone" required>
                                </div>
                            </div>
                        </div>
                        
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="request_type" class="form-label">نوع المطلب *</label>
                                    <select class="form-select" id="request_type" name="request_type" required>
                                        <option value="">اختر النوع</option>
                                        <option value="financial">مالي</option>
                                        <option value="medical">طبي</option>
                                        <option value="educational">تعليمي</option>
                                        <option value="employment">توظيف</option>
                                        <option value="housing">سكن</option>
                                        <option value="other">أخرى</option>
                                    </select>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="priority" class="form-label">الأولوية</label>
                                    <select class="form-select" id="priority" name="priority">
                                        <option value="normal">عادي</option>
                                        <option value="high">عالي</option>
                                        <option value="urgent">عاجل</option>
                                    </select>
                                </div>
                            </div>
                        </div>
                        
                        <div class="mb-3">
                            <label for="title" class="form-label">عنوان المطلب *</label>
                            <input type="text" class="form-control" id="title" name="title" required>
                        </div>
                        
                        <div class="mb-3">
                            <label for="description" class="form-label">وصف المطلب *</label>
                            <textarea class="form-control" id="description" name="description" rows="4" required></textarea>
                        </div>
                        
                        <div class="mb-3">
                            <label for="estimated_cost" class="form-label">التكلفة المقدرة (دينار)</label>
                            <input type="number" class="form-control" id="estimated_cost" name="estimated_cost" step="0.01">
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-save me-2"></i>إضافة المطلب
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
