<?php
// صفحة مطالب المؤيدين للإداريين
session_start();
header('Content-Type: text/html; charset=utf-8');
ini_set('default_charset', 'utf-8');
mb_internal_encoding('UTF-8');

require_once '../../config/config.php';
require_once '../../config/database.php';

// التحقق من تسجيل الدخول
if (!isset($_SESSION['admin_id'])) {
    redirect('login.php');
}

$admin_id = $_SESSION['admin_id'];
$admin_name = $_SESSION['admin_name'];

// معالجة إضافة مطلب جديد
if ($_SERVER['REQUEST_METHOD'] == 'POST' && isset($_POST['action']) && $_POST['action'] == 'add') {
    $supporter_name = sanitize($_POST['supporter_name']);
    $supporter_phone = sanitize($_POST['supporter_phone']);
    $request_type = sanitize($_POST['request_type']);
    $title = sanitize($_POST['title']);
    $description = sanitize($_POST['description']);
    $priority = sanitize($_POST['priority']);

    if (!empty($supporter_name) && !empty($supporter_phone) && !empty($title) && !empty($description)) {
        // البحث عن المؤيد في قاعدة البيانات
        $supporter = fetchOne("SELECT id FROM supporters WHERE phone = ?", [$supporter_phone]);
        $supporter_id = $supporter ? $supporter['id'] : null;

        $sql = "INSERT INTO supporter_requests (admin_id, supporter_id, supporter_name, supporter_phone, request_type, title, description, priority, created_at)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, NOW())";

        $result = executeQuery($sql, [$admin_id, $supporter_id, $supporter_name, $supporter_phone, $request_type, $title, $description, $priority]);

        if ($result) {
            $request_id = $pdo->lastInsertId();

            // معالجة رفع المرفقات إذا وجدت
            if (isset($_FILES['attachments'])) {
                $files = $_FILES['attachments'];
                $upload_dir = "../../uploads/requests/";

                // إنشاء المجلد إذا لم يكن موجوداً
                if (!file_exists($upload_dir)) {
                    mkdir($upload_dir, 0755, true);
                }

                // التحقق من أن الملفات مصفوفة
                if (is_array($files['name'])) {
                    for ($i = 0; $i < count($files['name']); $i++) {
                        if ($files['error'][$i] == 0) {
                            $file_name = $files['name'][$i];
                            $file_tmp = $files['tmp_name'][$i];
                            $file_size = $files['size'][$i];
                            $file_type = $files['type'][$i];

                            // التحقق من نوع الملف
                            $allowed_types = ['image/jpeg', 'image/jpg', 'image/png', 'image/gif', 'application/pdf', 'application/msword', 'application/vnd.openxmlformats-officedocument.wordprocessingml.document', 'application/vnd.ms-excel', 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'];

                            if (in_array($file_type, $allowed_types) && $file_size <= 10 * 1024 * 1024) { // 10MB
                                // إنشاء اسم ملف فريد
                                $file_extension = pathinfo($file_name, PATHINFO_EXTENSION);
                                $unique_name = $request_id . "_" . time() . "_" . $i . "." . $file_extension;
                                $file_path = $upload_dir . $unique_name;

                                // رفع الملف
                                if (move_uploaded_file($file_tmp, $file_path)) {
                                    // حفظ في قاعدة البيانات
                                    $attachment_sql = "INSERT INTO request_attachments (request_id, file_name, file_path, file_size, file_type, uploaded_by)
                                                     VALUES (?, ?, ?, ?, ?, ?)";

                                    executeQuery($attachment_sql, [
                                        $request_id, $file_name, $file_path,
                                        $file_size, $file_type, $admin_id
                                    ]);
                                }
                            }
                        }
                    }
                } else {
                    // ملف واحد فقط
                    if ($files['error'] == 0) {
                        $file_name = $files['name'];
                        $file_tmp = $files['tmp_name'];
                        $file_size = $files['size'];
                        $file_type = $files['type'];

                        $allowed_types = ['image/jpeg', 'image/jpg', 'image/png', 'image/gif', 'application/pdf', 'application/msword', 'application/vnd.openxmlformats-officedocument.wordprocessingml.document', 'application/vnd.ms-excel', 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'];

                        if (in_array($file_type, $allowed_types) && $file_size <= 10 * 1024 * 1024) {
                            $file_extension = pathinfo($file_name, PATHINFO_EXTENSION);
                            $unique_name = $request_id . "_" . time() . "." . $file_extension;
                            $file_path = $upload_dir . $unique_name;

                            if (move_uploaded_file($file_tmp, $file_path)) {
                                $attachment_sql = "INSERT INTO request_attachments (request_id, file_name, file_path, file_size, file_type, uploaded_by)
                                                 VALUES (?, ?, ?, ?, ?, ?)";

                                executeQuery($attachment_sql, [
                                    $request_id, $file_name, $file_path,
                                    $file_size, $file_type, $admin_id
                                ]);
                            }
                        }
                    }
                }
            }

            showMessage('تم إضافة المطلب والمرفقات بنجاح', 'success');
        } else {
            showMessage('حدث خطأ أثناء إضافة المطلب', 'error');
        }
    } else {
        showMessage('يرجى ملء جميع الحقول المطلوبة', 'error');
    }
}

// جلب المطالب
$requests = fetchAll("SELECT * FROM supporter_requests WHERE admin_id = ? ORDER BY created_at DESC", [$admin_id]);

// إحصائيات المطالب
$pending_count = count(array_filter($requests, function($r) { return $r['status'] == 'pending'; }));
$completed_count = count(array_filter($requests, function($r) { return $r['status'] == 'completed'; }));
$in_progress_count = count(array_filter($requests, function($r) { return $r['status'] == 'in_progress'; }));
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>مطالب المؤيدين - لوحة تحكم الإداريين</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: #f8f9fa;
        }
        .main-content {
            margin-top: 80px;
        }
        .request-card {
            border-radius: 10px;
            border: none;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 15px;
            transition: transform 0.2s;
        }
        .request-card:hover {
            transform: translateY(-2px);
        }
        .status-pending { border-left: 4px solid #ffc107; }
        .status-received { border-left: 4px solid #17a2b8; }
        .status-in_progress { border-left: 4px solid #007bff; }
        .status-completed { border-left: 4px solid #28a745; }
        .status-rejected { border-left: 4px solid #dc3545; }
    </style>
</head>
<body>
    <!-- شريط التنقل العلوي -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-dark fixed-top">
        <div class="container-fluid">
            <a class="navbar-brand" href="dashboard.php">
                <i class="fas fa-user-shield me-2"></i>
                لوحة تحكم الإداريين
            </a>

            <div class="navbar-nav ms-auto">
                <div class="nav-item dropdown">
                    <a class="nav-link dropdown-toggle" href="#" id="userDropdown" role="button" data-bs-toggle="dropdown">
                        <i class="fas fa-user-circle me-1"></i>
                        <?php echo htmlspecialchars($admin_name); ?>
                    </a>
                    <ul class="dropdown-menu dropdown-menu-end">
                        <li><a class="dropdown-item" href="dashboard.php"><i class="fas fa-tachometer-alt me-2"></i>لوحة التحكم</a></li>
                        <li><a class="dropdown-item" href="logout.php"><i class="fas fa-sign-out-alt me-2"></i>تسجيل الخروج</a></li>
                    </ul>
                </div>
            </div>
        </div>
    </nav>

    <!-- المحتوى الرئيسي -->
    <div class="container-fluid main-content">
        <div class="row">
            <div class="col-md-12">
                <div class="d-flex justify-content-between align-items-center mb-4">
                    <h2><i class="fas fa-hand-holding-heart me-2"></i>مطالب المؤيدين</h2>
                    <div>
                        <button type="button" class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#addRequestModal">
                            <i class="fas fa-plus me-2"></i>مطلب جديد
                        </button>
                        <a href="dashboard.php" class="btn btn-secondary">
                            <i class="fas fa-arrow-left me-2"></i>العودة
                        </a>
                    </div>
                </div>

                <?php displayMessage(); ?>

                <!-- الإحصائيات -->
                <div class="row mb-4">
                    <div class="col-md-3">
                        <div class="card bg-warning text-white">
                            <div class="card-body text-center">
                                <h3><?php echo $pending_count; ?></h3>
                                <p class="mb-0">قيد الانتظار</p>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="card bg-info text-white">
                            <div class="card-body text-center">
                                <h3><?php echo $in_progress_count; ?></h3>
                                <p class="mb-0">جاري العمل</p>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="card bg-success text-white">
                            <div class="card-body text-center">
                                <h3><?php echo $completed_count; ?></h3>
                                <p class="mb-0">مكتمل</p>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="card bg-primary text-white">
                            <div class="card-body text-center">
                                <h3><?php echo count($requests); ?></h3>
                                <p class="mb-0">إجمالي المطالب</p>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- قائمة المطالب -->
                <div class="row">
                    <?php if (empty($requests)): ?>
                        <div class="col-12">
                            <div class="alert alert-info text-center">
                                <i class="fas fa-hand-holding-heart fa-3x mb-3"></i>
                                <h5>لا توجد مطالب</h5>
                                <p>لم تقم بإضافة أي مطالب للمؤيدين بعد</p>
                                <button type="button" class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#addRequestModal">
                                    <i class="fas fa-plus me-2"></i>إضافة مطلب جديد
                                </button>
                            </div>
                        </div>
                    <?php else: ?>
                        <?php foreach ($requests as $request): ?>
                        <div class="col-md-6">
                            <div class="card request-card status-<?php echo $request['status']; ?>">
                                <div class="card-body">
                                    <div class="d-flex justify-content-between align-items-start mb-2">
                                        <h6 class="card-title"><?php echo htmlspecialchars($request['title']); ?></h6>
                                        <span class="badge bg-<?php
                                            echo $request['status'] == 'pending' ? 'warning' :
                                                ($request['status'] == 'completed' ? 'success' :
                                                ($request['status'] == 'in_progress' ? 'info' :
                                                ($request['status'] == 'received' ? 'primary' : 'danger')));
                                        ?>">
                                            <?php
                                            $status_map = [
                                                'pending' => 'قيد الانتظار',
                                                'received' => 'تم الاستلام',
                                                'in_progress' => 'جاري العمل',
                                                'completed' => 'مكتمل',
                                                'rejected' => 'مرفوض'
                                            ];
                                            echo $status_map[$request['status']] ?? $request['status'];
                                            ?>
                                        </span>
                                    </div>

                                    <p class="card-text">
                                        <strong>المؤيد:</strong> <?php echo htmlspecialchars($request['supporter_name']); ?><br>
                                        <strong>الهاتف:</strong> <?php echo htmlspecialchars($request['supporter_phone']); ?><br>
                                        <strong>النوع:</strong>
                                        <?php
                                        $type_map = [
                                            'financial' => 'مالي',
                                            'medical' => 'طبي',
                                            'educational' => 'تعليمي',
                                            'employment' => 'توظيف',
                                            'housing' => 'سكن',
                                            'other' => 'أخرى'
                                        ];
                                        echo $type_map[$request['request_type']] ?? $request['request_type'];
                                        ?>
                                    </p>

                                    <p class="card-text"><?php echo nl2br(htmlspecialchars($request['description'])); ?></p>

                                    <?php
                                    // جلب المرفقات لهذا المطلب
                                    $request_attachments = fetchAll("SELECT * FROM request_attachments WHERE request_id = ?", [$request['id']]);
                                    if (!empty($request_attachments)):
                                    ?>
                                    <div class="mt-2">
                                        <strong>المرفقات:</strong>
                                        <div class="d-flex flex-wrap gap-2 mt-1">
                                            <?php foreach ($request_attachments as $attachment): ?>
                                                <a href="../../<?php echo htmlspecialchars($attachment['file_path']); ?>"
                                                   target="_blank" class="btn btn-outline-primary btn-sm">
                                                    <i class="fas fa-<?php
                                                        echo strpos($attachment['file_type'], 'image/') === 0 ? 'image' :
                                                            (strpos($attachment['file_type'], 'pdf') !== false ? 'file-pdf' :
                                                            (strpos($attachment['file_type'], 'word') !== false ? 'file-word' :
                                                            (strpos($attachment['file_type'], 'excel') !== false || strpos($attachment['file_type'], 'sheet') !== false ? 'file-excel' : 'file')));
                                                    ?> me-1"></i>
                                                    <?php echo htmlspecialchars($attachment['file_name']); ?>
                                                </a>
                                            <?php endforeach; ?>
                                        </div>
                                    </div>
                                    <?php endif; ?>

                                    <?php if ($request['management_response']): ?>
                                    <div class="alert alert-info">
                                        <strong>رد الإدارة:</strong><br>
                                        <?php echo nl2br(htmlspecialchars($request['management_response'])); ?>
                                    </div>
                                    <?php endif; ?>

                                    <small class="text-muted">
                                        <i class="fas fa-clock me-1"></i>
                                        <?php echo date('Y-m-d H:i', strtotime($request['created_at'])); ?>
                                    </small>
                                </div>
                            </div>
                        </div>
                        <?php endforeach; ?>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>

    <!-- نافذة إضافة مطلب -->
    <div class="modal fade" id="addRequestModal" tabindex="-1">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">إضافة مطلب مؤيد</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <form method="POST" action="" enctype="multipart/form-data">
                    <input type="hidden" name="action" value="add">
                    <div class="modal-body">
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="supporter_name" class="form-label">اسم المؤيد *</label>
                                    <input type="text" class="form-control" id="supporter_name" name="supporter_name" required>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="supporter_phone" class="form-label">رقم هاتف المؤيد *</label>
                                    <input type="tel" class="form-control" id="supporter_phone" name="supporter_phone" required>
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="request_type" class="form-label">نوع المطلب *</label>
                                    <select class="form-select" id="request_type" name="request_type" required>
                                        <option value="">اختر النوع</option>
                                        <option value="financial">مالي</option>
                                        <option value="medical">طبي</option>
                                        <option value="educational">تعليمي</option>
                                        <option value="employment">توظيف</option>
                                        <option value="housing">سكن</option>
                                        <option value="other">أخرى</option>
                                    </select>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="priority" class="form-label">الأولوية</label>
                                    <select class="form-select" id="priority" name="priority">
                                        <option value="normal">عادي</option>
                                        <option value="high">عالي</option>
                                        <option value="urgent">عاجل</option>
                                    </select>
                                </div>
                            </div>
                        </div>

                        <div class="mb-3">
                            <label for="title" class="form-label">عنوان المطلب *</label>
                            <input type="text" class="form-control" id="title" name="title" required>
                        </div>

                        <div class="mb-3">
                            <label for="description" class="form-label">وصف المطلب *</label>
                            <textarea class="form-control" id="description" name="description" rows="4" required></textarea>
                        </div>

                        <div class="mb-3">
                            <label for="attachments" class="form-label">المرفقات (اختياري)</label>
                            <input type="file" class="form-control" id="attachments" name="attachments[]"
                                   accept="image/*,application/pdf,.doc,.docx,.xls,.xlsx" multiple>
                            <div class="form-text">
                                يمكنك رفع عدة ملفات: الصور، PDF، Word، Excel - الحد الأقصى 10MB لكل ملف
                            </div>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-save me-2"></i>إضافة المطلب
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
