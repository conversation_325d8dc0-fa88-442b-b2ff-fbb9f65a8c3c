<?php
require_once '../../config/config.php';
require_once '../../config/database.php';

// التحقق من تسجيل الدخول والصلاحيات
if (!isLoggedIn()) {
    redirect('../../login.php');
}

$can_add = isCandidate() || (isAdmin() && in_array('add_supporters', $_SESSION['permissions'] ?? []));
if (!$can_add) {
    showMessage('ليس لديك صلاحية لاستيراد البيانات', 'error');
    redirect('supporters.php');
}

if ($_SERVER['REQUEST_METHOD'] == 'POST' && isset($_FILES['import_file'])) {
    $file = $_FILES['import_file'];
    
    // التحقق من نوع الملف
    $allowed_types = [
        'application/vnd.ms-excel',
        'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
    ];
    
    if (!in_array($file['type'], $allowed_types)) {
        showMessage('نوع الملف غير مدعوم. يرجى رفع ملف Excel', 'error');
        redirect('supporters.php');
    }
    
    // التحقق من حجم الملف (10MB)
    if ($file['size'] > 10 * 1024 * 1024) {
        showMessage('حجم الملف كبير جداً. الحد الأقصى 10 ميجابايت', 'error');
        redirect('supporters.php');
    }
    
    // رفع الملف
    $upload_dir = '../../uploads/imports/';
    if (!file_exists($upload_dir)) {
        mkdir($upload_dir, 0755, true);
    }
    
    $filename = uniqid() . '_' . $file['name'];
    $upload_path = $upload_dir . $filename;
    
    if (!move_uploaded_file($file['tmp_name'], $upload_path)) {
        showMessage('فشل في رفع الملف', 'error');
        redirect('supporters.php');
    }
    
    // قراءة ملف Excel
    try {
        require_once '../../vendor/autoload.php'; // مكتبة PhpSpreadsheet
        
        $reader = \PhpOffice\PhpSpreadsheet\IOFactory::createReader('Xlsx');
        $spreadsheet = $reader->load($upload_path);
        $worksheet = $spreadsheet->getActiveSheet();
        $rows = $worksheet->toArray();
        
        // إزالة الصف الأول (العناوين)
        array_shift($rows);
        
        $success_count = 0;
        $error_count = 0;
        $errors = [];
        
        // جلب المناطق
        $regions = fetchAll("SELECT id, name FROM regions");
        $region_map = [];
        foreach ($regions as $region) {
            $region_map[strtolower($region['name'])] = $region['id'];
        }
        
        foreach ($rows as $index => $row) {
            $row_number = $index + 2; // +2 لأن المصفوفة تبدأ من 0 والصف الأول محذوف
            
            // تخطي الصفوف الفارغة
            if (empty(array_filter($row))) {
                continue;
            }
            
            // استخراج البيانات
            $full_name = trim($row[0] ?? '');
            $gender = strtolower(trim($row[1] ?? ''));
            $marital_status = strtolower(trim($row[2] ?? ''));
            $birth_date = trim($row[3] ?? '');
            $education = trim($row[4] ?? '');
            $profession = trim($row[5] ?? '');
            $address = trim($row[6] ?? '');
            $phone = trim($row[7] ?? '');
            $voter_number = trim($row[8] ?? '');
            $voting_center = trim($row[9] ?? '');
            $region_name = strtolower(trim($row[10] ?? ''));
            $notes = trim($row[11] ?? '');
            
            // التحقق من البيانات المطلوبة
            if (empty($full_name) || empty($gender) || empty($address) || empty($phone)) {
                $errors[] = "الصف $row_number: بيانات مطلوبة مفقودة";
                $error_count++;
                continue;
            }
            
            // تحويل الجنس
            $gender_map = ['ذكر' => 'male', 'أنثى' => 'female', 'male' => 'male', 'female' => 'female'];
            if (!isset($gender_map[$gender])) {
                $errors[] = "الصف $row_number: جنس غير صحيح ($gender)";
                $error_count++;
                continue;
            }
            $gender = $gender_map[$gender];
            
            // تحويل الحالة الاجتماعية
            $marital_map = [
                'أعزب' => 'single', 'عزباء' => 'single',
                'متزوج' => 'married', 'متزوجة' => 'married',
                'مطلق' => 'divorced', 'مطلقة' => 'divorced',
                'أرمل' => 'widowed', 'أرملة' => 'widowed',
                'single' => 'single', 'married' => 'married',
                'divorced' => 'divorced', 'widowed' => 'widowed'
            ];
            if (!isset($marital_map[$marital_status])) {
                $marital_status = 'single'; // افتراضي
            } else {
                $marital_status = $marital_map[$marital_status];
            }
            
            // تحويل تاريخ الميلاد
            if (!empty($birth_date)) {
                $birth_date = date('Y-m-d', strtotime($birth_date));
                if ($birth_date === '1970-01-01') {
                    $errors[] = "الصف $row_number: تاريخ ميلاد غير صحيح";
                    $error_count++;
                    continue;
                }
            } else {
                $birth_date = '1990-01-01'; // افتراضي
            }
            
            // التحقق من رقم الهاتف
            if (!validateIraqiPhone($phone)) {
                $errors[] = "الصف $row_number: رقم هاتف غير صحيح ($phone)";
                $error_count++;
                continue;
            }
            
            // البحث عن المنطقة
            $region_id = null;
            if (!empty($region_name)) {
                if (isset($region_map[$region_name])) {
                    $region_id = $region_map[$region_name];
                } else {
                    // إنشاء منطقة جديدة
                    $sql = "INSERT INTO regions (name) VALUES (?)";
                    $result = executeQuery($sql, [ucfirst($region_name)]);
                    if ($result) {
                        $region_id = getLastInsertId();
                        $region_map[$region_name] = $region_id;
                    }
                }
            }
            
            if (!$region_id) {
                $errors[] = "الصف $row_number: منطقة غير محددة";
                $error_count++;
                continue;
            }
            
            // التحقق من تكرار رقم الناخب
            if (!empty($voter_number)) {
                $existing = fetchOne("SELECT id FROM supporters WHERE voter_number = ?", [$voter_number]);
                if ($existing) {
                    $errors[] = "الصف $row_number: رقم الناخب موجود مسبقاً ($voter_number)";
                    $error_count++;
                    continue;
                }
            }
            
            // إدراج البيانات
            $sql = "INSERT INTO supporters (full_name, gender, marital_status, birth_date, education, profession, address, phone, voter_number, voting_center, region_id, notes, added_by) 
                    VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)";
            
            $params = [
                $full_name, $gender, $marital_status, $birth_date, $education,
                $profession, $address, $phone, $voter_number, $voting_center,
                $region_id, $notes, $_SESSION['user_id']
            ];
            
            $result = executeQuery($sql, $params);
            
            if ($result) {
                $success_count++;
            } else {
                $errors[] = "الصف $row_number: فشل في إدراج البيانات";
                $error_count++;
            }
        }
        
        // حذف الملف المؤقت
        unlink($upload_path);
        
        // عرض النتائج
        $message = "تم استيراد $success_count مؤيد بنجاح";
        if ($error_count > 0) {
            $message .= " مع $error_count خطأ";
        }
        
        showMessage($message, $error_count > 0 ? 'warning' : 'success');
        
        // حفظ الأخطاء في الجلسة لعرضها
        if (!empty($errors)) {
            $_SESSION['import_errors'] = $errors;
        }
        
    } catch (Exception $e) {
        // حذف الملف في حالة الخطأ
        if (file_exists($upload_path)) {
            unlink($upload_path);
        }
        
        showMessage('خطأ في قراءة الملف: ' . $e->getMessage(), 'error');
    }
    
    redirect('supporters.php');
}

// إذا لم يتم رفع ملف
showMessage('لم يتم رفع أي ملف', 'error');
redirect('supporters.php');
?>
