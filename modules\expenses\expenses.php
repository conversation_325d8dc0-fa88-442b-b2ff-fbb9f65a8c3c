<?php
require_once '../../config/config.php';
require_once '../../config/database.php';

// التحقق من تسجيل الدخول
if (!isLoggedIn()) {
    redirect('../../login.php');
}

// التحقق من الصلاحيات
$can_add = isCandidate() || (isAdmin() && in_array('add_expenses', $_SESSION['permissions'] ?? []));
$can_edit = isCandidate() || (isAdmin() && in_array('edit_expenses', $_SESSION['permissions'] ?? []));
$can_delete = isCandidate() || (isAdmin() && in_array('delete_expenses', $_SESSION['permissions'] ?? []));

// معالجة الطلبات
if ($_SERVER['REQUEST_METHOD'] == 'POST') {
    if (isset($_POST['action'])) {
        switch ($_POST['action']) {
            case 'add':
                if ($can_add) addExpense();
                break;
            case 'edit':
                if ($can_edit) editExpense();
                break;
            case 'delete':
                if ($can_delete) deleteExpense();
                break;
        }
    }
}

// بناء الاستعلام مع التصفية
$where_conditions = [];
$params = [];

// تصفية حسب المنطقة للإداريين
if (isAdmin() && $_SESSION['region_id']) {
    $where_conditions[] = "e.region_id = ?";
    $params[] = $_SESSION['region_id'];
}

// تطبيق فلاتر البحث
if (!empty($_GET['search'])) {
    $search = '%' . $_GET['search'] . '%';
    $where_conditions[] = "(e.description LIKE ? OR e.category LIKE ?)";
    $params = array_merge($params, [$search, $search]);
}

if (!empty($_GET['category'])) {
    $where_conditions[] = "e.category = ?";
    $params[] = $_GET['category'];
}

if (!empty($_GET['date_from'])) {
    $where_conditions[] = "e.expense_date >= ?";
    $params[] = $_GET['date_from'];
}

if (!empty($_GET['date_to'])) {
    $where_conditions[] = "e.expense_date <= ?";
    $params[] = $_GET['date_to'];
}

$where_clause = !empty($where_conditions) ? 'WHERE ' . implode(' AND ', $where_conditions) : '';

// جلب المصروفات
$sql = "SELECT e.*, r.name as region_name, u.full_name as added_by_name
        FROM expenses e 
        LEFT JOIN regions r ON e.region_id = r.id 
        LEFT JOIN users u ON e.added_by = u.id 
        $where_clause 
        ORDER BY e.expense_date DESC, e.created_at DESC";

$expenses = fetchAll($sql, $params);

// جلب المناطق للتصفية
$regions = fetchAll("SELECT * FROM regions ORDER BY name");

// إحصائيات
$total_amount = fetchOne("SELECT COALESCE(SUM(amount), 0) as total FROM expenses e $where_clause", $params)['total'];
$total_expenses = count($expenses);

// إحصائيات حسب الفئة
$categories_stats = fetchAll("
    SELECT category, COUNT(*) as count, SUM(amount) as total_amount 
    FROM expenses e $where_clause 
    GROUP BY category 
    ORDER BY total_amount DESC", $params);

function addExpense() {
    $data = [
        'description' => sanitize($_POST['description']),
        'amount' => (float)$_POST['amount'],
        'category' => sanitize($_POST['category']),
        'expense_date' => sanitize($_POST['expense_date']),
        'region_id' => !empty($_POST['region_id']) ? (int)$_POST['region_id'] : null,
        'notes' => sanitize($_POST['notes']),
        'added_by' => $_SESSION['user_id']
    ];
    
    // التحقق من صحة البيانات
    if (empty($data['description']) || $data['amount'] <= 0 || empty($data['category']) || empty($data['expense_date'])) {
        showMessage('يرجى ملء جميع الحقول المطلوبة', 'error');
        return;
    }
    
    // رفع المرفق إن وجد
    if (isset($_FILES['attachment']) && $_FILES['attachment']['error'] == 0) {
        $attachment_path = uploadAttachment($_FILES['attachment']);
        if ($attachment_path) {
            $data['attachment'] = $attachment_path;
        }
    }
    
    $sql = "INSERT INTO expenses (description, amount, category, expense_date, region_id, attachment, notes, added_by) 
            VALUES (?, ?, ?, ?, ?, ?, ?, ?)";
    
    $result = executeQuery($sql, array_values($data));
    
    if ($result) {
        showMessage('تم إضافة المصروف بنجاح', 'success');
        redirect('expenses.php');
    } else {
        showMessage('حدث خطأ أثناء إضافة المصروف', 'error');
    }
}

function editExpense() {
    $id = (int)$_POST['id'];
    $data = [
        'description' => sanitize($_POST['description']),
        'amount' => (float)$_POST['amount'],
        'category' => sanitize($_POST['category']),
        'expense_date' => sanitize($_POST['expense_date']),
        'region_id' => !empty($_POST['region_id']) ? (int)$_POST['region_id'] : null,
        'notes' => sanitize($_POST['notes'])
    ];
    
    // التحقق من صحة البيانات
    if (empty($data['description']) || $data['amount'] <= 0 || empty($data['category']) || empty($data['expense_date'])) {
        showMessage('يرجى ملء جميع الحقول المطلوبة', 'error');
        return;
    }
    
    // رفع المرفق الجديد إن وجد
    if (isset($_FILES['attachment']) && $_FILES['attachment']['error'] == 0) {
        $attachment_path = uploadAttachment($_FILES['attachment']);
        if ($attachment_path) {
            $data['attachment'] = $attachment_path;
        }
    }
    
    $set_clause = implode(', ', array_map(function($key) { return "$key = ?"; }, array_keys($data)));
    $sql = "UPDATE expenses SET $set_clause WHERE id = ?";
    
    $params = array_values($data);
    $params[] = $id;
    
    $result = executeQuery($sql, $params);
    
    if ($result) {
        showMessage('تم تحديث المصروف بنجاح', 'success');
        redirect('expenses.php');
    } else {
        showMessage('حدث خطأ أثناء تحديث المصروف', 'error');
    }
}

function deleteExpense() {
    $id = (int)$_POST['id'];
    
    // حذف المرفق إن وجد
    $expense = fetchOne("SELECT attachment FROM expenses WHERE id = ?", [$id]);
    if ($expense && $expense['attachment'] && file_exists('../../' . $expense['attachment'])) {
        unlink('../../' . $expense['attachment']);
    }
    
    $result = executeQuery("DELETE FROM expenses WHERE id = ?", [$id]);
    
    if ($result) {
        showMessage('تم حذف المصروف بنجاح', 'success');
    } else {
        showMessage('حدث خطأ أثناء حذف المصروف', 'error');
    }
}

function uploadAttachment($file) {
    $allowed_types = [
        'application/pdf',
        'image/jpeg',
        'image/png',
        'image/gif',
        'application/msword',
        'application/vnd.openxmlformats-officedocument.wordprocessingml.document'
    ];
    $max_size = 10 * 1024 * 1024; // 10MB
    
    if (!in_array($file['type'], $allowed_types)) {
        showMessage('نوع الملف غير مدعوم', 'error');
        return false;
    }
    
    if ($file['size'] > $max_size) {
        showMessage('حجم الملف كبير جداً. الحد الأقصى 10 ميجابايت', 'error');
        return false;
    }
    
    $upload_dir = '../../uploads/expenses/';
    if (!file_exists($upload_dir)) {
        mkdir($upload_dir, 0755, true);
    }
    
    $file_extension = pathinfo($file['name'], PATHINFO_EXTENSION);
    $new_filename = uniqid() . '.' . $file_extension;
    $upload_path = $upload_dir . $new_filename;
    
    if (move_uploaded_file($file['tmp_name'], $upload_path)) {
        return 'uploads/expenses/' . $new_filename;
    }
    
    return false;
}
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إدارة المصروفات - <?php echo SITE_NAME; ?></title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="https://cdn.datatables.net/1.13.6/css/dataTables.bootstrap5.min.css" rel="stylesheet">
    <link href="../../assets/css/dashboard.css" rel="stylesheet">
    <link href="../../assets/css/expenses.css" rel="stylesheet">
</head>
<body>
    <!-- شريط التنقل العلوي -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary fixed-top">
        <div class="container-fluid">
            <a class="navbar-brand" href="../../dashboard.php">
                <i class="fas fa-vote-yea me-2"></i>
                نظام إدارة الحملة الانتخابية
            </a>
            
            <div class="navbar-nav ms-auto">
                <div class="nav-item dropdown">
                    <a class="nav-link dropdown-toggle" href="#" id="userDropdown" role="button" data-bs-toggle="dropdown">
                        <i class="fas fa-user-circle me-1"></i>
                        <?php echo htmlspecialchars($_SESSION['full_name']); ?>
                    </a>
                    <ul class="dropdown-menu dropdown-menu-end">
                        <li><a class="dropdown-item" href="../../logout.php"><i class="fas fa-sign-out-alt me-2"></i>تسجيل الخروج</a></li>
                    </ul>
                </div>
            </div>
        </div>
    </nav>

    <!-- المحتوى الرئيسي -->
    <div class="container-fluid main-content">
        <div class="row">
            <!-- الشريط الجانبي -->
            <nav class="col-md-3 col-lg-2 d-md-block sidebar">
                <div class="position-sticky pt-3">
                    <ul class="nav flex-column">
                        <li class="nav-item">
                            <a class="nav-link" href="../../dashboard.php">
                                <i class="fas fa-tachometer-alt me-2"></i>
                                لوحة التحكم
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="../supporters/supporters.php">
                                <i class="fas fa-users me-2"></i>
                                المؤيدين
                            </a>
                        </li>
                        <?php if (isCandidate()): ?>
                        <li class="nav-item">
                            <a class="nav-link" href="../regions/regions.php">
                                <i class="fas fa-map-marked-alt me-2"></i>
                                المناطق
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="../admins/admins.php">
                                <i class="fas fa-user-tie me-2"></i>
                                الإداريين
                            </a>
                        </li>
                        <?php endif; ?>
                        <li class="nav-item">
                            <a class="nav-link active" href="expenses.php">
                                <i class="fas fa-money-bill-wave me-2"></i>
                                المصروفات
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="../events/events.php">
                                <i class="fas fa-calendar-alt me-2"></i>
                                الفعاليات
                            </a>
                        </li>
                        <?php if (isCandidate()): ?>
                        <li class="nav-item">
                            <a class="nav-link" href="../competitors/competitors.php">
                                <i class="fas fa-chess me-2"></i>
                                المنافسين
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="../reports/reports.php">
                                <i class="fas fa-chart-bar me-2"></i>
                                التقارير
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="../settings/settings.php">
                                <i class="fas fa-cog me-2"></i>
                                الإعدادات
                            </a>
                        </li>
                        <?php endif; ?>
                    </ul>
                </div>
            </nav>

            <!-- المحتوى الرئيسي -->
            <main class="col-md-9 ms-sm-auto col-lg-10 px-md-4">
                <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                    <h1 class="h2">
                        <i class="fas fa-money-bill-wave me-2"></i>
                        إدارة المصروفات
                    </h1>
                    <div class="btn-toolbar mb-2 mb-md-0">
                        <div class="btn-group me-2">
                            <?php if ($can_add): ?>
                            <button type="button" class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#addExpenseModal">
                                <i class="fas fa-plus"></i> إضافة مصروف
                            </button>
                            <?php endif; ?>
                            <button type="button" class="btn btn-success" onclick="exportExpenses()">
                                <i class="fas fa-file-excel"></i> تصدير Excel
                            </button>
                            <button type="button" class="btn btn-danger" onclick="exportExpensesPDF()">
                                <i class="fas fa-file-pdf"></i> تصدير PDF
                            </button>
                        </div>
                    </div>
                </div>

                <?php displayMessage(); ?>

                <!-- الإحصائيات -->
                <div class="row mb-4">
                    <div class="col-md-3">
                        <div class="card bg-primary text-white">
                            <div class="card-body">
                                <div class="d-flex justify-content-between">
                                    <div>
                                        <h4><?php echo number_format($total_amount); ?> د.ع</h4>
                                        <p class="mb-0">إجمالي المصروفات</p>
                                    </div>
                                    <div class="align-self-center">
                                        <i class="fas fa-money-bill-wave fa-2x"></i>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="card bg-info text-white">
                            <div class="card-body">
                                <div class="d-flex justify-content-between">
                                    <div>
                                        <h4><?php echo number_format($total_expenses); ?></h4>
                                        <p class="mb-0">عدد المصروفات</p>
                                    </div>
                                    <div class="align-self-center">
                                        <i class="fas fa-receipt fa-2x"></i>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="card bg-warning text-white">
                            <div class="card-body">
                                <div class="d-flex justify-content-between">
                                    <div>
                                        <h4><?php echo $total_expenses > 0 ? number_format($total_amount / $total_expenses) : 0; ?> د.ع</h4>
                                        <p class="mb-0">متوسط المصروف</p>
                                    </div>
                                    <div class="align-self-center">
                                        <i class="fas fa-calculator fa-2x"></i>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="card bg-success text-white">
                            <div class="card-body">
                                <div class="d-flex justify-content-between">
                                    <div>
                                        <h4><?php echo count($categories_stats); ?></h4>
                                        <p class="mb-0">فئات المصروفات</p>
                                    </div>
                                    <div class="align-self-center">
                                        <i class="fas fa-tags fa-2x"></i>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- فلاتر البحث -->
                <div class="card mb-4">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="fas fa-filter me-2"></i>
                            فلاتر البحث والتصفية
                        </h5>
                    </div>
                    <div class="card-body">
                        <form method="GET" action="">
                            <div class="row">
                                <div class="col-md-3 mb-3">
                                    <label for="search" class="form-label">البحث</label>
                                    <input type="text" class="form-control" id="search" name="search" 
                                           value="<?php echo htmlspecialchars($_GET['search'] ?? ''); ?>" 
                                           placeholder="الوصف، الفئة...">
                                </div>
                                <div class="col-md-2 mb-3">
                                    <label for="category" class="form-label">الفئة</label>
                                    <select class="form-select" id="category" name="category">
                                        <option value="">جميع الفئات</option>
                                        <option value="دعاية وإعلان" <?php echo ($_GET['category'] ?? '') == 'دعاية وإعلان' ? 'selected' : ''; ?>>دعاية وإعلان</option>
                                        <option value="فعاليات" <?php echo ($_GET['category'] ?? '') == 'فعاليات' ? 'selected' : ''; ?>>فعاليات</option>
                                        <option value="مواصلات" <?php echo ($_GET['category'] ?? '') == 'مواصلات' ? 'selected' : ''; ?>>مواصلات</option>
                                        <option value="ضيافة" <?php echo ($_GET['category'] ?? '') == 'ضيافة' ? 'selected' : ''; ?>>ضيافة</option>
                                        <option value="مكتبية" <?php echo ($_GET['category'] ?? '') == 'مكتبية' ? 'selected' : ''; ?>>مكتبية</option>
                                        <option value="أخرى" <?php echo ($_GET['category'] ?? '') == 'أخرى' ? 'selected' : ''; ?>>أخرى</option>
                                    </select>
                                </div>
                                <div class="col-md-2 mb-3">
                                    <label for="date_from" class="form-label">من تاريخ</label>
                                    <input type="date" class="form-control" id="date_from" name="date_from" 
                                           value="<?php echo htmlspecialchars($_GET['date_from'] ?? ''); ?>">
                                </div>
                                <div class="col-md-2 mb-3">
                                    <label for="date_to" class="form-label">إلى تاريخ</label>
                                    <input type="date" class="form-control" id="date_to" name="date_to" 
                                           value="<?php echo htmlspecialchars($_GET['date_to'] ?? ''); ?>">
                                </div>
                                <?php if (isCandidate()): ?>
                                <div class="col-md-2 mb-3">
                                    <label for="region_id" class="form-label">المنطقة</label>
                                    <select class="form-select" id="region_id" name="region_id">
                                        <option value="">جميع المناطق</option>
                                        <?php foreach ($regions as $region): ?>
                                            <option value="<?php echo $region['id']; ?>" 
                                                    <?php echo ($_GET['region_id'] ?? '') == $region['id'] ? 'selected' : ''; ?>>
                                                <?php echo htmlspecialchars($region['name']); ?>
                                            </option>
                                        <?php endforeach; ?>
                                    </select>
                                </div>
                                <?php endif; ?>
                                <div class="col-md-1 mb-3 d-flex align-items-end">
                                    <button type="submit" class="btn btn-primary w-100">
                                        <i class="fas fa-search"></i>
                                    </button>
                                </div>
                            </div>
                        </form>
                    </div>
                </div>

                <!-- جدول المصروفات -->
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="fas fa-list me-2"></i>
                            قائمة المصروفات (<?php echo number_format($total_expenses); ?>)
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="table-responsive">
                            <table class="table table-striped table-hover" id="expensesTable">
                                <thead class="table-dark">
                                    <tr>
                                        <th>التاريخ</th>
                                        <th>الوصف</th>
                                        <th>الفئة</th>
                                        <th>المبلغ</th>
                                        <th>المنطقة</th>
                                        <th>المرفق</th>
                                        <th>أضيف بواسطة</th>
                                        <th>الإجراءات</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php foreach ($expenses as $expense): ?>
                                    <tr>
                                        <td><?php echo formatArabicDate($expense['expense_date']); ?></td>
                                        <td><strong><?php echo htmlspecialchars($expense['description']); ?></strong></td>
                                        <td>
                                            <span class="badge bg-secondary"><?php echo htmlspecialchars($expense['category']); ?></span>
                                        </td>
                                        <td>
                                            <span class="amount"><?php echo number_format($expense['amount']); ?> د.ع</span>
                                        </td>
                                        <td><?php echo htmlspecialchars($expense['region_name']) ?: 'عام'; ?></td>
                                        <td>
                                            <?php if ($expense['attachment']): ?>
                                                <a href="../../<?php echo htmlspecialchars($expense['attachment']); ?>" 
                                                   target="_blank" class="btn btn-sm btn-outline-primary">
                                                    <i class="fas fa-paperclip"></i> عرض
                                                </a>
                                            <?php else: ?>
                                                <span class="text-muted">لا يوجد</span>
                                            <?php endif; ?>
                                        </td>
                                        <td><?php echo htmlspecialchars($expense['added_by_name']); ?></td>
                                        <td>
                                            <div class="btn-group" role="group">
                                                <button type="button" class="btn btn-sm btn-info" 
                                                        onclick="viewExpense(<?php echo $expense['id']; ?>)" 
                                                        title="عرض">
                                                    <i class="fas fa-eye"></i>
                                                </button>
                                                <?php if ($can_edit): ?>
                                                <button type="button" class="btn btn-sm btn-warning" 
                                                        onclick="editExpense(<?php echo $expense['id']; ?>)" 
                                                        title="تعديل">
                                                    <i class="fas fa-edit"></i>
                                                </button>
                                                <?php endif; ?>
                                                <?php if ($can_delete): ?>
                                                <button type="button" class="btn btn-sm btn-danger" 
                                                        onclick="deleteExpense(<?php echo $expense['id']; ?>)" 
                                                        title="حذف">
                                                    <i class="fas fa-trash"></i>
                                                </button>
                                                <?php endif; ?>
                                            </div>
                                        </td>
                                    </tr>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </main>
        </div>
    </div>

    <!-- النوافذ المنبثقة -->
    <?php include 'modals.php'; ?>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://code.jquery.com/jquery-3.7.0.min.js"></script>
    <script src="https://cdn.datatables.net/1.13.6/js/jquery.dataTables.min.js"></script>
    <script src="https://cdn.datatables.net/1.13.6/js/dataTables.bootstrap5.min.js"></script>
    <script src="../../assets/js/expenses.js"></script>
</body>
</html>
