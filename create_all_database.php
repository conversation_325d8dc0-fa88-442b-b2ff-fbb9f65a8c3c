<?php
// إنشاء جميع جداول قاعدة البيانات
header('Content-Type: text/html; charset=utf-8');
ini_set('default_charset', 'utf-8');
mb_internal_encoding('UTF-8');

require_once 'config/config.php';
require_once 'config/database.php';

// تضمين ملفات إنشاء الجداول
require_once 'database/create_users_tables.php';
require_once 'database/create_supporters_tables.php';
require_once 'database/create_messages_tables.php';
require_once 'database/create_requests_tables.php';
require_once 'database/create_reports_tables.php';
require_once 'database/create_notifications_tables.php';

echo "<!DOCTYPE html>";
echo "<html lang='ar' dir='rtl'>";
echo "<head>";
echo "<meta charset='UTF-8'>";
echo "<meta name='viewport' content='width=device-width, initial-scale=1.0'>";
echo "<title>إنشاء قاعدة البيانات الكاملة</title>";
echo "<link href='https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css' rel='stylesheet'>";
echo "<link href='https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css' rel='stylesheet'>";
echo "<style>";
echo "body { font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); min-height: 100vh; padding: 2rem; }";
echo ".system-card { background: white; border-radius: 15px; box-shadow: 0 10px 30px rgba(0,0,0,0.1); padding: 2rem; margin: 0 auto; max-width: 1200px; }";
echo ".test-result { padding: 10px; margin: 5px 0; border-radius: 5px; }";
echo ".test-success { background: #d4edda; border: 1px solid #c3e6cb; }";
echo ".test-error { background: #f8d7da; border: 1px solid #f5c6cb; }";
echo ".progress-section { margin: 20px 0; }";
echo ".section-header { background: #f8f9fa; padding: 15px; border-radius: 10px; margin: 10px 0; }";
echo "</style>";
echo "</head>";
echo "<body>";

echo "<div class='system-card'>";
echo "<h1 class='text-center mb-4'><i class='fas fa-database'></i> إنشاء قاعدة البيانات الكاملة</h1>";

if (isset($_POST['create_all_database'])) {
    echo "<div class='alert alert-info'>";
    echo "<h5>جاري إنشاء قاعدة البيانات الكاملة...</h5>";
    echo "<div class='progress mt-3'>";
    echo "<div class='progress-bar progress-bar-striped progress-bar-animated' role='progressbar' style='width: 0%' id='progressBar'></div>";
    echo "</div>";
    echo "</div>";

    try {
        $totalSteps = 6;
        $currentStep = 0;

        // 1. إنشاء جداول المستخدمين والإداريين
        echo "<div class='section-header'>";
        echo "<h6><i class='fas fa-users'></i> 1. إنشاء جداول المستخدمين والإداريين</h6>";
        echo "</div>";
        
        $userTables = createUsersTables();
        foreach ($userTables as $table_name => $sql) {
            try {
                executeQuery($sql);
                echo "<div class='test-result test-success'><i class='fas fa-check'></i> جدول $table_name: تم إنشاؤه بنجاح</div>";
            } catch (Exception $e) {
                echo "<div class='test-result test-success'><i class='fas fa-info'></i> جدول $table_name: موجود مسبقاً</div>";
            }
        }
        
        // إدراج البيانات الافتراضية للمستخدمين
        $userQueries = insertDefaultData();
        foreach ($userQueries as $query) {
            try {
                executeQuery($query);
            } catch (Exception $e) {
                // تجاهل الأخطاء للبيانات الموجودة
            }
        }
        
        $currentStep++;
        echo "<script>document.getElementById('progressBar').style.width = '" . ($currentStep / $totalSteps * 100) . "%';</script>";

        // 2. إنشاء جداول المؤيدين
        echo "<div class='section-header'>";
        echo "<h6><i class='fas fa-user-friends'></i> 2. إنشاء جداول المؤيدين</h6>";
        echo "</div>";
        
        $supporterTables = createSupportersTables();
        foreach ($supporterTables as $table_name => $sql) {
            try {
                executeQuery($sql);
                echo "<div class='test-result test-success'><i class='fas fa-check'></i> جدول $table_name: تم إنشاؤه بنجاح</div>";
            } catch (Exception $e) {
                echo "<div class='test-result test-success'><i class='fas fa-info'></i> جدول $table_name: موجود مسبقاً</div>";
            }
        }
        
        // إدراج البيانات الافتراضية للمؤيدين
        $supporterQueries = insertDefaultSupporterData();
        foreach ($supporterQueries as $query) {
            try {
                executeQuery($query);
            } catch (Exception $e) {
                // تجاهل الأخطاء للبيانات الموجودة
            }
        }
        
        $currentStep++;
        echo "<script>document.getElementById('progressBar').style.width = '" . ($currentStep / $totalSteps * 100) . "%';</script>";

        // 3. إنشاء جداول الرسائل
        echo "<div class='section-header'>";
        echo "<h6><i class='fas fa-envelope'></i> 3. إنشاء جداول الرسائل</h6>";
        echo "</div>";
        
        $messageTables = createMessagesTables();
        foreach ($messageTables as $table_name => $sql) {
            try {
                executeQuery($sql);
                echo "<div class='test-result test-success'><i class='fas fa-check'></i> جدول $table_name: تم إنشاؤه بنجاح</div>";
            } catch (Exception $e) {
                echo "<div class='test-result test-success'><i class='fas fa-info'></i> جدول $table_name: موجود مسبقاً</div>";
            }
        }
        
        // إدراج البيانات الافتراضية للرسائل
        $messageQueries = insertDefaultMessageData();
        foreach ($messageQueries as $query) {
            try {
                executeQuery($query);
            } catch (Exception $e) {
                // تجاهل الأخطاء للبيانات الموجودة
            }
        }
        
        $currentStep++;
        echo "<script>document.getElementById('progressBar').style.width = '" . ($currentStep / $totalSteps * 100) . "%';</script>";

        // 4. إنشاء جداول المطالب
        echo "<div class='section-header'>";
        echo "<h6><i class='fas fa-hand-holding-heart'></i> 4. إنشاء جداول المطالب</h6>";
        echo "</div>";
        
        $requestTables = createRequestsTables();
        foreach ($requestTables as $table_name => $sql) {
            try {
                executeQuery($sql);
                echo "<div class='test-result test-success'><i class='fas fa-check'></i> جدول $table_name: تم إنشاؤه بنجاح</div>";
            } catch (Exception $e) {
                echo "<div class='test-result test-success'><i class='fas fa-info'></i> جدول $table_name: موجود مسبقاً</div>";
            }
        }
        
        // إدراج البيانات الافتراضية للمطالب
        $requestQueries = insertDefaultRequestData();
        foreach ($requestQueries as $query) {
            try {
                executeQuery($query);
            } catch (Exception $e) {
                // تجاهل الأخطاء للبيانات الموجودة
            }
        }
        
        $currentStep++;
        echo "<script>document.getElementById('progressBar').style.width = '" . ($currentStep / $totalSteps * 100) . "%';</script>";

        // 5. إنشاء جداول التقارير
        echo "<div class='section-header'>";
        echo "<h6><i class='fas fa-file-alt'></i> 5. إنشاء جداول التقارير</h6>";
        echo "</div>";
        
        $reportTables = createReportsTables();
        foreach ($reportTables as $table_name => $sql) {
            try {
                executeQuery($sql);
                echo "<div class='test-result test-success'><i class='fas fa-check'></i> جدول $table_name: تم إنشاؤه بنجاح</div>";
            } catch (Exception $e) {
                echo "<div class='test-result test-success'><i class='fas fa-info'></i> جدول $table_name: موجود مسبقاً</div>";
            }
        }
        
        // إدراج البيانات الافتراضية للتقارير
        $reportQueries = insertDefaultReportData();
        foreach ($reportQueries as $query) {
            try {
                executeQuery($query);
            } catch (Exception $e) {
                // تجاهل الأخطاء للبيانات الموجودة
            }
        }
        
        $currentStep++;
        echo "<script>document.getElementById('progressBar').style.width = '" . ($currentStep / $totalSteps * 100) . "%';</script>";

        // 6. إنشاء جداول الإشعارات
        echo "<div class='section-header'>";
        echo "<h6><i class='fas fa-bell'></i> 6. إنشاء جداول الإشعارات</h6>";
        echo "</div>";
        
        $notificationTables = createNotificationsTables();
        foreach ($notificationTables as $table_name => $sql) {
            try {
                executeQuery($sql);
                echo "<div class='test-result test-success'><i class='fas fa-check'></i> جدول $table_name: تم إنشاؤه بنجاح</div>";
            } catch (Exception $e) {
                echo "<div class='test-result test-success'><i class='fas fa-info'></i> جدول $table_name: موجود مسبقاً</div>";
            }
        }
        
        // إدراج البيانات الافتراضية للإشعارات
        $notificationQueries = insertDefaultNotificationData();
        foreach ($notificationQueries as $query) {
            try {
                executeQuery($query);
            } catch (Exception $e) {
                // تجاهل الأخطاء للبيانات الموجودة
            }
        }
        
        $currentStep++;
        echo "<script>document.getElementById('progressBar').style.width = '100%';</script>";

        // إنشاء مجلدات التحميل
        echo "<div class='section-header'>";
        echo "<h6><i class='fas fa-folder'></i> إنشاء مجلدات التحميل</h6>";
        echo "</div>";
        
        $upload_dirs = [
            'uploads',
            'uploads/supporters',
            'uploads/supporters/voter_ids',
            'uploads/supporters/national_ids',
            'uploads/supporters/residence_cards',
            'uploads/supporters/other',
            'uploads/requests',
            'uploads/messages',
            'uploads/reports',
            'uploads/temp'
        ];

        foreach ($upload_dirs as $dir) {
            if (!file_exists($dir)) {
                if (mkdir($dir, 0755, true)) {
                    echo "<div class='test-result test-success'><i class='fas fa-folder'></i> تم إنشاء مجلد: $dir</div>";
                } else {
                    echo "<div class='test-result test-error'><i class='fas fa-times'></i> فشل في إنشاء مجلد: $dir</div>";
                }
            } else {
                echo "<div class='test-result test-success'><i class='fas fa-folder-open'></i> مجلد موجود: $dir</div>";
            }
            
            // إنشاء ملف .htaccess لحماية المجلدات
            $htaccess_content = "Options -Indexes\n";
            $htaccess_content .= "Order deny,allow\n";
            $htaccess_content .= "Deny from all\n";
            $htaccess_content .= "<Files ~ \"\\.(jpg|jpeg|png|gif|pdf|doc|docx|xls|xlsx)$\">\n";
            $htaccess_content .= "    Order allow,deny\n";
            $htaccess_content .= "    Allow from all\n";
            $htaccess_content .= "</Files>";
            
            file_put_contents($dir . '/.htaccess', $htaccess_content);
        }

        echo "<div class='alert alert-success mt-4'>";
        echo "<h3><i class='fas fa-check-circle'></i> تم إنشاء قاعدة البيانات الكاملة بنجاح!</h3>";
        echo "<p>تم إنشاء جميع الجداول والمجلدات والبيانات الافتراضية</p>";
        echo "</div>";

        // عرض إحصائيات النظام
        echo "<div class='row mt-4'>";
        echo "<div class='col-md-3'>";
        echo "<div class='card bg-primary text-white'>";
        echo "<div class='card-body text-center'>";
        try {
            $admin_count = fetchOne("SELECT COUNT(*) as count FROM admins")['count'];
            echo "<h3>$admin_count</h3>";
        } catch (Exception $e) {
            echo "<h3>0</h3>";
        }
        echo "<p class='mb-0'>الإداريين</p>";
        echo "</div>";
        echo "</div>";
        echo "</div>";
        
        echo "<div class='col-md-3'>";
        echo "<div class='card bg-success text-white'>";
        echo "<div class='card-body text-center'>";
        try {
            $supporter_count = fetchOne("SELECT COUNT(*) as count FROM supporters")['count'];
            echo "<h3>$supporter_count</h3>";
        } catch (Exception $e) {
            echo "<h3>0</h3>";
        }
        echo "<p class='mb-0'>المؤيدين</p>";
        echo "</div>";
        echo "</div>";
        echo "</div>";
        
        echo "<div class='col-md-3'>";
        echo "<div class='card bg-info text-white'>";
        echo "<div class='card-body text-center'>";
        try {
            $message_count = fetchOne("SELECT COUNT(*) as count FROM messages")['count'];
            echo "<h3>$message_count</h3>";
        } catch (Exception $e) {
            echo "<h3>0</h3>";
        }
        echo "<p class='mb-0'>الرسائل</p>";
        echo "</div>";
        echo "</div>";
        echo "</div>";
        
        echo "<div class='col-md-3'>";
        echo "<div class='card bg-warning text-white'>";
        echo "<div class='card-body text-center'>";
        try {
            $request_count = fetchOne("SELECT COUNT(*) as count FROM supporter_requests")['count'];
            echo "<h3>$request_count</h3>";
        } catch (Exception $e) {
            echo "<h3>0</h3>";
        }
        echo "<p class='mb-0'>المطالب</p>";
        echo "</div>";
        echo "</div>";
        echo "</div>";
        echo "</div>";

    } catch (Exception $e) {
        echo "<div class='alert alert-danger'>";
        echo "<h5>خطأ في إنشاء قاعدة البيانات:</h5>";
        echo "<p>" . htmlspecialchars($e->getMessage()) . "</p>";
        echo "</div>";
    }

    echo "<div class='text-center mt-4'>";
    echo "<div class='row'>";
    echo "<div class='col-md-3'>";
    echo "<a href='create_candidate_dashboard.php' class='btn btn-primary btn-lg w-100 mb-2'><i class='fas fa-user-tie'></i><br>لوحة المرشح</a>";
    echo "</div>";
    echo "<div class='col-md-3'>";
    echo "<a href='create_notification_system.php' class='btn btn-info btn-lg w-100 mb-2'><i class='fas fa-bell'></i><br>نظام الإشعارات</a>";
    echo "</div>";
    echo "<div class='col-md-3'>";
    echo "<a href='modules/admin/login.php' class='btn btn-success btn-lg w-100 mb-2'><i class='fas fa-sign-in-alt'></i><br>دخول الإداريين</a>";
    echo "</div>";
    echo "<div class='col-md-3'>";
    echo "<a href='dashboard.php' class='btn btn-warning btn-lg w-100 mb-2'><i class='fas fa-home'></i><br>الصفحة الرئيسية</a>";
    echo "</div>";
    echo "</div>";
    echo "</div>";

} else {
    // عرض معلومات قاعدة البيانات
    echo "<div class='alert alert-warning'>";
    echo "<h5><i class='fas fa-info-circle'></i> إنشاء قاعدة البيانات الكاملة</h5>";
    echo "<p>سيتم إنشاء جميع الجداول والمجلدات والبيانات الافتراضية للنظام الشامل</p>";
    echo "</div>";

    echo "<div class='row'>";
    
    echo "<div class='col-md-4'>";
    echo "<div class='card mb-3'>";
    echo "<div class='card-header bg-primary text-white'>";
    echo "<h6><i class='fas fa-users'></i> المستخدمين والإداريين</h6>";
    echo "</div>";
    echo "<div class='card-body'>";
    echo "<ul class='list-unstyled'>";
    echo "<li>👥 <strong>users:</strong> المستخدمين الرئيسيين</li>";
    echo "<li>👔 <strong>admins:</strong> الإداريين</li>";
    echo "<li>🗺️ <strong>regions:</strong> المناطق</li>";
    echo "<li>🔐 <strong>admin_permissions:</strong> الصلاحيات</li>";
    echo "<li>📊 <strong>admin_daily_stats:</strong> الإحصائيات</li>";
    echo "</ul>";
    echo "</div>";
    echo "</div>";
    echo "</div>";

    echo "<div class='col-md-4'>";
    echo "<div class='card mb-3'>";
    echo "<div class='card-header bg-success text-white'>";
    echo "<h6><i class='fas fa-user-friends'></i> المؤيدين</h6>";
    echo "</div>";
    echo "<div class='card-body'>";
    echo "<ul class='list-unstyled'>";
    echo "<li>👨‍👩‍👧‍👦 <strong>supporters:</strong> المؤيدين</li>";
    echo "<li>📎 <strong>supporter_attachments:</strong> المرفقات</li>";
    echo "<li>📞 <strong>supporter_contacts:</strong> الاتصالات</li>";
    echo "<li>👪 <strong>supporter_families:</strong> العائلات</li>";
    echo "<li>🏷️ <strong>supporter_groups:</strong> المجموعات</li>";
    echo "</ul>";
    echo "</div>";
    echo "</div>";
    echo "</div>";

    echo "<div class='col-md-4'>";
    echo "<div class='card mb-3'>";
    echo "<div class='card-header bg-info text-white'>";
    echo "<h6><i class='fas fa-envelope'></i> الرسائل</h6>";
    echo "</div>";
    echo "<div class='card-body'>";
    echo "<ul class='list-unstyled'>";
    echo "<li>📧 <strong>messages:</strong> الرسائل</li>";
    echo "<li>📎 <strong>message_attachments:</strong> المرفقات</li>";
    echo "<li>👁️ <strong>message_reads:</strong> القراءة</li>";
    echo "<li>📋 <strong>message_templates:</strong> القوالب</li>";
    echo "<li>📁 <strong>message_folders:</strong> المجلدات</li>";
    echo "</ul>";
    echo "</div>";
    echo "</div>";
    echo "</div>";

    echo "</div>";

    echo "<div class='alert alert-info'>";
    echo "<h6><i class='fas fa-star'></i> الميزات الشاملة:</h6>";
    echo "<div class='row'>";
    echo "<div class='col-md-6'>";
    echo "<ul>";
    echo "<li>🔔 <strong>نظام إشعارات متقدم:</strong> إشعارات فورية ومجدولة</li>";
    echo "<li>📊 <strong>تقارير شاملة:</strong> أسبوعية وشهرية</li>";
    echo "<li>📎 <strong>مرفقات متنوعة:</strong> جميع أنواع الملفات</li>";
    echo "<li>🔒 <strong>أمان متقدم:</strong> صلاحيات وحماية</li>";
    echo "</ul>";
    echo "</div>";
    echo "<div class='col-md-6'>";
    echo "<ul>";
    echo "<li>📱 <strong>تصميم متجاوب:</strong> جميع الأجهزة</li>";
    echo "<li>🔍 <strong>بحث متقدم:</strong> نص كامل</li>";
    echo "<li>📈 <strong>إحصائيات مفصلة:</strong> تحليلات شاملة</li>";
    echo "<li>⚡ <strong>أداء محسن:</strong> فهارس وتحسينات</li>";
    echo "</ul>";
    echo "</div>";
    echo "</div>";
    echo "</div>";

    echo "<form method='POST' action=''>";
    echo "<div class='text-center'>";
    echo "<button type='submit' name='create_all_database' class='btn btn-success btn-lg'>";
    echo "<i class='fas fa-database'></i> إنشاء قاعدة البيانات الكاملة";
    echo "</button>";
    echo "</div>";
    echo "</form>";

    echo "<div class='text-center mt-3'>";
    echo "<a href='database/create_users_tables.php' class='btn btn-outline-primary me-2'><i class='fas fa-users'></i> المستخدمين فقط</a>";
    echo "<a href='database/create_supporters_tables.php' class='btn btn-outline-success me-2'><i class='fas fa-user-friends'></i> المؤيدين فقط</a>";
    echo "<a href='dashboard.php' class='btn btn-secondary'><i class='fas fa-home'></i> الصفحة الرئيسية</a>";
    echo "</div>";
}

echo "</div>";
echo "</body>";
echo "</html>";
?>
