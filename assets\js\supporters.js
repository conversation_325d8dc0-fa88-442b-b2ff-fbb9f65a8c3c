// JavaScript لصفحة المؤيدين

$(document).ready(function() {
    // تهيئة جدول البيانات
    initializeDataTable();

    // تهيئة رفع الصور
    initializePhotoUpload();

    // تهيئة التحقق من النماذج
    initializeFormValidation();

    // تهيئة البحث المباشر
    initializeLiveSearch();
});

// تهيئة جدول البيانات
function initializeDataTable() {
    $('#supportersTable').DataTable({
        language: {
            url: 'https://cdn.datatables.net/plug-ins/1.13.6/i18n/ar.json'
        },
        responsive: true,
        pageLength: 25,
        lengthMenu: [[10, 25, 50, 100, -1], [10, 25, 50, 100, "الكل"]],
        order: [[7, 'desc']], // ترتيب حسب تاريخ الإضافة
        columnDefs: [
            { orderable: false, targets: [0, 8] }, // عدم ترتيب الصورة والإجراءات
            { searchable: false, targets: [0, 8] }, // عدم البحث في الصورة والإجراءات
            { className: "text-center", targets: "_all" }
        ],
        dom: 'Bfrtip',
        buttons: [
            {
                extend: 'excel',
                text: '<i class="fas fa-file-excel"></i> Excel',
                className: 'btn btn-success btn-sm',
                exportOptions: {
                    columns: [1, 2, 3, 4, 5, 6, 7] // استبعاد الصورة والإجراءات
                }
            },
            {
                extend: 'pdf',
                text: '<i class="fas fa-file-pdf"></i> PDF',
                className: 'btn btn-danger btn-sm',
                exportOptions: {
                    columns: [1, 2, 3, 4, 5, 6, 7]
                },
                customize: function(doc) {
                    // تخصيص PDF للعربية
                    doc.defaultStyle.font = 'Arial';
                    doc.styles.tableHeader.alignment = 'center';
                    doc.content[1].table.widths = ['*', '*', '*', '*', '*', '*', '*'];
                }
            },
            {
                extend: 'print',
                text: '<i class="fas fa-print"></i> طباعة',
                className: 'btn btn-info btn-sm',
                exportOptions: {
                    columns: [1, 2, 3, 4, 5, 6, 7]
                }
            }
        ]
    });
}

// تهيئة رفع الصور
function initializePhotoUpload() {
    const photoInput = document.getElementById('photo');
    const photoPreview = document.getElementById('photoPreview');
    const uploadArea = document.querySelector('.photo-upload-area');

    if (photoInput && uploadArea) {
        // رفع الصور بالسحب والإفلات
        uploadArea.addEventListener('dragover', function(e) {
            e.preventDefault();
            this.classList.add('dragover');
        });

        uploadArea.addEventListener('dragleave', function(e) {
            e.preventDefault();
            this.classList.remove('dragover');
        });

        uploadArea.addEventListener('drop', function(e) {
            e.preventDefault();
            this.classList.remove('dragover');

            const files = e.dataTransfer.files;
            if (files.length > 0) {
                photoInput.files = files;
                previewPhoto(files[0]);
            }
        });

        // رفع الصور بالنقر
        uploadArea.addEventListener('click', function() {
            photoInput.click();
        });

        photoInput.addEventListener('change', function() {
            if (this.files && this.files[0]) {
                previewPhoto(this.files[0]);
            }
        });
    }
}

// معاينة الصورة
function previewPhoto(file) {
    const reader = new FileReader();
    const photoPreview = document.getElementById('photoPreview');

    reader.onload = function(e) {
        if (photoPreview) {
            photoPreview.src = e.target.result;
            photoPreview.style.display = 'block';
        }
    };

    reader.readAsDataURL(file);
}

// تهيئة التحقق من النماذج
function initializeFormValidation() {
    const forms = document.querySelectorAll('.needs-validation');

    forms.forEach(function(form) {
        form.addEventListener('submit', function(event) {
            if (!form.checkValidity()) {
                event.preventDefault();
                event.stopPropagation();
            }

            form.classList.add('was-validated');
        });
    });

    // التحقق من رقم الهاتف العراقي
    const phoneInputs = document.querySelectorAll('input[name="phone"]');
    phoneInputs.forEach(function(input) {
        input.addEventListener('input', function() {
            validateIraqiPhone(this);
        });
    });

    // التحقق من رقم الناخب
    const voterInputs = document.querySelectorAll('input[name="voter_number"]');
    voterInputs.forEach(function(input) {
        input.addEventListener('blur', function() {
            checkVoterNumber(this);
        });
    });
}

// التحقق من رقم الهاتف العراقي
function validateIraqiPhone(input) {
    const phonePattern = /^07[3-9][0-9]{8}$/;
    const isValid = phonePattern.test(input.value);

    if (input.value && !isValid) {
        input.setCustomValidity('رقم الهاتف غير صحيح. يجب أن يبدأ بـ 07 ويتكون من 11 رقم');
        input.classList.add('is-invalid');
        input.classList.remove('is-valid');
    } else {
        input.setCustomValidity('');
        input.classList.remove('is-invalid');
        if (input.value) {
            input.classList.add('is-valid');
        }
    }
}

// التحقق من رقم الناخب
function checkVoterNumber(input) {
    if (input.value) {
        fetch('check_voter_number.php', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                voter_number: input.value,
                supporter_id: input.dataset.supporterId || null
            })
        })
        .then(response => response.json())
        .then(data => {
            if (data.exists) {
                input.setCustomValidity('رقم الناخب موجود مسبقاً');
                input.classList.add('is-invalid');
                input.classList.remove('is-valid');
                showToast('رقم الناخب موجود مسبقاً', 'error');
            } else {
                input.setCustomValidity('');
                input.classList.remove('is-invalid');
                input.classList.add('is-valid');
            }
        })
        .catch(error => {
            console.error('خطأ في التحقق من رقم الناخب:', error);
        });
    }
}

// تهيئة البحث المباشر
function initializeLiveSearch() {
    const searchInput = document.getElementById('search');
    if (searchInput) {
        let searchTimeout;

        searchInput.addEventListener('input', function() {
            clearTimeout(searchTimeout);
            searchTimeout = setTimeout(() => {
                const table = $('#supportersTable').DataTable();
                table.search(this.value).draw();
            }, 300);
        });
    }
}

// عرض تفاصيل المؤيد
function viewSupporter(id) {
    // توجيه إلى صفحة عرض التفاصيل الجديدة
    window.location.href = `view_supporter.php?id=${id}`;
}

// عرض تفاصيل المؤيد في نافذة منبثقة
function displaySupporterDetails(supporter) {
    const modal = new bootstrap.Modal(document.getElementById('viewSupporterModal'));

    // ملء البيانات
    document.getElementById('viewFullName').textContent = supporter.full_name;
    document.getElementById('viewGender').textContent = supporter.gender === 'male' ? 'ذكر' : 'أنثى';
    document.getElementById('viewMaritalStatus').textContent = getMaritalStatusText(supporter.marital_status);
    document.getElementById('viewBirthDate').textContent = supporter.birth_date;
    document.getElementById('viewAge').textContent = supporter.age + ' سنة';
    document.getElementById('viewEducation').textContent = supporter.education || 'غير محدد';
    document.getElementById('viewProfession').textContent = supporter.profession || 'غير محدد';
    document.getElementById('viewAddress').textContent = supporter.address;
    document.getElementById('viewPhone').textContent = supporter.phone;
    document.getElementById('viewVoterNumber').textContent = supporter.voter_number || 'غير محدد';
    document.getElementById('viewVotingCenter').textContent = supporter.voting_center || 'غير محدد';
    document.getElementById('viewRegion').textContent = supporter.region_name;
    document.getElementById('viewNotes').textContent = supporter.notes || 'لا توجد ملاحظات';
    document.getElementById('viewAddedBy').textContent = supporter.added_by_name;
    document.getElementById('viewCreatedAt').textContent = supporter.created_at;

    // عرض الصورة
    const photoElement = document.getElementById('viewPhoto');
    if (supporter.photo) {
        photoElement.src = '../../' + supporter.photo;
        photoElement.style.display = 'block';
    } else {
        photoElement.style.display = 'none';
    }

    modal.show();
}

// تعديل المؤيد
function editSupporter(id) {
    showLoading(true);

    fetch(`get_supporter.php?id=${id}`)
        .then(response => response.json())
        .then(data => {
            showLoading(false);
            if (data.success) {
                fillEditForm(data.supporter);
                const modal = new bootstrap.Modal(document.getElementById('editSupporterModal'));
                modal.show();
            } else {
                showToast('حدث خطأ في جلب بيانات المؤيد', 'error');
            }
        })
        .catch(error => {
            showLoading(false);
            console.error('خطأ:', error);
            showToast('حدث خطأ في الاتصال', 'error');
        });
}

// ملء نموذج التعديل
function fillEditForm(supporter) {
    document.getElementById('editId').value = supporter.id;
    document.getElementById('editFullName').value = supporter.full_name;
    document.getElementById('editGender').value = supporter.gender;
    document.getElementById('editMaritalStatus').value = supporter.marital_status;
    document.getElementById('editBirthDate').value = supporter.birth_date;
    document.getElementById('editEducation').value = supporter.education || '';
    document.getElementById('editProfession').value = supporter.profession || '';
    document.getElementById('editAddress').value = supporter.address;
    document.getElementById('editPhone').value = supporter.phone;
    document.getElementById('editVoterNumber').value = supporter.voter_number || '';
    document.getElementById('editVotingCenter').value = supporter.voting_center || '';
    document.getElementById('editRegionId').value = supporter.region_id;
    document.getElementById('editNotes').value = supporter.notes || '';

    // عرض الصورة الحالية
    const currentPhoto = document.getElementById('editCurrentPhoto');
    if (supporter.photo) {
        currentPhoto.src = '../../' + supporter.photo;
        currentPhoto.style.display = 'block';
    } else {
        currentPhoto.style.display = 'none';
    }
}

// حذف المؤيد
function deleteSupporter(id) {
    if (confirm('هل أنت متأكد من حذف هذا المؤيد؟ لا يمكن التراجع عن هذا الإجراء.')) {
        showLoading(true);

        const formData = new FormData();
        formData.append('action', 'delete');
        formData.append('id', id);

        fetch('supporters.php', {
            method: 'POST',
            body: formData
        })
        .then(response => response.text())
        .then(data => {
            showLoading(false);
            location.reload(); // إعادة تحميل الصفحة
        })
        .catch(error => {
            showLoading(false);
            console.error('خطأ:', error);
            showToast('حدث خطأ أثناء الحذف', 'error');
        });
    }
}

// طباعة بطاقة المؤيد
function printSupporterCard(id) {
    showLoading(true);

    fetch(`get_supporter.php?id=${id}`)
        .then(response => response.json())
        .then(data => {
            showLoading(false);
            if (data.success) {
                generateSupporterCard(data.supporter);
            } else {
                showToast('حدث خطأ في جلب بيانات المؤيد', 'error');
            }
        })
        .catch(error => {
            showLoading(false);
            console.error('خطأ:', error);
            showToast('حدث خطأ في الاتصال', 'error');
        });
}

// إنشاء بطاقة المؤيد للطباعة
function generateSupporterCard(supporter) {
    const printWindow = window.open('', '_blank');
    const cardHTML = `
        <!DOCTYPE html>
        <html lang="ar" dir="rtl">
        <head>
            <meta charset="UTF-8">
            <title>بطاقة المؤيد - ${supporter.full_name}</title>
            <style>
                body { font-family: Arial, sans-serif; margin: 20px; }
                .supporter-card {
                    width: 350px;
                    height: 220px;
                    border: 2px solid #007bff;
                    border-radius: 15px;
                    padding: 15px;
                    background: linear-gradient(135deg, #f8f9fa, #ffffff);
                    margin: 0 auto;
                }
                .card-header { text-align: center; margin-bottom: 10px; color: #007bff; font-weight: bold; }
                .supporter-info { display: flex; align-items: center; gap: 15px; }
                .supporter-photo-large {
                    width: 80px; height: 80px; border-radius: 50%;
                    object-fit: cover; border: 3px solid #007bff;
                }
                .supporter-details-card { flex: 1; }
                .detail-row { display: flex; justify-content: space-between; margin-bottom: 5px; font-size: 0.9rem; }
                .detail-label-card { font-weight: 600; color: #495057; }
                .detail-value-card { color: #6c757d; }
                @media print {
                    body { margin: 0; }
                    .supporter-card { margin: 0; }
                }
            </style>
        </head>
        <body>
            <div class="supporter-card">
                <div class="card-header">بطاقة مؤيد</div>
                <div class="supporter-info">
                    ${supporter.photo ? `<img src="../../${supporter.photo}" class="supporter-photo-large" alt="صورة المؤيد">` : '<div style="width:80px;height:80px;border-radius:50%;background:#f0f0f0;display:flex;align-items:center;justify-content:center;border:3px solid #007bff;"><i class="fas fa-user"></i></div>'}
                    <div class="supporter-details-card">
                        <div class="detail-row">
                            <span class="detail-label-card">الاسم:</span>
                            <span class="detail-value-card">${supporter.full_name}</span>
                        </div>
                        <div class="detail-row">
                            <span class="detail-label-card">الهاتف:</span>
                            <span class="detail-value-card">${supporter.phone}</span>
                        </div>
                        <div class="detail-row">
                            <span class="detail-label-card">رقم الناخب:</span>
                            <span class="detail-value-card">${supporter.voter_number || 'غير محدد'}</span>
                        </div>
                        <div class="detail-row">
                            <span class="detail-label-card">المنطقة:</span>
                            <span class="detail-value-card">${supporter.region_name}</span>
                        </div>
                        <div class="detail-row">
                            <span class="detail-label-card">العمر:</span>
                            <span class="detail-value-card">${supporter.age} سنة</span>
                        </div>
                    </div>
                </div>
            </div>
            <script>
                window.onload = function() {
                    window.print();
                    window.onafterprint = function() {
                        window.close();
                    };
                };
            </script>
        </body>
        </html>
    `;

    printWindow.document.write(cardHTML);
    printWindow.document.close();
}

// عرض الصورة في نافذة منبثقة
function showPhotoModal(photoUrl) {
    const modal = new bootstrap.Modal(document.getElementById('photoModal') || createPhotoModal());
    document.getElementById('modalPhoto').src = photoUrl;
    modal.show();
}

// إنشاء نافذة عرض الصورة
function createPhotoModal() {
    const modalHTML = `
        <div class="modal fade" id="photoModal" tabindex="-1">
            <div class="modal-dialog modal-lg">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title">صورة المؤيد</h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                    </div>
                    <div class="modal-body text-center">
                        <img id="modalPhoto" src="" alt="صورة المؤيد" class="img-fluid">
                    </div>
                </div>
            </div>
        </div>
    `;
    document.body.insertAdjacentHTML('beforeend', modalHTML);
    return document.getElementById('photoModal');
}

// تصدير إلى Excel
function exportToExcel() {
    // جمع معاملات البحث الحالية
    const urlParams = new URLSearchParams(window.location.search);
    const exportUrl = 'export_excel.php?' + urlParams.toString();
    window.location.href = exportUrl;
}

// تصدير إلى PDF
function exportToPDF() {
    // جمع معاملات البحث الحالية
    const urlParams = new URLSearchParams(window.location.search);
    const exportUrl = 'export_pdf.php?' + urlParams.toString();
    window.open(exportUrl, '_blank');
}

// الحصول على نص الحالة الاجتماعية
function getMaritalStatusText(status) {
    const statuses = {
        'single': 'أعزب',
        'married': 'متزوج',
        'divorced': 'مطلق',
        'widowed': 'أرمل'
    };
    return statuses[status] || status;
}

// إظهار مؤشر التحميل
function showLoading(show) {
    const loader = document.getElementById('loadingOverlay') || createLoadingOverlay();
    loader.style.display = show ? 'flex' : 'none';
}

// إنشاء مؤشر التحميل
function createLoadingOverlay() {
    const loaderHTML = `
        <div id="loadingOverlay" class="loading-overlay">
            <div class="loading-spinner"></div>
        </div>
    `;
    document.body.insertAdjacentHTML('beforeend', loaderHTML);
    return document.getElementById('loadingOverlay');
}

// دالة عامة لإظهار الرسائل
function showToast(message, type = 'info') {
    const toastContainer = getOrCreateToastContainer();
    const toast = document.createElement('div');
    toast.className = `toast align-items-center text-white bg-${getBootstrapColor(type)} border-0`;
    toast.setAttribute('role', 'alert');
    toast.innerHTML = `
        <div class="d-flex">
            <div class="toast-body">
                <i class="fas fa-${getIcon(type)} me-2"></i>
                ${message}
            </div>
            <button type="button" class="btn-close btn-close-white me-2 m-auto" data-bs-dismiss="toast"></button>
        </div>
    `;

    toastContainer.appendChild(toast);
    const bsToast = new bootstrap.Toast(toast);
    bsToast.show();

    toast.addEventListener('hidden.bs.toast', function() {
        toast.remove();
    });
}

// دالة للحصول على حاوية التوست
function getOrCreateToastContainer() {
    let container = document.querySelector('.toast-container');
    if (!container) {
        container = document.createElement('div');
        container.className = 'toast-container position-fixed top-0 end-0 p-3';
        container.style.zIndex = '9999';
        document.body.appendChild(container);
    }
    return container;
}

// دالة للحصول على لون Bootstrap
function getBootstrapColor(type) {
    const colors = {
        'success': 'success',
        'error': 'danger',
        'warning': 'warning',
        'info': 'info'
    };
    return colors[type] || 'info';
}

// دالة للحصول على الأيقونة
function getIcon(type) {
    const icons = {
        'success': 'check-circle',
        'error': 'exclamation-triangle',
        'warning': 'exclamation-circle',
        'info': 'info-circle'
    };
    return icons[type] || 'info-circle';
}
