/* تصميم لوحة التحكم */
:root {
    --primary-color: #007bff;
    --secondary-color: #6c757d;
    --success-color: #28a745;
    --info-color: #17a2b8;
    --warning-color: #ffc107;
    --danger-color: #dc3545;
    --light-color: #f8f9fa;
    --dark-color: #343a40;
    --sidebar-width: 250px;
    --navbar-height: 60px;
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background-color: #f8f9fa;
}

/* شريط التنقل العلوي */
.navbar {
    height: var(--navbar-height);
    box-shadow: 0 2px 4px rgba(0,0,0,.1);
    z-index: 1030;
}

.navbar-brand {
    font-weight: bold;
    font-size: 1.2rem;
}

/* الشريط الجانبي */
.sidebar {
    position: fixed;
    top: var(--navbar-height);
    bottom: 0;
    right: 0;
    z-index: 100;
    padding: 48px 0 0;
    box-shadow: inset -1px 0 0 rgba(0, 0, 0, .1);
    background-color: #fff;
    width: var(--sidebar-width);
}

.sidebar .nav-link {
    color: #333;
    padding: 12px 20px;
    border-radius: 0;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.sidebar .nav-link:hover {
    background-color: #f8f9fa;
    color: var(--primary-color);
    transform: translateX(-5px);
}

.sidebar .nav-link.active {
    background-color: var(--primary-color);
    color: white;
}

.sidebar .nav-link i {
    width: 20px;
    text-align: center;
}

/* المحتوى الرئيسي */
.main-content {
    margin-top: var(--navbar-height);
    padding-right: var(--sidebar-width);
}

/* بطاقات الإحصائيات */
.stats-card {
    cursor: pointer;
    transition: all 0.3s ease;
    border: none;
    border-radius: 10px;
}

.stats-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 25px rgba(0,0,0,0.15) !important;
}

.border-left-primary {
    border-right: 4px solid var(--primary-color) !important;
}

.border-left-success {
    border-right: 4px solid var(--success-color) !important;
}

.border-left-info {
    border-right: 4px solid var(--info-color) !important;
}

.border-left-warning {
    border-right: 4px solid var(--warning-color) !important;
}

.border-left-danger {
    border-right: 4px solid var(--danger-color) !important;
}

.border-left-secondary {
    border-right: 4px solid var(--secondary-color) !important;
}

.border-left-dark {
    border-right: 4px solid var(--dark-color) !important;
}

/* الأزرار ثلاثية الأبعاد */
.btn-3d {
    display: block;
    width: 100%;
    height: 120px;
    text-decoration: none;
    color: white;
    border-radius: 15px;
    position: relative;
    overflow: hidden;
    transition: all 0.3s ease;
    box-shadow: 0 8px 15px rgba(0,0,0,0.1);
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    text-align: center;
}

.btn-3d:hover {
    transform: translateY(-3px);
    box-shadow: 0 15px 25px rgba(0,0,0,0.2);
    color: white;
    text-decoration: none;
}

.btn-3d:active {
    transform: translateY(-1px);
    box-shadow: 0 5px 10px rgba(0,0,0,0.2);
}

.btn-3d i {
    font-size: 2rem;
    margin-bottom: 10px;
}

.btn-3d span {
    font-size: 1rem;
    font-weight: bold;
}

.btn-3d-primary {
    background: linear-gradient(135deg, #007bff, #0056b3);
}

.btn-3d-success {
    background: linear-gradient(135deg, #28a745, #1e7e34);
}

.btn-3d-info {
    background: linear-gradient(135deg, #17a2b8, #117a8b);
}

.btn-3d-warning {
    background: linear-gradient(135deg, #ffc107, #e0a800);
    color: #212529 !important;
}

.btn-3d-danger {
    background: linear-gradient(135deg, #dc3545, #c82333);
}

.btn-3d-secondary {
    background: linear-gradient(135deg, #6c757d, #545b62);
}

.btn-3d-dark {
    background: linear-gradient(135deg, #343a40, #23272b);
}

.btn-3d-light {
    background: linear-gradient(135deg, #f8f9fa, #e9ecef);
    color: #212529 !important;
}

/* تأثير الموجة للأزرار */
.btn-3d::before {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 0;
    height: 0;
    border-radius: 50%;
    background: rgba(255,255,255,0.3);
    transform: translate(-50%, -50%);
    transition: width 0.6s, height 0.6s;
}

.btn-3d:active::before {
    width: 300px;
    height: 300px;
}

/* الإشعارات */
.notifications-dropdown {
    width: 300px;
    max-height: 400px;
    overflow-y: auto;
}

.notification-item {
    padding: 10px;
    border-bottom: 1px solid #eee;
}

.notification-item:last-child {
    border-bottom: none;
}

/* التقويم */
#calendar {
    max-width: 100%;
    margin: 0 auto;
}

.fc-event {
    border-radius: 5px;
    border: none;
    padding: 2px 5px;
}

/* التقارير */
.report-item {
    transition: all 0.3s ease;
}

.report-item:hover {
    background-color: #f8f9fa;
    border-radius: 5px;
}

/* البحث الشامل */
#globalSearch {
    border-radius: 25px;
    padding: 12px 20px;
    border: 2px solid #e9ecef;
    transition: all 0.3s ease;
}

#globalSearch:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 0.2rem rgba(0,123,255,.25);
}

/* تحسينات للجوال */
@media (max-width: 768px) {
    .sidebar {
        position: fixed;
        top: var(--navbar-height);
        right: -var(--sidebar-width);
        width: var(--sidebar-width);
        height: calc(100vh - var(--navbar-height));
        transition: right 0.3s ease;
        z-index: 1040;
    }
    
    .sidebar.show {
        right: 0;
    }
    
    .main-content {
        padding-right: 0;
        margin-right: 0;
    }
    
    .btn-3d {
        height: 100px;
    }
    
    .btn-3d i {
        font-size: 1.5rem;
        margin-bottom: 5px;
    }
    
    .btn-3d span {
        font-size: 0.9rem;
    }
    
    .stats-card {
        margin-bottom: 1rem;
    }
}

/* تحسينات إضافية */
.card {
    border: none;
    border-radius: 10px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    transition: all 0.3s ease;
}

.card:hover {
    box-shadow: 0 5px 15px rgba(0,0,0,0.15);
}

.card-header {
    background-color: transparent;
    border-bottom: 1px solid #eee;
    font-weight: bold;
}

/* تأثيرات التحميل */
.loading {
    position: relative;
    overflow: hidden;
}

.loading::after {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.4), transparent);
    animation: loading 1.5s infinite;
}

@keyframes loading {
    0% {
        left: -100%;
    }
    100% {
        left: 100%;
    }
}

/* تحسين الألوان */
.text-gray-800 {
    color: #5a5c69 !important;
}

.text-gray-300 {
    color: #dddfeb !important;
}

/* تحسين الخطوط */
.font-weight-bold {
    font-weight: 700 !important;
}

.text-xs {
    font-size: 0.7rem;
}

/* تحسين المسافات */
.no-gutters {
    margin-right: 0;
    margin-left: 0;
}

.no-gutters > .col,
.no-gutters > [class*="col-"] {
    padding-right: 0;
    padding-left: 0;
}

/* تحسين الحدود */
.border-bottom {
    border-bottom: 1px solid #e3e6f0 !important;
}

/* تحسين الظلال */
.shadow {
    box-shadow: 0 0.15rem 1.75rem 0 rgba(58, 59, 69, 0.15) !important;
}

/* تحسين الانتقالات */
* {
    transition: all 0.3s ease;
}

/* إزالة التحديد للنصوص */
.stats-card, .btn-3d {
    user-select: none;
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
}

/* تحسين شريط التمرير */
::-webkit-scrollbar {
    width: 8px;
}

::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 10px;
}

::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 10px;
}

::-webkit-scrollbar-thumb:hover {
    background: #a8a8a8;
}
