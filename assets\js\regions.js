// JavaScript لصفحة المناطق

$(document).ready(function() {
    // تهيئة جدول البيانات
    initializeDataTable();

    // تهيئة التحقق من النماذج
    initializeFormValidation();

    // تهيئة تأثيرات البطاقات
    initializeCardEffects();
});

// تهيئة جدول البيانات
function initializeDataTable() {
    $('#regionsTable').DataTable({
        language: {
            url: 'https://cdn.datatables.net/plug-ins/1.13.6/i18n/ar.json'
        },
        responsive: true,
        pageLength: 25,
        lengthMenu: [[10, 25, 50, 100, -1], [10, 25, 50, 100, "الكل"]],
        order: [[0, 'asc']], // ترتيب حسب اسم المنطقة
        columnDefs: [
            { orderable: false, targets: [6] }, // عدم ترتيب الإجراءات
            { searchable: false, targets: [6] }, // عدم البحث في الإجراءات
            { className: "text-center", targets: "_all" }
        ]
    });
}

// تهيئة التحقق من النماذج
function initializeFormValidation() {
    const forms = document.querySelectorAll('.needs-validation');

    forms.forEach(function(form) {
        form.addEventListener('submit', function(event) {
            if (!form.checkValidity()) {
                event.preventDefault();
                event.stopPropagation();
            }

            form.classList.add('was-validated');
        });
    });
}

// تهيئة تأثيرات البطاقات
function initializeCardEffects() {
    const regionCards = document.querySelectorAll('.region-card');

    regionCards.forEach(card => {
        card.addEventListener('click', function(e) {
            if (!e.target.closest('.btn-group')) {
                const regionId = this.dataset.regionId;
                viewRegionSupporters(regionId);
            }
        });
    });
}

// عرض مؤيدي المنطقة
function viewRegionSupporters(regionId) {
    window.location.href = `../supporters/supporters.php?region_id=${regionId}`;
}

// تعديل المنطقة
function editRegion(id) {
    showLoading(true);

    fetch(`get_region.php?id=${id}`)
        .then(response => response.json())
        .then(data => {
            showLoading(false);
            if (data.success) {
                fillEditForm(data.region);
                const modal = new bootstrap.Modal(document.getElementById('editRegionModal'));
                modal.show();
            } else {
                showToast('حدث خطأ في جلب بيانات المنطقة', 'error');
            }
        })
        .catch(error => {
            showLoading(false);
            console.error('خطأ:', error);
            showToast('حدث خطأ في الاتصال', 'error');
        });
}

// ملء نموذج التعديل
function fillEditForm(region) {
    document.getElementById('editId').value = region.id;
    document.getElementById('editName').value = region.name;
    document.getElementById('editDescription').value = region.description || '';
}

// حذف المنطقة
function deleteRegion(id) {
    if (confirm('هل أنت متأكد من حذف هذه المنطقة؟ لا يمكن التراجع عن هذا الإجراء.')) {
        showLoading(true);

        const formData = new FormData();
        formData.append('action', 'delete');
        formData.append('id', id);

        fetch('regions.php', {
            method: 'POST',
            body: formData
        })
        .then(response => response.text())
        .then(data => {
            showLoading(false);
            location.reload();
        })
        .catch(error => {
            showLoading(false);
            console.error('خطأ:', error);
            showToast('حدث خطأ أثناء الحذف', 'error');
        });
    }
}

// تصدير المناطق
function exportRegions() {
    window.location.href = 'export.php?type=excel';
}

function exportRegionsPDF() {
    window.location.href = 'export.php?type=pdf';
}

// إظهار مؤشر التحميل
function showLoading(show) {
    const loader = document.getElementById('loadingOverlay') || createLoadingOverlay();
    loader.style.display = show ? 'flex' : 'none';
}

// إنشاء مؤشر التحميل
function createLoadingOverlay() {
    const loaderHTML = `
        <div id="loadingOverlay" class="loading-overlay">
            <div class="loading-spinner"></div>
        </div>
    `;
    document.body.insertAdjacentHTML('beforeend', loaderHTML);
    return document.getElementById('loadingOverlay');
}

// دالة عامة لإظهار الرسائل
function showToast(message, type = 'info') {
    const toastContainer = getOrCreateToastContainer();
    const toast = document.createElement('div');
    toast.className = `toast align-items-center text-white bg-${getBootstrapColor(type)} border-0`;
    toast.setAttribute('role', 'alert');
    toast.innerHTML = `
        <div class="d-flex">
            <div class="toast-body">
                <i class="fas fa-${getIcon(type)} me-2"></i>
                ${message}
            </div>
            <button type="button" class="btn-close btn-close-white me-2 m-auto" data-bs-dismiss="toast"></button>
        </div>
    `;

    toastContainer.appendChild(toast);
    const bsToast = new bootstrap.Toast(toast);
    bsToast.show();

    toast.addEventListener('hidden.bs.toast', function() {
        toast.remove();
    });
}

// دالة للحصول على حاوية التوست
function getOrCreateToastContainer() {
    let container = document.querySelector('.toast-container');
    if (!container) {
        container = document.createElement('div');
        container.className = 'toast-container position-fixed top-0 end-0 p-3';
        container.style.zIndex = '9999';
        document.body.appendChild(container);
    }
    return container;
}

// دالة للحصول على لون Bootstrap
function getBootstrapColor(type) {
    const colors = {
        'success': 'success',
        'error': 'danger',
        'warning': 'warning',
        'info': 'info'
    };
    return colors[type] || 'info';
}

// دالة للحصول على الأيقونة
function getIcon(type) {
    const icons = {
        'success': 'check-circle',
        'error': 'exclamation-triangle',
        'warning': 'exclamation-circle',
        'info': 'info-circle'
    };
    return icons[type] || 'info-circle';
}

// تعديل المنطقة
function editRegion(id) {
    showLoading(true);

    fetch(`get_region.php?id=${id}`)
        .then(response => response.json())
        .then(data => {
            showLoading(false);
            if (data.success) {
                fillEditForm(data.region);
                const modal = new bootstrap.Modal(document.getElementById('editRegionModal'));
                modal.show();
            } else {
                showToast('حدث خطأ في جلب بيانات المنطقة', 'error');
            }
        })
        .catch(error => {
            showLoading(false);
            console.error('خطأ:', error);
            showToast('حدث خطأ في الاتصال', 'error');
        });
}

// ملء نموذج التعديل
function fillEditForm(region) {
    document.getElementById('editId').value = region.id;
    document.getElementById('editName').value = region.name;
    document.getElementById('editDescription').value = region.description || '';

    // إضافة معرف المنطقة للتحقق من التكرار
    document.getElementById('editName').dataset.regionId = region.id;
}

// حذف المنطقة
function deleteRegion(id) {
    // جلب معلومات المنطقة أولاً
    fetch(`get_region.php?id=${id}`)
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                const region = data.region;
                let confirmMessage = `هل أنت متأكد من حذف منطقة "${region.name}"؟`;

                if (region.supporters_count > 0) {
                    confirmMessage += `\n\nتحتوي هذه المنطقة على ${region.supporters_count} مؤيد. لا يمكن حذفها.`;
                    showToast(`لا يمكن حذف المنطقة لأنها تحتوي على ${region.supporters_count} مؤيد`, 'error');
                    return;
                }

                confirmMessage += '\n\nلا يمكن التراجع عن هذا الإجراء.';

                if (confirm(confirmMessage)) {
                    performDelete(id);
                }
            }
        })
        .catch(error => {
            console.error('خطأ:', error);
            showToast('حدث خطأ في جلب بيانات المنطقة', 'error');
        });
}

// تنفيذ الحذف
function performDelete(id) {
    showLoading(true);

    const formData = new FormData();
    formData.append('action', 'delete');
    formData.append('id', id);

    fetch('regions.php', {
        method: 'POST',
        body: formData
    })
    .then(response => response.text())
    .then(data => {
        showLoading(false);
        location.reload(); // إعادة تحميل الصفحة
    })
    .catch(error => {
        showLoading(false);
        console.error('خطأ:', error);
        showToast('حدث خطأ أثناء الحذف', 'error');
    });
}

// تصدير المناطق إلى Excel
function exportRegions() {
    const table = $('#regionsTable').DataTable();
    table.button('.buttons-excel').trigger();
}

// تصدير المناطق إلى PDF
function exportRegionsPDF() {
    const table = $('#regionsTable').DataTable();
    table.button('.buttons-pdf').trigger();
}

// إحصائيات متقدمة للمناطق
function showRegionStats() {
    showLoading(true);

    fetch('get_region_stats.php')
        .then(response => response.json())
        .then(data => {
            showLoading(false);
            if (data.success) {
                displayRegionStatsModal(data.stats);
            } else {
                showToast('حدث خطأ في جلب الإحصائيات', 'error');
            }
        })
        .catch(error => {
            showLoading(false);
            console.error('خطأ:', error);
            showToast('حدث خطأ في الاتصال', 'error');
        });
}

// عرض إحصائيات المناطق في نافذة منبثقة
function displayRegionStatsModal(stats) {
    const modal = new bootstrap.Modal(document.getElementById('regionStatsModal') || createRegionStatsModal());

    // ملء البيانات
    document.getElementById('statsContent').innerHTML = generateStatsHTML(stats);

    modal.show();
}

// إنشاء نافذة الإحصائيات
function createRegionStatsModal() {
    const modalHTML = `
        <div class="modal fade" id="regionStatsModal" tabindex="-1">
            <div class="modal-dialog modal-lg">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title">إحصائيات المناطق المتقدمة</h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                    </div>
                    <div class="modal-body">
                        <div id="statsContent"></div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إغلاق</button>
                        <button type="button" class="btn btn-primary" onclick="exportStats()">تصدير الإحصائيات</button>
                    </div>
                </div>
            </div>
        </div>
    `;
    document.body.insertAdjacentHTML('beforeend', modalHTML);
    return document.getElementById('regionStatsModal');
}

// إنشاء HTML للإحصائيات
function generateStatsHTML(stats) {
    return `
        <div class="row">
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <h6>أكثر المناطق مؤيدين</h6>
                    </div>
                    <div class="card-body">
                        <canvas id="topRegionsChart"></canvas>
                    </div>
                </div>
            </div>
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <h6>توزيع الجنس حسب المناطق</h6>
                    </div>
                    <div class="card-body">
                        <canvas id="genderDistributionChart"></canvas>
                    </div>
                </div>
            </div>
        </div>
        <div class="row mt-3">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h6>تفاصيل المناطق</h6>
                    </div>
                    <div class="card-body">
                        <div class="table-responsive">
                            <table class="table table-sm">
                                <thead>
                                    <tr>
                                        <th>المنطقة</th>
                                        <th>المؤيدين</th>
                                        <th>الذكور</th>
                                        <th>الإناث</th>
                                        <th>النسبة</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    ${stats.regions.map(region => `
                                        <tr>
                                            <td>${region.name}</td>
                                            <td>${region.supporters_count}</td>
                                            <td>${region.male_count}</td>
                                            <td>${region.female_count}</td>
                                            <td>${((region.supporters_count / stats.total_supporters) * 100).toFixed(1)}%</td>
                                        </tr>
                                    `).join('')}
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    `;
}

// تحديث الإحصائيات في الوقت الفعلي
function updateRegionStats() {
    const statsCards = document.querySelectorAll('.card[data-stat]');

    fetch('get_quick_stats.php')
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                statsCards.forEach(card => {
                    const statType = card.dataset.stat;
                    const valueElement = card.querySelector('h4');
                    if (valueElement && data.stats[statType]) {
                        animateNumber(valueElement, parseInt(valueElement.textContent), data.stats[statType]);
                    }
                });
            }
        })
        .catch(error => {
            console.error('خطأ في تحديث الإحصائيات:', error);
        });
}

// تحريك الأرقام
function animateNumber(element, start, end) {
    const duration = 1000;
    const startTime = performance.now();

    function update(currentTime) {
        const elapsed = currentTime - startTime;
        const progress = Math.min(elapsed / duration, 1);

        const current = Math.floor(start + (end - start) * progress);
        element.textContent = current.toLocaleString('ar-EG');

        if (progress < 1) {
            requestAnimationFrame(update);
        }
    }

    requestAnimationFrame(update);
}

// إظهار مؤشر التحميل
function showLoading(show) {
    const loader = document.getElementById('loadingOverlay') || createLoadingOverlay();
    loader.style.display = show ? 'flex' : 'none';
}

// إنشاء مؤشر التحميل
function createLoadingOverlay() {
    const loaderHTML = `
        <div id="loadingOverlay" class="loading-overlay">
            <div class="loading-spinner"></div>
        </div>
    `;
    document.body.insertAdjacentHTML('beforeend', loaderHTML);
    return document.getElementById('loadingOverlay');
}

// دالة عامة لإظهار الرسائل
function showToast(message, type = 'info') {
    const toastContainer = getOrCreateToastContainer();
    const toast = document.createElement('div');
    toast.className = `toast align-items-center text-white bg-${getBootstrapColor(type)} border-0`;
    toast.setAttribute('role', 'alert');
    toast.innerHTML = `
        <div class="d-flex">
            <div class="toast-body">
                <i class="fas fa-${getIcon(type)} me-2"></i>
                ${message}
            </div>
            <button type="button" class="btn-close btn-close-white me-2 m-auto" data-bs-dismiss="toast"></button>
        </div>
    `;

    toastContainer.appendChild(toast);
    const bsToast = new bootstrap.Toast(toast);
    bsToast.show();

    toast.addEventListener('hidden.bs.toast', function() {
        toast.remove();
    });
}

// دالة للحصول على حاوية التوست
function getOrCreateToastContainer() {
    let container = document.querySelector('.toast-container');
    if (!container) {
        container = document.createElement('div');
        container.className = 'toast-container position-fixed top-0 end-0 p-3';
        container.style.zIndex = '9999';
        document.body.appendChild(container);
    }
    return container;
}

// دالة للحصول على لون Bootstrap
function getBootstrapColor(type) {
    const colors = {
        'success': 'success',
        'error': 'danger',
        'warning': 'warning',
        'info': 'info'
    };
    return colors[type] || 'info';
}

// دالة للحصول على الأيقونة
function getIcon(type) {
    const icons = {
        'success': 'check-circle',
        'error': 'exclamation-triangle',
        'warning': 'exclamation-circle',
        'info': 'info-circle'
    };
    return icons[type] || 'info-circle';
}

// تحديث الإحصائيات كل 30 ثانية
setInterval(updateRegionStats, 30000);
