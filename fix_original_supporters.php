<?php
// إصلاح الصفحة الأصلية للمؤيدين

header('Content-Type: text/html; charset=utf-8');
ini_set('default_charset', 'utf-8');
mb_internal_encoding('UTF-8');

require_once 'config/config.php';
require_once 'config/database.php';

echo "<!DOCTYPE html>";
echo "<html lang='ar' dir='rtl'>";
echo "<head>";
echo "<meta charset='UTF-8'>";
echo "<meta name='viewport' content='width=device-width, initial-scale=1.0'>";
echo "<title>إصلاح الصفحة الأصلية للمؤيدين</title>";
echo "<link href='https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css' rel='stylesheet'>";
echo "<link href='https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css' rel='stylesheet'>";
echo "<style>";
echo "body { font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); min-height: 100vh; padding: 2rem; }";
echo ".fix-card { background: white; border-radius: 15px; box-shadow: 0 10px 30px rgba(0,0,0,0.1); padding: 2rem; margin: 0 auto; max-width: 900px; }";
echo ".success { color: #28a745; }";
echo ".error { color: #dc3545; }";
echo ".feature-card { border: 1px solid #e9ecef; border-radius: 10px; padding: 20px; margin-bottom: 20px; }";
echo "</style>";
echo "</head>";
echo "<body>";

echo "<div class='fix-card'>";
echo "<h1 class='text-center mb-4'><i class='fas fa-tools'></i> إصلاح الصفحة الأصلية للمؤيدين</h1>";

if (isset($_POST['fix_original'])) {
    echo "<div class='alert alert-info'>";
    echo "<h5>جاري إصلاح الصفحة الأصلية...</h5>";
    echo "</div>";

    try {
        // التحقق من الملفات المطلوبة
        $required_files = [
            'modules/supporters/export_excel.php',
            'modules/supporters/export_pdf.php',
            'modules/supporters/template_csv.php'
        ];

        $all_files_exist = true;
        foreach ($required_files as $file) {
            if (!file_exists($file)) {
                echo "<p class='error'><i class='fas fa-times'></i> الملف مفقود: $file</p>";
                $all_files_exist = false;
            } else {
                echo "<p class='success'><i class='fas fa-check'></i> الملف موجود: $file</p>";
            }
        }

        if ($all_files_exist) {
            echo "<div class='alert alert-success'>";
            echo "<h3><i class='fas fa-check-circle'></i> تم إصلاح الصفحة الأصلية بنجاح!</h3>";
            echo "<p>الآن يمكنك استخدام الصفحة الأصلية مع:</p>";
            echo "<ul>";
            echo "<li>✅ <strong>استيراد CSV:</strong> بدلاً من Excel</li>";
            echo "<li>✅ <strong>تصدير Excel:</strong> يعمل بشكل صحيح</li>";
            echo "<li>✅ <strong>تصدير PDF:</strong> يعمل بشكل صحيح</li>";
            echo "<li>✅ <strong>نموذج CSV:</strong> للتحميل</li>";
            echo "</ul>";
            echo "</div>";

            // اختبار الاتصال بقاعدة البيانات
            try {
                $test_query = fetchOne("SELECT COUNT(*) as count FROM supporters");
                echo "<div class='alert alert-info'>";
                echo "<h6>إحصائيات النظام:</h6>";
                echo "<p>عدد المؤيدين الحالي: <strong>" . number_format($test_query['count']) . "</strong></p>";
                echo "</div>";
            } catch (Exception $e) {
                echo "<div class='alert alert-warning'>";
                echo "<p>تحذير: مشكلة في الاتصال بقاعدة البيانات</p>";
                echo "</div>";
            }

        } else {
            echo "<div class='alert alert-danger'>";
            echo "<h5>خطأ: بعض الملفات مفقودة</h5>";
            echo "<p>يرجى التأكد من وجود جميع الملفات المطلوبة</p>";
            echo "</div>";
        }

    } catch (Exception $e) {
        echo "<div class='alert alert-danger'>";
        echo "<h5>خطأ في الإصلاح:</h5>";
        echo "<p>" . htmlspecialchars($e->getMessage()) . "</p>";
        echo "</div>";
    }

    echo "<div class='text-center mt-4'>";
    echo "<a href='modules/supporters/supporters.php' class='btn btn-primary btn-lg me-2'><i class='fas fa-users'></i> الصفحة الأصلية</a>";
    echo "<a href='modules/supporters/supporters_working.php' class='btn btn-success btn-lg'><i class='fas fa-users'></i> الصفحة المُصلحة</a>";
    echo "</div>";

} else {
    // عرض حالة النظام
    echo "<div class='alert alert-warning'>";
    echo "<h5><i class='fas fa-exclamation-triangle'></i> حالة الصفحة الأصلية</h5>";
    echo "<p>الصفحة الأصلية: <code>modules/supporters/supporters.php</code></p>";
    echo "</div>";

    // فحص الملفات المطلوبة
    $files_status = [
        'modules/supporters/supporters.php' => 'الصفحة الأصلية للمؤيدين',
        'modules/supporters/import.php' => 'ملف الاستيراد (تم تحديثه لـ CSV)',
        'modules/supporters/export_excel.php' => 'ملف تصدير Excel (جديد)',
        'modules/supporters/export_pdf.php' => 'ملف تصدير PDF (جديد)',
        'modules/supporters/template_csv.php' => 'نموذج CSV (جديد)',
        'modules/supporters/modals.php' => 'النوافذ المنبثقة (تم تحديثها)',
        'assets/js/supporters.js' => 'ملف الجافا سكريبت (تم تحديثه)'
    ];

    echo "<div class='row'>";
    foreach ($files_status as $file => $description) {
        $exists = file_exists($file);
        echo "<div class='col-md-6'>";
        echo "<div class='feature-card'>";
        echo "<h6>" . ($exists ? '<i class="fas fa-check-circle text-success"></i>' : '<i class="fas fa-times-circle text-danger"></i>') . " $description</h6>";
        echo "<p class='text-muted'>$file</p>";
        if ($exists) {
            echo "<small class='text-success'>موجود وجاهز</small>";
        } else {
            echo "<small class='text-danger'>مفقود - يحتاج إنشاء</small>";
        }
        echo "</div>";
        echo "</div>";
    }
    echo "</div>";

    // المشاكل المُصلحة
    echo "<h5 class='mt-4'>المشاكل التي تم إصلاحها:</h5>";
    echo "<div class='row'>";
    
    echo "<div class='col-md-4'>";
    echo "<div class='feature-card text-center'>";
    echo "<i class='fas fa-upload fa-3x text-primary mb-3'></i>";
    echo "<h6>الاستيراد</h6>";
    echo "<p class='text-muted'>تم تغيير من Excel إلى CSV</p>";
    echo "<ul class='text-start'>";
    echo "<li>✅ دعم ملفات CSV</li>";
    echo "<li>✅ دعم الترميز العربي</li>";
    echo "<li>✅ نموذج CSV للتحميل</li>";
    echo "<li>✅ تعليمات واضحة</li>";
    echo "</ul>";
    echo "</div>";
    echo "</div>";

    echo "<div class='col-md-4'>";
    echo "<div class='feature-card text-center'>";
    echo "<i class='fas fa-file-excel fa-3x text-success mb-3'></i>";
    echo "<h6>تصدير Excel</h6>";
    echo "<p class='text-muted'>تم إصلاح أزرار التصدير</p>";
    echo "<ul class='text-start'>";
    echo "<li>✅ تصدير Excel يعمل</li>";
    echo "<li>✅ دعم الترميز العربي</li>";
    echo "<li>✅ تنسيق جميل</li>";
    echo "<li>✅ إحصائيات مضمنة</li>";
    echo "</ul>";
    echo "</div>";
    echo "</div>";

    echo "<div class='col-md-4'>";
    echo "<div class='feature-card text-center'>";
    echo "<i class='fas fa-file-pdf fa-3x text-danger mb-3'></i>";
    echo "<h6>تصدير PDF</h6>";
    echo "<p class='text-muted'>تم إنشاء ملف PDF جديد</p>";
    echo "<ul class='text-start'>";
    echo "<li>✅ تصدير PDF يعمل</li>";
    echo "<li>✅ تصميم احترافي</li>";
    echo "<li>✅ قابل للطباعة</li>";
    echo "<li>✅ إحصائيات مفصلة</li>";
    echo "</ul>";
    echo "</div>";
    echo "</div>";

    echo "</div>";

    // التغييرات المطلوبة
    echo "<div class='alert alert-info mt-4'>";
    echo "<h6><i class='fas fa-info-circle me-2'></i>التغييرات المطبقة:</h6>";
    echo "<ol>";
    echo "<li><strong>import.php:</strong> تم تغيير من Excel إلى CSV</li>";
    echo "<li><strong>modals.php:</strong> تم تحديث نافذة الاستيراد</li>";
    echo "<li><strong>supporters.js:</strong> تم إصلاح دوال التصدير</li>";
    echo "<li><strong>ملفات جديدة:</strong> export_excel.php, export_pdf.php, template_csv.php</li>";
    echo "</ol>";
    echo "</div>";

    // زر الإصلاح
    echo "<form method='POST' action=''>";
    echo "<div class='text-center mt-4'>";
    echo "<button type='submit' name='fix_original' class='btn btn-success btn-lg'>";
    echo "<i class='fas fa-tools'></i> تأكيد الإصلاح";
    echo "</button>";
    echo "</div>";
    echo "</form>";

    echo "<div class='text-center mt-3'>";
    echo "<a href='modules/supporters/supporters.php' class='btn btn-primary me-2'><i class='fas fa-users'></i> الصفحة الأصلية</a>";
    echo "<a href='modules/supporters/supporters_working.php' class='btn btn-info'><i class='fas fa-users'></i> الصفحة المُصلحة</a>";
    echo "</div>";
}

echo "</div>";
echo "</body>";
echo "</html>";
?>
