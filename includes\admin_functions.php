<?php
// دوال الإداريين
require_once __DIR__ . '/../config/database.php';
require_once __DIR__ . '/notification_functions.php';

/**
 * إضافة إداري جديد
 */
function addAdmin($data, $created_by = null) {
    $password_hash = password_hash($data['password'], PASSWORD_DEFAULT);
    
    $sql = "INSERT INTO admins (
        username, password, full_name, phone, email, region_id,
        role, hire_date, salary, notes, created_by
    ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)";
    
    $params = [
        $data['username'],
        $password_hash,
        $data['full_name'],
        $data['phone'],
        $data['email'] ?? null,
        $data['region_id'] ?? null,
        $data['role'] ?? 'admin',
        $data['hire_date'] ?? date('Y-m-d'),
        $data['salary'] ?? null,
        $data['notes'] ?? null,
        $created_by
    ];
    
    try {
        $result = executeQuery($sql, $params);
        $admin_id = $GLOBALS['pdo']->lastInsertId();
        
        // إضافة الصلاحيات الافتراضية
        $default_permissions = [
            'add_supporters', 'edit_supporters', 'view_supporters',
            'send_messages', 'submit_requests', 'create_reports', 'view_statistics'
        ];
        
        foreach ($default_permissions as $permission) {
            addAdminPermission($admin_id, $permission, $created_by);
        }
        
        // تسجيل النشاط
        logActivity([
            'user_type' => 'management',
            'user_id' => $created_by,
            'action_type' => 'create',
            'target_type' => 'admin',
            'target_id' => $admin_id,
            'target_name' => $data['full_name'],
            'description' => 'تم إضافة إداري جديد: ' . $data['full_name']
        ]);
        
        // إنشاء إشعار
        notifyNewAdmin($admin_id);
        
        return $admin_id;
    } catch (Exception $e) {
        error_log("خطأ في إضافة الإداري: " . $e->getMessage());
        return false;
    }
}

/**
 * تحديث بيانات إداري
 */
function updateAdmin($admin_id, $data, $updated_by = null) {
    $sql = "UPDATE admins SET 
        full_name = ?, phone = ?, email = ?, region_id = ?,
        role = ?, salary = ?, notes = ?, updated_at = NOW()
        WHERE id = ?";
    
    $params = [
        $data['full_name'],
        $data['phone'],
        $data['email'],
        $data['region_id'],
        $data['role'],
        $data['salary'],
        $data['notes'],
        $admin_id
    ];
    
    // تحديث كلمة المرور إذا تم توفيرها
    if (!empty($data['password'])) {
        $sql = "UPDATE admins SET 
            full_name = ?, phone = ?, email = ?, region_id = ?,
            role = ?, salary = ?, notes = ?, password = ?, updated_at = NOW()
            WHERE id = ?";
        
        $password_hash = password_hash($data['password'], PASSWORD_DEFAULT);
        array_splice($params, -1, 0, $password_hash);
    }
    
    try {
        $result = executeQuery($sql, $params);
        
        // تسجيل النشاط
        logActivity([
            'user_type' => 'management',
            'user_id' => $updated_by,
            'action_type' => 'update',
            'target_type' => 'admin',
            'target_id' => $admin_id,
            'target_name' => $data['full_name'],
            'description' => 'تم تحديث بيانات الإداري: ' . $data['full_name']
        ]);
        
        return true;
    } catch (Exception $e) {
        error_log("خطأ في تحديث الإداري: " . $e->getMessage());
        return false;
    }
}

/**
 * تغيير حالة الإداري
 */
function changeAdminStatus($admin_id, $status, $changed_by = null) {
    $admin = fetchOne("SELECT full_name FROM admins WHERE id = ?", [$admin_id]);
    
    if (!$admin) {
        return false;
    }
    
    $result = executeQuery("UPDATE admins SET status = ?, updated_at = NOW() WHERE id = ?", [$status, $admin_id]);
    
    if ($result) {
        // تسجيل النشاط
        logActivity([
            'user_type' => 'management',
            'user_id' => $changed_by,
            'action_type' => 'update',
            'target_type' => 'admin',
            'target_id' => $admin_id,
            'target_name' => $admin['full_name'],
            'description' => "تم تغيير حالة الإداري إلى: $status"
        ]);
        
        // إنشاء إشعار للإداري
        $status_text = [
            'active' => 'تم تفعيل حسابك',
            'inactive' => 'تم إلغاء تفعيل حسابك',
            'suspended' => 'تم تعليق حسابك'
        ];
        
        createNotification([
            'user_type' => 'admin',
            'user_id' => $admin_id,
            'title' => 'تحديث حالة الحساب',
            'message' => $status_text[$status] ?? 'تم تحديث حالة حسابك',
            'type' => $status === 'active' ? 'success' : 'warning',
            'category' => 'admin'
        ]);
    }
    
    return $result;
}

/**
 * حذف إداري
 */
function deleteAdmin($admin_id, $deleted_by = null) {
    $admin = fetchOne("SELECT full_name FROM admins WHERE id = ?", [$admin_id]);
    
    if (!$admin) {
        return false;
    }
    
    try {
        $result = executeQuery("DELETE FROM admins WHERE id = ?", [$admin_id]);
        
        if ($result) {
            // تسجيل النشاط
            logActivity([
                'user_type' => 'management',
                'user_id' => $deleted_by,
                'action_type' => 'delete',
                'target_type' => 'admin',
                'target_id' => $admin_id,
                'target_name' => $admin['full_name'],
                'description' => 'تم حذف الإداري: ' . $admin['full_name']
            ]);
        }
        
        return $result;
    } catch (Exception $e) {
        error_log("خطأ في حذف الإداري: " . $e->getMessage());
        return false;
    }
}

/**
 * البحث في الإداريين
 */
function searchAdmins($search_term = '', $filters = [], $limit = 50, $offset = 0) {
    $sql = "SELECT a.*, r.name as region_name, u.full_name as created_by_name,
            (SELECT COUNT(*) FROM supporters WHERE added_by = a.id) as supporters_count,
            (SELECT COUNT(*) FROM supporter_requests WHERE admin_id = a.id) as requests_count
            FROM admins a 
            LEFT JOIN regions r ON a.region_id = r.id 
            LEFT JOIN users u ON a.created_by = u.id 
            WHERE 1=1";
    $params = [];
    
    // البحث النصي
    if (!empty($search_term)) {
        $sql .= " AND (a.full_name LIKE ? OR a.username LIKE ? OR a.phone LIKE ? OR a.email LIKE ?)";
        $params[] = "%$search_term%";
        $params[] = "%$search_term%";
        $params[] = "%$search_term%";
        $params[] = "%$search_term%";
    }
    
    // فلاتر
    if (!empty($filters['status'])) {
        $sql .= " AND a.status = ?";
        $params[] = $filters['status'];
    }
    
    if (!empty($filters['role'])) {
        $sql .= " AND a.role = ?";
        $params[] = $filters['role'];
    }
    
    if (!empty($filters['region_id'])) {
        $sql .= " AND a.region_id = ?";
        $params[] = $filters['region_id'];
    }
    
    $sql .= " ORDER BY a.created_at DESC LIMIT ? OFFSET ?";
    $params[] = $limit;
    $params[] = $offset;
    
    return fetchAll($sql, $params);
}

/**
 * عدد الإداريين حسب الفلاتر
 */
function countAdmins($search_term = '', $filters = []) {
    $sql = "SELECT COUNT(*) as count FROM admins a WHERE 1=1";
    $params = [];
    
    // البحث النصي
    if (!empty($search_term)) {
        $sql .= " AND (a.full_name LIKE ? OR a.username LIKE ? OR a.phone LIKE ? OR a.email LIKE ?)";
        $params[] = "%$search_term%";
        $params[] = "%$search_term%";
        $params[] = "%$search_term%";
        $params[] = "%$search_term%";
    }
    
    // فلاتر
    if (!empty($filters['status'])) {
        $sql .= " AND a.status = ?";
        $params[] = $filters['status'];
    }
    
    if (!empty($filters['role'])) {
        $sql .= " AND a.role = ?";
        $params[] = $filters['role'];
    }
    
    if (!empty($filters['region_id'])) {
        $sql .= " AND a.region_id = ?";
        $params[] = $filters['region_id'];
    }
    
    $result = fetchOne($sql, $params);
    return $result['count'] ?? 0;
}

/**
 * إضافة صلاحية للإداري
 */
function addAdminPermission($admin_id, $permission_name, $granted_by = null) {
    $sql = "INSERT IGNORE INTO admin_permissions (admin_id, permission_name, granted_by) VALUES (?, ?, ?)";
    return executeQuery($sql, [$admin_id, $permission_name, $granted_by]);
}

/**
 * إزالة صلاحية من الإداري
 */
function removeAdminPermission($admin_id, $permission_name) {
    $sql = "DELETE FROM admin_permissions WHERE admin_id = ? AND permission_name = ?";
    return executeQuery($sql, [$admin_id, $permission_name]);
}

/**
 * جلب صلاحيات الإداري
 */
function getAdminPermissions($admin_id) {
    return fetchAll("SELECT permission_name FROM admin_permissions WHERE admin_id = ?", [$admin_id]);
}

/**
 * التحقق من صلاحية الإداري
 */
function hasAdminPermission($admin_id, $permission_name) {
    $result = fetchOne("SELECT id FROM admin_permissions WHERE admin_id = ? AND permission_name = ?", [$admin_id, $permission_name]);
    return $result !== null;
}

/**
 * تسجيل دخول الإداري
 */
function adminLogin($username, $password) {
    $admin = fetchOne("SELECT * FROM admins WHERE username = ? AND status = 'active'", [$username]);
    
    if (!$admin) {
        return false;
    }
    
    // التحقق من كلمة المرور
    if (!password_verify($password, $admin['password'])) {
        // تسجيل محاولة دخول فاشلة
        executeQuery("UPDATE admins SET login_attempts = login_attempts + 1 WHERE id = ?", [$admin['id']]);
        return false;
    }
    
    // تحديث آخر دخول وإعادة تعيين محاولات الدخول
    executeQuery("UPDATE admins SET last_login = NOW(), login_attempts = 0 WHERE id = ?", [$admin['id']]);
    
    // إنشاء جلسة
    $session_token = bin2hex(random_bytes(32));
    $expires_at = date('Y-m-d H:i:s', strtotime('+24 hours'));
    
    executeQuery("INSERT INTO admin_sessions (admin_id, session_token, ip_address, user_agent, expires_at) VALUES (?, ?, ?, ?, ?)", [
        $admin['id'], $session_token, $_SERVER['REMOTE_ADDR'] ?? null,
        $_SERVER['HTTP_USER_AGENT'] ?? null, $expires_at
    ]);
    
    // تسجيل النشاط
    logActivity([
        'user_type' => 'admin',
        'user_id' => $admin['id'],
        'user_name' => $admin['full_name'],
        'action_type' => 'login',
        'target_type' => 'system',
        'description' => 'تسجيل دخول ناجح'
    ]);
    
    // تحديث الإحصائيات اليومية
    updateAdminDailyStats($admin['id'], 'login_count', 1);
    
    return [
        'admin' => $admin,
        'session_token' => $session_token
    ];
}

/**
 * تسجيل خروج الإداري
 */
function adminLogout($admin_id, $session_token = null) {
    // إنهاء الجلسة
    if ($session_token) {
        executeQuery("UPDATE admin_sessions SET is_active = 0, logout_at = NOW() WHERE admin_id = ? AND session_token = ?", [$admin_id, $session_token]);
    } else {
        executeQuery("UPDATE admin_sessions SET is_active = 0, logout_at = NOW() WHERE admin_id = ? AND is_active = 1", [$admin_id]);
    }
    
    // تسجيل النشاط
    logActivity([
        'user_type' => 'admin',
        'user_id' => $admin_id,
        'action_type' => 'logout',
        'target_type' => 'system',
        'description' => 'تسجيل خروج'
    ]);
    
    return true;
}

/**
 * التحقق من صحة الجلسة
 */
function validateAdminSession($session_token) {
    $session = fetchOne("
        SELECT s.*, a.* 
        FROM admin_sessions s 
        JOIN admins a ON s.admin_id = a.id 
        WHERE s.session_token = ? AND s.is_active = 1 AND s.expires_at > NOW() AND a.status = 'active'
    ", [$session_token]);
    
    if ($session) {
        // تحديث آخر نشاط
        executeQuery("UPDATE admin_sessions SET last_activity = NOW() WHERE session_token = ?", [$session_token]);
    }
    
    return $session;
}

/**
 * إحصائيات الإداري
 */
function getAdminStatistics($admin_id, $period = 'month') {
    $date_condition = match($period) {
        'today' => "DATE(created_at) = CURDATE()",
        'week' => "created_at >= DATE_SUB(NOW(), INTERVAL 1 WEEK)",
        'month' => "created_at >= DATE_SUB(NOW(), INTERVAL 1 MONTH)",
        'year' => "created_at >= DATE_SUB(NOW(), INTERVAL 1 YEAR)",
        default => "created_at >= DATE_SUB(NOW(), INTERVAL 1 MONTH)"
    };
    
    // إحصائيات المؤيدين
    $supporters_stats = fetchOne("
        SELECT COUNT(*) as count, 
               SUM(CASE WHEN gender = 'male' THEN 1 ELSE 0 END) as male_count,
               SUM(CASE WHEN gender = 'female' THEN 1 ELSE 0 END) as female_count
        FROM supporters 
        WHERE added_by = ? AND $date_condition
    ", [$admin_id]);
    
    // إحصائيات المطالب
    $requests_stats = fetchOne("
        SELECT COUNT(*) as count,
               SUM(CASE WHEN status = 'completed' THEN 1 ELSE 0 END) as completed_count,
               SUM(CASE WHEN status = 'pending' THEN 1 ELSE 0 END) as pending_count
        FROM supporter_requests 
        WHERE admin_id = ? AND $date_condition
    ", [$admin_id]);
    
    // إحصائيات الرسائل
    $messages_stats = fetchOne("
        SELECT COUNT(*) as sent_count
        FROM messages 
        WHERE sender_type = 'admin' AND sender_id = ? AND $date_condition
    ", [$admin_id]);
    
    // إحصائيات التقارير
    $reports_stats = fetchOne("
        SELECT COUNT(*) as count,
               SUM(CASE WHEN status = 'approved' THEN 1 ELSE 0 END) as approved_count
        FROM weekly_reports 
        WHERE admin_id = ? AND $date_condition
    ", [$admin_id]);
    
    return [
        'supporters' => $supporters_stats,
        'requests' => $requests_stats,
        'messages' => $messages_stats,
        'reports' => $reports_stats
    ];
}

/**
 * أفضل الإداريين أداءً
 */
function getTopPerformingAdmins($limit = 10, $period = 'month') {
    $date_condition = match($period) {
        'week' => "DATE_SUB(NOW(), INTERVAL 1 WEEK)",
        'month' => "DATE_SUB(NOW(), INTERVAL 1 MONTH)",
        'quarter' => "DATE_SUB(NOW(), INTERVAL 3 MONTH)",
        'year' => "DATE_SUB(NOW(), INTERVAL 1 YEAR)",
        default => "DATE_SUB(NOW(), INTERVAL 1 MONTH)"
    };
    
    return fetchAll("
        SELECT a.id, a.full_name, r.name as region_name,
               COUNT(DISTINCT s.id) as supporters_count,
               COUNT(DISTINCT sr.id) as requests_count,
               COUNT(DISTINCT wr.id) as reports_count,
               (COUNT(DISTINCT s.id) * 3 + COUNT(DISTINCT sr.id) * 2 + COUNT(DISTINCT wr.id) * 1) as performance_score
        FROM admins a
        LEFT JOIN regions r ON a.region_id = r.id
        LEFT JOIN supporters s ON a.id = s.added_by AND s.created_at >= ?
        LEFT JOIN supporter_requests sr ON a.id = sr.admin_id AND sr.submitted_at >= ?
        LEFT JOIN weekly_reports wr ON a.id = wr.admin_id AND wr.created_at >= ?
        WHERE a.status = 'active'
        GROUP BY a.id, a.full_name, r.name
        ORDER BY performance_score DESC
        LIMIT ?
    ", [$date_condition, $date_condition, $date_condition, $limit]);
}

/**
 * الإداريين النشطين
 */
function getActiveAdmins() {
    return fetchAll("
        SELECT a.*, r.name as region_name,
               s.last_activity,
               (SELECT COUNT(*) FROM supporters WHERE added_by = a.id) as supporters_count
        FROM admins a
        LEFT JOIN regions r ON a.region_id = r.id
        LEFT JOIN admin_sessions s ON a.id = s.admin_id AND s.is_active = 1
        WHERE a.status = 'active'
        ORDER BY s.last_activity DESC
    ");
}
?>
