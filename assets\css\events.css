/* تصميم صفحة الفعاليات */

/* جدول الفعاليات */
#eventsTable {
    font-size: 0.9rem;
}

#eventsTable th {
    background-color: #343a40;
    color: white;
    font-weight: 600;
    border: none;
    padding: 12px 8px;
    text-align: center;
    vertical-align: middle;
}

#eventsTable td {
    padding: 10px 8px;
    vertical-align: middle;
    text-align: center;
    border-bottom: 1px solid #dee2e6;
}

#eventsTable tbody tr {
    transition: all 0.3s ease;
}

#eventsTable tbody tr:hover {
    background-color: #f8f9fa;
    transform: scale(1.01);
}

/* بطاقات الإحصائيات */
.card {
    transition: all 0.3s ease;
    border: none;
    border-radius: 10px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
}

.card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 15px rgba(0,0,0,0.15);
}

/* النوافذ المنبثقة */
.modal-content {
    border-radius: 15px;
    border: none;
    box-shadow: 0 10px 30px rgba(0,0,0,0.3);
}

.modal-header {
    background: linear-gradient(135deg, #007bff, #0056b3);
    color: white;
    border-radius: 15px 15px 0 0;
    border: none;
}

.modal-footer {
    border: none;
    border-radius: 0 0 15px 15px;
}

/* نموذج إضافة/تعديل الفعالية */
.event-form .form-label {
    font-weight: 600;
    color: #495057;
    margin-bottom: 0.5rem;
}

.event-form .form-control,
.event-form .form-select {
    margin-bottom: 1rem;
    border-radius: 8px;
    border: 2px solid #e9ecef;
    transition: all 0.3s ease;
}

.event-form .form-control:focus,
.event-form .form-select:focus {
    border-color: #007bff;
    box-shadow: 0 0 0 0.2rem rgba(0,123,255,0.25);
    transform: translateY(-1px);
}

.event-form .required {
    color: #dc3545;
}

/* معرض الصور */
.photo-gallery {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));
    gap: 10px;
    margin-top: 1rem;
}

.photo-item {
    position: relative;
    border-radius: 8px;
    overflow: hidden;
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.photo-item img {
    width: 100%;
    height: 150px;
    object-fit: cover;
    transition: transform 0.3s ease;
}

.photo-item:hover img {
    transform: scale(1.05);
}

.photo-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0,0,0,0.7);
    display: flex;
    align-items: center;
    justify-content: center;
    opacity: 0;
    transition: opacity 0.3s ease;
}

.photo-item:hover .photo-overlay {
    opacity: 1;
}

.photo-overlay .btn {
    margin: 0 5px;
}

/* رفع الصور */
.photo-upload-area {
    border: 2px dashed #dee2e6;
    border-radius: 8px;
    padding: 2rem;
    text-align: center;
    transition: all 0.3s ease;
    cursor: pointer;
}

.photo-upload-area:hover {
    border-color: #007bff;
    background-color: #f8f9fa;
}

.photo-upload-area.dragover {
    border-color: #007bff;
    background-color: #e3f2fd;
}

/* شارات الحالة */
.badge {
    font-size: 0.75rem;
    padding: 0.35em 0.65em;
}

.badge.bg-warning {
    background-color: #ffc107 !important;
    color: #000;
}

.badge.bg-info {
    background-color: #17a2b8 !important;
}

.badge.bg-success {
    background-color: #28a745 !important;
}

.badge.bg-danger {
    background-color: #dc3545 !important;
}

/* تأثيرات التحميل */
.loading {
    position: relative;
    overflow: hidden;
}

.loading::after {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.4), transparent);
    animation: loading 1.5s infinite;
}

@keyframes loading {
    0% {
        left: -100%;
    }
    100% {
        left: 100%;
    }
}

/* تحسينات للجوال */
@media (max-width: 768px) {
    #eventsTable {
        font-size: 0.8rem;
    }
    
    #eventsTable th,
    #eventsTable td {
        padding: 6px 4px;
    }
    
    .btn-group .btn {
        padding: 0.2rem 0.4rem;
        font-size: 0.75rem;
    }
    
    .card-body {
        padding: 1rem;
    }
    
    .photo-gallery {
        grid-template-columns: repeat(auto-fill, minmax(100px, 1fr));
    }
    
    .photo-item img {
        height: 100px;
    }
}

/* تحسينات إضافية */
.table-responsive {
    border-radius: 10px;
    overflow: hidden;
}

.dataTables_wrapper .dataTables_length,
.dataTables_wrapper .dataTables_filter,
.dataTables_wrapper .dataTables_info,
.dataTables_wrapper .dataTables_paginate {
    margin-bottom: 1rem;
}

.dataTables_wrapper .dataTables_filter input {
    border-radius: 20px;
    border: 2px solid #e9ecef;
    padding: 8px 15px;
    margin-left: 10px;
}

.dataTables_wrapper .dataTables_length select {
    border-radius: 8px;
    border: 2px solid #e9ecef;
    padding: 5px 10px;
    margin: 0 10px;
}

/* أزرار التصدير */
.btn-toolbar .btn-group .btn {
    margin-left: 5px;
    border-radius: 8px;
    font-weight: 600;
    transition: all 0.3s ease;
}

.btn-toolbar .btn-group .btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0,0,0,0.2);
}

/* مؤشر التحميل */
.loading-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(255,255,255,0.8);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 9999;
}

.loading-spinner {
    width: 50px;
    height: 50px;
    border: 5px solid #f3f3f3;
    border-top: 5px solid #007bff;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* تحسين الألوان */
.text-primary {
    color: #007bff !important;
}

.text-success {
    color: #28a745 !important;
}

.text-warning {
    color: #ffc107 !important;
}

.text-danger {
    color: #dc3545 !important;
}

.text-info {
    color: #17a2b8 !important;
}

/* تحسين الحدود */
.border-primary {
    border-color: #007bff !important;
}

.border-success {
    border-color: #28a745 !important;
}

.border-warning {
    border-color: #ffc107 !important;
}

.border-danger {
    border-color: #dc3545 !important;
}

/* تحسين الظلال */
.shadow-sm {
    box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075) !important;
}

.shadow {
    box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15) !important;
}

.shadow-lg {
    box-shadow: 0 1rem 3rem rgba(0, 0, 0, 0.175) !important;
}

/* تحسين الانتقالات */
.fade-in {
    animation: fadeIn 0.5s ease-in;
}

@keyframes fadeIn {
    from { opacity: 0; transform: translateY(20px); }
    to { opacity: 1; transform: translateY(0); }
}

.slide-in {
    animation: slideIn 0.5s ease-out;
}

@keyframes slideIn {
    from { transform: translateX(-100%); }
    to { transform: translateX(0); }
}

/* تحسين التمرير */
.smooth-scroll {
    scroll-behavior: smooth;
}

/* تحسين التركيز */
.form-control:focus,
.form-select:focus,
.btn:focus {
    outline: none;
    box-shadow: 0 0 0 0.2rem rgba(0,123,255,0.25);
}

/* تحسين النصوص */
.text-truncate {
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

.text-wrap {
    word-wrap: break-word;
    word-break: break-word;
}

/* تأثيرات خاصة للأزرار */
.btn-group .btn {
    position: relative;
    overflow: hidden;
}

.btn-group .btn::before {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 0;
    height: 0;
    border-radius: 50%;
    background: rgba(255,255,255,0.3);
    transform: translate(-50%, -50%);
    transition: width 0.6s, height 0.6s;
}

.btn-group .btn:active::before {
    width: 300px;
    height: 300px;
}

/* تحسين عرض التاريخ والوقت */
.datetime-display {
    display: flex;
    flex-direction: column;
    align-items: center;
}

.datetime-display .date {
    font-weight: 600;
    color: #495057;
}

.datetime-display .time {
    font-size: 0.875rem;
    color: #6c757d;
}

/* تحسين عرض المكان */
.location-display {
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.location-display i {
    color: #6c757d;
}

/* تحسين الروابط */
a {
    text-decoration: none;
    transition: all 0.3s ease;
}

a:hover {
    text-decoration: underline;
}

/* تحسين الأيقونات */
.fas, .far, .fab {
    transition: all 0.3s ease;
}

.btn:hover .fas,
.btn:hover .far,
.btn:hover .fab {
    transform: scale(1.1);
}
