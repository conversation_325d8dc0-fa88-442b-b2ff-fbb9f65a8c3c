<?php
// صفحة الإحصائيات للإداريين
session_start();
header('Content-Type: text/html; charset=utf-8');
ini_set('default_charset', 'utf-8');
mb_internal_encoding('UTF-8');

require_once '../../config/config.php';
require_once '../../config/database.php';

// التحقق من تسجيل الدخول
if (!isset($_SESSION['admin_id'])) {
    redirect('login.php');
}

$admin_id = $_SESSION['admin_id'];
$admin_name = $_SESSION['admin_name'];

// حساب التواريخ
$today = date('Y-m-d');
$week_start = date('Y-m-d', strtotime('monday this week'));
$month_start = date('Y-m-01');
$year_start = date('Y-01-01');

// إحصائيات المؤيدين
$supporters_today = fetchOne("SELECT COUNT(*) as count FROM supporters WHERE added_by = ? AND DATE(created_at) = ?", [$admin_id, $today])['count'];
$supporters_week = fetchOne("SELECT COUNT(*) as count FROM supporters WHERE added_by = ? AND DATE(created_at) >= ?", [$admin_id, $week_start])['count'];
$supporters_month = fetchOne("SELECT COUNT(*) as count FROM supporters WHERE added_by = ? AND DATE(created_at) >= ?", [$admin_id, $month_start])['count'];
$supporters_year = fetchOne("SELECT COUNT(*) as count FROM supporters WHERE added_by = ? AND DATE(created_at) >= ?", [$admin_id, $year_start])['count'];
$supporters_total = fetchOne("SELECT COUNT(*) as count FROM supporters WHERE added_by = ?", [$admin_id])['count'];

// إحصائيات المؤيدين حسب الجنس
$male_supporters = fetchOne("SELECT COUNT(*) as count FROM supporters WHERE added_by = ? AND gender = 'male'", [$admin_id])['count'];
$female_supporters = fetchOne("SELECT COUNT(*) as count FROM supporters WHERE added_by = ? AND gender = 'female'", [$admin_id])['count'];

// إحصائيات المطالب
$requests_total = fetchOne("SELECT COUNT(*) as count FROM supporter_requests WHERE admin_id = ?", [$admin_id])['count'];
$requests_pending = fetchOne("SELECT COUNT(*) as count FROM supporter_requests WHERE admin_id = ? AND status = 'pending'", [$admin_id])['count'];
$requests_completed = fetchOne("SELECT COUNT(*) as count FROM supporter_requests WHERE admin_id = ? AND status = 'completed'", [$admin_id])['count'];

// إحصائيات الرسائل
$messages_total = fetchOne("SELECT COUNT(*) as count FROM messages WHERE sender_type = 'admin' AND sender_id = ?", [$admin_id])['count'];
$messages_received = fetchOne("SELECT COUNT(*) as count FROM messages WHERE receiver_type = 'admin' AND receiver_id = ?", [$admin_id])['count'];

// إحصائيات التقارير
$reports_total = fetchOne("SELECT COUNT(*) as count FROM weekly_reports WHERE admin_id = ?", [$admin_id])['count'];
$reports_submitted = fetchOne("SELECT COUNT(*) as count FROM weekly_reports WHERE admin_id = ? AND status = 'submitted'", [$admin_id])['count'];

// إحصائيات يومية للأسبوع الماضي
$daily_stats = fetchAll("
    SELECT DATE(created_at) as date, COUNT(*) as count 
    FROM supporters 
    WHERE added_by = ? AND DATE(created_at) >= DATE_SUB(CURDATE(), INTERVAL 7 DAY)
    GROUP BY DATE(created_at)
    ORDER BY date DESC
", [$admin_id]);

// إحصائيات شهرية للسنة الحالية
$monthly_stats = fetchAll("
    SELECT MONTH(created_at) as month, COUNT(*) as count 
    FROM supporters 
    WHERE added_by = ? AND YEAR(created_at) = YEAR(CURDATE())
    GROUP BY MONTH(created_at)
    ORDER BY month
", [$admin_id]);
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>الإحصائيات - لوحة تحكم الإداريين</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: #f8f9fa;
        }
        .main-content {
            margin-top: 80px;
        }
        .stat-card {
            border-radius: 15px;
            border: none;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
            transition: transform 0.3s;
        }
        .stat-card:hover {
            transform: translateY(-5px);
        }
        .stat-icon {
            font-size: 2.5rem;
            opacity: 0.8;
        }
        .chart-container {
            background: white;
            border-radius: 15px;
            padding: 20px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
    </style>
</head>
<body>
    <!-- شريط التنقل العلوي -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-dark fixed-top">
        <div class="container-fluid">
            <a class="navbar-brand" href="dashboard.php">
                <i class="fas fa-user-shield me-2"></i>
                لوحة تحكم الإداريين
            </a>
            
            <div class="navbar-nav ms-auto">
                <div class="nav-item dropdown">
                    <a class="nav-link dropdown-toggle" href="#" id="userDropdown" role="button" data-bs-toggle="dropdown">
                        <i class="fas fa-user-circle me-1"></i>
                        <?php echo htmlspecialchars($admin_name); ?>
                    </a>
                    <ul class="dropdown-menu dropdown-menu-end">
                        <li><a class="dropdown-item" href="dashboard.php"><i class="fas fa-tachometer-alt me-2"></i>لوحة التحكم</a></li>
                        <li><a class="dropdown-item" href="logout.php"><i class="fas fa-sign-out-alt me-2"></i>تسجيل الخروج</a></li>
                    </ul>
                </div>
            </div>
        </div>
    </nav>

    <!-- المحتوى الرئيسي -->
    <div class="container-fluid main-content">
        <div class="row">
            <div class="col-md-12">
                <div class="d-flex justify-content-between align-items-center mb-4">
                    <h2><i class="fas fa-chart-bar me-2"></i>الإحصائيات الشخصية</h2>
                    <a href="dashboard.php" class="btn btn-secondary">
                        <i class="fas fa-arrow-left me-2"></i>العودة
                    </a>
                </div>

                <!-- إحصائيات المؤيدين -->
                <h5 class="mb-3"><i class="fas fa-users me-2"></i>إحصائيات المؤيدين</h5>
                <div class="row mb-4">
                    <div class="col-md-2">
                        <div class="card stat-card bg-primary text-white">
                            <div class="card-body text-center">
                                <i class="fas fa-user-plus stat-icon"></i>
                                <h3><?php echo number_format($supporters_today); ?></h3>
                                <p class="mb-0">اليوم</p>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-2">
                        <div class="card stat-card bg-success text-white">
                            <div class="card-body text-center">
                                <i class="fas fa-calendar-week stat-icon"></i>
                                <h3><?php echo number_format($supporters_week); ?></h3>
                                <p class="mb-0">هذا الأسبوع</p>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-2">
                        <div class="card stat-card bg-info text-white">
                            <div class="card-body text-center">
                                <i class="fas fa-calendar-alt stat-icon"></i>
                                <h3><?php echo number_format($supporters_month); ?></h3>
                                <p class="mb-0">هذا الشهر</p>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-2">
                        <div class="card stat-card bg-warning text-white">
                            <div class="card-body text-center">
                                <i class="fas fa-calendar stat-icon"></i>
                                <h3><?php echo number_format($supporters_year); ?></h3>
                                <p class="mb-0">هذا العام</p>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-2">
                        <div class="card stat-card bg-dark text-white">
                            <div class="card-body text-center">
                                <i class="fas fa-users stat-icon"></i>
                                <h3><?php echo number_format($supporters_total); ?></h3>
                                <p class="mb-0">الإجمالي</p>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-2">
                        <div class="card stat-card bg-secondary text-white">
                            <div class="card-body text-center">
                                <i class="fas fa-percentage stat-icon"></i>
                                <h3><?php echo $supporters_total > 0 ? round(($male_supporters / $supporters_total) * 100, 1) : 0; ?>%</h3>
                                <p class="mb-0">نسبة الذكور</p>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- إحصائيات أخرى -->
                <div class="row mb-4">
                    <div class="col-md-3">
                        <div class="card stat-card bg-gradient" style="background: linear-gradient(45deg, #667eea, #764ba2);">
                            <div class="card-body text-white text-center">
                                <i class="fas fa-hand-holding-heart stat-icon"></i>
                                <h3><?php echo number_format($requests_total); ?></h3>
                                <p class="mb-0">إجمالي المطالب</p>
                                <small><?php echo $requests_pending; ?> معلق | <?php echo $requests_completed; ?> مكتمل</small>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="card stat-card bg-gradient" style="background: linear-gradient(45deg, #f093fb, #f5576c);">
                            <div class="card-body text-white text-center">
                                <i class="fas fa-envelope stat-icon"></i>
                                <h3><?php echo number_format($messages_total); ?></h3>
                                <p class="mb-0">رسائل مرسلة</p>
                                <small><?php echo $messages_received; ?> رسالة واردة</small>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="card stat-card bg-gradient" style="background: linear-gradient(45deg, #4facfe, #00f2fe);">
                            <div class="card-body text-white text-center">
                                <i class="fas fa-file-alt stat-icon"></i>
                                <h3><?php echo number_format($reports_total); ?></h3>
                                <p class="mb-0">التقارير الأسبوعية</p>
                                <small><?php echo $reports_submitted; ?> مقدم للإدارة</small>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="card stat-card bg-gradient" style="background: linear-gradient(45deg, #43e97b, #38f9d7);">
                            <div class="card-body text-white text-center">
                                <i class="fas fa-chart-line stat-icon"></i>
                                <h3><?php echo $supporters_week > 0 ? round($supporters_week / 7, 1) : 0; ?></h3>
                                <p class="mb-0">متوسط يومي</p>
                                <small>مؤيد/يوم هذا الأسبوع</small>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- الرسوم البيانية -->
                <div class="row">
                    <div class="col-md-6">
                        <div class="chart-container">
                            <h6><i class="fas fa-chart-line me-2"></i>المؤيدين خلال الأسبوع الماضي</h6>
                            <canvas id="dailyChart" width="400" height="200"></canvas>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="chart-container">
                            <h6><i class="fas fa-chart-bar me-2"></i>المؤيدين حسب الشهر (هذا العام)</h6>
                            <canvas id="monthlyChart" width="400" height="200"></canvas>
                        </div>
                    </div>
                </div>

                <!-- تفاصيل إضافية -->
                <div class="row">
                    <div class="col-md-6">
                        <div class="chart-container">
                            <h6><i class="fas fa-venus-mars me-2"></i>توزيع المؤيدين حسب الجنس</h6>
                            <div class="row text-center">
                                <div class="col-6">
                                    <div class="p-3 bg-primary text-white rounded">
                                        <h4><?php echo number_format($male_supporters); ?></h4>
                                        <p class="mb-0">ذكور</p>
                                        <small><?php echo $supporters_total > 0 ? round(($male_supporters / $supporters_total) * 100, 1) : 0; ?>%</small>
                                    </div>
                                </div>
                                <div class="col-6">
                                    <div class="p-3 bg-pink text-white rounded" style="background: #e91e63 !important;">
                                        <h4><?php echo number_format($female_supporters); ?></h4>
                                        <p class="mb-0">إناث</p>
                                        <small><?php echo $supporters_total > 0 ? round(($female_supporters / $supporters_total) * 100, 1) : 0; ?>%</small>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="chart-container">
                            <h6><i class="fas fa-trophy me-2"></i>إنجازاتك</h6>
                            <div class="list-group list-group-flush">
                                <div class="list-group-item d-flex justify-content-between align-items-center">
                                    إجمالي المؤيدين المضافين
                                    <span class="badge bg-primary rounded-pill"><?php echo number_format($supporters_total); ?></span>
                                </div>
                                <div class="list-group-item d-flex justify-content-between align-items-center">
                                    مطالب المؤيدين المقدمة
                                    <span class="badge bg-warning rounded-pill"><?php echo number_format($requests_total); ?></span>
                                </div>
                                <div class="list-group-item d-flex justify-content-between align-items-center">
                                    التقارير الأسبوعية
                                    <span class="badge bg-info rounded-pill"><?php echo number_format($reports_total); ?></span>
                                </div>
                                <div class="list-group-item d-flex justify-content-between align-items-center">
                                    الرسائل المرسلة
                                    <span class="badge bg-success rounded-pill"><?php echo number_format($messages_total); ?></span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <script>
        // رسم بياني للإحصائيات اليومية
        const dailyCtx = document.getElementById('dailyChart').getContext('2d');
        const dailyChart = new Chart(dailyCtx, {
            type: 'line',
            data: {
                labels: [<?php 
                    $labels = [];
                    for ($i = 6; $i >= 0; $i--) {
                        $labels[] = "'" . date('m-d', strtotime("-$i days")) . "'";
                    }
                    echo implode(',', $labels);
                ?>],
                datasets: [{
                    label: 'مؤيدين جدد',
                    data: [<?php 
                        $data = [];
                        for ($i = 6; $i >= 0; $i--) {
                            $date = date('Y-m-d', strtotime("-$i days"));
                            $count = 0;
                            foreach ($daily_stats as $stat) {
                                if ($stat['date'] == $date) {
                                    $count = $stat['count'];
                                    break;
                                }
                            }
                            $data[] = $count;
                        }
                        echo implode(',', $data);
                    ?>],
                    borderColor: 'rgb(75, 192, 192)',
                    backgroundColor: 'rgba(75, 192, 192, 0.2)',
                    tension: 0.1
                }]
            },
            options: {
                responsive: true,
                scales: {
                    y: {
                        beginAtZero: true
                    }
                }
            }
        });

        // رسم بياني للإحصائيات الشهرية
        const monthlyCtx = document.getElementById('monthlyChart').getContext('2d');
        const monthlyChart = new Chart(monthlyCtx, {
            type: 'bar',
            data: {
                labels: ['يناير', 'فبراير', 'مارس', 'أبريل', 'مايو', 'يونيو', 'يوليو', 'أغسطس', 'سبتمبر', 'أكتوبر', 'نوفمبر', 'ديسمبر'],
                datasets: [{
                    label: 'مؤيدين جدد',
                    data: [<?php 
                        $monthly_data = [];
                        for ($i = 1; $i <= 12; $i++) {
                            $count = 0;
                            foreach ($monthly_stats as $stat) {
                                if ($stat['month'] == $i) {
                                    $count = $stat['count'];
                                    break;
                                }
                            }
                            $monthly_data[] = $count;
                        }
                        echo implode(',', $monthly_data);
                    ?>],
                    backgroundColor: 'rgba(54, 162, 235, 0.5)',
                    borderColor: 'rgba(54, 162, 235, 1)',
                    borderWidth: 1
                }]
            },
            options: {
                responsive: true,
                scales: {
                    y: {
                        beginAtZero: true
                    }
                }
            }
        });
    </script>
</body>
</html>
