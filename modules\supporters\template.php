<?php
require_once '../../config/config.php';

// التحقق من تسجيل الدخول
if (!isLoggedIn()) {
    redirect('../../login.php');
}

// إنشاء ملف Excel نموذجي
header('Content-Type: application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');
header('Content-Disposition: attachment; filename="نموذج_استيراد_المؤيدين.xlsx"');
header('Cache-Control: max-age=0');

// بيانات نموذجية
$headers = [
    'الاسم الكامل',
    'الجنس',
    'الحالة الاجتماعية',
    'تاريخ الميلاد',
    'التحصيل الدراسي',
    'المهنة',
    'العنوان',
    'رقم الهاتف',
    'رقم الناخب',
    'المركز الانتخابي',
    'المنطقة',
    'ملاحظات'
];

$sample_data = [
    [
        'أحمد محمد علي حسن',
        'ذكر',
        'متزوج',
        '1985-05-15',
        'بكالوريوس',
        'مهندس',
        'بغداد - الكرادة - شارع الرشيد',
        '07701234567',
        '12345678901',
        'مدرسة الكرادة الابتدائية',
        'المنطقة الأولى',
        'مؤيد نشط'
    ],
    [
        'فاطمة سعد كريم جاسم',
        'أنثى',
        'متزوجة',
        '1990-08-22',
        'ماجستير',
        'طبيبة',
        'بغداد - الجادرية - مجمع السكني',
        '07712345678',
        '12345678902',
        'مدرسة الجادرية الثانوية',
        'المنطقة الثانية',
        'مؤيدة فعالة'
    ]
];

// إنشاء محتوى CSV (بديل بسيط عن Excel)
$output = fopen('php://output', 'w');

// كتابة BOM للدعم العربي
fprintf($output, chr(0xEF).chr(0xBB).chr(0xBF));

// كتابة العناوين
fputcsv($output, $headers);

// كتابة البيانات النموذجية
foreach ($sample_data as $row) {
    fputcsv($output, $row);
}

fclose($output);
exit;
?>
