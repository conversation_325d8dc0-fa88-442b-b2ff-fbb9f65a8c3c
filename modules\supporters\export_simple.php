<?php
// تصدير المؤيدين - نسخة مبسطة وعملية
header('Content-Type: text/html; charset=utf-8');
ini_set('default_charset', 'utf-8');
mb_internal_encoding('UTF-8');

require_once '../../config/config.php';
require_once '../../config/database.php';

// التحقق من تسجيل الدخول
if (!isLoggedIn()) {
    redirect('../../login.php');
}

// معالجة التصدير
if (isset($_GET['export'])) {
    $export_type = $_GET['export'];
    
    // بناء الاستعلام
    $sql = "SELECT s.full_name, 
                   CASE s.gender WHEN 'male' THEN 'ذكر' ELSE 'أنثى' END as gender,
                   CASE s.marital_status 
                       WHEN 'single' THEN 'أعزب'
                       WHEN 'married' THEN 'متزوج'
                       WHEN 'divorced' THEN 'مطلق'
                       WHEN 'widowed' THEN 'أرمل'
                       ELSE 'أعزب'
                   END as marital_status,
                   s.birth_date,
                   YEAR(CURDATE()) - YEAR(s.birth_date) as age,
                   s.education,
                   s.profession,
                   s.address,
                   s.phone,
                   s.voter_number,
                   s.voting_center,
                   r.name as region_name,
                   s.notes,
                   DATE_FORMAT(s.created_at, '%Y-%m-%d') as created_date
            FROM supporters s 
            LEFT JOIN regions r ON s.region_id = r.id 
            ORDER BY s.created_at DESC";
    
    $supporters = fetchAll($sql);
    
    if (empty($supporters)) {
        showMessage('لا توجد بيانات للتصدير', 'warning');
        redirect('export_simple.php');
    }
    
    if ($export_type === 'csv') {
        exportToCSV($supporters);
    } elseif ($export_type === 'excel') {
        exportToExcel($supporters);
    } elseif ($export_type === 'json') {
        exportToJSON($supporters);
    }
}

function exportToCSV($data) {
    $filename = 'المؤيدين_' . date('Y-m-d_H-i-s') . '.csv';
    
    header('Content-Type: text/csv; charset=utf-8');
    header('Content-Disposition: attachment; filename="' . $filename . '"');
    header('Cache-Control: max-age=0');
    
    $output = fopen('php://output', 'w');
    
    // كتابة BOM للدعم العربي
    fprintf($output, chr(0xEF).chr(0xBB).chr(0xBF));
    
    // العناوين
    $headers = [
        'الاسم الكامل', 'الجنس', 'الحالة الاجتماعية', 'تاريخ الميلاد', 'العمر',
        'التحصيل الدراسي', 'المهنة', 'العنوان', 'رقم الهاتف', 'رقم الناخب',
        'المركز الانتخابي', 'المنطقة', 'ملاحظات', 'تاريخ الإضافة'
    ];
    fputcsv($output, $headers);
    
    // البيانات
    foreach ($data as $row) {
        $csv_row = [
            $row['full_name'],
            $row['gender'],
            $row['marital_status'],
            $row['birth_date'],
            $row['age'] . ' سنة',
            $row['education'] ?: 'غير محدد',
            $row['profession'] ?: 'غير محدد',
            $row['address'],
            $row['phone'],
            $row['voter_number'] ?: 'غير محدد',
            $row['voting_center'] ?: 'غير محدد',
            $row['region_name'],
            $row['notes'] ?: 'لا توجد',
            $row['created_date']
        ];
        fputcsv($output, $csv_row);
    }
    
    fclose($output);
    exit;
}

function exportToExcel($data) {
    // تصدير كـ HTML يمكن فتحه في Excel
    $filename = 'المؤيدين_' . date('Y-m-d_H-i-s') . '.xls';
    
    header('Content-Type: application/vnd.ms-excel; charset=utf-8');
    header('Content-Disposition: attachment; filename="' . $filename . '"');
    header('Cache-Control: max-age=0');
    
    echo "\xEF\xBB\xBF"; // BOM for UTF-8
    
    echo '<table border="1">';
    echo '<tr style="background-color: #f0f0f0; font-weight: bold;">';
    echo '<td>الاسم الكامل</td>';
    echo '<td>الجنس</td>';
    echo '<td>الحالة الاجتماعية</td>';
    echo '<td>تاريخ الميلاد</td>';
    echo '<td>العمر</td>';
    echo '<td>التحصيل الدراسي</td>';
    echo '<td>المهنة</td>';
    echo '<td>العنوان</td>';
    echo '<td>رقم الهاتف</td>';
    echo '<td>رقم الناخب</td>';
    echo '<td>المركز الانتخابي</td>';
    echo '<td>المنطقة</td>';
    echo '<td>ملاحظات</td>';
    echo '<td>تاريخ الإضافة</td>';
    echo '</tr>';
    
    foreach ($data as $row) {
        echo '<tr>';
        echo '<td>' . htmlspecialchars($row['full_name']) . '</td>';
        echo '<td>' . htmlspecialchars($row['gender']) . '</td>';
        echo '<td>' . htmlspecialchars($row['marital_status']) . '</td>';
        echo '<td>' . htmlspecialchars($row['birth_date']) . '</td>';
        echo '<td>' . htmlspecialchars($row['age']) . ' سنة</td>';
        echo '<td>' . htmlspecialchars($row['education'] ?: 'غير محدد') . '</td>';
        echo '<td>' . htmlspecialchars($row['profession'] ?: 'غير محدد') . '</td>';
        echo '<td>' . htmlspecialchars($row['address']) . '</td>';
        echo '<td>' . htmlspecialchars($row['phone']) . '</td>';
        echo '<td>' . htmlspecialchars($row['voter_number'] ?: 'غير محدد') . '</td>';
        echo '<td>' . htmlspecialchars($row['voting_center'] ?: 'غير محدد') . '</td>';
        echo '<td>' . htmlspecialchars($row['region_name']) . '</td>';
        echo '<td>' . htmlspecialchars($row['notes'] ?: 'لا توجد') . '</td>';
        echo '<td>' . htmlspecialchars($row['created_date']) . '</td>';
        echo '</tr>';
    }
    
    echo '</table>';
    exit;
}

function exportToJSON($data) {
    $filename = 'المؤيدين_' . date('Y-m-d_H-i-s') . '.json';
    
    header('Content-Type: application/json; charset=utf-8');
    header('Content-Disposition: attachment; filename="' . $filename . '"');
    header('Cache-Control: max-age=0');
    
    echo json_encode($data, JSON_UNESCAPED_UNICODE | JSON_PRETTY_PRINT);
    exit;
}

// جلب الإحصائيات
$total_supporters = fetchOne("SELECT COUNT(*) as count FROM supporters")['count'];
$male_supporters = fetchOne("SELECT COUNT(*) as count FROM supporters WHERE gender = 'male'")['count'];
$female_supporters = fetchOne("SELECT COUNT(*) as count FROM supporters WHERE gender = 'female'")['count'];
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>تصدير المؤيدين - <?php echo SITE_NAME; ?></title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        body { font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; background: #f8f9fa; }
        .main-content { margin-top: 80px; }
        .export-card { background: white; border-radius: 15px; box-shadow: 0 5px 15px rgba(0,0,0,0.1); }
        .export-option { border: 2px solid #e9ecef; border-radius: 10px; padding: 20px; margin-bottom: 15px; transition: all 0.3s; }
        .export-option:hover { border-color: #007bff; background: #f8f9ff; }
        .export-icon { font-size: 3rem; margin-bottom: 15px; }
    </style>
</head>
<body>
    <!-- شريط التنقل العلوي -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary fixed-top">
        <div class="container-fluid">
            <a class="navbar-brand" href="../../dashboard.php">
                <i class="fas fa-vote-yea me-2"></i>
                نظام إدارة الحملة الانتخابية
            </a>
            
            <div class="navbar-nav ms-auto">
                <div class="nav-item dropdown">
                    <a class="nav-link dropdown-toggle" href="#" id="userDropdown" role="button" data-bs-toggle="dropdown">
                        <i class="fas fa-user-circle me-1"></i>
                        <?php echo htmlspecialchars($_SESSION['full_name']); ?>
                    </a>
                    <ul class="dropdown-menu dropdown-menu-end">
                        <li><a class="dropdown-item" href="../../logout.php"><i class="fas fa-sign-out-alt me-2"></i>تسجيل الخروج</a></li>
                    </ul>
                </div>
            </div>
        </div>
    </nav>

    <!-- المحتوى الرئيسي -->
    <div class="container main-content">
        <div class="row justify-content-center">
            <div class="col-md-8">
                <div class="export-card p-4">
                    <div class="text-center mb-4">
                        <h2><i class="fas fa-download text-success me-2"></i>تصدير المؤيدين</h2>
                        <p class="text-muted">اختر صيغة التصدير المناسبة</p>
                    </div>

                    <?php displayMessage(); ?>

                    <!-- الإحصائيات -->
                    <div class="row mb-4">
                        <div class="col-md-4">
                            <div class="card bg-primary text-white text-center">
                                <div class="card-body">
                                    <h3><?php echo number_format($total_supporters); ?></h3>
                                    <p class="mb-0">إجمالي المؤيدين</p>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="card bg-info text-white text-center">
                                <div class="card-body">
                                    <h3><?php echo number_format($male_supporters); ?></h3>
                                    <p class="mb-0">الذكور</p>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="card bg-warning text-white text-center">
                                <div class="card-body">
                                    <h3><?php echo number_format($female_supporters); ?></h3>
                                    <p class="mb-0">الإناث</p>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- خيارات التصدير -->
                    <div class="row">
                        <div class="col-md-4">
                            <div class="export-option text-center">
                                <div class="export-icon text-success">
                                    <i class="fas fa-file-excel"></i>
                                </div>
                                <h5>Excel</h5>
                                <p class="text-muted">ملف Excel يمكن فتحه في Microsoft Excel</p>
                                <a href="?export=excel" class="btn btn-success">
                                    <i class="fas fa-download me-2"></i>تحميل Excel
                                </a>
                            </div>
                        </div>
                        
                        <div class="col-md-4">
                            <div class="export-option text-center">
                                <div class="export-icon text-primary">
                                    <i class="fas fa-file-csv"></i>
                                </div>
                                <h5>CSV</h5>
                                <p class="text-muted">ملف CSV يمكن فتحه في أي برنامج جداول</p>
                                <a href="?export=csv" class="btn btn-primary">
                                    <i class="fas fa-download me-2"></i>تحميل CSV
                                </a>
                            </div>
                        </div>
                        
                        <div class="col-md-4">
                            <div class="export-option text-center">
                                <div class="export-icon text-info">
                                    <i class="fas fa-file-code"></i>
                                </div>
                                <h5>JSON</h5>
                                <p class="text-muted">ملف JSON للاستخدام في التطبيقات</p>
                                <a href="?export=json" class="btn btn-info">
                                    <i class="fas fa-download me-2"></i>تحميل JSON
                                </a>
                            </div>
                        </div>
                    </div>

                    <!-- معلومات إضافية -->
                    <div class="alert alert-info mt-4">
                        <h6><i class="fas fa-info-circle me-2"></i>معلومات التصدير:</h6>
                        <ul class="mb-0">
                            <li><strong>Excel:</strong> الأفضل للعرض والطباعة</li>
                            <li><strong>CSV:</strong> الأفضل للاستيراد في برامج أخرى</li>
                            <li><strong>JSON:</strong> الأفضل للمطورين والتطبيقات</li>
                        </ul>
                    </div>

                    <!-- أزرار التنقل -->
                    <div class="text-center mt-4">
                        <a href="supporters_working.php" class="btn btn-secondary me-2">
                            <i class="fas fa-arrow-left me-2"></i>العودة للمؤيدين
                        </a>
                        <a href="import_simple.php" class="btn btn-warning">
                            <i class="fas fa-upload me-2"></i>استيراد المؤيدين
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
