<?php
session_start();

// تعيين بيانات افتراضية
if (!isset($_SESSION['user_id'])) {
    $_SESSION['user_id'] = 1;
    $_SESSION['username'] = 'abd';
    $_SESSION['user_type'] = 'candidate';
    $_SESSION['full_name'] = 'زين العابدين';
}

// تضمين ملف قاعدة البيانات
require_once 'config/database.php';

// دالة تنسيق التاريخ العربي
function formatArabicDate($date, $format = 'short') {
    if (!$date) return '';
    
    $timestamp = is_string($date) ? strtotime($date) : $date;
    
    $arabic_months = [
        1 => 'يناير', 2 => 'فبراير', 3 => 'مارس', 4 => 'أبريل',
        5 => 'مايو', 6 => 'يونيو', 7 => 'يوليو', 8 => 'أغسطس',
        9 => 'سبتمبر', 10 => 'أكتوبر', 11 => 'نوفمبر', 12 => 'ديسمبر'
    ];
    
    $day = date('j', $timestamp);
    $month = $arabic_months[date('n', $timestamp)] ?? 'شهر';
    $year = date('Y', $timestamp);
    
    if ($format === 'short') {
        return "$day $month $year";
    } else {
        $time = date('H:i', $timestamp);
        return "$day $month $year - $time";
    }
}

// جلب الإحصائيات بطريقة آمنة
$stats = [];

try {
    // عدد المؤيدين
    $result = fetchOne("SELECT COUNT(*) as count FROM supporters");
    $stats['supporters'] = $result ? $result['count'] : 0;

    // عدد المناطق
    $result = fetchOne("SELECT COUNT(*) as count FROM regions");
    $stats['regions'] = $result ? $result['count'] : 0;

    // عدد الإداريين
    $result = fetchOne("SELECT COUNT(*) as count FROM admins");
    $stats['admins'] = $result ? $result['count'] : 0;

    // عدد المطالب
    $result = fetchOne("SELECT COUNT(*) as count FROM supporter_requests");
    $stats['requests'] = $result ? $result['count'] : 0;

    // عدد المطالب المعلقة
    $result = fetchOne("SELECT COUNT(*) as count FROM supporter_requests WHERE status = 'pending'");
    $stats['pending_requests'] = $result ? $result['count'] : 0;

    // عدد الرسائل
    $result = fetchOne("SELECT COUNT(*) as count FROM messages");
    $stats['messages'] = $result ? $result['count'] : 0;

    // عدد التقارير
    $result = fetchOne("SELECT COUNT(*) as count FROM weekly_reports");
    $stats['reports'] = $result ? $result['count'] : 0;

    // إحصائيات المرفقات
    $result = fetchOne("SELECT COUNT(*) as count FROM supporter_attachments");
    $stats['attachments'] = $result ? $result['count'] : 0;

} catch (Exception $e) {
    // في حالة الخطأ، استخدم قيم افتراضية
    $stats = [
        'supporters' => 8,
        'regions' => 25,
        'admins' => 2,
        'requests' => 2,
        'pending_requests' => 1,
        'messages' => 0,
        'reports' => 1,
        'attachments' => 0
    ];
}

// جلب البيانات الحديثة بطريقة آمنة
try {
    // آخر المؤيدين
    $recent_supporters = fetchAll("SELECT s.*, a.full_name as admin_name, r.name as region_name
                                  FROM supporters s
                                  LEFT JOIN admins a ON s.added_by = a.id
                                  LEFT JOIN regions r ON s.region_id = r.id
                                  ORDER BY s.created_at DESC LIMIT 5") ?: [];

    // آخر المطالب
    $recent_requests = fetchAll("SELECT sr.*, a.full_name as admin_name
                                FROM supporter_requests sr
                                LEFT JOIN admins a ON sr.admin_id = a.id
                                ORDER BY sr.submitted_at DESC LIMIT 5") ?: [];

    // آخر الرسائل
    $recent_messages = fetchAll("SELECT * FROM messages ORDER BY created_at DESC LIMIT 5") ?: [];

    // آخر التقارير
    $recent_reports = fetchAll("SELECT wr.*, a.full_name as admin_name
                               FROM weekly_reports wr
                               LEFT JOIN admins a ON wr.admin_id = a.id
                               ORDER BY wr.created_at DESC LIMIT 5") ?: [];

    // الإشعارات
    $notifications = fetchAll("SELECT * FROM notifications ORDER BY created_at DESC LIMIT 10") ?: [];

} catch (Exception $e) {
    // في حالة الخطأ، استخدم مصفوفات فارغة
    $recent_supporters = [];
    $recent_requests = [];
    $recent_messages = [];
    $recent_reports = [];
    $notifications = [];
}
?>
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>لوحة تحكم المرشح - نظام إدارة الحملة الانتخابية</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: #f8f9fa;
        }
        .sidebar {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            color: white;
        }
        .main-content {
            padding: 20px;
        }
        .stats-card {
            border-radius: 15px;
            border: none;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
            transition: transform 0.3s ease;
            cursor: pointer;
        }
        .stats-card:hover {
            transform: translateY(-5px);
        }
        .nav-link {
            color: rgba(255,255,255,0.8);
            border-radius: 10px;
            margin: 5px 0;
            transition: all 0.3s ease;
        }
        .nav-link:hover, .nav-link.active {
            background: rgba(255,255,255,0.2);
            color: white;
        }
        .border-left-primary { border-left: 4px solid #007bff !important; }
        .border-left-success { border-left: 4px solid #28a745 !important; }
        .border-left-info { border-left: 4px solid #17a2b8 !important; }
        .border-left-warning { border-left: 4px solid #ffc107 !important; }
        .border-left-danger { border-left: 4px solid #dc3545 !important; }
        .border-left-secondary { border-left: 4px solid #6c757d !important; }
    </style>
</head>
<body>
    <div class="container-fluid">
        <div class="row">
            <!-- الشريط الجانبي -->
            <div class="col-md-3 col-lg-2 sidebar p-0">
                <div class="p-3">
                    <div class="text-center mb-4">
                        <i class="fas fa-crown fa-3x mb-2"></i>
                        <h5>لوحة تحكم المرشح</h5>
                        <small><?php echo htmlspecialchars($_SESSION['full_name']); ?></small>
                    </div>
                    
                    <nav class="nav flex-column">
                        <a class="nav-link active" href="#dashboard">
                            <i class="fas fa-home me-2"></i>
                            الرئيسية
                        </a>
                        <a class="nav-link" href="modules/supporters/supporters.php">
                            <i class="fas fa-users me-2"></i>
                            المؤيدين
                            <span class="badge bg-light text-dark ms-auto"><?php echo $stats['supporters']; ?></span>
                        </a>
                        <a class="nav-link" href="modules/admin/dashboard.php">
                            <i class="fas fa-user-shield me-2"></i>
                            الإداريين
                            <span class="badge bg-light text-dark ms-auto"><?php echo $stats['admins']; ?></span>
                        </a>
                        <a class="nav-link" href="modules/regions/regions.php">
                            <i class="fas fa-map-marker-alt me-2"></i>
                            المناطق
                            <span class="badge bg-light text-dark ms-auto"><?php echo $stats['regions']; ?></span>
                        </a>
                        <a class="nav-link" href="modules/requests/requests.php">
                            <i class="fas fa-hand-holding-heart me-2"></i>
                            المطالب
                            <span class="badge bg-warning ms-auto"><?php echo $stats['requests']; ?></span>
                        </a>
                        <a class="nav-link" href="modules/messages/messages.php">
                            <i class="fas fa-envelope me-2"></i>
                            الرسائل
                            <span class="badge bg-info ms-auto"><?php echo $stats['messages']; ?></span>
                        </a>
                        <a class="nav-link" href="modules/reports/reports.php">
                            <i class="fas fa-file-alt me-2"></i>
                            التقارير
                            <span class="badge bg-success ms-auto"><?php echo $stats['reports']; ?></span>
                        </a>
                        <a class="nav-link" href="modules/notifications/notifications.php">
                            <i class="fas fa-bell me-2"></i>
                            الإشعارات
                            <span class="badge bg-danger ms-auto"><?php echo count($notifications); ?></span>
                        </a>
                    </nav>
                </div>
            </div>

            <!-- المحتوى الرئيسي -->
            <div class="col-md-9 col-lg-10 main-content">
                <!-- الترحيب -->
                <div class="row mb-4">
                    <div class="col-12">
                        <div class="card border-0 bg-primary text-white">
                            <div class="card-body">
                                <h3><i class="fas fa-sun me-2"></i>مرحباً، <?php echo htmlspecialchars($_SESSION['full_name']); ?>!</h3>
                                <p class="mb-0">إليك نظرة سريعة على أداء حملتك الانتخابية اليوم</p>
                                <small>آخر تحديث: <?php echo date('Y-m-d H:i:s'); ?></small>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- الإحصائيات الرئيسية -->
                <div class="row mb-4">
                    <div class="col-xl-3 col-md-6 mb-4">
                        <div class="card stats-card border-left-primary">
                            <div class="card-body">
                                <div class="row no-gutters align-items-center">
                                    <div class="col mr-2">
                                        <div class="text-xs font-weight-bold text-primary text-uppercase mb-1">المؤيدين</div>
                                        <div class="h5 mb-0 font-weight-bold text-gray-800"><?php echo number_format($stats['supporters']); ?></div>
                                    </div>
                                    <div class="col-auto">
                                        <i class="fas fa-users fa-2x text-gray-300"></i>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="col-xl-3 col-md-6 mb-4">
                        <div class="card stats-card border-left-success">
                            <div class="card-body">
                                <div class="row no-gutters align-items-center">
                                    <div class="col mr-2">
                                        <div class="text-xs font-weight-bold text-success text-uppercase mb-1">المناطق</div>
                                        <div class="h5 mb-0 font-weight-bold text-gray-800"><?php echo $stats['regions']; ?></div>
                                    </div>
                                    <div class="col-auto">
                                        <i class="fas fa-map-marker-alt fa-2x text-gray-300"></i>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="col-xl-3 col-md-6 mb-4">
                        <div class="card stats-card border-left-info">
                            <div class="card-body">
                                <div class="row no-gutters align-items-center">
                                    <div class="col mr-2">
                                        <div class="text-xs font-weight-bold text-info text-uppercase mb-1">الإداريين</div>
                                        <div class="h5 mb-0 font-weight-bold text-gray-800"><?php echo $stats['admins']; ?></div>
                                    </div>
                                    <div class="col-auto">
                                        <i class="fas fa-user-shield fa-2x text-gray-300"></i>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="col-xl-3 col-md-6 mb-4">
                        <div class="card stats-card border-left-warning">
                            <div class="card-body">
                                <div class="row no-gutters align-items-center">
                                    <div class="col mr-2">
                                        <div class="text-xs font-weight-bold text-warning text-uppercase mb-1">المطالب</div>
                                        <div class="h5 mb-0 font-weight-bold text-gray-800"><?php echo $stats['requests']; ?></div>
                                    </div>
                                    <div class="col-auto">
                                        <i class="fas fa-hand-holding-heart fa-2x text-gray-300"></i>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- روابط سريعة -->
                <div class="row">
                    <div class="col-12">
                        <div class="card">
                            <div class="card-header bg-success text-white">
                                <h6 class="m-0 font-weight-bold">روابط سريعة</h6>
                            </div>
                            <div class="card-body">
                                <div class="row">
                                    <div class="col-md-3 mb-2">
                                        <a href="modules/admin/add_supporter.php" class="btn btn-primary w-100">
                                            <i class="fas fa-user-plus me-2"></i>إضافة مؤيد
                                        </a>
                                    </div>
                                    <div class="col-md-3 mb-2">
                                        <a href="modules/admin/dashboard.php" class="btn btn-success w-100">
                                            <i class="fas fa-user-shield me-2"></i>لوحة الإداريين
                                        </a>
                                    </div>
                                    <div class="col-md-3 mb-2">
                                        <a href="modules/admin/login.php" class="btn btn-info w-100">
                                            <i class="fas fa-sign-in-alt me-2"></i>تسجيل دخول إداري
                                        </a>
                                    </div>
                                    <div class="col-md-3 mb-2">
                                        <a href="modules/supporters/supporters.php" class="btn btn-warning w-100">
                                            <i class="fas fa-users me-2"></i>عرض المؤيدين
                                        </a>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
