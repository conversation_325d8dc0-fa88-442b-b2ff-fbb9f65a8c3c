// JavaScript للوحة التحكم

document.addEventListener('DOMContentLoaded', function() {
    // تهيئة التقويم
    initializeCalendar();
    
    // تهيئة البحث الشامل
    initializeGlobalSearch();
    
    // تهيئة بطاقات الإحصائيات
    initializeStatsCards();
    
    // تهيئة الإشعارات
    initializeNotifications();
    
    // تحديث الإحصائيات كل 30 ثانية
    setInterval(updateStats, 30000);
    
    // تهيئة الشريط الجانبي للجوال
    initializeMobileSidebar();
});

// تهيئة التقويم
function initializeCalendar() {
    const calendarEl = document.getElementById('calendar');
    if (calendarEl) {
        const calendar = new FullCalendar.Calendar(calendarEl, {
            initialView: 'dayGridMonth',
            locale: 'ar',
            direction: 'rtl',
            headerToolbar: {
                left: 'prev,next today',
                center: 'title',
                right: 'dayGridMonth,timeGridWeek,timeGridDay'
            },
            events: function(fetchInfo, successCallback, failureCallback) {
                // جلب الفعاليات من الخادم
                fetch('modules/events/get_events.php')
                    .then(response => response.json())
                    .then(data => {
                        const events = data.map(event => ({
                            id: event.id,
                            title: event.name,
                            start: event.event_date + 'T' + event.event_time,
                            description: event.description,
                            location: event.location,
                            backgroundColor: getEventColor(event.status),
                            borderColor: getEventColor(event.status)
                        }));
                        successCallback(events);
                    })
                    .catch(error => {
                        console.error('خطأ في جلب الفعاليات:', error);
                        failureCallback(error);
                    });
            },
            eventClick: function(info) {
                showEventDetails(info.event);
            },
            eventMouseEnter: function(info) {
                // إظهار تفاصيل الفعالية عند التمرير
                showEventTooltip(info);
            },
            eventMouseLeave: function(info) {
                hideEventTooltip();
            }
        });
        
        calendar.render();
        window.calendar = calendar; // حفظ مرجع للتقويم
    }
}

// الحصول على لون الفعالية حسب الحالة
function getEventColor(status) {
    const colors = {
        'planned': '#007bff',
        'ongoing': '#28a745',
        'completed': '#6c757d',
        'cancelled': '#dc3545'
    };
    return colors[status] || '#007bff';
}

// إظهار تفاصيل الفعالية
function showEventDetails(event) {
    const modal = new bootstrap.Modal(document.getElementById('eventModal') || createEventModal());
    document.getElementById('eventModalTitle').textContent = event.title;
    document.getElementById('eventModalBody').innerHTML = `
        <p><strong>التاريخ:</strong> ${event.start.toLocaleDateString('ar-EG')}</p>
        <p><strong>الوقت:</strong> ${event.start.toLocaleTimeString('ar-EG')}</p>
        <p><strong>الوصف:</strong> ${event.extendedProps.description || 'لا يوجد وصف'}</p>
        <p><strong>الموقع:</strong> ${event.extendedProps.location || 'غير محدد'}</p>
    `;
    modal.show();
}

// إنشاء نافذة منبثقة للفعالية
function createEventModal() {
    const modalHTML = `
        <div class="modal fade" id="eventModal" tabindex="-1">
            <div class="modal-dialog">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title" id="eventModalTitle">تفاصيل الفعالية</h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                    </div>
                    <div class="modal-body" id="eventModalBody">
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إغلاق</button>
                        <a href="#" class="btn btn-primary" id="editEventBtn">تعديل</a>
                    </div>
                </div>
            </div>
        </div>
    `;
    document.body.insertAdjacentHTML('beforeend', modalHTML);
    return document.getElementById('eventModal');
}

// تهيئة البحث الشامل
function initializeGlobalSearch() {
    const searchInput = document.getElementById('globalSearch');
    if (searchInput) {
        // البحث أثناء الكتابة مع تأخير
        let searchTimeout;
        searchInput.addEventListener('input', function() {
            clearTimeout(searchTimeout);
            searchTimeout = setTimeout(() => {
                performGlobalSearch();
            }, 500);
        });
        
        // البحث عند الضغط على Enter
        searchInput.addEventListener('keypress', function(e) {
            if (e.key === 'Enter') {
                e.preventDefault();
                performGlobalSearch();
            }
        });
    }
}

// تنفيذ البحث الشامل
function performGlobalSearch() {
    const query = document.getElementById('globalSearch').value.trim();
    if (query.length < 2) {
        showToast('يرجى إدخال كلمة بحث أطول', 'warning');
        return;
    }
    
    // إظهار مؤشر التحميل
    showSearchLoading(true);
    
    // إرسال طلب البحث
    fetch('modules/search/global_search.php', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({ query: query })
    })
    .then(response => response.json())
    .then(data => {
        showSearchLoading(false);
        displaySearchResults(data);
    })
    .catch(error => {
        showSearchLoading(false);
        console.error('خطأ في البحث:', error);
        showToast('حدث خطأ أثناء البحث', 'error');
    });
}

// إظهار نتائج البحث
function displaySearchResults(results) {
    const modal = new bootstrap.Modal(document.getElementById('searchModal') || createSearchModal());
    const resultsContainer = document.getElementById('searchResults');
    
    if (results.length === 0) {
        resultsContainer.innerHTML = '<p class="text-center text-muted">لم يتم العثور على نتائج</p>';
    } else {
        let html = '';
        results.forEach(result => {
            html += `
                <div class="search-result-item p-3 border-bottom">
                    <div class="d-flex justify-content-between align-items-start">
                        <div>
                            <h6 class="mb-1">${result.title}</h6>
                            <p class="mb-1 text-muted">${result.description}</p>
                            <small class="text-muted">
                                <i class="fas fa-${getModuleIcon(result.module)} me-1"></i>
                                ${getModuleName(result.module)}
                            </small>
                        </div>
                        <a href="${result.url}" class="btn btn-sm btn-outline-primary">عرض</a>
                    </div>
                </div>
            `;
        });
        resultsContainer.innerHTML = html;
    }
    
    modal.show();
}

// إنشاء نافذة نتائج البحث
function createSearchModal() {
    const modalHTML = `
        <div class="modal fade" id="searchModal" tabindex="-1">
            <div class="modal-dialog modal-lg">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title">نتائج البحث</h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                    </div>
                    <div class="modal-body">
                        <div id="searchResults"></div>
                    </div>
                </div>
            </div>
        </div>
    `;
    document.body.insertAdjacentHTML('beforeend', modalHTML);
    return document.getElementById('searchModal');
}

// تهيئة بطاقات الإحصائيات
function initializeStatsCards() {
    const statsCards = document.querySelectorAll('.stats-card');
    statsCards.forEach(card => {
        card.addEventListener('click', function() {
            const module = this.dataset.module;
            if (module) {
                window.location.href = `modules/${module}/${module}.php`;
            }
        });
        
        // تأثير النقر
        card.addEventListener('mousedown', function() {
            this.style.transform = 'translateY(-3px) scale(0.98)';
        });
        
        card.addEventListener('mouseup', function() {
            this.style.transform = 'translateY(-5px) scale(1)';
        });
        
        card.addEventListener('mouseleave', function() {
            this.style.transform = 'translateY(0) scale(1)';
        });
    });
}

// تهيئة الإشعارات
function initializeNotifications() {
    // تحديث الإشعارات كل دقيقة
    setInterval(updateNotifications, 60000);
    
    // تمييز الإشعارات كمقروءة عند النقر
    const notificationItems = document.querySelectorAll('.notification-item');
    notificationItems.forEach(item => {
        item.addEventListener('click', function() {
            markNotificationAsRead(this.dataset.notificationId);
        });
    });
}

// تحديث الإشعارات
function updateNotifications() {
    fetch('modules/notifications/get_notifications.php')
        .then(response => response.json())
        .then(data => {
            updateNotificationBadge(data.unread_count);
            updateNotificationDropdown(data.notifications);
        })
        .catch(error => {
            console.error('خطأ في تحديث الإشعارات:', error);
        });
}

// تحديث شارة الإشعارات
function updateNotificationBadge(count) {
    const badge = document.querySelector('#notificationsDropdown .badge');
    if (count > 0) {
        if (badge) {
            badge.textContent = count;
        } else {
            const newBadge = document.createElement('span');
            newBadge.className = 'position-absolute top-0 start-100 translate-middle badge rounded-pill bg-danger';
            newBadge.textContent = count;
            document.querySelector('#notificationsDropdown').appendChild(newBadge);
        }
    } else {
        if (badge) {
            badge.remove();
        }
    }
}

// تحديث الإحصائيات
function updateStats() {
    fetch('modules/dashboard/get_stats.php')
        .then(response => response.json())
        .then(data => {
            updateStatsDisplay(data);
        })
        .catch(error => {
            console.error('خطأ في تحديث الإحصائيات:', error);
        });
}

// تحديث عرض الإحصائيات
function updateStatsDisplay(stats) {
    Object.keys(stats).forEach(key => {
        const element = document.querySelector(`[data-stat="${key}"]`);
        if (element) {
            animateNumber(element, parseInt(element.textContent.replace(/[^\d]/g, '')), stats[key]);
        }
    });
}

// تحريك الأرقام
function animateNumber(element, start, end) {
    const duration = 1000;
    const startTime = performance.now();
    
    function update(currentTime) {
        const elapsed = currentTime - startTime;
        const progress = Math.min(elapsed / duration, 1);
        
        const current = Math.floor(start + (end - start) * progress);
        element.textContent = current.toLocaleString('ar-EG');
        
        if (progress < 1) {
            requestAnimationFrame(update);
        }
    }
    
    requestAnimationFrame(update);
}

// تهيئة الشريط الجانبي للجوال
function initializeMobileSidebar() {
    // إضافة زر القائمة للجوال
    if (window.innerWidth <= 768) {
        addMobileMenuButton();
    }
    
    // مراقبة تغيير حجم الشاشة
    window.addEventListener('resize', function() {
        if (window.innerWidth <= 768) {
            addMobileMenuButton();
        } else {
            removeMobileMenuButton();
        }
    });
}

// إضافة زر القائمة للجوال
function addMobileMenuButton() {
    if (!document.getElementById('mobileMenuBtn')) {
        const button = document.createElement('button');
        button.id = 'mobileMenuBtn';
        button.className = 'btn btn-outline-light d-md-none';
        button.innerHTML = '<i class="fas fa-bars"></i>';
        button.addEventListener('click', toggleMobileSidebar);
        
        const navbar = document.querySelector('.navbar .container-fluid');
        navbar.insertBefore(button, navbar.firstChild);
    }
}

// إزالة زر القائمة للجوال
function removeMobileMenuButton() {
    const button = document.getElementById('mobileMenuBtn');
    if (button) {
        button.remove();
    }
}

// تبديل الشريط الجانبي للجوال
function toggleMobileSidebar() {
    const sidebar = document.querySelector('.sidebar');
    sidebar.classList.toggle('show');
}

// تحديث لوحة التحكم
function refreshDashboard() {
    // إظهار مؤشر التحميل
    showPageLoading(true);
    
    // تحديث الإحصائيات
    updateStats();
    
    // تحديث التقويم
    if (window.calendar) {
        window.calendar.refetchEvents();
    }
    
    // تحديث الإشعارات
    updateNotifications();
    
    // إخفاء مؤشر التحميل
    setTimeout(() => {
        showPageLoading(false);
        showToast('تم تحديث البيانات بنجاح', 'success');
    }, 1000);
}

// إظهار مؤشر تحميل الصفحة
function showPageLoading(show) {
    const loader = document.getElementById('pageLoader') || createPageLoader();
    loader.style.display = show ? 'flex' : 'none';
}

// إنشاء مؤشر تحميل الصفحة
function createPageLoader() {
    const loaderHTML = `
        <div id="pageLoader" class="position-fixed top-0 start-0 w-100 h-100 d-flex align-items-center justify-content-center" style="background: rgba(255,255,255,0.8); z-index: 9999; display: none;">
            <div class="text-center">
                <div class="spinner-border text-primary" role="status">
                    <span class="visually-hidden">جاري التحميل...</span>
                </div>
                <p class="mt-2">جاري تحديث البيانات...</p>
            </div>
        </div>
    `;
    document.body.insertAdjacentHTML('beforeend', loaderHTML);
    return document.getElementById('pageLoader');
}

// إظهار مؤشر تحميل البحث
function showSearchLoading(show) {
    const searchBtn = document.querySelector('button[onclick="performGlobalSearch()"]');
    if (searchBtn) {
        if (show) {
            searchBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> جاري البحث...';
            searchBtn.disabled = true;
        } else {
            searchBtn.innerHTML = 'بحث';
            searchBtn.disabled = false;
        }
    }
}

// الحصول على أيقونة الوحدة
function getModuleIcon(module) {
    const icons = {
        'supporters': 'users',
        'regions': 'map-marked-alt',
        'admins': 'user-tie',
        'expenses': 'money-bill-wave',
        'events': 'calendar-alt',
        'competitors': 'chess',
        'reports': 'chart-bar'
    };
    return icons[module] || 'file';
}

// الحصول على اسم الوحدة
function getModuleName(module) {
    const names = {
        'supporters': 'المؤيدين',
        'regions': 'المناطق',
        'admins': 'الإداريين',
        'expenses': 'المصروفات',
        'events': 'الفعاليات',
        'competitors': 'المنافسين',
        'reports': 'التقارير'
    };
    return names[module] || 'غير محدد';
}

// دالة عامة لإظهار الرسائل
function showToast(message, type = 'info') {
    const toastContainer = getOrCreateToastContainer();
    const toast = document.createElement('div');
    toast.className = `toast align-items-center text-white bg-${getBootstrapColor(type)} border-0`;
    toast.setAttribute('role', 'alert');
    toast.innerHTML = `
        <div class="d-flex">
            <div class="toast-body">
                <i class="fas fa-${getIcon(type)} me-2"></i>
                ${message}
            </div>
            <button type="button" class="btn-close btn-close-white me-2 m-auto" data-bs-dismiss="toast"></button>
        </div>
    `;
    
    toastContainer.appendChild(toast);
    const bsToast = new bootstrap.Toast(toast);
    bsToast.show();
    
    toast.addEventListener('hidden.bs.toast', function() {
        toast.remove();
    });
}

// دالة للحصول على حاوية التوست
function getOrCreateToastContainer() {
    let container = document.querySelector('.toast-container');
    if (!container) {
        container = document.createElement('div');
        container.className = 'toast-container position-fixed top-0 end-0 p-3';
        container.style.zIndex = '9999';
        document.body.appendChild(container);
    }
    return container;
}

// دالة للحصول على لون Bootstrap
function getBootstrapColor(type) {
    const colors = {
        'success': 'success',
        'error': 'danger',
        'warning': 'warning',
        'info': 'info'
    };
    return colors[type] || 'info';
}

// دالة للحصول على الأيقونة
function getIcon(type) {
    const icons = {
        'success': 'check-circle',
        'error': 'exclamation-triangle',
        'warning': 'exclamation-circle',
        'info': 'info-circle'
    };
    return icons[type] || 'info-circle';
}
