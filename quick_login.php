<?php
// تسجيل دخول سريع للاختبار

require_once 'config/config.php';
require_once 'config/database.php';

echo "<!DOCTYPE html>";
echo "<html lang='ar' dir='rtl'>";
echo "<head>";
echo "<meta charset='UTF-8'>";
echo "<meta name='viewport' content='width=device-width, initial-scale=1.0'>";
echo "<title>تسجيل دخول سريع</title>";
echo "<link href='https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css' rel='stylesheet'>";
echo "<link href='https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css' rel='stylesheet'>";
echo "<style>";
echo "body { font-family: 'Se<PERSON>e UI', Tahoma, Geneva, Verdana, sans-serif; background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); min-height: 100vh; padding: 2rem; }";
echo ".login-card { background: white; border-radius: 15px; box-shadow: 0 10px 30px rgba(0,0,0,0.1); padding: 2rem; margin: 0 auto; max-width: 600px; }";
echo ".success { color: #28a745; }";
echo ".error { color: #dc3545; }";
echo "</style>";
echo "</head>";
echo "<body>";

echo "<div class='login-card'>";
echo "<h1 class='text-center mb-4'><i class='fas fa-sign-in-alt'></i> تسجيل دخول سريع</h1>";

// إذا تم الضغط على تسجيل دخول تلقائي
if (isset($_GET['auto_login'])) {
    try {
        // البحث عن المستخدم
        $user = fetchOne("SELECT * FROM users WHERE username = 'abd'", []);
        
        if ($user) {
            // تسجيل الدخول مباشرة
            $_SESSION['user_id'] = $user['id'];
            $_SESSION['username'] = $user['username'];
            $_SESSION['user_type'] = $user['user_type'];
            $_SESSION['full_name'] = $user['full_name'];
            $_SESSION['region_id'] = $user['region_id'];
            $_SESSION['permissions'] = json_decode($user['permissions'], true);
            $_SESSION['login_time'] = time();
            
            echo "<div class='alert alert-success'>";
            echo "<h5><i class='fas fa-check'></i> تم تسجيل الدخول بنجاح!</h5>";
            echo "<p>جاري التوجيه إلى لوحة التحكم...</p>";
            echo "</div>";
            
            echo "<script>";
            echo "setTimeout(function() { window.location.href = 'dashboard.php'; }, 2000);";
            echo "</script>";
        } else {
            echo "<div class='alert alert-danger'>";
            echo "<h5>خطأ: المستخدم غير موجود</h5>";
            echo "</div>";
        }
    } catch (Exception $e) {
        echo "<div class='alert alert-danger'>";
        echo "<h5>خطأ في قاعدة البيانات:</h5>";
        echo "<p>" . htmlspecialchars($e->getMessage()) . "</p>";
        echo "</div>";
    }
}

// عرض معلومات المستخدم
try {
    $user = fetchOne("SELECT * FROM users WHERE username = 'abd'", []);
    
    if ($user) {
        echo "<div class='alert alert-info'>";
        echo "<h5>معلومات المستخدم الموجود:</h5>";
        echo "<p><strong>اسم المستخدم:</strong> " . htmlspecialchars($user['username']) . "</p>";
        echo "<p><strong>رقم الهاتف:</strong> " . htmlspecialchars($user['phone']) . "</p>";
        echo "<p><strong>الاسم الكامل:</strong> " . htmlspecialchars($user['full_name']) . "</p>";
        echo "<p><strong>نوع المستخدم:</strong> " . htmlspecialchars($user['user_type']) . "</p>";
        echo "<p><strong>الحالة:</strong> " . htmlspecialchars($user['status']) . "</p>";
        echo "</div>";
        
        // اختبار كلمات المرور المختلفة
        echo "<div class='alert alert-warning'>";
        echo "<h5>اختبار كلمات المرور:</h5>";
        
        $test_passwords = ['abdabd', '123456', 'admin', 'password'];
        
        foreach ($test_passwords as $test_pass) {
            if (password_verify($test_pass, $user['password'])) {
                echo "<p class='success'><i class='fas fa-check'></i> كلمة المرور '$test_pass' صحيحة</p>";
            } else {
                echo "<p class='error'><i class='fas fa-times'></i> كلمة المرور '$test_pass' خاطئة</p>";
            }
        }
        echo "</div>";
        
    } else {
        echo "<div class='alert alert-warning'>";
        echo "<h5>المستخدم غير موجود</h5>";
        echo "<p>يجب إنشاء المستخدم الافتراضي أولاً</p>";
        echo "</div>";
    }
    
} catch (Exception $e) {
    echo "<div class='alert alert-danger'>";
    echo "<h5>خطأ في الاتصال بقاعدة البيانات:</h5>";
    echo "<p>" . htmlspecialchars($e->getMessage()) . "</p>";
    echo "</div>";
}

// أزرار الإجراءات
echo "<div class='text-center mt-4'>";

if (isset($user) && $user) {
    echo "<a href='?auto_login=1' class='btn btn-success btn-lg me-2'>";
    echo "<i class='fas fa-sign-in-alt'></i> تسجيل دخول تلقائي";
    echo "</a>";
}

echo "<a href='login.php' class='btn btn-primary me-2'>";
echo "<i class='fas fa-sign-in-alt'></i> صفحة تسجيل الدخول";
echo "</a>";

echo "<a href='fix_password.php' class='btn btn-warning me-2'>";
echo "<i class='fas fa-key'></i> إصلاح كلمة المرور";
echo "</a>";

echo "<a href='reset_database.php' class='btn btn-danger'>";
echo "<i class='fas fa-database'></i> إعادة تعيين قاعدة البيانات";
echo "</a>";

echo "</div>";

// معلومات إضافية
echo "<div class='mt-4'>";
echo "<h6>ملاحظات:</h6>";
echo "<ul>";
echo "<li>إذا كانت كلمة المرور لا تعمل، استخدم 'إصلاح كلمة المرور'</li>";
echo "<li>إذا كان المستخدم غير موجود، استخدم 'إعادة تعيين قاعدة البيانات'</li>";
echo "<li>تسجيل الدخول التلقائي يتجاوز التحقق من كلمة المرور</li>";
echo "</ul>";
echo "</div>";

echo "</div>";
echo "</body>";
echo "</html>";
?>
