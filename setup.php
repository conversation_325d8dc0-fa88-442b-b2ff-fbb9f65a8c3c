<?php
/**
 * ملف الإعداد السريع لنظام إدارة الحملة الانتخابية
 * يقوم بإنشاء كلمة مرور مشفرة وإعداد النظام
 */

// إنشاء كلمة مرور مشفرة
$password = 'abdabd';
$hashed = password_hash($password, PASSWORD_DEFAULT);

echo "<!DOCTYPE html>";
echo "<html lang='ar' dir='rtl'>";
echo "<head>";
echo "<meta charset='UTF-8'>";
echo "<meta name='viewport' content='width=device-width, initial-scale=1.0'>";
echo "<title>إعداد النظام</title>";
echo "<link href='https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css' rel='stylesheet'>";
echo "<style>";
echo "body { font-family: '<PERSON><PERSON>e UI', Tahoma, Geneva, Verdana, sans-serif; background: #f8f9fa; padding: 2rem; }";
echo ".setup-card { max-width: 800px; margin: 0 auto; background: white; border-radius: 10px; box-shadow: 0 4px 6px rgba(0,0,0,0.1); padding: 2rem; }";
echo ".code-block { background: #f8f9fa; border: 1px solid #dee2e6; border-radius: 5px; padding: 1rem; margin: 1rem 0; font-family: monospace; }";
echo "</style>";
echo "</head>";
echo "<body>";

echo "<div class='setup-card'>";
echo "<h1 class='text-center mb-4'><i class='fas fa-cog'></i> إعداد نظام إدارة الحملة الانتخابية</h1>";

echo "<div class='alert alert-info'>";
echo "<h5><i class='fas fa-info-circle'></i> معلومات النظام</h5>";
echo "<p>مرحباً بك في نظام إدارة الحملة الانتخابية. هذا النظام يوفر إدارة شاملة للحملات الانتخابية.</p>";
echo "</div>";

echo "<div class='row'>";
echo "<div class='col-md-6'>";
echo "<div class='card'>";
echo "<div class='card-header bg-primary text-white'>";
echo "<h6><i class='fas fa-database'></i> إعدادات قاعدة البيانات</h6>";
echo "</div>";
echo "<div class='card-body'>";
echo "<div class='code-block'>";
echo "<strong>اسم قاعدة البيانات:</strong> irjnpfzw_mr<br>";
echo "<strong>اسم المستخدم:</strong> irjnpfzw_mr<br>";
echo "<strong>كلمة المرور:</strong> irjnpfzw_mr<br>";
echo "<strong>المضيف:</strong> localhost";
echo "</div>";
echo "</div>";
echo "</div>";
echo "</div>";

echo "<div class='col-md-6'>";
echo "<div class='card'>";
echo "<div class='card-header bg-success text-white'>";
echo "<h6><i class='fas fa-user'></i> بيانات تسجيل الدخول</h6>";
echo "</div>";
echo "<div class='card-body'>";
echo "<div class='code-block'>";
echo "<strong>اسم المستخدم:</strong> abd<br>";
echo "<strong>كلمة المرور:</strong> abdabd<br>";
echo "<strong>رقم الهاتف:</strong> 07719992716";
echo "</div>";
echo "</div>";
echo "</div>";
echo "</div>";
echo "</div>";

echo "<div class='card mt-4'>";
echo "<div class='card-header bg-warning text-dark'>";
echo "<h6><i class='fas fa-key'></i> كلمة المرور المشفرة</h6>";
echo "</div>";
echo "<div class='card-body'>";
echo "<p>كلمة المرور المشفرة لاستخدامها في قاعدة البيانات:</p>";
echo "<div class='code-block'>";
echo "<small>$hashed</small>";
echo "</div>";
echo "</div>";
echo "</div>";

echo "<div class='card mt-4'>";
echo "<div class='card-header bg-info text-white'>";
echo "<h6><i class='fas fa-list'></i> خطوات التثبيت</h6>";
echo "</div>";
echo "<div class='card-body'>";
echo "<ol>";
echo "<li><strong>إنشاء قاعدة البيانات:</strong> قم بإنشاء قاعدة بيانات MySQL بالاسم المحدد أعلاه</li>";
echo "<li><strong>تشغيل ملف SQL:</strong> قم بتشغيل ملف <code>database/schema.sql</code> في قاعدة البيانات</li>";
echo "<li><strong>إعداد الصلاحيات:</strong> تأكد من أن مجلدات <code>uploads/</code> و <code>exports/</code> قابلة للكتابة</li>";
echo "<li><strong>الوصول للنظام:</strong> انتقل إلى <code>index.php</code> أو <code>login.php</code></li>";
echo "</ol>";
echo "</div>";
echo "</div>";

echo "<div class='card mt-4'>";
echo "<div class='card-header bg-secondary text-white'>";
echo "<h6><i class='fas fa-terminal'></i> أوامر سريعة</h6>";
echo "</div>";
echo "<div class='card-body'>";
echo "<h6>إنشاء قاعدة البيانات (MySQL):</h6>";
echo "<div class='code-block'>";
echo "CREATE DATABASE irjnpfzw_mr CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;<br>";
echo "CREATE USER 'irjnpfzw_mr'@'localhost' IDENTIFIED BY 'irjnpfzw_mr';<br>";
echo "GRANT ALL PRIVILEGES ON irjnpfzw_mr.* TO 'irjnpfzw_mr'@'localhost';<br>";
echo "FLUSH PRIVILEGES;";
echo "</div>";

echo "<h6 class='mt-3'>إعداد الصلاحيات (Linux/Unix):</h6>";
echo "<div class='code-block'>";
echo "chmod 755 uploads/<br>";
echo "chmod 755 exports/<br>";
echo "chown -R www-data:www-data uploads/ exports/";
echo "</div>";
echo "</div>";
echo "</div>";

echo "<div class='alert alert-success mt-4'>";
echo "<h6><i class='fas fa-check-circle'></i> ميزات النظام</h6>";
echo "<ul class='mb-0'>";
echo "<li>إدارة شاملة للمؤيدين مع الصور والبيانات الكاملة</li>";
echo "<li>إدارة المناطق الانتخابية والإداريين</li>";
echo "<li>تتبع المصروفات والفعاليات</li>";
echo "<li>نظام تقارير متقدم مع التصدير</li>";
echo "<li>واجهة متجاوبة مع الجوال</li>";
echo "<li>دعم كامل للغة العربية</li>";
echo "<li>نظام أمان متقدم</li>";
echo "</ul>";
echo "</div>";

echo "<div class='text-center mt-4'>";
echo "<a href='install.php' class='btn btn-primary btn-lg me-2'>";
echo "<i class='fas fa-play'></i> بدء التثبيت";
echo "</a>";
echo "<a href='login.php' class='btn btn-success btn-lg'>";
echo "<i class='fas fa-sign-in-alt'></i> تسجيل الدخول";
echo "</a>";
echo "</div>";

echo "<div class='text-center mt-3'>";
echo "<small class='text-muted'>نظام إدارة الحملة الانتخابية - الإصدار 1.0</small>";
echo "</div>";

echo "</div>";

echo "<script src='https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js'></script>";
echo "<script src='https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/js/all.min.js'></script>";
echo "</body>";
echo "</html>";

// إنشاء ملف معلومات النظام
$system_info = [
    'version' => '1.0.0',
    'created' => date('Y-m-d H:i:s'),
    'php_version' => PHP_VERSION,
    'database' => [
        'host' => 'localhost',
        'name' => 'irjnpfzw_mr',
        'user' => 'irjnpfzw_mr'
    ],
    'default_user' => [
        'username' => 'abd',
        'password' => 'abdabd',
        'phone' => '07719992716'
    ],
    'features' => [
        'supporters_management',
        'regions_management', 
        'admins_management',
        'expenses_tracking',
        'events_management',
        'competitors_analysis',
        'reports_generation',
        'data_export',
        'mobile_responsive',
        'arabic_support'
    ]
];

file_put_contents('system_info.json', json_encode($system_info, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE));
?>
