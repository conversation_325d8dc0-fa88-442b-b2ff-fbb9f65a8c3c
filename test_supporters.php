<?php
// اختبار إضافة المؤيدين

header('Content-Type: text/html; charset=utf-8');
ini_set('default_charset', 'utf-8');
mb_internal_encoding('UTF-8');

require_once 'config/config.php';
require_once 'config/database.php';

echo "<!DOCTYPE html>";
echo "<html lang='ar' dir='rtl'>";
echo "<head>";
echo "<meta charset='UTF-8'>";
echo "<meta name='viewport' content='width=device-width, initial-scale=1.0'>";
echo "<title>اختبار المؤيدين</title>";
echo "<link href='https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css' rel='stylesheet'>";
echo "<link href='https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css' rel='stylesheet'>";
echo "<style>";
echo "body { font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); min-height: 100vh; padding: 2rem; }";
echo ".test-card { background: white; border-radius: 15px; box-shadow: 0 10px 30px rgba(0,0,0,0.1); padding: 2rem; margin: 0 auto; max-width: 800px; }";
echo ".success { color: #28a745; }";
echo ".error { color: #dc3545; }";
echo "</style>";
echo "</head>";
echo "<body>";

echo "<div class='test-card'>";
echo "<h1 class='text-center mb-4'><i class='fas fa-users'></i> اختبار نظام المؤيدين</h1>";

// اختبار الاتصال بقاعدة البيانات
echo "<div class='alert alert-info'>";
echo "<h5>1. اختبار الاتصال بقاعدة البيانات</h5>";

try {
    $database = new Database();
    $db = $database->getConnection();
    
    if ($db) {
        echo "<p class='success'><i class='fas fa-check'></i> تم الاتصال بقاعدة البيانات بنجاح</p>";
        
        // اختبار جدول المناطق
        $regions = fetchAll("SELECT * FROM regions LIMIT 5");
        if ($regions) {
            echo "<p class='success'><i class='fas fa-check'></i> جدول المناطق يعمل بشكل صحيح</p>";
            echo "<ul>";
            foreach ($regions as $region) {
                echo "<li>" . htmlspecialchars($region['name']) . "</li>";
            }
            echo "</ul>";
        } else {
            echo "<p class='error'><i class='fas fa-times'></i> جدول المناطق فارغ أو لا يعمل</p>";
        }
        
        // اختبار جدول المؤيدين
        $supporters = fetchAll("SELECT * FROM supporters LIMIT 5");
        echo "<p class='success'><i class='fas fa-check'></i> جدول المؤيدين موجود - عدد المؤيدين: " . count($supporters) . "</p>";
        
    } else {
        echo "<p class='error'><i class='fas fa-times'></i> فشل الاتصال بقاعدة البيانات</p>";
    }
} catch (Exception $e) {
    echo "<p class='error'><i class='fas fa-times'></i> خطأ: " . htmlspecialchars($e->getMessage()) . "</p>";
}

echo "</div>";

// اختبار إضافة مؤيد تجريبي
if (isset($_POST['test_add'])) {
    echo "<div class='alert alert-warning'>";
    echo "<h5>2. اختبار إضافة مؤيد تجريبي</h5>";
    
    try {
        // بيانات تجريبية
        $test_data = [
            'أحمد محمد علي',
            'male',
            'married',
            '1990-01-01',
            'بكالوريوس',
            'مهندس',
            'بغداد - الكرادة',
            '07701234567',
            '123456789',
            'مدرسة الكرادة',
            1, // أول منطقة
            'مؤيد تجريبي',
            1  // أول مستخدم
        ];
        
        $sql = "INSERT INTO supporters (full_name, gender, marital_status, birth_date, education, profession, address, phone, voter_number, voting_center, region_id, notes, added_by, created_at) 
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, NOW())";
        
        $result = executeQuery($sql, $test_data);
        
        if ($result) {
            echo "<p class='success'><i class='fas fa-check'></i> تم إضافة المؤيد التجريبي بنجاح!</p>";
            
            // جلب المؤيد المضاف
            $added_supporter = fetchOne("SELECT * FROM supporters WHERE phone = ?", ['07701234567']);
            if ($added_supporter) {
                echo "<p class='success'><i class='fas fa-check'></i> تم التحقق من إضافة المؤيد:</p>";
                echo "<ul>";
                echo "<li><strong>الاسم:</strong> " . htmlspecialchars($added_supporter['full_name']) . "</li>";
                echo "<li><strong>الهاتف:</strong> " . htmlspecialchars($added_supporter['phone']) . "</li>";
                echo "<li><strong>العنوان:</strong> " . htmlspecialchars($added_supporter['address']) . "</li>";
                echo "</ul>";
            }
        } else {
            echo "<p class='error'><i class='fas fa-times'></i> فشل في إضافة المؤيد التجريبي</p>";
        }
        
    } catch (Exception $e) {
        echo "<p class='error'><i class='fas fa-times'></i> خطأ في إضافة المؤيد: " . htmlspecialchars($e->getMessage()) . "</p>";
    }
    
    echo "</div>";
}

// اختبار حذف المؤيد التجريبي
if (isset($_POST['test_delete'])) {
    echo "<div class='alert alert-secondary'>";
    echo "<h5>3. اختبار حذف المؤيد التجريبي</h5>";
    
    try {
        $result = executeQuery("DELETE FROM supporters WHERE phone = ?", ['07701234567']);
        
        if ($result) {
            echo "<p class='success'><i class='fas fa-check'></i> تم حذف المؤيد التجريبي بنجاح!</p>";
        } else {
            echo "<p class='error'><i class='fas fa-times'></i> فشل في حذف المؤيد التجريبي</p>";
        }
        
    } catch (Exception $e) {
        echo "<p class='error'><i class='fas fa-times'></i> خطأ في حذف المؤيد: " . htmlspecialchars($e->getMessage()) . "</p>";
    }
    
    echo "</div>";
}

// عرض المؤيدين الحاليين
echo "<div class='alert alert-primary'>";
echo "<h5>المؤيدين الحاليين في النظام</h5>";

try {
    $supporters = fetchAll("SELECT s.*, r.name as region_name FROM supporters s LEFT JOIN regions r ON s.region_id = r.id ORDER BY s.created_at DESC LIMIT 10");
    
    if ($supporters && count($supporters) > 0) {
        echo "<div class='table-responsive'>";
        echo "<table class='table table-striped'>";
        echo "<thead><tr><th>الاسم</th><th>الهاتف</th><th>المنطقة</th><th>تاريخ الإضافة</th></tr></thead>";
        echo "<tbody>";
        foreach ($supporters as $supporter) {
            echo "<tr>";
            echo "<td>" . htmlspecialchars($supporter['full_name']) . "</td>";
            echo "<td>" . htmlspecialchars($supporter['phone']) . "</td>";
            echo "<td>" . htmlspecialchars($supporter['region_name'] ?: 'غير محدد') . "</td>";
            echo "<td>" . date('Y-m-d H:i', strtotime($supporter['created_at'])) . "</td>";
            echo "</tr>";
        }
        echo "</tbody></table>";
        echo "</div>";
    } else {
        echo "<p class='text-muted'>لا يوجد مؤيدين في النظام حالياً</p>";
    }
    
} catch (Exception $e) {
    echo "<p class='error'><i class='fas fa-times'></i> خطأ في جلب المؤيدين: " . htmlspecialchars($e->getMessage()) . "</p>";
}

echo "</div>";

// أزرار الاختبار
echo "<div class='text-center mt-4'>";
echo "<form method='POST' action='' class='d-inline me-2'>";
echo "<button type='submit' name='test_add' class='btn btn-success btn-lg'>";
echo "<i class='fas fa-plus'></i> اختبار إضافة مؤيد";
echo "</button>";
echo "</form>";

echo "<form method='POST' action='' class='d-inline me-2'>";
echo "<button type='submit' name='test_delete' class='btn btn-danger btn-lg'>";
echo "<i class='fas fa-trash'></i> حذف المؤيد التجريبي";
echo "</button>";
echo "</form>";

echo "<a href='modules/supporters/supporters_simple.php' class='btn btn-primary btn-lg'>";
echo "<i class='fas fa-users'></i> صفحة المؤيدين";
echo "</a>";
echo "</div>";

// معلومات إضافية
echo "<div class='mt-4'>";
echo "<h6>معلومات النظام:</h6>";
echo "<ul>";
echo "<li><strong>إصدار PHP:</strong> " . PHP_VERSION . "</li>";
echo "<li><strong>ترميز الصفحة:</strong> UTF-8</li>";
echo "<li><strong>ترميز PHP:</strong> " . mb_internal_encoding() . "</li>";
echo "<li><strong>PDO MySQL:</strong> " . (extension_loaded('pdo_mysql') ? 'متوفر' : 'غير متوفر') . "</li>";
echo "</ul>";
echo "</div>";

echo "</div>";
echo "</body>";
echo "</html>";
?>
