<?php
// إصلاح مشكلة إضافة المؤيدين

header('Content-Type: text/html; charset=utf-8');
ini_set('default_charset', 'utf-8');
mb_internal_encoding('UTF-8');

require_once 'config/config.php';
require_once 'config/database.php';

echo "<!DOCTYPE html>";
echo "<html lang='ar' dir='rtl'>";
echo "<head>";
echo "<meta charset='UTF-8'>";
echo "<meta name='viewport' content='width=device-width, initial-scale=1.0'>";
echo "<title>إصلاح مشكلة المؤيدين</title>";
echo "<link href='https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css' rel='stylesheet'>";
echo "<link href='https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css' rel='stylesheet'>";
echo "<style>";
echo "body { font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); min-height: 100vh; padding: 2rem; }";
echo ".fix-card { background: white; border-radius: 15px; box-shadow: 0 10px 30px rgba(0,0,0,0.1); padding: 2rem; margin: 0 auto; max-width: 800px; }";
echo ".success { color: #28a745; }";
echo ".error { color: #dc3545; }";
echo "</style>";
echo "</head>";
echo "<body>";

echo "<div class='fix-card'>";
echo "<h1 class='text-center mb-4'><i class='fas fa-users'></i> إصلاح مشكلة المؤيدين</h1>";

if (isset($_POST['fix_supporters'])) {
    try {
        echo "<div class='alert alert-info'>";
        echo "<h5>جاري إصلاح مشكلة المؤيدين...</h5>";
        echo "</div>";

        // إنشاء ملف مؤيدين مبسط وعملي
        $supporters_content = '<?php
// تعيين الترميز العربي
header(\'Content-Type: text/html; charset=utf-8\');
ini_set(\'default_charset\', \'utf-8\');
mb_internal_encoding(\'UTF-8\');

require_once \'../../config/config.php\';
require_once \'../../config/database.php\';

// التحقق من تسجيل الدخول
if (!isLoggedIn()) {
    redirect(\'../../login.php\');
}

// معالجة إضافة مؤيد جديد
if ($_SERVER[\'REQUEST_METHOD\'] == \'POST\' && isset($_POST[\'action\']) && $_POST[\'action\'] == \'add\') {
    $full_name = sanitize($_POST[\'full_name\']);
    $gender = sanitize($_POST[\'gender\']);
    $marital_status = sanitize($_POST[\'marital_status\']);
    $birth_date = sanitize($_POST[\'birth_date\']);
    $education = sanitize($_POST[\'education\']);
    $profession = sanitize($_POST[\'profession\']);
    $address = sanitize($_POST[\'address\']);
    $phone = sanitize($_POST[\'phone\']);
    $voter_number = sanitize($_POST[\'voter_number\']);
    $voting_center = sanitize($_POST[\'voting_center\']);
    $region_id = (int)$_POST[\'region_id\'];
    $notes = sanitize($_POST[\'notes\']);
    $added_by = $_SESSION[\'user_id\'];

    // التحقق من صحة البيانات
    if (empty($full_name) || empty($phone) || empty($region_id) || empty($address) || empty($birth_date)) {
        showMessage(\'يرجى ملء جميع الحقول المطلوبة (الاسم، الهاتف، المنطقة، العنوان، تاريخ الميلاد)\', \'error\');
    } else {
        // التحقق من عدم تكرار رقم الناخب
        if (!empty($voter_number)) {
            $existing = fetchOne("SELECT id FROM supporters WHERE voter_number = ?", [$voter_number]);
            if ($existing) {
                showMessage(\'رقم الناخب موجود مسبقاً\', \'error\');
            } else {
                $insert_success = true;
            }
        } else {
            $insert_success = true;
        }

        if (isset($insert_success)) {
            $sql = "INSERT INTO supporters (full_name, gender, marital_status, birth_date, education, profession, address, phone, voter_number, voting_center, region_id, notes, added_by, created_at) 
                    VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, NOW())";
            
            $result = executeQuery($sql, [
                $full_name, $gender, $marital_status, $birth_date, $education, 
                $profession, $address, $phone, $voter_number, $voting_center, 
                $region_id, $notes, $added_by
            ]);

            if ($result) {
                showMessage(\'تم إضافة المؤيد بنجاح\', \'success\');
                redirect(\'supporters_fixed.php\');
            } else {
                showMessage(\'حدث خطأ أثناء إضافة المؤيد\', \'error\');
            }
        }
    }
}

// معالجة حذف مؤيد
if ($_SERVER[\'REQUEST_METHOD\'] == \'POST\' && isset($_POST[\'action\']) && $_POST[\'action\'] == \'delete\') {
    $id = (int)$_POST[\'id\'];
    $result = executeQuery("DELETE FROM supporters WHERE id = ?", [$id]);
    
    if ($result) {
        showMessage(\'تم حذف المؤيد بنجاح\', \'success\');
    } else {
        showMessage(\'حدث خطأ أثناء حذف المؤيد\', \'error\');
    }
    redirect(\'supporters_fixed.php\');
}

// جلب المؤيدين
$search = isset($_GET[\'search\']) ? sanitize($_GET[\'search\']) : \'\';
$region_filter = isset($_GET[\'region\']) ? (int)$_GET[\'region\'] : 0;

$sql = "SELECT s.*, r.name as region_name, u.full_name as added_by_name,
               YEAR(CURDATE()) - YEAR(s.birth_date) as age
        FROM supporters s
        LEFT JOIN regions r ON s.region_id = r.id
        LEFT JOIN users u ON s.added_by = u.id
        WHERE 1=1";

$params = [];

if (!empty($search)) {
    $sql .= " AND (s.full_name LIKE ? OR s.phone LIKE ? OR s.voter_number LIKE ?)";
    $search_term = \'%\' . $search . \'%\';
    $params = array_merge($params, [$search_term, $search_term, $search_term]);
}

if ($region_filter > 0) {
    $sql .= " AND s.region_id = ?";
    $params[] = $region_filter;
}

$sql .= " ORDER BY s.created_at DESC";

$supporters = fetchAll($sql, $params);

// جلب المناطق
$regions = fetchAll("SELECT * FROM regions ORDER BY name");

// إحصائيات
$total_supporters = count($supporters);
$male_supporters = count(array_filter($supporters, function($s) { return $s[\'gender\'] == \'male\'; }));
$female_supporters = count(array_filter($supporters, function($s) { return $s[\'gender\'] == \'female\'; }));
?>';

        // كتابة الملف
        file_put_contents('modules/supporters/supporters_fixed.php', $supporters_content);
        
        echo "<div class='alert alert-success'>";
        echo "<h6>تم إنشاء ملف المؤيدين المُصلح</h6>";
        echo "<p class='success'><i class='fas fa-check'></i> تم إنشاء: modules/supporters/supporters_fixed.php</p>";
        echo "</div>";

        // إنشاء ملف HTML للصفحة
        $html_content = file_get_contents('modules/supporters/supporters_simple.php');
        $html_content = str_replace('supporters_simple.php', 'supporters_fixed.php', $html_content);
        
        // إضافة HTML للملف
        $supporters_content .= "\n\n" . substr($html_content, strpos($html_content, '<!DOCTYPE html>'));
        file_put_contents('modules/supporters/supporters_fixed.php', $supporters_content);
        
        echo "<div class='alert alert-info'>";
        echo "<h6>تم إضافة واجهة HTML للصفحة</h6>";
        echo "<p class='success'><i class='fas fa-check'></i> الصفحة جاهزة للاستخدام</p>";
        echo "</div>";

        echo "<div class='alert alert-success'>";
        echo "<h3><i class='fas fa-check-circle'></i> تم إصلاح مشكلة المؤيدين بنجاح!</h3>";
        echo "<p>يمكنك الآن استخدام الصفحة المُصلحة لإضافة المؤيدين</p>";
        echo "</div>";
        
    } catch (Exception $e) {
        echo "<div class='alert alert-danger'>";
        echo "<h5>خطأ في الإصلاح:</h5>";
        echo "<p>" . htmlspecialchars($e->getMessage()) . "</p>";
        echo "</div>";
    }
    
    echo "<div class='text-center mt-4'>";
    echo "<a href='modules/supporters/supporters_fixed.php' class='btn btn-primary btn-lg me-2'><i class='fas fa-users'></i> صفحة المؤيدين المُصلحة</a>";
    echo "<a href='modules/supporters/supporters.php' class='btn btn-secondary btn-lg'><i class='fas fa-users'></i> الصفحة الأصلية</a>";
    echo "</div>";
    
} else {
    // عرض نموذج التأكيد
    echo "<div class='alert alert-warning'>";
    echo "<h5><i class='fas fa-exclamation-triangle'></i> إصلاح مشكلة المؤيدين</h5>";
    echo "<p>هذا الإجراء سيقوم بـ:</p>";
    echo "<ul>";
    echo "<li>✅ إنشاء ملف مؤيدين مُصلح وعملي</li>";
    echo "<li>✅ إصلاح مشكلة إضافة المؤيدين</li>";
    echo "<li>✅ إصلاح مشكلة عرض المؤيدين</li>";
    echo "<li>✅ إضافة نموذج إضافة مبسط</li>";
    echo "<li>✅ إضافة البحث والفلترة</li>";
    echo "<li>✅ إضافة الإحصائيات</li>";
    echo "</ul>";
    echo "<p><strong>آمن:</strong> لا يؤثر على الملف الأصلي</p>";
    echo "</div>";
    
    echo "<form method='POST' action=''>";
    echo "<div class='text-center'>";
    echo "<button type='submit' name='fix_supporters' class='btn btn-success btn-lg'>";
    echo "<i class='fas fa-tools'></i> إصلاح مشكلة المؤيدين";
    echo "</button>";
    echo "</div>";
    echo "</form>";
    
    echo "<div class='text-center mt-4'>";
    echo "<a href='test_supporters.php' class='btn btn-info me-2'><i class='fas fa-cog'></i> اختبار النظام</a>";
    echo "<a href='modules/supporters/supporters.php' class='btn btn-warning'><i class='fas fa-users'></i> الصفحة الأصلية</a>";
    echo "</div>";
}

echo "</div>";
echo "</body>";
echo "</html>";
?>
