// JavaScript لصفحة الإداريين

$(document).ready(function() {
    // تهيئة جدول البيانات
    initializeDataTable();
    
    // تهيئة التحقق من النماذج
    initializeFormValidation();
});

// تهيئة جدول البيانات
function initializeDataTable() {
    $('#adminsTable').DataTable({
        language: {
            url: 'https://cdn.datatables.net/plug-ins/1.13.6/i18n/ar.json'
        },
        responsive: true,
        pageLength: 25,
        lengthMenu: [[10, 25, 50, 100, -1], [10, 25, 50, 100, "الكل"]],
        order: [[6, 'desc']], // ترتيب حسب تاريخ الإضافة
        columnDefs: [
            { orderable: false, targets: [7] }, // عدم ترتيب الإجراءات
            { searchable: false, targets: [7] }, // عدم البحث في الإجراءات
            { className: "text-center", targets: "_all" }
        ]
    });
}

// تهيئة التحقق من النماذج
function initializeFormValidation() {
    const forms = document.querySelectorAll('.needs-validation');
    
    forms.forEach(function(form) {
        form.addEventListener('submit', function(event) {
            if (!form.checkValidity()) {
                event.preventDefault();
                event.stopPropagation();
            }
            
            form.classList.add('was-validated');
        });
    });
    
    // التحقق من رقم الهاتف العراقي
    const phoneInputs = document.querySelectorAll('input[name="phone"]');
    phoneInputs.forEach(function(input) {
        input.addEventListener('input', function() {
            validateIraqiPhone(this);
        });
    });
    
    // التحقق من اسم المستخدم
    const usernameInputs = document.querySelectorAll('input[name="username"]');
    usernameInputs.forEach(function(input) {
        input.addEventListener('blur', function() {
            checkUsername(this);
        });
    });
}

// التحقق من رقم الهاتف العراقي
function validateIraqiPhone(input) {
    const phonePattern = /^07[3-9][0-9]{8}$/;
    const isValid = phonePattern.test(input.value);
    
    if (input.value && !isValid) {
        input.setCustomValidity('رقم الهاتف غير صحيح. يجب أن يبدأ بـ 07 ويتكون من 11 رقم');
        input.classList.add('is-invalid');
        input.classList.remove('is-valid');
    } else {
        input.setCustomValidity('');
        input.classList.remove('is-invalid');
        if (input.value) {
            input.classList.add('is-valid');
        }
    }
}

// التحقق من اسم المستخدم
function checkUsername(input) {
    if (input.value) {
        fetch('check_username.php', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                username: input.value,
                admin_id: input.dataset.adminId || null
            })
        })
        .then(response => response.json())
        .then(data => {
            if (data.exists) {
                input.setCustomValidity('اسم المستخدم موجود مسبقاً');
                input.classList.add('is-invalid');
                input.classList.remove('is-valid');
                showToast('اسم المستخدم موجود مسبقاً', 'error');
            } else {
                input.setCustomValidity('');
                input.classList.remove('is-invalid');
                input.classList.add('is-valid');
            }
        })
        .catch(error => {
            console.error('خطأ في التحقق من اسم المستخدم:', error);
        });
    }
}

// عرض تفاصيل الإداري
function viewAdmin(id) {
    showLoading(true);
    
    fetch(`get_admin.php?id=${id}`)
        .then(response => response.json())
        .then(data => {
            showLoading(false);
            if (data.success) {
                displayAdminDetails(data.admin);
            } else {
                showToast('حدث خطأ في جلب بيانات الإداري', 'error');
            }
        })
        .catch(error => {
            showLoading(false);
            console.error('خطأ:', error);
            showToast('حدث خطأ في الاتصال', 'error');
        });
}

// عرض تفاصيل الإداري في نافذة منبثقة
function displayAdminDetails(admin) {
    const modal = new bootstrap.Modal(document.getElementById('viewAdminModal'));
    
    // ملء البيانات
    document.getElementById('viewFullName').textContent = admin.full_name;
    document.getElementById('viewUsername').textContent = admin.username;
    document.getElementById('viewPhone').textContent = admin.phone;
    document.getElementById('viewRegion').textContent = admin.region_name || 'جميع المناطق';
    document.getElementById('viewSupportersCount').textContent = admin.supporters_count;
    document.getElementById('viewStatus').innerHTML = admin.is_active ? 
        '<span class="badge bg-success">نشط</span>' : 
        '<span class="badge bg-danger">غير نشط</span>';
    document.getElementById('viewCreatedAt').textContent = admin.created_at;
    document.getElementById('viewLastLogin').textContent = admin.last_login || 'لم يسجل دخول بعد';
    
    modal.show();
}

// تعديل الإداري
function editAdmin(id) {
    showLoading(true);
    
    fetch(`get_admin.php?id=${id}`)
        .then(response => response.json())
        .then(data => {
            showLoading(false);
            if (data.success) {
                fillEditForm(data.admin);
                const modal = new bootstrap.Modal(document.getElementById('editAdminModal'));
                modal.show();
            } else {
                showToast('حدث خطأ في جلب بيانات الإداري', 'error');
            }
        })
        .catch(error => {
            showLoading(false);
            console.error('خطأ:', error);
            showToast('حدث خطأ في الاتصال', 'error');
        });
}

// ملء نموذج التعديل
function fillEditForm(admin) {
    document.getElementById('editId').value = admin.id;
    document.getElementById('editFullName').value = admin.full_name;
    document.getElementById('editUsername').value = admin.username;
    document.getElementById('editPhone').value = admin.phone;
    document.getElementById('editRegionId').value = admin.region_id || '';
    
    // إضافة معرف الإداري للتحقق من التكرار
    document.getElementById('editUsername').dataset.adminId = admin.id;
    document.getElementById('editPhone').dataset.adminId = admin.id;
}

// حذف الإداري
function deleteAdmin(id) {
    if (confirm('هل أنت متأكد من حذف هذا الإداري؟ لا يمكن التراجع عن هذا الإجراء.')) {
        showLoading(true);
        
        const formData = new FormData();
        formData.append('action', 'delete');
        formData.append('id', id);
        
        fetch('admins.php', {
            method: 'POST',
            body: formData
        })
        .then(response => response.text())
        .then(data => {
            showLoading(false);
            location.reload();
        })
        .catch(error => {
            showLoading(false);
            console.error('خطأ:', error);
            showToast('حدث خطأ أثناء الحذف', 'error');
        });
    }
}

// تغيير حالة الإداري
function toggleAdminStatus(id, status) {
    const action = status ? 'تفعيل' : 'إلغاء تفعيل';
    
    if (confirm(`هل أنت متأكد من ${action} هذا الإداري؟`)) {
        showLoading(true);
        
        const formData = new FormData();
        formData.append('action', 'toggle_status');
        formData.append('id', id);
        formData.append('status', status);
        
        fetch('admins.php', {
            method: 'POST',
            body: formData
        })
        .then(response => response.text())
        .then(data => {
            showLoading(false);
            location.reload();
        })
        .catch(error => {
            showLoading(false);
            console.error('خطأ:', error);
            showToast('حدث خطأ أثناء تغيير الحالة', 'error');
        });
    }
}

// تصدير الإداريين
function exportAdmins() {
    window.location.href = 'export.php?type=excel';
}

// إرسال رسالة جماعية
function sendBulkMessage() {
    const modal = new bootstrap.Modal(document.getElementById('bulkMessageModal') || createBulkMessageModal());
    modal.show();
}

// إنشاء نافذة الرسالة الجماعية
function createBulkMessageModal() {
    const modalHTML = `
        <div class="modal fade" id="bulkMessageModal" tabindex="-1">
            <div class="modal-dialog">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title">إرسال رسالة جماعية</h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                    </div>
                    <form method="POST" action="send_message.php">
                        <div class="modal-body">
                            <div class="mb-3">
                                <label for="messageRecipients" class="form-label">المستقبلين</label>
                                <select class="form-select" id="messageRecipients" name="recipients" required>
                                    <option value="all">جميع الإداريين</option>
                                    <option value="active">الإداريين النشطين فقط</option>
                                    <option value="inactive">الإداريين غير النشطين فقط</option>
                                </select>
                            </div>
                            <div class="mb-3">
                                <label for="messageSubject" class="form-label">موضوع الرسالة</label>
                                <input type="text" class="form-control" id="messageSubject" name="subject" required>
                            </div>
                            <div class="mb-3">
                                <label for="messageContent" class="form-label">محتوى الرسالة</label>
                                <textarea class="form-control" id="messageContent" name="content" rows="5" required></textarea>
                            </div>
                        </div>
                        <div class="modal-footer">
                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                            <button type="submit" class="btn btn-primary">إرسال الرسالة</button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    `;
    document.body.insertAdjacentHTML('beforeend', modalHTML);
    return document.getElementById('bulkMessageModal');
}

// إظهار مؤشر التحميل
function showLoading(show) {
    const loader = document.getElementById('loadingOverlay') || createLoadingOverlay();
    loader.style.display = show ? 'flex' : 'none';
}

// إنشاء مؤشر التحميل
function createLoadingOverlay() {
    const loaderHTML = `
        <div id="loadingOverlay" class="loading-overlay">
            <div class="loading-spinner"></div>
        </div>
    `;
    document.body.insertAdjacentHTML('beforeend', loaderHTML);
    return document.getElementById('loadingOverlay');
}

// دالة عامة لإظهار الرسائل
function showToast(message, type = 'info') {
    const toastContainer = getOrCreateToastContainer();
    const toast = document.createElement('div');
    toast.className = `toast align-items-center text-white bg-${getBootstrapColor(type)} border-0`;
    toast.setAttribute('role', 'alert');
    toast.innerHTML = `
        <div class="d-flex">
            <div class="toast-body">
                <i class="fas fa-${getIcon(type)} me-2"></i>
                ${message}
            </div>
            <button type="button" class="btn-close btn-close-white me-2 m-auto" data-bs-dismiss="toast"></button>
        </div>
    `;
    
    toastContainer.appendChild(toast);
    const bsToast = new bootstrap.Toast(toast);
    bsToast.show();
    
    toast.addEventListener('hidden.bs.toast', function() {
        toast.remove();
    });
}

// دالة للحصول على حاوية التوست
function getOrCreateToastContainer() {
    let container = document.querySelector('.toast-container');
    if (!container) {
        container = document.createElement('div');
        container.className = 'toast-container position-fixed top-0 end-0 p-3';
        container.style.zIndex = '9999';
        document.body.appendChild(container);
    }
    return container;
}

// دالة للحصول على لون Bootstrap
function getBootstrapColor(type) {
    const colors = {
        'success': 'success',
        'error': 'danger',
        'warning': 'warning',
        'info': 'info'
    };
    return colors[type] || 'info';
}

// دالة للحصول على الأيقونة
function getIcon(type) {
    const icons = {
        'success': 'check-circle',
        'error': 'exclamation-triangle',
        'warning': 'exclamation-circle',
        'info': 'info-circle'
    };
    return icons[type] || 'info-circle';
}
