<?php
// إنشاء قاعدة بيانات جديدة
error_reporting(E_ALL);
ini_set('display_errors', 1);
header('Content-Type: text/html; charset=utf-8');

echo "<!DOCTYPE html>";
echo "<html lang='ar' dir='rtl'>";
echo "<head>";
echo "<meta charset='UTF-8'>";
echo "<meta name='viewport' content='width=device-width, initial-scale=1.0'>";
echo "<title>إنشاء قاعدة بيانات جديدة</title>";
echo "<link href='https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css' rel='stylesheet'>";
echo "<style>body { font-family: Arial, sans-serif; padding: 20px; background: #f8f9fa; }</style>";
echo "</head>";
echo "<body>";

echo "<div class='container'>";
echo "<div class='row justify-content-center'>";
echo "<div class='col-md-10'>";

echo "<div class='card'>";
echo "<div class='card-header bg-primary text-white'>";
echo "<h3><i class='fas fa-plus-circle'></i> إنشاء قاعدة بيانات جديدة</h3>";
echo "</div>";
echo "<div class='card-body'>";

echo "<div class='alert alert-info'>";
echo "<h5><i class='fas fa-info-circle'></i> خطوات إنشاء قاعدة بيانات جديدة في cPanel:</h5>";
echo "</div>";

echo "<div class='row'>";

// الخطوة 1
echo "<div class='col-md-6 mb-4'>";
echo "<div class='card border-primary'>";
echo "<div class='card-header bg-primary text-white'>";
echo "<h6>الخطوة 1: إنشاء قاعدة البيانات</h6>";
echo "</div>";
echo "<div class='card-body'>";
echo "<ol>";
echo "<li>ادخل إلى <strong>cPanel</strong></li>";
echo "<li>ابحث عن <strong>MySQL Databases</strong></li>";
echo "<li>في قسم 'Create New Database':</li>";
echo "<ul>";
echo "<li>اكتب اسم قاعدة البيانات: <code>campaign</code></li>";
echo "<li>اضغط 'Create Database'</li>";
echo "</ul>";
echo "<li>ستصبح قاعدة البيانات: <code>username_campaign</code></li>";
echo "</ol>";
echo "</div>";
echo "</div>";
echo "</div>";

// الخطوة 2
echo "<div class='col-md-6 mb-4'>";
echo "<div class='card border-success'>";
echo "<div class='card-header bg-success text-white'>";
echo "<h6>الخطوة 2: إنشاء مستخدم</h6>";
echo "</div>";
echo "<div class='card-body'>";
echo "<ol>";
echo "<li>في قسم 'MySQL Users':</li>";
echo "<ul>";
echo "<li>اسم المستخدم: <code>campaign_user</code></li>";
echo "<li>كلمة المرور: <code>Campaign123!</code></li>";
echo "<li>اضغط 'Create User'</li>";
echo "</ul>";
echo "<li>ستصبح المستخدم: <code>username_campaign_user</code></li>";
echo "</ol>";
echo "</div>";
echo "</div>";
echo "</div>";

// الخطوة 3
echo "<div class='col-md-6 mb-4'>";
echo "<div class='card border-warning'>";
echo "<div class='card-header bg-warning text-dark'>";
echo "<h6>الخطوة 3: ربط المستخدم بقاعدة البيانات</h6>";
echo "</div>";
echo "<div class='card-body'>";
echo "<ol>";
echo "<li>في قسم 'Add User To Database':</li>";
echo "<ul>";
echo "<li>اختر المستخدم الذي أنشأته</li>";
echo "<li>اختر قاعدة البيانات التي أنشأتها</li>";
echo "<li>اضغط 'Add'</li>";
echo "</ul>";
echo "<li>اختر <strong>ALL PRIVILEGES</strong></li>";
echo "<li>اضغط 'Make Changes'</li>";
echo "</ol>";
echo "</div>";
echo "</div>";
echo "</div>";

// الخطوة 4
echo "<div class='col-md-6 mb-4'>";
echo "<div class='card border-info'>";
echo "<div class='card-header bg-info text-white'>";
echo "<h6>الخطوة 4: استخدام المعلومات الجديدة</h6>";
echo "</div>";
echo "<div class='card-body'>";
echo "<p><strong>استخدم هذه المعلومات في النظام:</strong></p>";
echo "<ul>";
echo "<li><strong>Host:</strong> <code>localhost</code></li>";
echo "<li><strong>Database:</strong> <code>username_campaign</code></li>";
echo "<li><strong>Username:</strong> <code>username_campaign_user</code></li>";
echo "<li><strong>Password:</strong> <code>Campaign123!</code></li>";
echo "</ul>";
echo "<p class='text-muted'><small>استبدل 'username' باسم المستخدم الخاص بك في cPanel</small></p>";
echo "</div>";
echo "</div>";
echo "</div>";

echo "</div>";

// نموذج اختبار سريع
echo "<div class='alert alert-warning'>";
echo "<h5><i class='fas fa-flask'></i> اختبار سريع للمعلومات الحالية:</h5>";
echo "</div>";

echo "<form method='POST'>";
echo "<div class='row'>";
echo "<div class='col-md-3'>";
echo "<input type='text' class='form-control' name='host' placeholder='localhost' value='localhost'>";
echo "</div>";
echo "<div class='col-md-3'>";
echo "<input type='text' class='form-control' name='dbname' placeholder='اسم قاعدة البيانات' value='irjnpfzw_mr'>";
echo "</div>";
echo "<div class='col-md-3'>";
echo "<input type='text' class='form-control' name='username' placeholder='اسم المستخدم'>";
echo "</div>";
echo "<div class='col-md-3'>";
echo "<input type='password' class='form-control' name='password' placeholder='كلمة المرور'>";
echo "</div>";
echo "</div>";
echo "<div class='text-center mt-3'>";
echo "<button type='submit' class='btn btn-primary'>اختبار الاتصال</button>";
echo "</div>";
echo "</form>";

// معالجة الاختبار
if ($_POST) {
    $host = $_POST['host'] ?? 'localhost';
    $dbname = $_POST['dbname'] ?? '';
    $username = $_POST['username'] ?? '';
    $password = $_POST['password'] ?? '';
    
    echo "<div class='mt-4'>";
    echo "<h5>نتيجة الاختبار:</h5>";
    
    try {
        $pdo = new PDO("mysql:host=$host;dbname=$dbname;charset=utf8mb4", $username, $password);
        $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
        
        echo "<div class='alert alert-success'>";
        echo "<h6>✅ نجح الاتصال!</h6>";
        echo "<p>المعلومات صحيحة:</p>";
        echo "<ul>";
        echo "<li><strong>Host:</strong> $host</li>";
        echo "<li><strong>Database:</strong> $dbname</li>";
        echo "<li><strong>Username:</strong> $username</li>";
        echo "<li><strong>Password:</strong> " . str_repeat('*', strlen($password)) . "</li>";
        echo "</ul>";
        echo "<a href='fix_database_config.php?host=$host&dbname=$dbname&username=$username&password=$password' class='btn btn-success'>استخدم هذه المعلومات</a>";
        echo "</div>";
        
    } catch (Exception $e) {
        echo "<div class='alert alert-danger'>";
        echo "<h6>❌ فشل الاتصال</h6>";
        echo "<p>الخطأ: " . $e->getMessage() . "</p>";
        echo "</div>";
    }
    echo "</div>";
}

// اقتراحات أسماء المستخدمين الشائعة
echo "<div class='alert alert-secondary'>";
echo "<h5><i class='fas fa-lightbulb'></i> اقتراحات أسماء المستخدمين الشائعة:</h5>";
echo "<div class='row'>";

$suggestions = [
    'irjnpfzw_mr',
    'irjnpfzw_abd',
    'irjnpfzw_user',
    'irjnpfzw_admin',
    'irjnpfzw_root'
];

foreach ($suggestions as $suggestion) {
    echo "<div class='col-md-4 mb-2'>";
    echo "<button type='button' class='btn btn-outline-secondary btn-sm w-100' onclick='document.querySelector(\"[name=username]\").value=\"$suggestion\"'>";
    echo "$suggestion";
    echo "</button>";
    echo "</div>";
}

echo "</div>";
echo "</div>";

// معلومات إضافية
echo "<div class='alert alert-info'>";
echo "<h5><i class='fas fa-question-circle'></i> لا تتذكر معلومات cPanel؟</h5>";
echo "<p>إذا كنت لا تتذكر معلومات cPanel:</p>";
echo "<ol>";
echo "<li>تواصل مع مزود الاستضافة</li>";
echo "<li>ابحث في إيميلاتك عن رسالة الترحيب من الاستضافة</li>";
echo "<li>جرب الدخول إلى: <code>yourdomain.com/cpanel</code></li>";
echo "<li>أو: <code>yourdomain.com:2083</code></li>";
echo "</ol>";
echo "</div>";

echo "</div>";
echo "</div>";

echo "</div>";
echo "</div>";
echo "</div>";

echo "</body>";
echo "</html>";
?>
