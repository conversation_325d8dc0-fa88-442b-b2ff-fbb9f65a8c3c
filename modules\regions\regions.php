<?php
require_once '../../config/config.php';
require_once '../../config/database.php';

// التحقق من تسجيل الدخول
if (!isLoggedIn()) {
    redirect('../../login.php');
}

// التحقق من الصلاحيات - المناطق للمرشح فقط
if (!isCandidate()) {
    showMessage('ليس لديك صلاحية للوصول إلى هذه الصفحة', 'error');
    redirect('../../dashboard.php');
}

// معالجة الطلبات
if ($_SERVER['REQUEST_METHOD'] == 'POST') {
    if (isset($_POST['action'])) {
        switch ($_POST['action']) {
            case 'add':
                addRegion();
                break;
            case 'edit':
                editRegion();
                break;
            case 'delete':
                deleteRegion();
                break;
        }
    }
}

// جلب المناطق مع إحصائيات المؤيدين
$sql = "SELECT r.*, 
               COUNT(s.id) as supporters_count,
               COUNT(CASE WHEN s.gender = 'male' THEN 1 END) as male_count,
               COUNT(CASE WHEN s.gender = 'female' THEN 1 END) as female_count
        FROM regions r 
        LEFT JOIN supporters s ON r.id = s.region_id 
        GROUP BY r.id 
        ORDER BY r.name";

$regions = fetchAll($sql);

// إحصائيات عامة
$total_regions = count($regions);
$total_supporters = fetchOne("SELECT COUNT(*) as count FROM supporters")['count'];
$regions_with_supporters = fetchOne("SELECT COUNT(DISTINCT region_id) as count FROM supporters WHERE region_id IS NOT NULL")['count'];

function addRegion() {
    $name = sanitize($_POST['name']);
    $description = sanitize($_POST['description']);
    
    if (empty($name)) {
        showMessage('يرجى إدخال اسم المنطقة', 'error');
        return;
    }
    
    // التحقق من عدم تكرار الاسم
    $existing = fetchOne("SELECT id FROM regions WHERE name = ?", [$name]);
    if ($existing) {
        showMessage('اسم المنطقة موجود مسبقاً', 'error');
        return;
    }
    
    $sql = "INSERT INTO regions (name, description) VALUES (?, ?)";
    $result = executeQuery($sql, [$name, $description]);
    
    if ($result) {
        showMessage('تم إضافة المنطقة بنجاح', 'success');
        redirect('regions.php');
    } else {
        showMessage('حدث خطأ أثناء إضافة المنطقة', 'error');
    }
}

function editRegion() {
    $id = (int)$_POST['id'];
    $name = sanitize($_POST['name']);
    $description = sanitize($_POST['description']);
    
    if (empty($name)) {
        showMessage('يرجى إدخال اسم المنطقة', 'error');
        return;
    }
    
    // التحقق من عدم تكرار الاسم
    $existing = fetchOne("SELECT id FROM regions WHERE name = ? AND id != ?", [$name, $id]);
    if ($existing) {
        showMessage('اسم المنطقة موجود مسبقاً', 'error');
        return;
    }
    
    $sql = "UPDATE regions SET name = ?, description = ? WHERE id = ?";
    $result = executeQuery($sql, [$name, $description, $id]);
    
    if ($result) {
        showMessage('تم تحديث المنطقة بنجاح', 'success');
        redirect('regions.php');
    } else {
        showMessage('حدث خطأ أثناء تحديث المنطقة', 'error');
    }
}

function deleteRegion() {
    $id = (int)$_POST['id'];
    
    // التحقق من وجود مؤيدين في المنطقة
    $supporters_count = fetchOne("SELECT COUNT(*) as count FROM supporters WHERE region_id = ?", [$id])['count'];
    if ($supporters_count > 0) {
        showMessage("لا يمكن حذف المنطقة لأنها تحتوي على $supporters_count مؤيد", 'error');
        return;
    }
    
    $result = executeQuery("DELETE FROM regions WHERE id = ?", [$id]);
    
    if ($result) {
        showMessage('تم حذف المنطقة بنجاح', 'success');
    } else {
        showMessage('حدث خطأ أثناء حذف المنطقة', 'error');
    }
}
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إدارة المناطق - <?php echo SITE_NAME; ?></title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="https://cdn.datatables.net/1.13.6/css/dataTables.bootstrap5.min.css" rel="stylesheet">
    <link href="../../assets/css/dashboard.css" rel="stylesheet">
    <link href="../../assets/css/regions.css" rel="stylesheet">
</head>
<body>
    <!-- شريط التنقل العلوي -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary fixed-top">
        <div class="container-fluid">
            <a class="navbar-brand" href="../../dashboard.php">
                <i class="fas fa-vote-yea me-2"></i>
                نظام إدارة الحملة الانتخابية
            </a>
            
            <div class="navbar-nav ms-auto">
                <div class="nav-item dropdown">
                    <a class="nav-link dropdown-toggle" href="#" id="userDropdown" role="button" data-bs-toggle="dropdown">
                        <i class="fas fa-user-circle me-1"></i>
                        <?php echo htmlspecialchars($_SESSION['full_name']); ?>
                    </a>
                    <ul class="dropdown-menu dropdown-menu-end">
                        <li><a class="dropdown-item" href="../../logout.php"><i class="fas fa-sign-out-alt me-2"></i>تسجيل الخروج</a></li>
                    </ul>
                </div>
            </div>
        </div>
    </nav>

    <!-- المحتوى الرئيسي -->
    <div class="container-fluid main-content">
        <div class="row">
            <!-- الشريط الجانبي -->
            <nav class="col-md-3 col-lg-2 d-md-block sidebar">
                <div class="position-sticky pt-3">
                    <ul class="nav flex-column">
                        <li class="nav-item">
                            <a class="nav-link" href="../../dashboard.php">
                                <i class="fas fa-tachometer-alt me-2"></i>
                                لوحة التحكم
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="../supporters/supporters.php">
                                <i class="fas fa-users me-2"></i>
                                المؤيدين
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link active" href="regions.php">
                                <i class="fas fa-map-marked-alt me-2"></i>
                                المناطق
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="../admins/admins.php">
                                <i class="fas fa-user-tie me-2"></i>
                                الإداريين
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="../expenses/expenses.php">
                                <i class="fas fa-money-bill-wave me-2"></i>
                                المصروفات
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="../events/events.php">
                                <i class="fas fa-calendar-alt me-2"></i>
                                الفعاليات
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="../competitors/competitors.php">
                                <i class="fas fa-chess me-2"></i>
                                المنافسين
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="../reports/reports.php">
                                <i class="fas fa-chart-bar me-2"></i>
                                التقارير
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="../settings/settings.php">
                                <i class="fas fa-cog me-2"></i>
                                الإعدادات
                            </a>
                        </li>
                    </ul>
                </div>
            </nav>

            <!-- المحتوى الرئيسي -->
            <main class="col-md-9 ms-sm-auto col-lg-10 px-md-4">
                <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                    <h1 class="h2">
                        <i class="fas fa-map-marked-alt me-2"></i>
                        إدارة المناطق
                    </h1>
                    <div class="btn-toolbar mb-2 mb-md-0">
                        <div class="btn-group me-2">
                            <button type="button" class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#addRegionModal">
                                <i class="fas fa-plus"></i> إضافة منطقة
                            </button>
                            <button type="button" class="btn btn-success" onclick="exportRegions()">
                                <i class="fas fa-file-excel"></i> تصدير Excel
                            </button>
                            <button type="button" class="btn btn-danger" onclick="exportRegionsPDF()">
                                <i class="fas fa-file-pdf"></i> تصدير PDF
                            </button>
                        </div>
                    </div>
                </div>

                <?php displayMessage(); ?>

                <!-- الإحصائيات -->
                <div class="row mb-4">
                    <div class="col-md-3">
                        <div class="card bg-primary text-white">
                            <div class="card-body">
                                <div class="d-flex justify-content-between">
                                    <div>
                                        <h4><?php echo number_format($total_regions); ?></h4>
                                        <p class="mb-0">إجمالي المناطق</p>
                                    </div>
                                    <div class="align-self-center">
                                        <i class="fas fa-map-marked-alt fa-2x"></i>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="card bg-success text-white">
                            <div class="card-body">
                                <div class="d-flex justify-content-between">
                                    <div>
                                        <h4><?php echo number_format($regions_with_supporters); ?></h4>
                                        <p class="mb-0">مناطق بها مؤيدين</p>
                                    </div>
                                    <div class="align-self-center">
                                        <i class="fas fa-users fa-2x"></i>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="card bg-info text-white">
                            <div class="card-body">
                                <div class="d-flex justify-content-between">
                                    <div>
                                        <h4><?php echo number_format($total_supporters); ?></h4>
                                        <p class="mb-0">إجمالي المؤيدين</p>
                                    </div>
                                    <div class="align-self-center">
                                        <i class="fas fa-user-friends fa-2x"></i>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="card bg-warning text-white">
                            <div class="card-body">
                                <div class="d-flex justify-content-between">
                                    <div>
                                        <h4><?php echo $total_regions > 0 ? round($total_supporters / $total_regions, 1) : 0; ?></h4>
                                        <p class="mb-0">متوسط المؤيدين/منطقة</p>
                                    </div>
                                    <div class="align-self-center">
                                        <i class="fas fa-chart-line fa-2x"></i>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- بطاقات المناطق -->
                <div class="row mb-4">
                    <?php foreach ($regions as $region): ?>
                    <div class="col-lg-4 col-md-6 mb-4">
                        <div class="card region-card h-100" data-region-id="<?php echo $region['id']; ?>">
                            <div class="card-header bg-gradient-primary text-white">
                                <h5 class="card-title mb-0">
                                    <i class="fas fa-map-marker-alt me-2"></i>
                                    <?php echo htmlspecialchars($region['name']); ?>
                                </h5>
                            </div>
                            <div class="card-body">
                                <p class="card-text text-muted">
                                    <?php echo htmlspecialchars($region['description']) ?: 'لا يوجد وصف'; ?>
                                </p>
                                
                                <div class="row text-center">
                                    <div class="col-4">
                                        <div class="stat-item">
                                            <h4 class="text-primary"><?php echo $region['supporters_count']; ?></h4>
                                            <small class="text-muted">إجمالي المؤيدين</small>
                                        </div>
                                    </div>
                                    <div class="col-4">
                                        <div class="stat-item">
                                            <h4 class="text-info"><?php echo $region['male_count']; ?></h4>
                                            <small class="text-muted">ذكور</small>
                                        </div>
                                    </div>
                                    <div class="col-4">
                                        <div class="stat-item">
                                            <h4 class="text-pink"><?php echo $region['female_count']; ?></h4>
                                            <small class="text-muted">إناث</small>
                                        </div>
                                    </div>
                                </div>
                                
                                <?php if ($region['supporters_count'] > 0): ?>
                                <div class="progress mt-3">
                                    <div class="progress-bar bg-info" style="width: <?php echo ($region['male_count'] / $region['supporters_count']) * 100; ?>%"></div>
                                    <div class="progress-bar bg-pink" style="width: <?php echo ($region['female_count'] / $region['supporters_count']) * 100; ?>%"></div>
                                </div>
                                <small class="text-muted">نسبة الذكور إلى الإناث</small>
                                <?php endif; ?>
                            </div>
                            <div class="card-footer bg-transparent">
                                <div class="btn-group w-100" role="group">
                                    <button type="button" class="btn btn-outline-primary btn-sm" 
                                            onclick="viewRegionSupporters(<?php echo $region['id']; ?>)">
                                        <i class="fas fa-eye"></i> عرض المؤيدين
                                    </button>
                                    <button type="button" class="btn btn-outline-warning btn-sm" 
                                            onclick="editRegion(<?php echo $region['id']; ?>)">
                                        <i class="fas fa-edit"></i> تعديل
                                    </button>
                                    <button type="button" class="btn btn-outline-danger btn-sm" 
                                            onclick="deleteRegion(<?php echo $region['id']; ?>)">
                                        <i class="fas fa-trash"></i> حذف
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                    <?php endforeach; ?>
                </div>

                <!-- جدول المناطق -->
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="fas fa-list me-2"></i>
                            قائمة المناطق التفصيلية
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="table-responsive">
                            <table class="table table-striped table-hover" id="regionsTable">
                                <thead class="table-dark">
                                    <tr>
                                        <th>اسم المنطقة</th>
                                        <th>الوصف</th>
                                        <th>عدد المؤيدين</th>
                                        <th>الذكور</th>
                                        <th>الإناث</th>
                                        <th>تاريخ الإنشاء</th>
                                        <th>الإجراءات</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php foreach ($regions as $region): ?>
                                    <tr>
                                        <td><strong><?php echo htmlspecialchars($region['name']); ?></strong></td>
                                        <td><?php echo htmlspecialchars(substr($region['description'], 0, 50)) . (strlen($region['description']) > 50 ? '...' : ''); ?></td>
                                        <td>
                                            <span class="badge bg-primary"><?php echo $region['supporters_count']; ?></span>
                                        </td>
                                        <td>
                                            <span class="badge bg-info"><?php echo $region['male_count']; ?></span>
                                        </td>
                                        <td>
                                            <span class="badge bg-pink"><?php echo $region['female_count']; ?></span>
                                        </td>
                                        <td><?php echo formatArabicDate($region['created_at']); ?></td>
                                        <td>
                                            <div class="btn-group" role="group">
                                                <button type="button" class="btn btn-sm btn-info" 
                                                        onclick="viewRegionSupporters(<?php echo $region['id']; ?>)" 
                                                        title="عرض المؤيدين">
                                                    <i class="fas fa-users"></i>
                                                </button>
                                                <button type="button" class="btn btn-sm btn-warning" 
                                                        onclick="editRegion(<?php echo $region['id']; ?>)" 
                                                        title="تعديل">
                                                    <i class="fas fa-edit"></i>
                                                </button>
                                                <button type="button" class="btn btn-sm btn-danger" 
                                                        onclick="deleteRegion(<?php echo $region['id']; ?>)" 
                                                        title="حذف">
                                                    <i class="fas fa-trash"></i>
                                                </button>
                                            </div>
                                        </td>
                                    </tr>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </main>
        </div>
    </div>

    <!-- النوافذ المنبثقة -->
    <?php include 'modals.php'; ?>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://code.jquery.com/jquery-3.7.0.min.js"></script>
    <script src="https://cdn.datatables.net/1.13.6/js/jquery.dataTables.min.js"></script>
    <script src="https://cdn.datatables.net/1.13.6/js/dataTables.bootstrap5.min.js"></script>
    <script src="../../assets/js/regions.js"></script>
</body>
</html>
