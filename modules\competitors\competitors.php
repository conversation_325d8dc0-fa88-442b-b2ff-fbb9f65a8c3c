<?php
require_once '../../config/config.php';
require_once '../../config/database.php';

// التحقق من تسجيل الدخول
if (!isLoggedIn()) {
    redirect('../../login.php');
}

// التحقق من الصلاحيات - المنافسين للمرشح فقط
if (!isCandidate()) {
    showMessage('ليس لديك صلاحية للوصول إلى هذه الصفحة', 'error');
    redirect('../../dashboard.php');
}

// معالجة الطلبات
if ($_SERVER['REQUEST_METHOD'] == 'POST') {
    if (isset($_POST['action'])) {
        switch ($_POST['action']) {
            case 'add':
                addCompetitor();
                break;
            case 'edit':
                editCompetitor();
                break;
            case 'delete':
                deleteCompetitor();
                break;
        }
    }
}

// بناء الاستعلام مع التصفية
$where_conditions = [];
$params = [];

// تطبيق فلاتر البحث
if (!empty($_GET['search'])) {
    $search = '%' . $_GET['search'] . '%';
    $where_conditions[] = "(c.name LIKE ? OR c.party LIKE ?)";
    $params = array_merge($params, [$search, $search]);
}

if (!empty($_GET['region_id'])) {
    $where_conditions[] = "c.region_id = ?";
    $params[] = $_GET['region_id'];
}

$where_clause = !empty($where_conditions) ? 'WHERE ' . implode(' AND ', $where_conditions) : '';

// جلب المنافسين
$sql = "SELECT c.*, r.name as region_name, u.full_name as added_by_name
        FROM competitors c 
        LEFT JOIN regions r ON c.region_id = r.id 
        LEFT JOIN users u ON c.added_by = u.id 
        $where_clause 
        ORDER BY c.created_at DESC";

$competitors = fetchAll($sql, $params);

// جلب المناطق للتصفية
$regions = fetchAll("SELECT * FROM regions ORDER BY name");

// إحصائيات
$total_competitors = count($competitors);
$competitors_with_party = count(array_filter($competitors, function($comp) { return !empty($comp['party']); }));

function addCompetitor() {
    $data = [
        'name' => sanitize($_POST['name']),
        'party' => sanitize($_POST['party']),
        'region_id' => !empty($_POST['region_id']) ? (int)$_POST['region_id'] : null,
        'strengths' => sanitize($_POST['strengths']),
        'weaknesses' => sanitize($_POST['weaknesses']),
        'notes' => sanitize($_POST['notes']),
        'added_by' => $_SESSION['user_id']
    ];
    
    // التحقق من صحة البيانات
    if (empty($data['name'])) {
        showMessage('يرجى إدخال اسم المنافس', 'error');
        return;
    }
    
    // رفع الصور إن وجدت
    $photos = [];
    if (isset($_FILES['photos']) && !empty($_FILES['photos']['name'][0])) {
        $photos = uploadCompetitorPhotos($_FILES['photos']);
    }
    
    if (!empty($photos)) {
        $data['photos'] = json_encode($photos);
    }
    
    $sql = "INSERT INTO competitors (name, party, region_id, strengths, weaknesses, photos, notes, added_by) 
            VALUES (?, ?, ?, ?, ?, ?, ?, ?)";
    
    $result = executeQuery($sql, array_values($data));
    
    if ($result) {
        showMessage('تم إضافة المنافس بنجاح', 'success');
        redirect('competitors.php');
    } else {
        showMessage('حدث خطأ أثناء إضافة المنافس', 'error');
    }
}

function editCompetitor() {
    $id = (int)$_POST['id'];
    $data = [
        'name' => sanitize($_POST['name']),
        'party' => sanitize($_POST['party']),
        'region_id' => !empty($_POST['region_id']) ? (int)$_POST['region_id'] : null,
        'strengths' => sanitize($_POST['strengths']),
        'weaknesses' => sanitize($_POST['weaknesses']),
        'notes' => sanitize($_POST['notes'])
    ];
    
    // التحقق من صحة البيانات
    if (empty($data['name'])) {
        showMessage('يرجى إدخال اسم المنافس', 'error');
        return;
    }
    
    // رفع الصور الجديدة إن وجدت
    if (isset($_FILES['photos']) && !empty($_FILES['photos']['name'][0])) {
        $photos = uploadCompetitorPhotos($_FILES['photos']);
        if (!empty($photos)) {
            $data['photos'] = json_encode($photos);
        }
    }
    
    $set_clause = implode(', ', array_map(function($key) { return "$key = ?"; }, array_keys($data)));
    $sql = "UPDATE competitors SET $set_clause WHERE id = ?";
    
    $params = array_values($data);
    $params[] = $id;
    
    $result = executeQuery($sql, $params);
    
    if ($result) {
        showMessage('تم تحديث بيانات المنافس بنجاح', 'success');
        redirect('competitors.php');
    } else {
        showMessage('حدث خطأ أثناء تحديث بيانات المنافس', 'error');
    }
}

function deleteCompetitor() {
    $id = (int)$_POST['id'];
    
    // حذف الصور المرتبطة
    $competitor = fetchOne("SELECT photos FROM competitors WHERE id = ?", [$id]);
    if ($competitor && $competitor['photos']) {
        $photos = json_decode($competitor['photos'], true);
        foreach ($photos as $photo) {
            if (file_exists('../../' . $photo)) {
                unlink('../../' . $photo);
            }
        }
    }
    
    $result = executeQuery("DELETE FROM competitors WHERE id = ?", [$id]);
    
    if ($result) {
        showMessage('تم حذف المنافس بنجاح', 'success');
    } else {
        showMessage('حدث خطأ أثناء حذف المنافس', 'error');
    }
}

function uploadCompetitorPhotos($files) {
    $uploaded_photos = [];
    $allowed_types = ['image/jpeg', 'image/png', 'image/gif'];
    $max_size = 5 * 1024 * 1024; // 5MB
    
    $upload_dir = '../../uploads/competitors/';
    if (!file_exists($upload_dir)) {
        mkdir($upload_dir, 0755, true);
    }
    
    for ($i = 0; $i < count($files['name']); $i++) {
        if ($files['error'][$i] == 0) {
            if (!in_array($files['type'][$i], $allowed_types)) {
                continue;
            }
            
            if ($files['size'][$i] > $max_size) {
                continue;
            }
            
            $file_extension = pathinfo($files['name'][$i], PATHINFO_EXTENSION);
            $new_filename = uniqid() . '.' . $file_extension;
            $upload_path = $upload_dir . $new_filename;
            
            if (move_uploaded_file($files['tmp_name'][$i], $upload_path)) {
                $uploaded_photos[] = 'uploads/competitors/' . $new_filename;
            }
        }
    }
    
    return $uploaded_photos;
}
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إدارة المنافسين - <?php echo SITE_NAME; ?></title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="https://cdn.datatables.net/1.13.6/css/dataTables.bootstrap5.min.css" rel="stylesheet">
    <link href="../../assets/css/dashboard.css" rel="stylesheet">
    <link href="../../assets/css/competitors.css" rel="stylesheet">
</head>
<body>
    <!-- شريط التنقل العلوي -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary fixed-top">
        <div class="container-fluid">
            <a class="navbar-brand" href="../../dashboard.php">
                <i class="fas fa-vote-yea me-2"></i>
                نظام إدارة الحملة الانتخابية
            </a>
            
            <div class="navbar-nav ms-auto">
                <div class="nav-item dropdown">
                    <a class="nav-link dropdown-toggle" href="#" id="userDropdown" role="button" data-bs-toggle="dropdown">
                        <i class="fas fa-user-circle me-1"></i>
                        <?php echo htmlspecialchars($_SESSION['full_name']); ?>
                    </a>
                    <ul class="dropdown-menu dropdown-menu-end">
                        <li><a class="dropdown-item" href="../../logout.php"><i class="fas fa-sign-out-alt me-2"></i>تسجيل الخروج</a></li>
                    </ul>
                </div>
            </div>
        </div>
    </nav>

    <!-- المحتوى الرئيسي -->
    <div class="container-fluid main-content">
        <div class="row">
            <!-- الشريط الجانبي -->
            <nav class="col-md-3 col-lg-2 d-md-block sidebar">
                <div class="position-sticky pt-3">
                    <ul class="nav flex-column">
                        <li class="nav-item">
                            <a class="nav-link" href="../../dashboard.php">
                                <i class="fas fa-tachometer-alt me-2"></i>
                                لوحة التحكم
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="../supporters/supporters.php">
                                <i class="fas fa-users me-2"></i>
                                المؤيدين
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="../regions/regions.php">
                                <i class="fas fa-map-marked-alt me-2"></i>
                                المناطق
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="../admins/admins.php">
                                <i class="fas fa-user-tie me-2"></i>
                                الإداريين
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="../expenses/expenses.php">
                                <i class="fas fa-money-bill-wave me-2"></i>
                                المصروفات
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="../events/events.php">
                                <i class="fas fa-calendar-alt me-2"></i>
                                الفعاليات
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link active" href="competitors.php">
                                <i class="fas fa-chess me-2"></i>
                                المنافسين
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="../reports/reports.php">
                                <i class="fas fa-chart-bar me-2"></i>
                                التقارير
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="../settings/settings.php">
                                <i class="fas fa-cog me-2"></i>
                                الإعدادات
                            </a>
                        </li>
                    </ul>
                </div>
            </nav>

            <!-- المحتوى الرئيسي -->
            <main class="col-md-9 ms-sm-auto col-lg-10 px-md-4">
                <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                    <h1 class="h2">
                        <i class="fas fa-chess me-2"></i>
                        إدارة المنافسين
                    </h1>
                    <div class="btn-toolbar mb-2 mb-md-0">
                        <div class="btn-group me-2">
                            <button type="button" class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#addCompetitorModal">
                                <i class="fas fa-plus"></i> إضافة منافس
                            </button>
                            <button type="button" class="btn btn-success" onclick="exportCompetitors()">
                                <i class="fas fa-file-excel"></i> تصدير Excel
                            </button>
                        </div>
                    </div>
                </div>

                <?php displayMessage(); ?>

                <!-- الإحصائيات -->
                <div class="row mb-4">
                    <div class="col-md-6">
                        <div class="card bg-primary text-white">
                            <div class="card-body">
                                <div class="d-flex justify-content-between">
                                    <div>
                                        <h4><?php echo number_format($total_competitors); ?></h4>
                                        <p class="mb-0">إجمالي المنافسين</p>
                                    </div>
                                    <div class="align-self-center">
                                        <i class="fas fa-chess fa-2x"></i>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="card bg-info text-white">
                            <div class="card-body">
                                <div class="d-flex justify-content-between">
                                    <div>
                                        <h4><?php echo number_format($competitors_with_party); ?></h4>
                                        <p class="mb-0">منافسين بأحزاب</p>
                                    </div>
                                    <div class="align-self-center">
                                        <i class="fas fa-flag fa-2x"></i>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- فلاتر البحث -->
                <div class="card mb-4">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="fas fa-filter me-2"></i>
                            فلاتر البحث والتصفية
                        </h5>
                    </div>
                    <div class="card-body">
                        <form method="GET" action="">
                            <div class="row">
                                <div class="col-md-4 mb-3">
                                    <label for="search" class="form-label">البحث</label>
                                    <input type="text" class="form-control" id="search" name="search" 
                                           value="<?php echo htmlspecialchars($_GET['search'] ?? ''); ?>" 
                                           placeholder="اسم المنافس، الحزب...">
                                </div>
                                <div class="col-md-3 mb-3">
                                    <label for="region_id" class="form-label">المنطقة</label>
                                    <select class="form-select" id="region_id" name="region_id">
                                        <option value="">جميع المناطق</option>
                                        <?php foreach ($regions as $region): ?>
                                            <option value="<?php echo $region['id']; ?>" 
                                                    <?php echo ($_GET['region_id'] ?? '') == $region['id'] ? 'selected' : ''; ?>>
                                                <?php echo htmlspecialchars($region['name']); ?>
                                            </option>
                                        <?php endforeach; ?>
                                    </select>
                                </div>
                                <div class="col-md-1 mb-3 d-flex align-items-end">
                                    <button type="submit" class="btn btn-primary w-100">
                                        <i class="fas fa-search"></i>
                                    </button>
                                </div>
                            </div>
                        </form>
                    </div>
                </div>

                <!-- جدول المنافسين -->
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="fas fa-list me-2"></i>
                            قائمة المنافسين (<?php echo number_format($total_competitors); ?>)
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="table-responsive">
                            <table class="table table-striped table-hover" id="competitorsTable">
                                <thead class="table-dark">
                                    <tr>
                                        <th>الاسم</th>
                                        <th>الحزب/التيار</th>
                                        <th>المنطقة</th>
                                        <th>نقاط القوة</th>
                                        <th>نقاط الضعف</th>
                                        <th>الصور</th>
                                        <th>أضيف بواسطة</th>
                                        <th>الإجراءات</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php foreach ($competitors as $competitor): ?>
                                    <tr>
                                        <td><strong><?php echo htmlspecialchars($competitor['name']); ?></strong></td>
                                        <td><?php echo htmlspecialchars($competitor['party']) ?: '<span class="text-muted">غير محدد</span>'; ?></td>
                                        <td><?php echo htmlspecialchars($competitor['region_name']) ?: '<span class="text-muted">عام</span>'; ?></td>
                                        <td>
                                            <?php if ($competitor['strengths']): ?>
                                                <span class="text-truncate" style="max-width: 150px; display: inline-block;" 
                                                      title="<?php echo htmlspecialchars($competitor['strengths']); ?>">
                                                    <?php echo htmlspecialchars(substr($competitor['strengths'], 0, 50)) . '...'; ?>
                                                </span>
                                            <?php else: ?>
                                                <span class="text-muted">لا يوجد</span>
                                            <?php endif; ?>
                                        </td>
                                        <td>
                                            <?php if ($competitor['weaknesses']): ?>
                                                <span class="text-truncate" style="max-width: 150px; display: inline-block;" 
                                                      title="<?php echo htmlspecialchars($competitor['weaknesses']); ?>">
                                                    <?php echo htmlspecialchars(substr($competitor['weaknesses'], 0, 50)) . '...'; ?>
                                                </span>
                                            <?php else: ?>
                                                <span class="text-muted">لا يوجد</span>
                                            <?php endif; ?>
                                        </td>
                                        <td>
                                            <?php if ($competitor['photos']): ?>
                                                <?php $photos = json_decode($competitor['photos'], true); ?>
                                                <span class="badge bg-info"><?php echo count($photos); ?> صورة</span>
                                            <?php else: ?>
                                                <span class="text-muted">لا توجد</span>
                                            <?php endif; ?>
                                        </td>
                                        <td><?php echo htmlspecialchars($competitor['added_by_name']); ?></td>
                                        <td>
                                            <div class="btn-group" role="group">
                                                <button type="button" class="btn btn-sm btn-info" 
                                                        onclick="viewCompetitor(<?php echo $competitor['id']; ?>)" 
                                                        title="عرض">
                                                    <i class="fas fa-eye"></i>
                                                </button>
                                                <button type="button" class="btn btn-sm btn-warning" 
                                                        onclick="editCompetitor(<?php echo $competitor['id']; ?>)" 
                                                        title="تعديل">
                                                    <i class="fas fa-edit"></i>
                                                </button>
                                                <button type="button" class="btn btn-sm btn-danger" 
                                                        onclick="deleteCompetitor(<?php echo $competitor['id']; ?>)" 
                                                        title="حذف">
                                                    <i class="fas fa-trash"></i>
                                                </button>
                                            </div>
                                        </td>
                                    </tr>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </main>
        </div>
    </div>

    <!-- النوافذ المنبثقة -->
    <?php include 'modals.php'; ?>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://code.jquery.com/jquery-3.7.0.min.js"></script>
    <script src="https://cdn.datatables.net/1.13.6/js/jquery.dataTables.min.js"></script>
    <script src="https://cdn.datatables.net/1.13.6/js/dataTables.bootstrap5.min.js"></script>
    <script src="../../assets/js/competitors.js"></script>
</body>
</html>
