<?php
require_once 'config/config.php';
require_once 'config/database.php';

// إذا كان المستخدم مسجل دخول بالفعل، إعادة توجيه
if (isLoggedIn()) {
    if (isCandidate()) {
        redirect('dashboard.php');
    } else {
        redirect('admin_dashboard.php');
    }
}

$error_message = '';
$login_attempts = $_SESSION['login_attempts'] ?? 0;

if ($_SERVER['REQUEST_METHOD'] == 'POST') {
    $username = sanitize($_POST['username'] ?? '');
    $password = $_POST['password'] ?? '';
    $phone = sanitize($_POST['phone'] ?? '');

    // التحقق من عدد محاولات تسجيل الدخول
    if ($login_attempts >= MAX_LOGIN_ATTEMPTS) {
        $error_message = 'تم تجاوز الحد الأقصى لمحاولات تسجيل الدخول. يرجى المحاولة لاحقاً.';
    } else {
        // التحقق من البيانات
        if (empty($username) || empty($password) || empty($phone)) {
            $error_message = 'يرجى ملء جميع الحقول المطلوبة.';
        } else {
            // البحث عن المستخدم في قاعدة البيانات
            $sql = "SELECT * FROM users WHERE username = ? AND phone = ? AND status = 'active'";
            $user = fetchOne($sql, [$username, $phone]);

            if ($user && verifyPassword($password, $user['password'])) {
                // تسجيل دخول ناجح
                $_SESSION['user_id'] = $user['id'];
                $_SESSION['username'] = $user['username'];
                $_SESSION['user_type'] = $user['user_type'];
                $_SESSION['full_name'] = $user['full_name'];
                $_SESSION['region_id'] = $user['region_id'];
                $_SESSION['permissions'] = json_decode($user['permissions'], true);
                $_SESSION['login_time'] = time();

                // إعادة تعيين محاولات تسجيل الدخول
                unset($_SESSION['login_attempts']);

                // إعادة التوجيه حسب نوع المستخدم
                if ($user['user_type'] === 'candidate') {
                    redirect('dashboard.php');
                } else {
                    redirect('admin_dashboard.php');
                }
            } else {
                $error_message = 'بيانات تسجيل الدخول غير صحيحة.';
                $_SESSION['login_attempts'] = $login_attempts + 1;
            }
        }
    }
}
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>تسجيل الدخول - <?php echo SITE_NAME; ?></title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="assets/css/login.css" rel="stylesheet">
</head>
<body>
    <div class="floating-shapes">
        <div class="shape"></div>
        <div class="shape"></div>
        <div class="shape"></div>
    </div>

    <div class="login-container">
        <div class="login-card">
            <div class="login-header">
                <i class="fas fa-vote-yea"></i>
                <h3>نظام إدارة الحملة الانتخابية</h3>
                <p class="mb-0">تسجيل الدخول</p>
            </div>

            <div class="login-body">
                <?php if (!empty($error_message)): ?>
                    <div class="alert alert-danger" role="alert">
                        <i class="fas fa-exclamation-triangle me-2"></i>
                        <?php echo $error_message; ?>
                    </div>
                <?php endif; ?>

                <form method="POST" action="">
                    <div class="mb-3">
                        <label for="username" class="form-label">اسم المستخدم</label>
                        <div class="input-group">
                            <span class="input-group-text">
                                <i class="fas fa-user"></i>
                            </span>
                            <input type="text" class="form-control" id="username" name="username"
                                   value="<?php echo htmlspecialchars($_POST['username'] ?? ''); ?>"
                                   required autocomplete="username">
                        </div>
                    </div>

                    <div class="mb-3">
                        <label for="password" class="form-label">كلمة المرور</label>
                        <div class="input-group">
                            <span class="input-group-text">
                                <i class="fas fa-lock"></i>
                            </span>
                            <input type="password" class="form-control" id="password" name="password"
                                   required autocomplete="current-password">
                        </div>
                    </div>

                    <div class="mb-4">
                        <label for="phone" class="form-label">رقم الهاتف</label>
                        <div class="input-group">
                            <span class="input-group-text">
                                <i class="fas fa-phone"></i>
                            </span>
                            <input type="tel" class="form-control" id="phone" name="phone"
                                   value="<?php echo htmlspecialchars($_POST['phone'] ?? ''); ?>"
                                   placeholder="07XXXXXXXXX" required>
                        </div>
                    </div>

                    <button type="submit" class="btn btn-primary btn-login">
                        <i class="fas fa-sign-in-alt me-2"></i>
                        تسجيل الدخول
                    </button>
                </form>

                <div class="text-center mt-3">
                    <small class="text-muted">
                        محاولات تسجيل الدخول: <?php echo $login_attempts; ?> / <?php echo MAX_LOGIN_ATTEMPTS; ?>
                    </small>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="assets/js/login.js"></script>
</body>
</html>
