<?php
// تصدير المؤيدين إلى PDF - للصفحة الأصلية
require_once '../../config/config.php';
require_once '../../config/database.php';

// التحقق من تسجيل الدخول
if (!isLoggedIn()) {
    redirect('../../login.php');
}

// بناء الاستعلام مع التصفية
$where_conditions = [];
$params = [];

// تصفية حسب المنطقة للإداريين
if (isAdmin() && $_SESSION['region_id']) {
    $where_conditions[] = "s.region_id = ?";
    $params[] = $_SESSION['region_id'];
}

// تطبيق فلاتر البحث إذا وجدت
if (!empty($_GET['search'])) {
    $search = '%' . $_GET['search'] . '%';
    $where_conditions[] = "(s.full_name LIKE ? OR s.phone LIKE ? OR s.voter_number LIKE ?)";
    $params = array_merge($params, [$search, $search, $search]);
}

if (!empty($_GET['region_id'])) {
    $where_conditions[] = "s.region_id = ?";
    $params[] = $_GET['region_id'];
}

if (!empty($_GET['gender'])) {
    $where_conditions[] = "s.gender = ?";
    $params[] = $_GET['gender'];
}

$where_clause = !empty($where_conditions) ? 'WHERE ' . implode(' AND ', $where_conditions) : '';

// جلب البيانات
$sql = "SELECT s.full_name, 
               CASE s.gender WHEN 'male' THEN 'ذكر' ELSE 'أنثى' END as gender,
               CASE s.marital_status 
                   WHEN 'single' THEN 'أعزب'
                   WHEN 'married' THEN 'متزوج'
                   WHEN 'divorced' THEN 'مطلق'
                   WHEN 'widowed' THEN 'أرمل'
                   ELSE 'أعزب'
               END as marital_status,
               s.birth_date,
               YEAR(CURDATE()) - YEAR(s.birth_date) as age,
               s.education,
               s.profession,
               s.address,
               s.phone,
               s.voter_number,
               s.voting_center,
               r.name as region_name,
               s.notes,
               DATE_FORMAT(s.created_at, '%Y-%m-%d') as created_date
        FROM supporters s 
        LEFT JOIN regions r ON s.region_id = r.id 
        $where_clause 
        ORDER BY s.created_at DESC";

$supporters = fetchAll($sql, $params);

if (empty($supporters)) {
    showMessage('لا توجد بيانات للتصدير', 'warning');
    redirect('supporters.php');
}

// إنشاء PDF بسيط باستخدام HTML
$filename = 'المؤيدين_' . date('Y-m-d_H-i-s') . '.pdf';

// استخدام مكتبة wkhtmltopdf أو تحويل HTML إلى PDF
// هنا سنستخدم طريقة بسيطة لإنشاء HTML يمكن طباعته كـ PDF

header('Content-Type: text/html; charset=utf-8');
?>
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>تقرير المؤيدين</title>
    <style>
        body {
            font-family: 'Arial', sans-serif;
            margin: 20px;
            direction: rtl;
            text-align: right;
        }
        .header {
            text-align: center;
            margin-bottom: 30px;
            border-bottom: 3px solid #007bff;
            padding-bottom: 20px;
        }
        .header h1 {
            color: #007bff;
            margin: 0;
            font-size: 28px;
        }
        .header p {
            color: #666;
            margin: 5px 0;
        }
        .stats {
            display: flex;
            justify-content: space-around;
            margin: 20px 0;
            background: #f8f9fa;
            padding: 15px;
            border-radius: 8px;
        }
        .stat-item {
            text-align: center;
        }
        .stat-number {
            font-size: 24px;
            font-weight: bold;
            color: #007bff;
        }
        .stat-label {
            font-size: 14px;
            color: #666;
        }
        table {
            width: 100%;
            border-collapse: collapse;
            margin: 20px 0;
            font-size: 12px;
        }
        th, td {
            border: 1px solid #ddd;
            padding: 8px;
            text-align: center;
        }
        th {
            background-color: #007bff;
            color: white;
            font-weight: bold;
        }
        tr:nth-child(even) {
            background-color: #f2f2f2;
        }
        tr:hover {
            background-color: #e8f4f8;
        }
        .footer {
            margin-top: 30px;
            text-align: center;
            font-size: 12px;
            color: #666;
            border-top: 1px solid #ddd;
            padding-top: 15px;
        }
        @media print {
            body { margin: 0; }
            .no-print { display: none; }
            table { font-size: 10px; }
            th, td { padding: 4px; }
        }
        .print-btn {
            position: fixed;
            top: 20px;
            left: 20px;
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            font-size: 14px;
        }
        .print-btn:hover {
            background: #0056b3;
        }
    </style>
</head>
<body>
    <button class="print-btn no-print" onclick="window.print()">
        🖨️ طباعة / حفظ PDF
    </button>

    <div class="header">
        <h1>تقرير المؤيدين</h1>
        <p>نظام إدارة الحملة الانتخابية</p>
        <p>تاريخ التقرير: <?php echo date('Y-m-d H:i:s'); ?></p>
    </div>

    <?php
    $total = count($supporters);
    $males = count(array_filter($supporters, function($s) { return $s['gender'] == 'ذكر'; }));
    $females = count(array_filter($supporters, function($s) { return $s['gender'] == 'أنثى'; }));
    ?>

    <div class="stats">
        <div class="stat-item">
            <div class="stat-number"><?php echo number_format($total); ?></div>
            <div class="stat-label">إجمالي المؤيدين</div>
        </div>
        <div class="stat-item">
            <div class="stat-number"><?php echo number_format($males); ?></div>
            <div class="stat-label">الذكور</div>
        </div>
        <div class="stat-item">
            <div class="stat-number"><?php echo number_format($females); ?></div>
            <div class="stat-label">الإناث</div>
        </div>
        <div class="stat-item">
            <div class="stat-number"><?php echo $total > 0 ? round(($males / $total) * 100, 1) : 0; ?>%</div>
            <div class="stat-label">نسبة الذكور</div>
        </div>
    </div>

    <table>
        <thead>
            <tr>
                <th>الاسم الكامل</th>
                <th>الجنس</th>
                <th>العمر</th>
                <th>رقم الهاتف</th>
                <th>رقم الناخب</th>
                <th>المنطقة</th>
                <th>المهنة</th>
                <th>تاريخ الإضافة</th>
            </tr>
        </thead>
        <tbody>
            <?php foreach ($supporters as $supporter): ?>
            <tr>
                <td><?php echo htmlspecialchars($supporter['full_name']); ?></td>
                <td><?php echo htmlspecialchars($supporter['gender']); ?></td>
                <td><?php echo htmlspecialchars($supporter['age']); ?> سنة</td>
                <td><?php echo htmlspecialchars($supporter['phone']); ?></td>
                <td><?php echo htmlspecialchars($supporter['voter_number'] ?: 'غير محدد'); ?></td>
                <td><?php echo htmlspecialchars($supporter['region_name']); ?></td>
                <td><?php echo htmlspecialchars($supporter['profession'] ?: 'غير محدد'); ?></td>
                <td><?php echo htmlspecialchars($supporter['created_date']); ?></td>
            </tr>
            <?php endforeach; ?>
        </tbody>
    </table>

    <div class="footer">
        <p>تم إنشاء هذا التقرير بواسطة نظام إدارة الحملة الانتخابية</p>
        <p>للحفظ كـ PDF: اضغط Ctrl+P واختر "حفظ كـ PDF"</p>
    </div>

    <script>
        // طباعة تلقائية عند التحميل (اختياري)
        // window.onload = function() { window.print(); };
    </script>
</body>
</html>
