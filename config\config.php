<?php
// بدء الجلسة
if (session_status() == PHP_SESSION_NONE) {
    session_start();
}

// إعدادات النظام العامة
define('SITE_NAME', 'نظام إدارة الحملة الانتخابية');
define('SITE_URL', 'http://localhost');
define('UPLOAD_PATH', 'uploads/');
define('MAX_FILE_SIZE', 5 * 1024 * 1024); // 5MB

// بيانات المرشح الافتراضية
define('DEFAULT_USERNAME', 'abd');
define('DEFAULT_PASSWORD', 'abdabd');
define('DEFAULT_PHONE', '07719992716');

// إعدادات الأمان
define('SESSION_TIMEOUT', 3600); // ساعة واحدة
define('MAX_LOGIN_ATTEMPTS', 5);

// إعدادات التصدير
define('EXPORT_PATH', 'exports/');

// دالة للتحقق من تسجيل الدخول
function isLoggedIn() {
    return isset($_SESSION['user_id']) && isset($_SESSION['username']);
}

// دالة للتحقق من صلاحيات الإداري
function isAdmin() {
    return isset($_SESSION['user_type']) && $_SESSION['user_type'] === 'admin';
}

// دالة للتحقق من صلاحيات المرشح
function isCandidate() {
    return isset($_SESSION['user_type']) && $_SESSION['user_type'] === 'candidate';
}

// دالة للتحقق من صلاحيات المدير العام
function isSuperAdmin() {
    return isLoggedIn() && (isCandidate() || isAdmin());
}

// دالة لإعادة التوجيه
function redirect($url) {
    header("Location: $url");
    exit();
}

// دالة لعرض الرسائل
function showMessage($message, $type = 'info') {
    $_SESSION['message'] = $message;
    $_SESSION['message_type'] = $type;
}

// دالة لعرض الرسائل المحفوظة
function displayMessage() {
    if (isset($_SESSION['message'])) {
        $type = $_SESSION['message_type'] ?? 'info';
        $message = $_SESSION['message'];
        unset($_SESSION['message'], $_SESSION['message_type']);

        $alertClass = [
            'success' => 'alert-success',
            'error' => 'alert-danger',
            'warning' => 'alert-warning',
            'info' => 'alert-info'
        ][$type] ?? 'alert-info';

        echo "<div class='alert $alertClass alert-dismissible fade show' role='alert'>
                $message
                <button type='button' class='btn-close' data-bs-dismiss='alert'></button>
              </div>";
    }
}

// دالة لتنظيف البيانات
function sanitize($data) {
    return htmlspecialchars(strip_tags(trim($data)));
}

// دالة للتحقق من صحة رقم الهاتف العراقي
function validateIraqiPhone($phone) {
    $pattern = '/^(07[3-9][0-9]{8})$/';
    return preg_match($pattern, $phone);
}

// دالة لتوليد كلمة مرور عشوائية
function generatePassword($length = 8) {
    $chars = 'abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789';
    return substr(str_shuffle($chars), 0, $length);
}

// دالة لتشفير كلمة المرور
function hashPassword($password) {
    return password_hash($password, PASSWORD_DEFAULT);
}

// دالة للتحقق من كلمة المرور
function verifyPassword($password, $hash) {
    return password_verify($password, $hash);
}

// دالة لإنشاء مجلد إذا لم يكن موجوداً
function createDirectoryIfNotExists($path) {
    if (!file_exists($path)) {
        mkdir($path, 0755, true);
    }
}

// إنشاء المجلدات المطلوبة
createDirectoryIfNotExists(UPLOAD_PATH);
createDirectoryIfNotExists(EXPORT_PATH);
createDirectoryIfNotExists(UPLOAD_PATH . 'supporters/');
createDirectoryIfNotExists(UPLOAD_PATH . 'events/');
createDirectoryIfNotExists(UPLOAD_PATH . 'competitors/');

// دالة لتحويل التاريخ إلى العربية
function formatArabicDate($date) {
    $months = [
        1 => 'يناير', 2 => 'فبراير', 3 => 'مارس', 4 => 'أبريل',
        5 => 'مايو', 6 => 'يونيو', 7 => 'يوليو', 8 => 'أغسطس',
        9 => 'سبتمبر', 10 => 'أكتوبر', 11 => 'نوفمبر', 12 => 'ديسمبر'
    ];

    $timestamp = strtotime($date);
    $day = date('d', $timestamp);
    $month = $months[date('n', $timestamp)];
    $year = date('Y', $timestamp);

    return "$day $month $year";
}

// دالة لتحويل الأرقام إلى العربية
function convertToArabicNumbers($number) {
    $arabic = ['٠', '١', '٢', '٣', '٤', '٥', '٦', '٧', '٨', '٩'];
    $english = ['0', '1', '2', '3', '4', '5', '6', '7', '8', '9'];
    return str_replace($english, $arabic, $number);
}
?>
