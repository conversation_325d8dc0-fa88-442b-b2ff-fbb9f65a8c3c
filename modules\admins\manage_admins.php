<?php
// إدارة الإداريين في النظام الرئيسي
require_once '../../config/config.php';
require_once '../../config/database.php';

// التحقق من تسجيل الدخول والصلاحيات
if (!isLoggedIn() || !isSuperAdmin()) {
    redirect('../../login.php');
}

// معالجة إضافة إداري جديد
if ($_SERVER['REQUEST_METHOD'] == 'POST' && isset($_POST['action']) && $_POST['action'] == 'add') {
    $username = sanitize($_POST['username']);
    $password = $_POST['password'];
    $full_name = sanitize($_POST['full_name']);
    $phone = sanitize($_POST['phone']);
    $email = sanitize($_POST['email']);
    $region_id = !empty($_POST['region_id']) ? (int)$_POST['region_id'] : null;
    $role = sanitize($_POST['role']);
    
    if (!empty($username) && !empty($password) && !empty($full_name) && !empty($phone)) {
        // التحقق من عدم تكرار اسم المستخدم
        $existing = fetchOne("SELECT id FROM admins WHERE username = ?", [$username]);
        
        if ($existing) {
            showMessage('اسم المستخدم موجود مسبقاً', 'error');
        } else {
            $hashed_password = password_hash($password, PASSWORD_DEFAULT);
            
            $sql = "INSERT INTO admins (username, password, full_name, phone, email, region_id, role, status, created_at) 
                    VALUES (?, ?, ?, ?, ?, ?, ?, 'active', NOW())";
            
            $result = executeQuery($sql, [$username, $hashed_password, $full_name, $phone, $email, $region_id, $role]);
            
            if ($result) {
                showMessage('تم إضافة الإداري بنجاح', 'success');
            } else {
                showMessage('حدث خطأ أثناء إضافة الإداري', 'error');
            }
        }
    } else {
        showMessage('يرجى ملء جميع الحقول المطلوبة', 'error');
    }
}

// معالجة تحديث حالة الإداري
if (isset($_GET['toggle_status']) && is_numeric($_GET['toggle_status'])) {
    $admin_id = (int)$_GET['toggle_status'];
    $current_status = fetchOne("SELECT status FROM admins WHERE id = ?", [$admin_id])['status'];
    $new_status = $current_status == 'active' ? 'inactive' : 'active';
    
    $result = executeQuery("UPDATE admins SET status = ? WHERE id = ?", [$new_status, $admin_id]);
    
    if ($result) {
        showMessage('تم تحديث حالة الإداري', 'success');
    } else {
        showMessage('حدث خطأ أثناء تحديث الحالة', 'error');
    }
}

// جلب الإداريين
$admins = fetchAll("
    SELECT a.*, r.name as region_name,
           (SELECT COUNT(*) FROM supporters WHERE added_by = a.id) as supporters_count,
           (SELECT COUNT(*) FROM supporter_requests WHERE admin_id = a.id) as requests_count
    FROM admins a 
    LEFT JOIN regions r ON a.region_id = r.id 
    ORDER BY a.created_at DESC
");

// جلب المناطق
$regions = fetchAll("SELECT * FROM regions ORDER BY name");
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إدارة الإداريين - <?php echo SITE_NAME; ?></title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        body { font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; }
        .main-content { margin-top: 80px; }
        .sidebar { background: #f8f9fa; min-height: calc(100vh - 80px); padding-top: 20px; }
        .admin-card { border-radius: 10px; border: none; box-shadow: 0 2px 10px rgba(0,0,0,0.1); margin-bottom: 15px; }
        .status-active { border-left: 4px solid #28a745; }
        .status-inactive { border-left: 4px solid #dc3545; }
    </style>
</head>
<body>
    <!-- شريط التنقل العلوي -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary fixed-top">
        <div class="container-fluid">
            <a class="navbar-brand" href="../../dashboard.php">
                <i class="fas fa-vote-yea me-2"></i>
                نظام إدارة الحملة الانتخابية
            </a>
            
            <div class="navbar-nav ms-auto">
                <div class="nav-item dropdown">
                    <a class="nav-link dropdown-toggle" href="#" id="userDropdown" role="button" data-bs-toggle="dropdown">
                        <i class="fas fa-user-circle me-1"></i>
                        <?php echo htmlspecialchars($_SESSION['full_name']); ?>
                    </a>
                    <ul class="dropdown-menu dropdown-menu-end">
                        <li><a class="dropdown-item" href="../../logout.php"><i class="fas fa-sign-out-alt me-2"></i>تسجيل الخروج</a></li>
                    </ul>
                </div>
            </div>
        </div>
    </nav>

    <!-- المحتوى الرئيسي -->
    <div class="container-fluid main-content">
        <div class="row">
            <!-- الشريط الجانبي -->
            <nav class="col-md-3 col-lg-2 d-md-block sidebar">
                <div class="position-sticky pt-3">
                    <ul class="nav flex-column">
                        <li class="nav-item">
                            <a class="nav-link" href="../../dashboard.php">
                                <i class="fas fa-tachometer-alt me-2"></i>
                                لوحة التحكم
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="../supporters/supporters.php">
                                <i class="fas fa-users me-2"></i>
                                المؤيدين
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link active" href="manage_admins.php">
                                <i class="fas fa-user-tie me-2"></i>
                                الإداريين
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="../regions/regions.php">
                                <i class="fas fa-map-marked-alt me-2"></i>
                                المناطق
                            </a>
                        </li>
                    </ul>
                </div>
            </nav>

            <!-- المحتوى الرئيسي -->
            <main class="col-md-9 ms-sm-auto col-lg-10 px-md-4">
                <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                    <h1 class="h2">
                        <i class="fas fa-user-tie me-2"></i>
                        إدارة الإداريين
                    </h1>
                    <div class="btn-toolbar mb-2 mb-md-0">
                        <button type="button" class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#addAdminModal">
                            <i class="fas fa-plus"></i> إضافة إداري
                        </button>
                    </div>
                </div>

                <?php displayMessage(); ?>

                <!-- إحصائيات -->
                <div class="row mb-4">
                    <div class="col-md-3">
                        <div class="card bg-primary text-white">
                            <div class="card-body text-center">
                                <h3><?php echo count($admins); ?></h3>
                                <p class="mb-0">إجمالي الإداريين</p>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="card bg-success text-white">
                            <div class="card-body text-center">
                                <h3><?php echo count(array_filter($admins, function($a) { return $a['status'] == 'active'; })); ?></h3>
                                <p class="mb-0">نشط</p>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="card bg-warning text-white">
                            <div class="card-body text-center">
                                <h3><?php echo array_sum(array_column($admins, 'supporters_count')); ?></h3>
                                <p class="mb-0">إجمالي المؤيدين</p>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="card bg-info text-white">
                            <div class="card-body text-center">
                                <h3><?php echo array_sum(array_column($admins, 'requests_count')); ?></h3>
                                <p class="mb-0">إجمالي المطالب</p>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- قائمة الإداريين -->
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="fas fa-list me-2"></i>
                            قائمة الإداريين (<?php echo count($admins); ?>)
                        </h5>
                    </div>
                    <div class="card-body">
                        <?php if (empty($admins)): ?>
                            <div class="alert alert-info text-center">
                                <i class="fas fa-user-tie fa-2x mb-3"></i>
                                <h5>لا يوجد إداريين</h5>
                                <p>لم يتم إضافة أي إداريين بعد</p>
                                <button type="button" class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#addAdminModal">
                                    <i class="fas fa-plus me-2"></i>إضافة إداري جديد
                                </button>
                            </div>
                        <?php else: ?>
                            <div class="row">
                                <?php foreach ($admins as $admin): ?>
                                <div class="col-md-6">
                                    <div class="card admin-card status-<?php echo $admin['status']; ?>">
                                        <div class="card-body">
                                            <div class="d-flex justify-content-between align-items-start mb-2">
                                                <h6 class="card-title"><?php echo htmlspecialchars($admin['full_name']); ?></h6>
                                                <div>
                                                    <span class="badge bg-<?php echo $admin['status'] == 'active' ? 'success' : 'danger'; ?>">
                                                        <?php echo $admin['status'] == 'active' ? 'نشط' : 'غير نشط'; ?>
                                                    </span>
                                                    <span class="badge bg-<?php echo $admin['role'] == 'admin' ? 'primary' : 'info'; ?>">
                                                        <?php echo $admin['role'] == 'admin' ? 'إداري' : 'مشرف'; ?>
                                                    </span>
                                                </div>
                                            </div>
                                            
                                            <p class="card-text">
                                                <strong>اسم المستخدم:</strong> <?php echo htmlspecialchars($admin['username']); ?><br>
                                                <strong>الهاتف:</strong> <?php echo htmlspecialchars($admin['phone']); ?><br>
                                                <?php if ($admin['email']): ?>
                                                <strong>البريد:</strong> <?php echo htmlspecialchars($admin['email']); ?><br>
                                                <?php endif; ?>
                                                <?php if ($admin['region_name']): ?>
                                                <strong>المنطقة:</strong> <?php echo htmlspecialchars($admin['region_name']); ?><br>
                                                <?php endif; ?>
                                            </p>
                                            
                                            <div class="row text-center">
                                                <div class="col-6">
                                                    <h6 class="text-primary"><?php echo number_format($admin['supporters_count']); ?></h6>
                                                    <small>مؤيدين</small>
                                                </div>
                                                <div class="col-6">
                                                    <h6 class="text-warning"><?php echo number_format($admin['requests_count']); ?></h6>
                                                    <small>مطالب</small>
                                                </div>
                                            </div>
                                            
                                            <div class="mt-3">
                                                <a href="?toggle_status=<?php echo $admin['id']; ?>" 
                                                   class="btn btn-outline-<?php echo $admin['status'] == 'active' ? 'danger' : 'success'; ?> btn-sm"
                                                   onclick="return confirm('هل تريد تغيير حالة هذا الإداري؟')">
                                                    <i class="fas fa-<?php echo $admin['status'] == 'active' ? 'ban' : 'check'; ?>"></i>
                                                    <?php echo $admin['status'] == 'active' ? 'إلغاء التفعيل' : 'تفعيل'; ?>
                                                </a>
                                                
                                                <?php if ($admin['last_login']): ?>
                                                <small class="text-muted d-block mt-2">
                                                    آخر دخول: <?php echo date('Y-m-d H:i', strtotime($admin['last_login'])); ?>
                                                </small>
                                                <?php endif; ?>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <?php endforeach; ?>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>
            </main>
        </div>
    </div>

    <!-- نافذة إضافة إداري -->
    <div class="modal fade" id="addAdminModal" tabindex="-1">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">إضافة إداري جديد</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <form method="POST" action="">
                    <input type="hidden" name="action" value="add">
                    <div class="modal-body">
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="username" class="form-label">اسم المستخدم *</label>
                                    <input type="text" class="form-control" id="username" name="username" required>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="password" class="form-label">كلمة المرور *</label>
                                    <input type="password" class="form-control" id="password" name="password" required>
                                </div>
                            </div>
                        </div>
                        
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="full_name" class="form-label">الاسم الكامل *</label>
                                    <input type="text" class="form-control" id="full_name" name="full_name" required>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="phone" class="form-label">رقم الهاتف *</label>
                                    <input type="tel" class="form-control" id="phone" name="phone" required>
                                </div>
                            </div>
                        </div>
                        
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="email" class="form-label">البريد الإلكتروني</label>
                                    <input type="email" class="form-control" id="email" name="email">
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="role" class="form-label">الدور *</label>
                                    <select class="form-select" id="role" name="role" required>
                                        <option value="admin">إداري</option>
                                        <option value="supervisor">مشرف</option>
                                    </select>
                                </div>
                            </div>
                        </div>
                        
                        <div class="mb-3">
                            <label for="region_id" class="form-label">المنطقة (اختياري)</label>
                            <select class="form-select" id="region_id" name="region_id">
                                <option value="">جميع المناطق</option>
                                <?php foreach ($regions as $region): ?>
                                <option value="<?php echo $region['id']; ?>"><?php echo htmlspecialchars($region['name']); ?></option>
                                <?php endforeach; ?>
                            </select>
                            <div class="form-text">إذا تم تحديد منطقة، سيكون الإداري مسؤولاً عن هذه المنطقة فقط</div>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-save me-2"></i>إضافة الإداري
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
