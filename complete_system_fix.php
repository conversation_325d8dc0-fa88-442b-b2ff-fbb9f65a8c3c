<?php
// إصلاح شامل للنظام الكامل
header('Content-Type: text/html; charset=utf-8');
ini_set('default_charset', 'utf-8');
mb_internal_encoding('UTF-8');

require_once 'config/config.php';
require_once 'config/database.php';

echo "<!DOCTYPE html>";
echo "<html lang='ar' dir='rtl'>";
echo "<head>";
echo "<meta charset='UTF-8'>";
echo "<meta name='viewport' content='width=device-width, initial-scale=1.0'>";
echo "<title>إصلاح شامل للنظام</title>";
echo "<link href='https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css' rel='stylesheet'>";
echo "<link href='https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css' rel='stylesheet'>";
echo "<style>";
echo "body { font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); min-height: 100vh; padding: 2rem; }";
echo ".system-card { background: white; border-radius: 15px; box-shadow: 0 10px 30px rgba(0,0,0,0.1); padding: 2rem; margin: 0 auto; max-width: 1200px; }";
echo ".success { color: #28a745; }";
echo ".error { color: #dc3545; }";
echo ".feature-box { border: 1px solid #e9ecef; border-radius: 10px; padding: 20px; margin-bottom: 20px; }";
echo "</style>";
echo "</head>";
echo "<body>";

echo "<div class='system-card'>";
echo "<h1 class='text-center mb-4'><i class='fas fa-tools'></i> إصلاح شامل للنظام الكامل</h1>";

if (isset($_POST['fix_all_system'])) {
    echo "<div class='alert alert-info'>";
    echo "<h5>جاري إصلاح النظام الكامل...</h5>";
    echo "</div>";

    try {
        // 1. إصلاح صفحة المؤيدين الأصلية
        echo "<h6>1. إصلاح صفحة المؤيدين الأصلية:</h6>";
        
        // التحقق من وجود الملفات المطلوبة
        $supporter_files = [
            'modules/supporters/import_handler.php' => 'معالج الاستيراد',
            'modules/supporters/export_excel.php' => 'تصدير Excel',
            'modules/supporters/export_pdf.php' => 'تصدير PDF',
            'modules/supporters/template_csv.php' => 'نموذج CSV'
        ];
        
        foreach ($supporter_files as $file => $description) {
            if (file_exists($file)) {
                echo "<p class='success'><i class='fas fa-check'></i> $description: موجود</p>";
            } else {
                echo "<p class='error'><i class='fas fa-times'></i> $description: مفقود</p>";
            }
        }

        // 2. إنشاء النظام الإداري
        echo "<h6>2. إنشاء النظام الإداري:</h6>";
        
        // إنشاء جداول النظام الإداري
        $admin_tables = [
            "CREATE TABLE IF NOT EXISTS admins (
                id INT AUTO_INCREMENT PRIMARY KEY,
                username VARCHAR(50) UNIQUE NOT NULL,
                password VARCHAR(255) NOT NULL,
                full_name VARCHAR(100) NOT NULL,
                phone VARCHAR(15) NOT NULL,
                email VARCHAR(100),
                region_id INT,
                role ENUM('admin', 'supervisor') DEFAULT 'admin',
                status ENUM('active', 'inactive') DEFAULT 'active',
                last_login DATETIME,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                FOREIGN KEY (region_id) REFERENCES regions(id)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci",
            
            "CREATE TABLE IF NOT EXISTS messages (
                id INT AUTO_INCREMENT PRIMARY KEY,
                sender_type ENUM('admin', 'management') NOT NULL,
                sender_id INT,
                receiver_type ENUM('admin', 'management') NOT NULL,
                receiver_id INT,
                subject VARCHAR(200) NOT NULL,
                message TEXT NOT NULL,
                attachment VARCHAR(255),
                status ENUM('unread', 'read') DEFAULT 'unread',
                priority ENUM('low', 'normal', 'high', 'urgent') DEFAULT 'normal',
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                read_at DATETIME
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci",
            
            "CREATE TABLE IF NOT EXISTS weekly_reports (
                id INT AUTO_INCREMENT PRIMARY KEY,
                admin_id INT NOT NULL,
                week_start DATE NOT NULL,
                week_end DATE NOT NULL,
                supporters_added INT DEFAULT 0,
                events_attended INT DEFAULT 0,
                calls_made INT DEFAULT 0,
                meetings_held INT DEFAULT 0,
                challenges TEXT,
                achievements TEXT,
                next_week_plans TEXT,
                notes TEXT,
                status ENUM('draft', 'submitted', 'reviewed') DEFAULT 'draft',
                submitted_at DATETIME,
                reviewed_at DATETIME,
                reviewed_by INT,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                FOREIGN KEY (admin_id) REFERENCES admins(id) ON DELETE CASCADE
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci",
            
            "CREATE TABLE IF NOT EXISTS supporter_requests (
                id INT AUTO_INCREMENT PRIMARY KEY,
                admin_id INT NOT NULL,
                supporter_id INT,
                supporter_name VARCHAR(100) NOT NULL,
                supporter_phone VARCHAR(15) NOT NULL,
                request_type ENUM('financial', 'medical', 'educational', 'employment', 'housing', 'other') NOT NULL,
                title VARCHAR(200) NOT NULL,
                description TEXT NOT NULL,
                attachment VARCHAR(255),
                priority ENUM('low', 'normal', 'high', 'urgent') DEFAULT 'normal',
                status ENUM('pending', 'received', 'in_progress', 'completed', 'rejected') DEFAULT 'pending',
                estimated_cost DECIMAL(10,2),
                actual_cost DECIMAL(10,2),
                management_response TEXT,
                management_attachment VARCHAR(255),
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                completed_at DATETIME,
                FOREIGN KEY (admin_id) REFERENCES admins(id) ON DELETE CASCADE,
                FOREIGN KEY (supporter_id) REFERENCES supporters(id) ON DELETE SET NULL
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci",
            
            "CREATE TABLE IF NOT EXISTS notifications (
                id INT AUTO_INCREMENT PRIMARY KEY,
                user_type ENUM('admin', 'management') NOT NULL,
                user_id INT NOT NULL,
                title VARCHAR(200) NOT NULL,
                message TEXT NOT NULL,
                type ENUM('info', 'success', 'warning', 'error') DEFAULT 'info',
                is_read BOOLEAN DEFAULT FALSE,
                action_url VARCHAR(255),
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci",
            
            "CREATE TABLE IF NOT EXISTS admin_sessions (
                id INT AUTO_INCREMENT PRIMARY KEY,
                admin_id INT NOT NULL,
                session_token VARCHAR(255) NOT NULL,
                ip_address VARCHAR(45),
                user_agent TEXT,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                expires_at DATETIME NOT NULL,
                is_active BOOLEAN DEFAULT TRUE,
                FOREIGN KEY (admin_id) REFERENCES admins(id) ON DELETE CASCADE
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci",
            
            "CREATE TABLE IF NOT EXISTS admin_statistics (
                id INT AUTO_INCREMENT PRIMARY KEY,
                admin_id INT NOT NULL,
                date DATE NOT NULL,
                supporters_added INT DEFAULT 0,
                calls_made INT DEFAULT 0,
                meetings_held INT DEFAULT 0,
                requests_submitted INT DEFAULT 0,
                login_count INT DEFAULT 0,
                active_hours DECIMAL(4,2) DEFAULT 0,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                FOREIGN KEY (admin_id) REFERENCES admins(id) ON DELETE CASCADE,
                UNIQUE KEY unique_admin_date (admin_id, date)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci"
        ];
        
        foreach ($admin_tables as $index => $sql) {
            try {
                executeQuery($sql);
                echo "<p class='success'><i class='fas fa-check'></i> جدول " . ($index + 1) . ": تم إنشاؤه بنجاح</p>";
            } catch (Exception $e) {
                echo "<p class='success'><i class='fas fa-info'></i> جدول " . ($index + 1) . ": موجود مسبقاً</p>";
            }
        }

        // إنشاء إداري تجريبي
        $admin_password = password_hash('admin123', PASSWORD_DEFAULT);
        $sql_demo_admin = "INSERT IGNORE INTO admins (username, password, full_name, phone, email, role, status) 
                          VALUES ('admin', ?, 'إداري تجريبي', '07701234567', '<EMAIL>', 'admin', 'active')";
        
        executeQuery($sql_demo_admin, [$admin_password]);
        echo "<p class='success'><i class='fas fa-check'></i> تم إنشاء إداري تجريبي (admin / admin123)</p>";

        // 3. التحقق من ملفات النظام الإداري
        echo "<h6>3. ملفات النظام الإداري:</h6>";
        
        $admin_files = [
            'modules/admin/login.php' => 'صفحة تسجيل دخول الإداريين',
            'modules/admin/dashboard.php' => 'لوحة تحكم الإداريين',
            'modules/admin/add_supporter.php' => 'إضافة مؤيد للإداريين',
            'modules/admin/logout.php' => 'تسجيل خروج الإداريين'
        ];
        
        foreach ($admin_files as $file => $description) {
            if (file_exists($file)) {
                echo "<p class='success'><i class='fas fa-check'></i> $description: موجود</p>";
            } else {
                echo "<p class='error'><i class='fas fa-times'></i> $description: مفقود</p>";
            }
        }

        echo "<div class='alert alert-success mt-4'>";
        echo "<h3><i class='fas fa-check-circle'></i> تم إصلاح النظام الكامل بنجاح!</h3>";
        echo "<p>النظام جاهز للاستخدام مع جميع الميزات</p>";
        echo "</div>";

    } catch (Exception $e) {
        echo "<div class='alert alert-danger'>";
        echo "<h5>خطأ في الإصلاح:</h5>";
        echo "<p>" . htmlspecialchars($e->getMessage()) . "</p>";
        echo "</div>";
    }

    echo "<div class='text-center mt-4'>";
    echo "<div class='row'>";
    echo "<div class='col-md-4'>";
    echo "<a href='modules/supporters/supporters.php' class='btn btn-primary btn-lg w-100 mb-2'>";
    echo "<i class='fas fa-users'></i><br>صفحة المؤيدين الأصلية";
    echo "</a>";
    echo "</div>";
    echo "<div class='col-md-4'>";
    echo "<a href='modules/admin/login.php' class='btn btn-success btn-lg w-100 mb-2'>";
    echo "<i class='fas fa-user-shield'></i><br>تسجيل دخول الإداريين";
    echo "</a>";
    echo "</div>";
    echo "<div class='col-md-4'>";
    echo "<a href='dashboard.php' class='btn btn-info btn-lg w-100 mb-2'>";
    echo "<i class='fas fa-home'></i><br>الصفحة الرئيسية";
    echo "</a>";
    echo "</div>";
    echo "</div>";
    echo "</div>";

} else {
    // عرض معلومات النظام
    echo "<div class='alert alert-warning'>";
    echo "<h5><i class='fas fa-info-circle'></i> إصلاح شامل للنظام الكامل</h5>";
    echo "<p>سيتم إصلاح وإنشاء:</p>";
    echo "</div>";

    echo "<div class='row'>";
    
    echo "<div class='col-md-6'>";
    echo "<div class='feature-box'>";
    echo "<h6><i class='fas fa-users text-primary'></i> إصلاح صفحة المؤيدين الأصلية</h6>";
    echo "<ul>";
    echo "<li>✅ إصلاح الاستيراد (CSV بدلاً من Excel)</li>";
    echo "<li>✅ إصلاح أزرار التصدير (Excel & PDF)</li>";
    echo "<li>✅ نموذج CSV للتحميل</li>";
    echo "<li>✅ دعم الترميز العربي</li>";
    echo "</ul>";
    echo "</div>";
    echo "</div>";

    echo "<div class='col-md-6'>";
    echo "<div class='feature-box'>";
    echo "<h6><i class='fas fa-user-shield text-success'></i> النظام الإداري الشامل</h6>";
    echo "<ul>";
    echo "<li>🔐 تسجيل دخول منفصل للإداريين</li>";
    echo "<li>📊 لوحة تحكم شخصية</li>";
    echo "<li>👥 إضافة مؤيدين مع تتبع الإحصائيات</li>";
    echo "<li>📧 نظام رسائل مع الإدارة</li>";
    echo "</ul>";
    echo "</div>";
    echo "</div>";

    echo "<div class='col-md-6'>";
    echo "<div class='feature-box'>";
    echo "<h6><i class='fas fa-hand-holding-heart text-warning'></i> مطالب المؤيدين</h6>";
    echo "<ul>";
    echo "<li>📝 تسجيل مطالب المؤيدين</li>";
    echo "<li>📎 رفع مرفقات وصور</li>";
    echo "<li>🔄 تتبع حالة المطالب</li>";
    echo "<li>💬 رد الإدارة على المطالب</li>";
    echo "</ul>";
    echo "</div>";
    echo "</div>";

    echo "<div class='col-md-6'>";
    echo "<div class='feature-box'>";
    echo "<h6><i class='fas fa-file-alt text-info'></i> التقارير والإحصائيات</h6>";
    echo "<ul>";
    echo "<li>📈 تقارير أسبوعية من الإداريين</li>";
    echo "<li>📊 إحصائيات شخصية لكل إداري</li>";
    echo "<li>🎯 تتبع الأهداف والإنجازات</li>";
    echo "<li>📋 خطط الأسبوع القادم</li>";
    echo "</ul>";
    echo "</div>";
    echo "</div>";

    echo "</div>";

    echo "<div class='alert alert-info'>";
    echo "<h6><i class='fas fa-database'></i> الجداول التي سيتم إنشاؤها:</h6>";
    echo "<ul>";
    echo "<li><strong>admins:</strong> بيانات الإداريين</li>";
    echo "<li><strong>messages:</strong> الرسائل بين الإدارة والإداريين</li>";
    echo "<li><strong>weekly_reports:</strong> التقارير الأسبوعية</li>";
    echo "<li><strong>supporter_requests:</strong> مطالب المؤيدين</li>";
    echo "<li><strong>notifications:</strong> الإشعارات</li>";
    echo "<li><strong>admin_sessions:</strong> جلسات الإداريين</li>";
    echo "<li><strong>admin_statistics:</strong> إحصائيات الإداريين</li>";
    echo "</ul>";
    echo "</div>";

    echo "<form method='POST' action=''>";
    echo "<div class='text-center mt-4'>";
    echo "<button type='submit' name='fix_all_system' class='btn btn-success btn-lg'>";
    echo "<i class='fas fa-tools'></i> إصلاح النظام الكامل";
    echo "</button>";
    echo "</div>";
    echo "</form>";

    echo "<div class='text-center mt-3'>";
    echo "<a href='test_original_supporters.php' class='btn btn-info me-2'><i class='fas fa-vial'></i> اختبار النظام</a>";
    echo "<a href='create_admin_system.php' class='btn btn-warning me-2'><i class='fas fa-cogs'></i> النظام الإداري فقط</a>";
    echo "<a href='dashboard.php' class='btn btn-secondary'><i class='fas fa-home'></i> الصفحة الرئيسية</a>";
    echo "</div>";
}

echo "</div>";
echo "</body>";
echo "</html>";
?>
