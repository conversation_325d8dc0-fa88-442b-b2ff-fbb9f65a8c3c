<?php
// دوال الرسائل
require_once __DIR__ . '/../config/database.php';
require_once __DIR__ . '/notification_functions.php';

/**
 * إرسال رسالة جديدة
 */
function sendMessage($data) {
    // توليد thread_id للمحادثة
    $thread_id = $data['thread_id'] ?? generateThreadId();
    
    $sql = "INSERT INTO messages (
        sender_type, sender_id, receiver_type, receiver_id, subject, message,
        message_type, priority, is_broadcast, scheduled_at, expires_at,
        parent_message_id, thread_id
    ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)";
    
    $params = [
        $data['sender_type'],
        $data['sender_id'],
        $data['receiver_type'],
        $data['receiver_id'] ?? null,
        $data['subject'],
        $data['message'],
        $data['message_type'] ?? 'personal',
        $data['priority'] ?? 'normal',
        $data['is_broadcast'] ?? false,
        $data['scheduled_at'] ?? null,
        $data['expires_at'] ?? null,
        $data['parent_message_id'] ?? null,
        $thread_id
    ];
    
    try {
        $result = executeQuery($sql, $params);
        $message_id = $GLOBALS['pdo']->lastInsertId();
        
        // تسجيل النشاط
        logActivity([
            'user_type' => $data['sender_type'],
            'user_id' => $data['sender_id'],
            'action_type' => 'send',
            'target_type' => 'message',
            'target_id' => $message_id,
            'target_name' => $data['subject'],
            'description' => 'تم إرسال رسالة: ' . $data['subject']
        ]);
        
        // إنشاء إشعار للمستقبل
        if ($data['receiver_type'] !== 'all_admins') {
            notifyNewMessage($message_id);
        } else {
            // إشعار لجميع الإداريين
            $admins = fetchAll("SELECT id FROM admins WHERE status = 'active'");
            foreach ($admins as $admin) {
                createNotification([
                    'user_type' => 'admin',
                    'user_id' => $admin['id'],
                    'title' => 'رسالة جديدة',
                    'message' => 'رسالة جديدة: ' . $data['subject'],
                    'type' => 'new_message',
                    'category' => 'message',
                    'related_type' => 'message',
                    'related_id' => $message_id,
                    'action_url' => "modules/admin/messages.php?view=$message_id"
                ]);
            }
        }
        
        // تحديث إحصائيات المرسل
        if ($data['sender_type'] === 'admin') {
            updateAdminDailyStats($data['sender_id'], 'messages_sent', 1);
        }
        
        return $message_id;
    } catch (Exception $e) {
        error_log("خطأ في إرسال الرسالة: " . $e->getMessage());
        return false;
    }
}

/**
 * توليد معرف المحادثة
 */
function generateThreadId() {
    return 'thread_' . time() . '_' . mt_rand(1000, 9999);
}

/**
 * الرد على رسالة
 */
function replyToMessage($original_message_id, $reply_data) {
    // جلب الرسالة الأصلية
    $original = fetchOne("SELECT * FROM messages WHERE id = ?", [$original_message_id]);
    
    if (!$original) {
        return false;
    }
    
    // إعداد بيانات الرد
    $reply_data['parent_message_id'] = $original_message_id;
    $reply_data['thread_id'] = $original['thread_id'];
    $reply_data['receiver_type'] = $original['sender_type'];
    $reply_data['receiver_id'] = $original['sender_id'];
    
    // إضافة "Re: " للموضوع إذا لم يكن موجوداً
    if (!str_starts_with($reply_data['subject'], 'Re: ')) {
        $reply_data['subject'] = 'Re: ' . $original['subject'];
    }
    
    $message_id = sendMessage($reply_data);
    
    if ($message_id) {
        // تحديث الرسالة الأصلية
        executeQuery("UPDATE messages SET replied_at = NOW() WHERE id = ?", [$original_message_id]);
    }
    
    return $message_id;
}

/**
 * جلب الرسائل للمستخدم
 */
function getUserMessages($user_type, $user_id = null, $folder = 'inbox', $limit = 50, $offset = 0) {
    $sql = "SELECT m.*, 
            CASE 
                WHEN m.sender_type = 'admin' THEN a.full_name
                WHEN m.sender_type = 'management' THEN u.full_name
                ELSE 'النظام'
            END as sender_name,
            (SELECT COUNT(*) FROM message_attachments WHERE message_id = m.id) as attachment_count,
            (SELECT COUNT(*) FROM messages WHERE parent_message_id = m.id) as reply_count
            FROM messages m 
            LEFT JOIN admins a ON m.sender_id = a.id AND m.sender_type = 'admin'
            LEFT JOIN users u ON m.sender_id = u.id AND m.sender_type = 'management'
            WHERE ";
    
    $params = [];
    
    if ($folder === 'inbox') {
        $sql .= "(m.receiver_type = ? AND (m.receiver_id = ? OR m.receiver_id IS NULL))";
        $params[] = $user_type;
        $params[] = $user_id;
    } elseif ($folder === 'sent') {
        $sql .= "(m.sender_type = ? AND m.sender_id = ?)";
        $params[] = $user_type;
        $params[] = $user_id;
    }
    
    $sql .= " AND m.status != 'deleted'";
    $sql .= " ORDER BY m.created_at DESC LIMIT ? OFFSET ?";
    $params[] = $limit;
    $params[] = $offset;
    
    return fetchAll($sql, $params);
}

/**
 * عدد الرسائل
 */
function countUserMessages($user_type, $user_id = null, $folder = 'inbox', $unread_only = false) {
    $sql = "SELECT COUNT(*) as count FROM messages m WHERE ";
    $params = [];
    
    if ($folder === 'inbox') {
        $sql .= "(m.receiver_type = ? AND (m.receiver_id = ? OR m.receiver_id IS NULL))";
        $params[] = $user_type;
        $params[] = $user_id;
    } elseif ($folder === 'sent') {
        $sql .= "(m.sender_type = ? AND m.sender_id = ?)";
        $params[] = $user_type;
        $params[] = $user_id;
    }
    
    $sql .= " AND m.status != 'deleted'";
    
    if ($unread_only && $folder === 'inbox') {
        $sql .= " AND m.status = 'unread'";
    }
    
    $result = fetchOne($sql, $params);
    return $result['count'] ?? 0;
}

/**
 * تحديد الرسالة كمقروءة
 */
function markMessageAsRead($message_id, $user_type, $user_id) {
    $sql = "UPDATE messages SET status = 'read', read_at = NOW() 
            WHERE id = ? AND receiver_type = ? AND (receiver_id = ? OR receiver_id IS NULL) AND status = 'unread'";
    
    $result = executeQuery($sql, [$message_id, $user_type, $user_id]);
    
    if ($result) {
        // تسجيل قراءة الرسالة
        $read_sql = "INSERT IGNORE INTO message_reads (message_id, reader_type, reader_id, ip_address, user_agent) 
                     VALUES (?, ?, ?, ?, ?)";
        executeQuery($read_sql, [
            $message_id, $user_type, $user_id,
            $_SERVER['REMOTE_ADDR'] ?? null,
            $_SERVER['HTTP_USER_AGENT'] ?? null
        ]);
    }
    
    return $result;
}

/**
 * حذف رسالة
 */
function deleteMessage($message_id, $user_type, $user_id) {
    // التحقق من صلاحية الحذف
    $message = fetchOne("
        SELECT * FROM messages 
        WHERE id = ? AND (
            (sender_type = ? AND sender_id = ?) OR 
            (receiver_type = ? AND (receiver_id = ? OR receiver_id IS NULL))
        )
    ", [$message_id, $user_type, $user_id, $user_type, $user_id]);
    
    if (!$message) {
        return false;
    }
    
    // تحديث حالة الرسالة إلى محذوفة
    $result = executeQuery("UPDATE messages SET status = 'deleted' WHERE id = ?", [$message_id]);
    
    if ($result) {
        // تسجيل النشاط
        logActivity([
            'user_type' => $user_type,
            'user_id' => $user_id,
            'action_type' => 'delete',
            'target_type' => 'message',
            'target_id' => $message_id,
            'target_name' => $message['subject'],
            'description' => 'تم حذف الرسالة: ' . $message['subject']
        ]);
    }
    
    return $result;
}

/**
 * إضافة مرفق للرسالة
 */
function addMessageAttachment($message_id, $file_data) {
    $sql = "INSERT INTO message_attachments (
        message_id, file_name, original_name, file_path, file_size,
        file_type, mime_type, attachment_type, is_inline
    ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)";
    
    $params = [
        $message_id,
        $file_data['file_name'],
        $file_data['original_name'],
        $file_data['file_path'],
        $file_data['file_size'],
        $file_data['file_type'],
        $file_data['mime_type'],
        $file_data['attachment_type'] ?? 'document',
        $file_data['is_inline'] ?? false
    ];
    
    try {
        $result = executeQuery($sql, $params);
        return $GLOBALS['pdo']->lastInsertId();
    } catch (Exception $e) {
        error_log("خطأ في إضافة مرفق الرسالة: " . $e->getMessage());
        return false;
    }
}

/**
 * جلب مرفقات الرسالة
 */
function getMessageAttachments($message_id) {
    return fetchAll("SELECT * FROM message_attachments WHERE message_id = ? ORDER BY upload_date", [$message_id]);
}

/**
 * تحديث عداد التحميل للمرفق
 */
function incrementAttachmentDownload($attachment_id) {
    return executeQuery("UPDATE message_attachments SET download_count = download_count + 1 WHERE id = ?", [$attachment_id]);
}

/**
 * البحث في الرسائل
 */
function searchMessages($user_type, $user_id, $search_term, $filters = [], $limit = 50) {
    $sql = "SELECT m.*, 
            CASE 
                WHEN m.sender_type = 'admin' THEN a.full_name
                WHEN m.sender_type = 'management' THEN u.full_name
                ELSE 'النظام'
            END as sender_name,
            (SELECT COUNT(*) FROM message_attachments WHERE message_id = m.id) as attachment_count
            FROM messages m 
            LEFT JOIN admins a ON m.sender_id = a.id AND m.sender_type = 'admin'
            LEFT JOIN users u ON m.sender_id = u.id AND m.sender_type = 'management'
            WHERE (
                (m.receiver_type = ? AND (m.receiver_id = ? OR m.receiver_id IS NULL)) OR
                (m.sender_type = ? AND m.sender_id = ?)
            ) AND m.status != 'deleted'";
    
    $params = [$user_type, $user_id, $user_type, $user_id];
    
    // البحث النصي
    if (!empty($search_term)) {
        $sql .= " AND (MATCH(m.subject, m.message) AGAINST(? IN NATURAL LANGUAGE MODE))";
        $params[] = $search_term;
    }
    
    // فلاتر إضافية
    if (!empty($filters['priority'])) {
        $sql .= " AND m.priority = ?";
        $params[] = $filters['priority'];
    }
    
    if (!empty($filters['message_type'])) {
        $sql .= " AND m.message_type = ?";
        $params[] = $filters['message_type'];
    }
    
    if (!empty($filters['date_from'])) {
        $sql .= " AND m.created_at >= ?";
        $params[] = $filters['date_from'];
    }
    
    if (!empty($filters['date_to'])) {
        $sql .= " AND m.created_at <= ?";
        $params[] = $filters['date_to'] . ' 23:59:59';
    }
    
    $sql .= " ORDER BY m.created_at DESC LIMIT ?";
    $params[] = $limit;
    
    return fetchAll($sql, $params);
}

/**
 * جلب المحادثة كاملة
 */
function getMessageThread($thread_id, $user_type, $user_id) {
    return fetchAll("
        SELECT m.*, 
        CASE 
            WHEN m.sender_type = 'admin' THEN a.full_name
            WHEN m.sender_type = 'management' THEN u.full_name
            ELSE 'النظام'
        END as sender_name,
        (SELECT COUNT(*) FROM message_attachments WHERE message_id = m.id) as attachment_count
        FROM messages m 
        LEFT JOIN admins a ON m.sender_id = a.id AND m.sender_type = 'admin'
        LEFT JOIN users u ON m.sender_id = u.id AND m.sender_type = 'management'
        WHERE m.thread_id = ? AND (
            (m.receiver_type = ? AND (m.receiver_id = ? OR m.receiver_id IS NULL)) OR
            (m.sender_type = ? AND m.sender_id = ?)
        ) AND m.status != 'deleted'
        ORDER BY m.created_at ASC
    ", [$thread_id, $user_type, $user_id, $user_type, $user_id]);
}

/**
 * إنشاء رسالة من قالب
 */
function createMessageFromTemplate($template_id, $variables, $sender_type, $sender_id, $receiver_type, $receiver_id = null) {
    $template = fetchOne("SELECT * FROM message_templates WHERE id = ? AND is_active = 1", [$template_id]);
    
    if (!$template) {
        return false;
    }
    
    // استبدال المتغيرات
    $subject = replaceTemplateVariables($template['subject'], $variables);
    $content = replaceTemplateVariables($template['content'], $variables);
    
    $data = [
        'sender_type' => $sender_type,
        'sender_id' => $sender_id,
        'receiver_type' => $receiver_type,
        'receiver_id' => $receiver_id,
        'subject' => $subject,
        'message' => $content,
        'message_type' => $template['template_type']
    ];
    
    return sendMessage($data);
}

/**
 * إحصائيات الرسائل
 */
function getMessagesStatistics($user_type, $user_id = null, $days = 30) {
    $sql = "SELECT 
        COUNT(*) as total,
        SUM(CASE WHEN status = 'unread' THEN 1 ELSE 0 END) as unread_count,
        SUM(CASE WHEN status = 'read' THEN 1 ELSE 0 END) as read_count,
        SUM(CASE WHEN priority = 'urgent' THEN 1 ELSE 0 END) as urgent_count,
        SUM(CASE WHEN message_type = 'announcement' THEN 1 ELSE 0 END) as announcement_count,
        COUNT(DISTINCT thread_id) as conversation_count
    FROM messages 
    WHERE receiver_type = ? AND created_at >= DATE_SUB(NOW(), INTERVAL ? DAY) AND status != 'deleted'";
    
    $params = [$user_type, $days];
    
    if ($user_id !== null) {
        $sql .= " AND (receiver_id = ? OR receiver_id IS NULL)";
        $params[] = $user_id;
    }
    
    return fetchOne($sql, $params);
}

/**
 * الرسائل غير المقروءة
 */
function getUnreadMessages($user_type, $user_id = null, $limit = 10) {
    $sql = "SELECT m.*, 
            CASE 
                WHEN m.sender_type = 'admin' THEN a.full_name
                WHEN m.sender_type = 'management' THEN u.full_name
                ELSE 'النظام'
            END as sender_name
            FROM messages m 
            LEFT JOIN admins a ON m.sender_id = a.id AND m.sender_type = 'admin'
            LEFT JOIN users u ON m.sender_id = u.id AND m.sender_type = 'management'
            WHERE m.receiver_type = ? AND m.status = 'unread'";
    
    $params = [$user_type];
    
    if ($user_id !== null) {
        $sql .= " AND (m.receiver_id = ? OR m.receiver_id IS NULL)";
        $params[] = $user_id;
    }
    
    $sql .= " ORDER BY m.created_at DESC LIMIT ?";
    $params[] = $limit;
    
    return fetchAll($sql, $params);
}
?>
