<?php
// استيراد المؤيدين - نسخة مبسطة وعملية
header('Content-Type: text/html; charset=utf-8');
ini_set('default_charset', 'utf-8');
mb_internal_encoding('UTF-8');

require_once '../../config/config.php';
require_once '../../config/database.php';

// التحقق من تسجيل الدخول
if (!isLoggedIn()) {
    redirect('../../login.php');
}

// معالجة رفع الملف
if ($_SERVER['REQUEST_METHOD'] == 'POST' && isset($_FILES['import_file'])) {
    $file = $_FILES['import_file'];

    // التحقق من نوع الملف
    $file_extension = strtolower(pathinfo($file['name'], PATHINFO_EXTENSION));
    if (!in_array($file_extension, ['csv', 'txt'])) {
        showMessage('نوع الملف غير مدعوم. يرجى رفع ملف CSV أو TXT', 'error');
    } else {
        // التحقق من حجم الملف (5MB)
        if ($file['size'] > 5 * 1024 * 1024) {
            showMessage('حجم الملف كبير جداً. الحد الأقصى 5 ميجابايت', 'error');
        } else {
            // قراءة الملف
            $file_content = file_get_contents($file['tmp_name']);

            // تحويل الترميز إلى UTF-8 إذا لزم الأمر
            if (!mb_check_encoding($file_content, 'UTF-8')) {
                $file_content = mb_convert_encoding($file_content, 'UTF-8', 'auto');
            }

            // تقسيم الملف إلى أسطر
            $lines = explode("\n", $file_content);

            // إزالة السطر الأول (العناوين) إذا كان موجود
            if (count($lines) > 1) {
                array_shift($lines);
            }

            $success_count = 0;
            $error_count = 0;
            $errors = [];

            // جلب المناطق
            $regions = fetchAll("SELECT id, name FROM regions");
            $region_map = [];
            foreach ($regions as $region) {
                $region_map[strtolower(trim($region['name']))] = $region['id'];
            }

            foreach ($lines as $line_number => $line) {
                $line = trim($line);
                if (empty($line)) continue;

                // تقسيم السطر (CSV)
                $data = str_getcsv($line);

                // التأكد من وجود البيانات الأساسية
                if (count($data) < 4) {
                    $errors[] = "السطر " . ($line_number + 2) . ": بيانات غير كافية";
                    $error_count++;
                    continue;
                }

                // استخراج البيانات
                $full_name = trim($data[0] ?? '');
                $gender = strtolower(trim($data[1] ?? ''));
                $phone = trim($data[2] ?? '');
                $address = trim($data[3] ?? '');
                $region_name = strtolower(trim($data[4] ?? ''));
                $birth_date = trim($data[5] ?? '');
                $voter_number = trim($data[6] ?? '');
                $profession = trim($data[7] ?? '');
                $notes = trim($data[8] ?? '');

                // التحقق من البيانات المطلوبة
                if (empty($full_name) || empty($phone) || empty($address)) {
                    $errors[] = "السطر " . ($line_number + 2) . ": الاسم والهاتف والعنوان مطلوبة";
                    $error_count++;
                    continue;
                }

                // تحويل الجنس
                $gender_map = [
                    'ذكر' => 'male', 'male' => 'male', 'm' => 'male',
                    'أنثى' => 'female', 'female' => 'female', 'f' => 'female'
                ];

                if (!isset($gender_map[$gender])) {
                    $gender = 'male'; // افتراضي
                } else {
                    $gender = $gender_map[$gender];
                }

                // التحقق من رقم الهاتف
                if (!preg_match('/^07[0-9]{9}$/', $phone)) {
                    $errors[] = "السطر " . ($line_number + 2) . ": رقم هاتف غير صحيح ($phone)";
                    $error_count++;
                    continue;
                }

                // البحث عن المنطقة أو إنشاء منطقة جديدة
                $region_id = 1; // افتراضي
                if (!empty($region_name)) {
                    if (isset($region_map[$region_name])) {
                        $region_id = $region_map[$region_name];
                    } else {
                        // إنشاء منطقة جديدة
                        $sql = "INSERT INTO regions (name, created_at) VALUES (?, NOW())";
                        $result = executeQuery($sql, [ucfirst($region_name)]);
                        if ($result) {
                            $region_id = getLastInsertId();
                            $region_map[$region_name] = $region_id;
                        }
                    }
                }

                // تحويل تاريخ الميلاد
                if (empty($birth_date)) {
                    $birth_date = '1990-01-01';
                } else {
                    $birth_date = date('Y-m-d', strtotime($birth_date));
                    if ($birth_date === '1970-01-01') {
                        $birth_date = '1990-01-01';
                    }
                }

                // التحقق من تكرار رقم الناخب
                if (!empty($voter_number)) {
                    $existing = fetchOne("SELECT id FROM supporters WHERE voter_number = ?", [$voter_number]);
                    if ($existing) {
                        $errors[] = "السطر " . ($line_number + 2) . ": رقم الناخب موجود مسبقاً ($voter_number)";
                        $error_count++;
                        continue;
                    }
                }

                // إدراج البيانات
                $sql = "INSERT INTO supporters (full_name, gender, marital_status, birth_date, profession, address, phone, voter_number, region_id, notes, added_by, created_at)
                        VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, NOW())";

                $params = [
                    $full_name, $gender, 'single', $birth_date, $profession,
                    $address, $phone, $voter_number, $region_id, $notes, $_SESSION['user_id']
                ];

                $result = executeQuery($sql, $params);

                if ($result) {
                    $success_count++;
                } else {
                    $errors[] = "السطر " . ($line_number + 2) . ": فشل في إدراج البيانات";
                    $error_count++;
                }
            }

            // عرض النتائج
            if ($success_count > 0) {
                showMessage("تم استيراد $success_count مؤيد بنجاح", 'success');
            }

            if ($error_count > 0) {
                showMessage("حدث $error_count خطأ أثناء الاستيراد", 'warning');
                $_SESSION['import_errors'] = array_slice($errors, 0, 10); // أول 10 أخطاء فقط
            }
        }
    }
}
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>استيراد المؤيدين - <?php echo SITE_NAME; ?></title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        body { font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; background: #f8f9fa; }
        .main-content { margin-top: 80px; }
        .import-card { background: white; border-radius: 15px; box-shadow: 0 5px 15px rgba(0,0,0,0.1); }
    </style>
</head>
<body>
    <!-- شريط التنقل العلوي -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary fixed-top">
        <div class="container-fluid">
            <a class="navbar-brand" href="../../dashboard.php">
                <i class="fas fa-vote-yea me-2"></i>
                نظام إدارة الحملة الانتخابية
            </a>

            <div class="navbar-nav ms-auto">
                <div class="nav-item dropdown">
                    <a class="nav-link dropdown-toggle" href="#" id="userDropdown" role="button" data-bs-toggle="dropdown">
                        <i class="fas fa-user-circle me-1"></i>
                        <?php echo htmlspecialchars($_SESSION['full_name']); ?>
                    </a>
                    <ul class="dropdown-menu dropdown-menu-end">
                        <li><a class="dropdown-item" href="../../logout.php"><i class="fas fa-sign-out-alt me-2"></i>تسجيل الخروج</a></li>
                    </ul>
                </div>
            </div>
        </div>
    </nav>

    <!-- المحتوى الرئيسي -->
    <div class="container main-content">
        <div class="row justify-content-center">
            <div class="col-md-8">
                <div class="import-card p-4">
                    <div class="text-center mb-4">
                        <h2><i class="fas fa-upload text-primary me-2"></i>استيراد المؤيدين</h2>
                        <p class="text-muted">رفع ملف CSV يحتوي على بيانات المؤيدين</p>
                    </div>

                    <?php displayMessage(); ?>

                    <!-- عرض الأخطاء إن وجدت -->
                    <?php if (isset($_SESSION['import_errors'])): ?>
                    <div class="alert alert-warning">
                        <h6><i class="fas fa-exclamation-triangle me-2"></i>أخطاء الاستيراد:</h6>
                        <ul class="mb-0">
                            <?php foreach ($_SESSION['import_errors'] as $error): ?>
                            <li><?php echo htmlspecialchars($error); ?></li>
                            <?php endforeach; ?>
                        </ul>
                    </div>
                    <?php unset($_SESSION['import_errors']); ?>
                    <?php endif; ?>

                    <!-- نموذج رفع الملف -->
                    <form method="POST" enctype="multipart/form-data" class="mb-4">
                        <div class="mb-3">
                            <label for="import_file" class="form-label">ملف CSV</label>
                            <input type="file" class="form-control" id="import_file" name="import_file" accept=".csv,.txt" required>
                            <div class="form-text">يجب أن يكون الملف بصيغة CSV أو TXT</div>
                        </div>

                        <div class="d-grid">
                            <button type="submit" class="btn btn-primary btn-lg">
                                <i class="fas fa-upload me-2"></i>استيراد البيانات
                            </button>
                        </div>
                    </form>

                    <!-- تعليمات الاستيراد -->
                    <div class="alert alert-info">
                        <h6><i class="fas fa-info-circle me-2"></i>تعليمات الاستيراد:</h6>
                        <ol class="mb-0">
                            <li><strong>ترتيب الأعمدة:</strong> الاسم الكامل، الجنس، رقم الهاتف، العنوان، المنطقة، تاريخ الميلاد، رقم الناخب، المهنة، ملاحظات</li>
                            <li><strong>الحقول المطلوبة:</strong> الاسم الكامل، رقم الهاتف، العنوان</li>
                            <li><strong>الجنس:</strong> ذكر أو أنثى (أو male/female)</li>
                            <li><strong>رقم الهاتف:</strong> يجب أن يبدأ بـ 07 ويتكون من 11 رقم</li>
                            <li><strong>تاريخ الميلاد:</strong> بصيغة YYYY-MM-DD أو DD/MM/YYYY</li>
                        </ol>
                    </div>

                    <!-- نموذج CSV -->
                    <div class="alert alert-secondary">
                        <h6><i class="fas fa-file-csv me-2"></i>نموذج ملف CSV:</h6>
                        <p>يمكنك تحميل نموذج جاهز للتعديل عليه:</p>
                        <a href="template_csv.php" class="btn btn-outline-primary btn-sm">
                            <i class="fas fa-download me-2"></i>تحميل نموذج CSV
                        </a>
                        <hr>
                        <strong>مثال على البيانات:</strong><br>
                        <code>
                            أحمد محمد علي,ذكر,07701234567,بغداد - الكرادة,الكرادة,1990-01-01,123456789,مهندس,مؤيد نشط<br>
                            فاطمة أحمد حسن,أنثى,07801234567,بغداد - الجادرية,الجادرية,1985-05-15,,طبيبة,
                        </code>
                    </div>

                    <!-- أزرار التنقل -->
                    <div class="text-center">
                        <a href="supporters_working.php" class="btn btn-secondary me-2">
                            <i class="fas fa-arrow-left me-2"></i>العودة للمؤيدين
                        </a>
                        <a href="export_simple.php" class="btn btn-success">
                            <i class="fas fa-download me-2"></i>تصدير المؤيدين
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
