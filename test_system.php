<?php
// ملف اختبار النظام
error_reporting(E_ALL);
ini_set('display_errors', 1);
header('Content-Type: text/html; charset=utf-8');

echo "<!DOCTYPE html>";
echo "<html lang='ar' dir='rtl'>";
echo "<head>";
echo "<meta charset='UTF-8'>";
echo "<meta name='viewport' content='width=device-width, initial-scale=1.0'>";
echo "<title>اختبار النظام</title>";
echo "<link href='https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css' rel='stylesheet'>";
echo "<style>body { font-family: Arial, sans-serif; padding: 20px; }</style>";
echo "</head>";
echo "<body>";

echo "<div class='container'>";
echo "<h1 class='text-center mb-4'>🔍 اختبار النظام</h1>";

// اختبار PHP
echo "<div class='card mb-3'>";
echo "<div class='card-header bg-primary text-white'>";
echo "<h5>اختبار PHP</h5>";
echo "</div>";
echo "<div class='card-body'>";
echo "<p><strong>إصدار PHP:</strong> " . phpversion() . "</p>";
echo "<p><strong>الوقت الحالي:</strong> " . date('Y-m-d H:i:s') . "</p>";
echo "<p><strong>الترميز:</strong> " . mb_internal_encoding() . "</p>";
echo "</div>";
echo "</div>";

// اختبار الملفات
echo "<div class='card mb-3'>";
echo "<div class='card-header bg-info text-white'>";
echo "<h5>اختبار الملفات</h5>";
echo "</div>";
echo "<div class='card-body'>";

$files_to_check = [
    'config/database.php',
    'config/config.php',
    'dashboard.php',
    'modules/admin/login.php',
    'modules/admin/dashboard.php'
];

foreach ($files_to_check as $file) {
    if (file_exists($file)) {
        echo "<p class='text-success'>✅ $file - موجود</p>";
    } else {
        echo "<p class='text-danger'>❌ $file - غير موجود</p>";
    }
}
echo "</div>";
echo "</div>";

// اختبار قاعدة البيانات
echo "<div class='card mb-3'>";
echo "<div class='card-header bg-warning text-dark'>";
echo "<h5>اختبار قاعدة البيانات</h5>";
echo "</div>";
echo "<div class='card-body'>";

try {
    // محاولة الاتصال بقاعدة البيانات
    $host = 'localhost';
    $dbname = 'cpses_irs7jkoxpg'; // اسم قاعدة البيانات الخاصة بك
    $username = 'cpses_irs7jkoxpg'; // اسم المستخدم
    $password = 'Zain@123456789'; // كلمة المرور
    
    $pdo = new PDO("mysql:host=$host;dbname=$dbname;charset=utf8mb4", $username, $password);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    echo "<p class='text-success'>✅ الاتصال بقاعدة البيانات نجح</p>";
    
    // اختبار الجداول
    $tables = ['users', 'admins', 'supporters', 'regions', 'messages', 'supporter_requests', 'weekly_reports', 'notifications'];
    
    foreach ($tables as $table) {
        try {
            $stmt = $pdo->query("SELECT COUNT(*) FROM $table");
            $count = $stmt->fetchColumn();
            echo "<p class='text-success'>✅ جدول $table - يحتوي على $count سجل</p>";
        } catch (Exception $e) {
            echo "<p class='text-danger'>❌ جدول $table - خطأ: " . $e->getMessage() . "</p>";
        }
    }
    
} catch (Exception $e) {
    echo "<p class='text-danger'>❌ خطأ في الاتصال بقاعدة البيانات: " . $e->getMessage() . "</p>";
}

echo "</div>";
echo "</div>";

// اختبار الدوال
echo "<div class='card mb-3'>";
echo "<div class='card-header bg-success text-white'>";
echo "<h5>اختبار الدوال</h5>";
echo "</div>";
echo "<div class='card-body'>";

if (file_exists('config/database.php')) {
    try {
        require_once 'config/database.php';
        echo "<p class='text-success'>✅ تم تحميل ملف database.php</p>";
        
        if (function_exists('getDBConnection')) {
            echo "<p class='text-success'>✅ دالة getDBConnection موجودة</p>";
        } else {
            echo "<p class='text-danger'>❌ دالة getDBConnection غير موجودة</p>";
        }
        
        if (function_exists('fetchOne')) {
            echo "<p class='text-success'>✅ دالة fetchOne موجودة</p>";
        } else {
            echo "<p class='text-danger'>❌ دالة fetchOne غير موجودة</p>";
        }
        
        if (function_exists('executeQuery')) {
            echo "<p class='text-success'>✅ دالة executeQuery موجودة</p>";
        } else {
            echo "<p class='text-danger'>❌ دالة executeQuery غير موجودة</p>";
        }
        
    } catch (Exception $e) {
        echo "<p class='text-danger'>❌ خطأ في تحميل database.php: " . $e->getMessage() . "</p>";
    }
} else {
    echo "<p class='text-danger'>❌ ملف database.php غير موجود</p>";
}

echo "</div>";
echo "</div>";

// روابط الاختبار
echo "<div class='card mb-3'>";
echo "<div class='card-header bg-secondary text-white'>";
echo "<h5>روابط الاختبار</h5>";
echo "</div>";
echo "<div class='card-body'>";

$test_links = [
    ['الصفحة الرئيسية', 'index.php'],
    ['لوحة تحكم المرشح', 'dashboard.php'],
    ['تسجيل دخول الإداريين', 'modules/admin/login.php'],
    ['لوحة تحكم الإداريين', 'modules/admin/dashboard.php'],
    ['إضافة مؤيد', 'modules/admin/add_supporter.php']
];

foreach ($test_links as $link) {
    echo "<p><a href='{$link[1]}' class='btn btn-sm btn-outline-primary' target='_blank'>{$link[0]}</a></p>";
}

echo "</div>";
echo "</div>";

// معلومات الخادم
echo "<div class='card mb-3'>";
echo "<div class='card-header bg-dark text-white'>";
echo "<h5>معلومات الخادم</h5>";
echo "</div>";
echo "<div class='card-body'>";
echo "<p><strong>نظام التشغيل:</strong> " . php_uname() . "</p>";
echo "<p><strong>مجلد العمل:</strong> " . getcwd() . "</p>";
echo "<p><strong>امتدادات PHP المحملة:</strong></p>";
echo "<ul>";
$extensions = ['pdo', 'pdo_mysql', 'mbstring', 'json'];
foreach ($extensions as $ext) {
    if (extension_loaded($ext)) {
        echo "<li class='text-success'>✅ $ext</li>";
    } else {
        echo "<li class='text-danger'>❌ $ext</li>";
    }
}
echo "</ul>";
echo "</div>";
echo "</div>";

echo "</div>";
echo "</body>";
echo "</html>";
?>
