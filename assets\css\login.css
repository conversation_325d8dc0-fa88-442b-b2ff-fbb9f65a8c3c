/* تصميم صفحة تسجيل الدخول */
body {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    min-height: 100vh;
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
}

.login-container {
    min-height: 100vh;
    display: flex;
    align-items: center;
    justify-content: center;
}

.login-card {
    background: rgba(255, 255, 255, 0.95);
    border-radius: 20px;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    overflow: hidden;
    max-width: 400px;
    width: 100%;
    animation: slideInUp 0.8s ease-out;
}

.login-header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 2rem;
    text-align: center;
}

.login-header i {
    font-size: 3rem;
    margin-bottom: 1rem;
    animation: pulse 2s infinite;
}

.login-body {
    padding: 2rem;
}

.form-control {
    border-radius: 10px;
    border: 2px solid #e9ecef;
    padding: 12px 15px;
    transition: all 0.3s ease;
    font-size: 16px;
}

.form-control:focus {
    border-color: #667eea;
    box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
    transform: translateY(-2px);
}

.btn-login {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border: none;
    border-radius: 10px;
    padding: 12px;
    font-weight: bold;
    transition: all 0.3s ease;
    width: 100%;
    font-size: 16px;
    color: white;
}

.btn-login:hover {
    transform: translateY(-2px);
    box-shadow: 0 10px 20px rgba(102, 126, 234, 0.3);
    color: white;
}

.btn-login:active {
    transform: translateY(0);
}

.input-group-text {
    background: #f8f9fa;
    border: 2px solid #e9ecef;
    border-left: none;
    border-radius: 10px 0 0 10px;
}

.input-group .form-control {
    border-right: none;
    border-radius: 0 10px 10px 0;
}

.alert {
    border-radius: 10px;
    border: none;
    animation: fadeInDown 0.5s ease-out;
}

.floating-shapes {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    pointer-events: none;
    z-index: -1;
}

.shape {
    position: absolute;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 50%;
    animation: float 6s ease-in-out infinite;
}

.shape:nth-child(1) {
    width: 80px;
    height: 80px;
    top: 20%;
    left: 10%;
    animation-delay: 0s;
}

.shape:nth-child(2) {
    width: 120px;
    height: 120px;
    top: 60%;
    right: 10%;
    animation-delay: 2s;
}

.shape:nth-child(3) {
    width: 60px;
    height: 60px;
    bottom: 20%;
    left: 20%;
    animation-delay: 4s;
}

/* الحركات والتأثيرات */
@keyframes float {
    0%, 100% { 
        transform: translateY(0px); 
    }
    50% { 
        transform: translateY(-20px); 
    }
}

@keyframes slideInUp {
    from {
        transform: translateY(50px);
        opacity: 0;
    }
    to {
        transform: translateY(0);
        opacity: 1;
    }
}

@keyframes fadeInDown {
    from {
        transform: translateY(-20px);
        opacity: 0;
    }
    to {
        transform: translateY(0);
        opacity: 1;
    }
}

@keyframes pulse {
    0% {
        transform: scale(1);
    }
    50% {
        transform: scale(1.1);
    }
    100% {
        transform: scale(1);
    }
}

/* تصميم متجاوب للجوال */
@media (max-width: 768px) {
    .login-card {
        margin: 1rem;
        max-width: none;
    }
    
    .login-header {
        padding: 1.5rem;
    }
    
    .login-header i {
        font-size: 2.5rem;
    }
    
    .login-body {
        padding: 1.5rem;
    }
    
    .form-control {
        font-size: 16px; /* منع التكبير في iOS */
    }
}

/* تحسينات إضافية */
.form-label {
    font-weight: 600;
    color: #495057;
    margin-bottom: 0.5rem;
}

.text-muted {
    font-size: 0.875rem;
}

/* تأثير التركيز على الحقول */
.input-group:focus-within .input-group-text {
    border-color: #667eea;
    background: rgba(102, 126, 234, 0.1);
}

/* تحسين شكل الأزرار */
.btn-login:focus {
    box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.5);
}

/* تأثيرات إضافية للشكل */
.login-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg, #667eea, #764ba2, #667eea);
    background-size: 200% 100%;
    animation: shimmer 3s ease-in-out infinite;
}

@keyframes shimmer {
    0% {
        background-position: -200% 0;
    }
    100% {
        background-position: 200% 0;
    }
}
