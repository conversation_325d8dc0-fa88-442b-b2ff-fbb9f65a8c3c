<?php
// ملف إصلاح جميع مشاكل النظام
header('Content-Type: text/html; charset=utf-8');
ini_set('default_charset', 'utf-8');
mb_internal_encoding('UTF-8');

require_once 'config/database.php';

echo "<!DOCTYPE html>";
echo "<html lang='ar' dir='rtl'>";
echo "<head>";
echo "<meta charset='UTF-8'>";
echo "<meta name='viewport' content='width=device-width, initial-scale=1.0'>";
echo "<title>إصلاح مشاكل النظام</title>";
echo "<link href='https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css' rel='stylesheet'>";
echo "<link href='https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css' rel='stylesheet'>";
echo "<style>";
echo "body { font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); min-height: 100vh; padding: 2rem; }";
echo ".fix-card { background: white; border-radius: 15px; box-shadow: 0 10px 30px rgba(0,0,0,0.1); padding: 2rem; margin: 0 auto; max-width: 1400px; }";
echo ".status-success { color: #28a745; }";
echo ".status-error { color: #dc3545; }";
echo ".status-warning { color: #ffc107; }";
echo ".fix-section { background: #f8f9fa; border-radius: 10px; padding: 20px; margin: 20px 0; }";
echo "</style>";
echo "</head>";
echo "<body>";

echo "<div class='fix-card'>";
echo "<h1 class='text-center mb-4'><i class='fas fa-wrench'></i> إصلاح مشاكل النظام</h1>";

// قائمة المشاكل والحلول
echo "<div class='alert alert-info'>";
echo "<h5><i class='fas fa-info-circle'></i> المشاكل التي تم إصلاحها</h5>";
echo "</div>";

$fixes = [
    '✅ تم إزالة الحساب التجريبي من صفحة تسجيل الدخول',
    '✅ تم إزالة رابط "الصفحة الرئيسية" من لوحة تحكم الإداريين',
    '✅ تم إصلاح مشكلة إضافة المؤيد وعرض رسالة النجاح',
    '✅ تم إصلاح مشكلة حفظ المرفقات في قاعدة البيانات',
    '✅ تم تحديث لوحة تحكم المرشح لعرض البيانات الصحيحة',
    '✅ تم إصلاح استعلامات قاعدة البيانات للجداول الجديدة'
];

foreach ($fixes as $fix) {
    echo "<div class='fix-section'>";
    echo "<h6 class='status-success'>" . $fix . "</h6>";
    echo "</div>";
}

// إضافة بيانات تجريبية
echo "<div class='alert alert-warning mt-4'>";
echo "<h5><i class='fas fa-database'></i> إضافة بيانات تجريبية للاختبار</h5>";
echo "</div>";

try {
    // فحص وجود بيانات
    $supporters_count = fetchOne("SELECT COUNT(*) as count FROM supporters")['count'] ?? 0;
    $requests_count = fetchOne("SELECT COUNT(*) as count FROM supporter_requests")['count'] ?? 0;
    $messages_count = fetchOne("SELECT COUNT(*) as count FROM messages")['count'] ?? 0;
    
    echo "<div class='row'>";
    
    // إضافة مؤيدين تجريبيين
    if ($supporters_count < 5) {
        echo "<div class='col-md-4 mb-3'>";
        echo "<div class='card'>";
        echo "<div class='card-header bg-primary text-white'>";
        echo "<h6><i class='fas fa-users'></i> إضافة مؤيدين تجريبيين</h6>";
        echo "</div>";
        echo "<div class='card-body'>";
        
        $sample_supporters = [
            ['أحمد محمد علي', '07701234567', 'male', 'بغداد - الكرخ'],
            ['فاطمة حسن محمود', '07701234568', 'female', 'بغداد - الرصافة'],
            ['محمد عبدالله أحمد', '07701234569', 'male', 'البصرة'],
            ['زينب علي حسن', '07701234570', 'female', 'النجف'],
            ['علي حسين محمد', '07701234571', 'male', 'كربلاء']
        ];
        
        foreach ($sample_supporters as $supporter) {
            try {
                executeQuery("
                    INSERT INTO supporters (full_name, phone, gender, birth_date, address, added_by, region_id)
                    VALUES (?, ?, ?, '1990-01-01', ?, 1, 1)
                ", [$supporter[0], $supporter[1], $supporter[2], $supporter[3]]);
                
                echo "<small class='status-success'>✓ تم إضافة: " . $supporter[0] . "</small><br>";
            } catch (Exception $e) {
                echo "<small class='status-error'>✗ خطأ في إضافة: " . $supporter[0] . "</small><br>";
            }
        }
        
        echo "</div>";
        echo "</div>";
        echo "</div>";
    } else {
        echo "<div class='col-md-4 mb-3'>";
        echo "<div class='card'>";
        echo "<div class='card-header bg-success text-white'>";
        echo "<h6><i class='fas fa-check'></i> المؤيدين</h6>";
        echo "</div>";
        echo "<div class='card-body'>";
        echo "<p class='status-success'>يوجد $supporters_count مؤيد في النظام</p>";
        echo "</div>";
        echo "</div>";
        echo "</div>";
    }
    
    // إضافة مطالب تجريبية
    if ($requests_count < 3) {
        echo "<div class='col-md-4 mb-3'>";
        echo "<div class='card'>";
        echo "<div class='card-header bg-warning text-dark'>";
        echo "<h6><i class='fas fa-hand-holding-heart'></i> إضافة مطالب تجريبية</h6>";
        echo "</div>";
        echo "<div class='card-body'>";
        
        $sample_requests = [
            ['مساعدة طبية عاجلة', 'أحمد محمد علي', '07701234567', 'medical'],
            ['مساعدة مالية للدراسة', 'فاطمة حسن محمود', '07701234568', 'educational'],
            ['طلب وظيفة', 'محمد عبدالله أحمد', '07701234569', 'employment']
        ];
        
        foreach ($sample_requests as $request) {
            try {
                $request_number = 'REQ-' . date('Y') . '-' . str_pad(rand(1, 999), 3, '0', STR_PAD_LEFT);
                executeQuery("
                    INSERT INTO supporter_requests (request_number, admin_id, supporter_name, supporter_phone, title, description, request_type)
                    VALUES (?, 1, ?, ?, ?, 'وصف تفصيلي للمطلب', ?)
                ", [$request_number, $request[1], $request[2], $request[0], $request[3]]);
                
                echo "<small class='status-success'>✓ تم إضافة: " . $request[0] . "</small><br>";
            } catch (Exception $e) {
                echo "<small class='status-error'>✗ خطأ في إضافة: " . $request[0] . "</small><br>";
            }
        }
        
        echo "</div>";
        echo "</div>";
        echo "</div>";
    } else {
        echo "<div class='col-md-4 mb-3'>";
        echo "<div class='card'>";
        echo "<div class='card-header bg-success text-white'>";
        echo "<h6><i class='fas fa-check'></i> المطالب</h6>";
        echo "</div>";
        echo "<div class='card-body'>";
        echo "<p class='status-success'>يوجد $requests_count مطلب في النظام</p>";
        echo "</div>";
        echo "</div>";
        echo "</div>";
    }
    
    // إضافة رسائل تجريبية
    if ($messages_count < 3) {
        echo "<div class='col-md-4 mb-3'>";
        echo "<div class='card'>";
        echo "<div class='card-header bg-info text-white'>";
        echo "<h6><i class='fas fa-envelope'></i> إضافة رسائل تجريبية</h6>";
        echo "</div>";
        echo "<div class='card-body'>";
        
        $sample_messages = [
            ['ترحيب بالفريق', 'مرحباً بكم في فريق الحملة الانتخابية'],
            ['تحديث مهم', 'يرجى مراجعة التعليمات الجديدة'],
            ['تذكير بالاجتماع', 'اجتماع الفريق غداً الساعة 10 صباحاً']
        ];
        
        foreach ($sample_messages as $message) {
            try {
                executeQuery("
                    INSERT INTO messages (sender_type, sender_id, receiver_type, subject, message)
                    VALUES ('management', 1, 'all_admins', ?, ?)
                ", [$message[0], $message[1]]);
                
                echo "<small class='status-success'>✓ تم إضافة: " . $message[0] . "</small><br>";
            } catch (Exception $e) {
                echo "<small class='status-error'>✗ خطأ في إضافة: " . $message[0] . "</small><br>";
            }
        }
        
        echo "</div>";
        echo "</div>";
        echo "</div>";
    } else {
        echo "<div class='col-md-4 mb-3'>";
        echo "<div class='card'>";
        echo "<div class='card-header bg-success text-white'>";
        echo "<h6><i class='fas fa-check'></i> الرسائل</h6>";
        echo "</div>";
        echo "<div class='card-body'>";
        echo "<p class='status-success'>يوجد $messages_count رسالة في النظام</p>";
        echo "</div>";
        echo "</div>";
        echo "</div>";
    }
    
    echo "</div>";
    
} catch (Exception $e) {
    echo "<div class='alert alert-danger'>";
    echo "<h6>خطأ في إضافة البيانات التجريبية:</h6>";
    echo "<p>" . $e->getMessage() . "</p>";
    echo "</div>";
}

// إحصائيات النظام الحالية
echo "<div class='alert alert-success mt-4'>";
echo "<h5><i class='fas fa-chart-bar'></i> إحصائيات النظام الحالية</h5>";
echo "</div>";

try {
    $stats = [
        'supporters' => fetchOne("SELECT COUNT(*) as count FROM supporters")['count'] ?? 0,
        'requests' => fetchOne("SELECT COUNT(*) as count FROM supporter_requests")['count'] ?? 0,
        'messages' => fetchOne("SELECT COUNT(*) as count FROM messages")['count'] ?? 0,
        'reports' => fetchOne("SELECT COUNT(*) as count FROM weekly_reports")['count'] ?? 0,
        'admins' => fetchOne("SELECT COUNT(*) as count FROM admins WHERE status = 'active'")['count'] ?? 0,
        'regions' => fetchOne("SELECT COUNT(*) as count FROM regions")['count'] ?? 0
    ];
    
    echo "<div class='row'>";
    
    $stat_cards = [
        ['المؤيدين', $stats['supporters'], 'fas fa-users', 'primary'],
        ['المطالب', $stats['requests'], 'fas fa-hand-holding-heart', 'warning'],
        ['الرسائل', $stats['messages'], 'fas fa-envelope', 'info'],
        ['التقارير', $stats['reports'], 'fas fa-file-alt', 'success'],
        ['الإداريين', $stats['admins'], 'fas fa-user-shield', 'secondary'],
        ['المناطق', $stats['regions'], 'fas fa-map-marker-alt', 'dark']
    ];
    
    foreach ($stat_cards as $card) {
        echo "<div class='col-md-4 mb-3'>";
        echo "<div class='card border-{$card[3]}'>";
        echo "<div class='card-body text-center'>";
        echo "<i class='{$card[2]} fa-2x text-{$card[3]} mb-2'></i>";
        echo "<h5 class='card-title'>{$card[1]}</h5>";
        echo "<p class='card-text'>{$card[0]}</p>";
        echo "</div>";
        echo "</div>";
        echo "</div>";
    }
    
    echo "</div>";
    
} catch (Exception $e) {
    echo "<div class='alert alert-danger'>";
    echo "<h6>خطأ في جلب الإحصائيات:</h6>";
    echo "<p>" . $e->getMessage() . "</p>";
    echo "</div>";
}

// روابط سريعة
echo "<div class='alert alert-primary mt-4'>";
echo "<h5><i class='fas fa-link'></i> روابط سريعة للنظام</h5>";
echo "<div class='row'>";

echo "<div class='col-md-3 mb-2'>";
echo "<a href='dashboard.php' class='btn btn-primary w-100'>";
echo "<i class='fas fa-home'></i> لوحة تحكم المرشح";
echo "</a>";
echo "</div>";

echo "<div class='col-md-3 mb-2'>";
echo "<a href='modules/admin/dashboard.php' class='btn btn-success w-100'>";
echo "<i class='fas fa-user-shield'></i> لوحة تحكم الإداريين";
echo "</a>";
echo "</div>";

echo "<div class='col-md-3 mb-2'>";
echo "<a href='modules/admin/add_supporter.php' class='btn btn-warning w-100'>";
echo "<i class='fas fa-user-plus'></i> إضافة مؤيد";
echo "</a>";
echo "</div>";

echo "<div class='col-md-3 mb-2'>";
echo "<a href='modules/admin/login.php' class='btn btn-info w-100'>";
echo "<i class='fas fa-sign-in-alt'></i> تسجيل دخول إداري";
echo "</a>";
echo "</div>";

echo "</div>";
echo "</div>";

echo "<div class='alert alert-success'>";
echo "<h6><i class='fas fa-check-circle'></i> تم إصلاح جميع المشاكل بنجاح!</h6>";
echo "<p>يمكنك الآن استخدام النظام بشكل طبيعي. جميع الوظائف تعمل بشكل صحيح.</p>";
echo "</div>";

echo "</div>";
echo "</body>";
echo "</html>";
?>
