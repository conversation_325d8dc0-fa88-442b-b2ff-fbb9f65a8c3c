<?php
// إنشاء جداول الإشعارات وسجل الأنشطة
header('Content-Type: text/html; charset=utf-8');
ini_set('default_charset', 'utf-8');
mb_internal_encoding('UTF-8');

require_once '../config/config.php';
require_once '../config/database.php';

function createNotificationsTables() {
    $tables = [];
    
    // جدول الإشعارات
    $tables['notifications'] = "CREATE TABLE IF NOT EXISTS notifications (
        id INT AUTO_INCREMENT PRIMARY KEY,
        notification_uuid VARCHAR(36) UNIQUE NOT NULL,
        user_type ENUM('admin', 'management', 'candidate', 'all') NOT NULL,
        user_id INT NULL,
        title VARCHAR(200) NOT NULL,
        message TEXT NOT NULL,
        short_message VARCHAR(255),
        
        -- نوع الإشعار
        type ENUM('info', 'success', 'warning', 'error', 'new_supporter', 'new_request', 'new_message', 'new_report', 'system', 'reminder', 'urgent') DEFAULT 'info',
        category ENUM('system', 'supporter', 'request', 'message', 'report', 'admin', 'general') DEFAULT 'general',
        priority ENUM('low', 'normal', 'high', 'urgent') DEFAULT 'normal',
        
        -- البيانات المرتبطة
        related_type ENUM('supporter', 'request', 'message', 'report', 'admin', 'user', 'system') NULL,
        related_id INT NULL,
        related_data JSON,
        
        -- إعدادات الإشعار
        is_read BOOLEAN DEFAULT FALSE,
        is_archived BOOLEAN DEFAULT FALSE,
        is_pinned BOOLEAN DEFAULT FALSE,
        requires_action BOOLEAN DEFAULT FALSE,
        
        -- روابط الإجراءات
        action_url VARCHAR(255),
        action_text VARCHAR(100),
        secondary_action_url VARCHAR(255),
        secondary_action_text VARCHAR(100),
        
        -- إعدادات العرض
        icon VARCHAR(50) DEFAULT 'fas fa-bell',
        color VARCHAR(7) DEFAULT '#007bff',
        image_url VARCHAR(255),
        
        -- تواريخ مهمة
        scheduled_at DATETIME NULL,
        expires_at DATETIME NULL,
        read_at DATETIME NULL,
        archived_at DATETIME NULL,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        
        -- معلومات إضافية
        sender_type ENUM('system', 'admin', 'management', 'candidate') DEFAULT 'system',
        sender_id INT NULL,
        device_tokens JSON,
        
        INDEX idx_user (user_type, user_id),
        INDEX idx_is_read (is_read),
        INDEX idx_type (type),
        INDEX idx_category (category),
        INDEX idx_priority (priority),
        INDEX idx_created_at (created_at),
        INDEX idx_related (related_type, related_id),
        INDEX idx_scheduled_at (scheduled_at),
        INDEX idx_expires_at (expires_at)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci";

    // جدول سجل الأنشطة
    $tables['activity_log'] = "CREATE TABLE IF NOT EXISTS activity_log (
        id INT AUTO_INCREMENT PRIMARY KEY,
        activity_uuid VARCHAR(36) UNIQUE NOT NULL,
        
        -- معلومات المستخدم
        user_type ENUM('admin', 'management', 'candidate', 'system') NOT NULL,
        user_id INT NULL,
        user_name VARCHAR(100),
        
        -- نوع النشاط
        action_type ENUM('create', 'update', 'delete', 'login', 'logout', 'view', 'export', 'import', 'approve', 'reject', 'send', 'receive') NOT NULL,
        target_type ENUM('supporter', 'request', 'message', 'report', 'admin', 'user', 'system', 'file', 'setting') NOT NULL,
        target_id INT NULL,
        target_name VARCHAR(200),
        
        -- تفاصيل النشاط
        description TEXT NOT NULL,
        details JSON,
        old_values JSON,
        new_values JSON,
        
        -- معلومات الجلسة
        session_id VARCHAR(255),
        ip_address VARCHAR(45),
        user_agent TEXT,
        device_info VARCHAR(255),
        browser_info VARCHAR(255),
        location VARCHAR(100),
        
        -- معلومات إضافية
        severity ENUM('low', 'medium', 'high', 'critical') DEFAULT 'medium',
        status ENUM('success', 'failed', 'pending', 'cancelled') DEFAULT 'success',
        duration_ms INT DEFAULT 0,
        
        -- تصنيف النشاط
        category ENUM('authentication', 'data_modification', 'file_operation', 'communication', 'system', 'security') DEFAULT 'data_modification',
        tags VARCHAR(255),
        
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        
        INDEX idx_user (user_type, user_id),
        INDEX idx_action_type (action_type),
        INDEX idx_target (target_type, target_id),
        INDEX idx_created_at (created_at),
        INDEX idx_ip_address (ip_address),
        INDEX idx_severity (severity),
        INDEX idx_category (category),
        INDEX idx_session_id (session_id)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci";

    // جدول إعدادات الإشعارات للمستخدمين
    $tables['notification_settings'] = "CREATE TABLE IF NOT EXISTS notification_settings (
        id INT AUTO_INCREMENT PRIMARY KEY,
        user_type ENUM('admin', 'management', 'candidate') NOT NULL,
        user_id INT NOT NULL,
        
        -- إعدادات عامة
        email_notifications BOOLEAN DEFAULT TRUE,
        sms_notifications BOOLEAN DEFAULT FALSE,
        push_notifications BOOLEAN DEFAULT TRUE,
        desktop_notifications BOOLEAN DEFAULT TRUE,
        
        -- إعدادات حسب النوع
        new_supporter_notifications BOOLEAN DEFAULT TRUE,
        new_request_notifications BOOLEAN DEFAULT TRUE,
        new_message_notifications BOOLEAN DEFAULT TRUE,
        new_report_notifications BOOLEAN DEFAULT TRUE,
        system_notifications BOOLEAN DEFAULT TRUE,
        reminder_notifications BOOLEAN DEFAULT TRUE,
        urgent_notifications BOOLEAN DEFAULT TRUE,
        
        -- إعدادات التوقيت
        quiet_hours_start TIME DEFAULT '22:00:00',
        quiet_hours_end TIME DEFAULT '08:00:00',
        weekend_notifications BOOLEAN DEFAULT FALSE,
        
        -- إعدادات التجميع
        digest_frequency ENUM('immediate', 'hourly', 'daily', 'weekly') DEFAULT 'immediate',
        max_notifications_per_hour INT DEFAULT 10,
        
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        
        UNIQUE KEY unique_user_settings (user_type, user_id),
        INDEX idx_user (user_type, user_id)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci";

    // جدول قوالب الإشعارات
    $tables['notification_templates'] = "CREATE TABLE IF NOT EXISTS notification_templates (
        id INT AUTO_INCREMENT PRIMARY KEY,
        name VARCHAR(100) NOT NULL,
        description TEXT,
        template_key VARCHAR(100) UNIQUE NOT NULL,
        
        -- محتوى القالب
        title_template VARCHAR(200) NOT NULL,
        message_template TEXT NOT NULL,
        short_message_template VARCHAR(255),
        
        -- إعدادات القالب
        type ENUM('info', 'success', 'warning', 'error', 'new_supporter', 'new_request', 'new_message', 'new_report', 'system', 'reminder', 'urgent') DEFAULT 'info',
        category ENUM('system', 'supporter', 'request', 'message', 'report', 'admin', 'general') DEFAULT 'general',
        priority ENUM('low', 'normal', 'high', 'urgent') DEFAULT 'normal',
        
        -- متغيرات القالب
        variables JSON,
        
        -- إعدادات العرض
        icon VARCHAR(50) DEFAULT 'fas fa-bell',
        color VARCHAR(7) DEFAULT '#007bff',
        
        -- حالة القالب
        is_active BOOLEAN DEFAULT TRUE,
        is_system BOOLEAN DEFAULT FALSE,
        
        created_by INT,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        
        FOREIGN KEY (created_by) REFERENCES users(id) ON DELETE SET NULL,
        
        INDEX idx_template_key (template_key),
        INDEX idx_type (type),
        INDEX idx_category (category),
        INDEX idx_is_active (is_active)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci";

    // جدول إحصائيات الإشعارات
    $tables['notification_statistics'] = "CREATE TABLE IF NOT EXISTS notification_statistics (
        id INT AUTO_INCREMENT PRIMARY KEY,
        date DATE NOT NULL,
        user_type ENUM('admin', 'management', 'candidate', 'all') NOT NULL,
        user_id INT NULL,
        
        -- إحصائيات الإشعارات
        total_sent INT DEFAULT 0,
        total_read INT DEFAULT 0,
        total_unread INT DEFAULT 0,
        total_archived INT DEFAULT 0,
        
        -- إحصائيات حسب النوع
        info_notifications INT DEFAULT 0,
        success_notifications INT DEFAULT 0,
        warning_notifications INT DEFAULT 0,
        error_notifications INT DEFAULT 0,
        urgent_notifications INT DEFAULT 0,
        
        -- إحصائيات حسب الفئة
        supporter_notifications INT DEFAULT 0,
        request_notifications INT DEFAULT 0,
        message_notifications INT DEFAULT 0,
        report_notifications INT DEFAULT 0,
        system_notifications INT DEFAULT 0,
        
        -- معدلات
        read_rate DECIMAL(5,2) DEFAULT 0,
        response_time_avg_minutes DECIMAL(8,2) DEFAULT 0,
        
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        
        UNIQUE KEY unique_date_user (date, user_type, user_id),
        INDEX idx_date (date),
        INDEX idx_user (user_type, user_id)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci";

    return $tables;
}

function insertDefaultNotificationData() {
    $queries = [];
    
    // إدراج قوالب الإشعارات الافتراضية
    $queries[] = "INSERT IGNORE INTO notification_templates (name, template_key, title_template, message_template, type, category, variables, icon, color, is_system) VALUES 
        ('مؤيد جديد', 'new_supporter', 'تم إضافة مؤيد جديد', 'تم إضافة المؤيد {supporter_name} بواسطة {admin_name}', 'new_supporter', 'supporter', '{\"supporter_name\": \"اسم المؤيد\", \"admin_name\": \"اسم الإداري\"}', 'fas fa-user-plus', '#28a745', TRUE),
        ('مطلب جديد', 'new_request', 'مطلب جديد من مؤيد', 'تم تقديم مطلب جديد: {request_title} من المؤيد {supporter_name}', 'new_request', 'request', '{\"request_title\": \"عنوان المطلب\", \"supporter_name\": \"اسم المؤيد\"}', 'fas fa-hand-holding-heart', '#ffc107', TRUE),
        ('رسالة جديدة', 'new_message', 'رسالة جديدة', 'رسالة جديدة من {sender_name}: {message_subject}', 'new_message', 'message', '{\"sender_name\": \"اسم المرسل\", \"message_subject\": \"موضوع الرسالة\"}', 'fas fa-envelope', '#007bff', TRUE),
        ('تقرير جديد', 'new_report', 'تقرير أسبوعي جديد', 'تم تقديم تقرير أسبوعي جديد من {admin_name} للفترة {week_period}', 'new_report', 'report', '{\"admin_name\": \"اسم الإداري\", \"week_period\": \"فترة الأسبوع\"}', 'fas fa-file-alt', '#6f42c1', TRUE),
        ('إداري جديد', 'new_admin', 'إداري جديد انضم للفريق', 'انضم إداري جديد للفريق: {admin_name} في منطقة {region_name}', 'info', 'admin', '{\"admin_name\": \"اسم الإداري\", \"region_name\": \"اسم المنطقة\"}', 'fas fa-user-tie', '#17a2b8', TRUE),
        ('تذكير تقرير', 'report_reminder', 'تذكير: التقرير الأسبوعي', 'تذكير بضرورة تقديم التقرير الأسبوعي قبل نهاية اليوم', 'reminder', 'report', '{}', 'fas fa-clock', '#fd7e14', TRUE),
        ('طلب عاجل', 'urgent_request', 'مطلب عاجل يحتاج متابعة', 'مطلب عاجل من {supporter_name}: {request_title}', 'urgent', 'request', '{\"supporter_name\": \"اسم المؤيد\", \"request_title\": \"عنوان المطلب\"}', 'fas fa-exclamation-triangle', '#dc3545', TRUE)";

    // إدراج إعدادات إشعارات افتراضية للمرشح
    $queries[] = "INSERT IGNORE INTO notification_settings (user_type, user_id, email_notifications, push_notifications, new_supporter_notifications, new_request_notifications, new_message_notifications, new_report_notifications) VALUES 
        ('candidate', 1, TRUE, TRUE, TRUE, TRUE, TRUE, TRUE)";

    return $queries;
}

// تشغيل الدوال إذا تم استدعاء الملف مباشرة
if (basename($_SERVER['PHP_SELF']) == 'create_notifications_tables.php') {
    echo "<!DOCTYPE html>";
    echo "<html lang='ar' dir='rtl'>";
    echo "<head>";
    echo "<meta charset='UTF-8'>";
    echo "<meta name='viewport' content='width=device-width, initial-scale=1.0'>";
    echo "<title>إنشاء جداول الإشعارات</title>";
    echo "<link href='https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css' rel='stylesheet'>";
    echo "<link href='https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css' rel='stylesheet'>";
    echo "<style>";
    echo "body { font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); min-height: 100vh; padding: 2rem; }";
    echo ".system-card { background: white; border-radius: 15px; box-shadow: 0 10px 30px rgba(0,0,0,0.1); padding: 2rem; margin: 0 auto; max-width: 1000px; }";
    echo ".test-result { padding: 10px; margin: 5px 0; border-radius: 5px; }";
    echo ".test-success { background: #d4edda; border: 1px solid #c3e6cb; }";
    echo ".test-error { background: #f8d7da; border: 1px solid #f5c6cb; }";
    echo "</style>";
    echo "</head>";
    echo "<body>";

    echo "<div class='system-card'>";
    echo "<h1 class='text-center mb-4'><i class='fas fa-bell'></i> إنشاء جداول الإشعارات</h1>";

    if (isset($_POST['create_notifications_tables'])) {
        echo "<div class='alert alert-info'>";
        echo "<h5>جاري إنشاء جداول الإشعارات...</h5>";
        echo "</div>";

        try {
            // إنشاء الجداول
            $tables = createNotificationsTables();
            foreach ($tables as $table_name => $sql) {
                try {
                    executeQuery($sql);
                    echo "<div class='test-result test-success'><i class='fas fa-check'></i> جدول $table_name: تم إنشاؤه بنجاح</div>";
                } catch (Exception $e) {
                    echo "<div class='test-result test-success'><i class='fas fa-info'></i> جدول $table_name: موجود مسبقاً</div>";
                }
            }

            // إدراج البيانات الافتراضية
            echo "<h6 class='mt-4'>إدراج البيانات الافتراضية:</h6>";
            $queries = insertDefaultNotificationData();
            foreach ($queries as $query) {
                try {
                    executeQuery($query);
                    echo "<div class='test-result test-success'><i class='fas fa-check'></i> تم إدراج قوالب الإشعارات الافتراضية</div>";
                } catch (Exception $e) {
                    echo "<div class='test-result test-success'><i class='fas fa-info'></i> القوالب موجودة مسبقاً</div>";
                }
            }

            echo "<div class='alert alert-success mt-4'>";
            echo "<h3><i class='fas fa-check-circle'></i> تم إنشاء جداول الإشعارات بنجاح!</h3>";
            echo "</div>";

        } catch (Exception $e) {
            echo "<div class='alert alert-danger'>";
            echo "<h5>خطأ في الإنشاء:</h5>";
            echo "<p>" . htmlspecialchars($e->getMessage()) . "</p>";
            echo "</div>";
        }

        echo "<div class='text-center mt-4'>";
        echo "<a href='../create_all_database.php' class='btn btn-primary btn-lg me-2'><i class='fas fa-database'></i> إنشاء جميع الجداول</a>";
        echo "<a href='../dashboard.php' class='btn btn-success btn-lg'><i class='fas fa-home'></i> الصفحة الرئيسية</a>";
        echo "</div>";

    } else {
        echo "<div class='alert alert-warning'>";
        echo "<h5><i class='fas fa-info-circle'></i> إنشاء جداول الإشعارات</h5>";
        echo "<p>سيتم إنشاء جميع الجداول المتعلقة بالإشعارات وسجل الأنشطة</p>";
        echo "</div>";

        echo "<div class='card mb-4'>";
        echo "<div class='card-header bg-danger text-white'>";
        echo "<h6><i class='fas fa-table'></i> الجداول التي سيتم إنشاؤها</h6>";
        echo "</div>";
        echo "<div class='card-body'>";
        echo "<div class='row'>";
        echo "<div class='col-md-6'>";
        echo "<ul>";
        echo "<li><strong>notifications:</strong> الإشعارات الأساسية</li>";
        echo "<li><strong>activity_log:</strong> سجل الأنشطة</li>";
        echo "<li><strong>notification_settings:</strong> إعدادات الإشعارات</li>";
        echo "</ul>";
        echo "</div>";
        echo "<div class='col-md-6'>";
        echo "<ul>";
        echo "<li><strong>notification_templates:</strong> قوالب الإشعارات</li>";
        echo "<li><strong>notification_statistics:</strong> إحصائيات الإشعارات</li>";
        echo "</ul>";
        echo "</div>";
        echo "</div>";
        echo "</div>";
        echo "</div>";

        echo "<div class='alert alert-info'>";
        echo "<h6><i class='fas fa-star'></i> الميزات المتضمنة:</h6>";
        echo "<ul>";
        echo "<li>🔔 <strong>إشعارات متقدمة:</strong> فورية ومجدولة</li>";
        echo "<li>📝 <strong>سجل شامل:</strong> تتبع جميع الأنشطة</li>";
        echo "<li>⚙️ <strong>إعدادات مخصصة:</strong> تحكم كامل في الإشعارات</li>";
        echo "<li>📋 <strong>قوالب جاهزة:</strong> قوالب معدة مسبقاً</li>";
        echo "<li>📊 <strong>إحصائيات مفصلة:</strong> تحليل الإشعارات</li>";
        echo "<li>🎯 <strong>إشعارات مستهدفة:</strong> حسب المستخدم والنوع</li>";
        echo "</ul>";
        echo "</div>";

        echo "<form method='POST' action=''>";
        echo "<div class='text-center'>";
        echo "<button type='submit' name='create_notifications_tables' class='btn btn-success btn-lg'>";
        echo "<i class='fas fa-database'></i> إنشاء جداول الإشعارات";
        echo "</button>";
        echo "</div>";
        echo "</form>";

        echo "<div class='text-center mt-3'>";
        echo "<a href='create_reports_tables.php' class='btn btn-secondary me-2'><i class='fas fa-arrow-right'></i> جداول التقارير</a>";
        echo "<a href='../dashboard.php' class='btn btn-secondary'><i class='fas fa-home'></i> الصفحة الرئيسية</a>";
        echo "</div>";
    }

    echo "</div>";
    echo "</body>";
    echo "</html>";
}
?>
