<?php
// اختبار جميع كلمات المرور المحتملة
error_reporting(E_ALL);
ini_set('display_errors', 1);
header('Content-Type: text/html; charset=utf-8');

echo "<!DOCTYPE html>";
echo "<html lang='ar' dir='rtl'>";
echo "<head>";
echo "<meta charset='UTF-8'>";
echo "<title>اختبار جميع كلمات المرور</title>";
echo "<link href='https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css' rel='stylesheet'>";
echo "</head>";
echo "<body style='padding:20px;'>";

echo "<div class='container'>";
echo "<h1>🔐 اختبار جميع كلمات المرور المحتملة</h1>";

$passwords_to_test = [
    'irjnpfzw_mr',
    'Zain@123456789', 
    '',
    'abd123',
    'password',
    '123456',
    'irjnpfzw',
    'mr123',
    'zainalabden',
    'admin123'
];

$working_password = null;

foreach ($passwords_to_test as $index => $password) {
    echo "<div class='mb-3'>";
    echo "<h5>اختبار " . ($index + 1) . ": ";
    
    if ($password === '') {
        echo "كلمة مرور فارغة";
    } else {
        echo str_repeat('*', strlen($password)) . " (طول: " . strlen($password) . ")";
    }
    
    echo "</h5>";
    
    try {
        $pdo = new PDO("mysql:host=localhost;dbname=irjnpfzw_mr;charset=utf8mb4", "irjnpfzw_mr", $password);
        $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
        
        echo "<div class='alert alert-success'>";
        echo "<h6>✅ نجح الاتصال!</h6>";
        echo "<p><strong>كلمة المرور الصحيحة:</strong> ";
        if ($password === '') {
            echo "فارغة (بدون كلمة مرور)";
        } else {
            echo $password;
        }
        echo "</p>";
        
        // اختبار جدول المؤيدين
        try {
            $stmt = $pdo->query("SELECT COUNT(*) FROM supporters");
            $count = $stmt->fetchColumn();
            echo "<p>عدد المؤيدين: <strong>$count</strong></p>";
        } catch (Exception $e) {
            echo "<p class='text-warning'>تحذير: " . $e->getMessage() . "</p>";
        }
        
        $working_password = $password;
        echo "</div>";
        break;
        
    } catch (Exception $e) {
        echo "<div class='alert alert-danger'>";
        echo "<p>❌ فشل: " . $e->getMessage() . "</p>";
        echo "</div>";
    }
    
    echo "</div>";
}

if ($working_password !== null) {
    echo "<div class='alert alert-success mt-4'>";
    echo "<h4>🎉 تم العثور على كلمة المرور الصحيحة!</h4>";
    echo "<p><strong>كلمة المرور:</strong> ";
    if ($working_password === '') {
        echo "فارغة (بدون كلمة مرور)";
    } else {
        echo $working_password;
    }
    echo "</p>";
    
    // تحديث ملف database.php
    $database_content = "<?php
// إعدادات قاعدة البيانات - صحيحة ومختبرة
define('DB_HOST', 'localhost');
define('DB_NAME', 'irjnpfzw_mr');
define('DB_USER', 'irjnpfzw_mr');
define('DB_PASS', '$working_password');
define('DB_CHARSET', 'utf8mb4');

class Database {
    private \$host = DB_HOST;
    private \$db_name = DB_NAME;
    private \$username = DB_USER;
    private \$password = DB_PASS;
    private \$charset = DB_CHARSET;
    public \$conn;

    public function getConnection() {
        \$this->conn = null;
        try {
            \$dsn = \"mysql:host=\" . \$this->host . \";dbname=\" . \$this->db_name . \";charset=utf8mb4\";
            \$this->conn = new PDO(\$dsn, \$this->username, \$this->password, array(
                PDO::MYSQL_ATTR_INIT_COMMAND => \"SET NAMES utf8mb4 COLLATE utf8mb4_unicode_ci\",
                PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
                PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
                PDO::ATTR_EMULATE_PREPARES => false
            ));
        } catch(PDOException \$exception) {
            echo \"خطأ في الاتصال: \" . \$exception->getMessage();
            return null;
        }
        return \$this->conn;
    }
}

function getDBConnection() {
    \$database = new Database();
    return \$database->getConnection();
}

function fetchOne(\$sql, \$params = []) {
    try {
        \$conn = getDBConnection();
        if (!\$conn) {
            return false;
        }
        \$stmt = \$conn->prepare(\$sql);
        \$stmt->execute(\$params);
        return \$stmt->fetch(PDO::FETCH_ASSOC);
    } catch(PDOException \$e) {
        error_log(\"Database Error: \" . \$e->getMessage());
        return false;
    }
}

function fetchAll(\$sql, \$params = []) {
    try {
        \$conn = getDBConnection();
        if (!\$conn) {
            return [];
        }
        \$stmt = \$conn->prepare(\$sql);
        \$stmt->execute(\$params);
        return \$stmt->fetchAll(PDO::FETCH_ASSOC);
    } catch(PDOException \$e) {
        error_log(\"Database Error: \" . \$e->getMessage());
        return [];
    }
}

function executeQuery(\$sql, \$params = []) {
    try {
        \$conn = getDBConnection();
        if (!\$conn) {
            return false;
        }
        \$stmt = \$conn->prepare(\$sql);
        return \$stmt->execute(\$params);
    } catch(PDOException \$e) {
        error_log(\"Database Error: \" . \$e->getMessage());
        return false;
    }
}

function getLastInsertId() {
    \$conn = getDBConnection();
    return \$conn ? \$conn->lastInsertId() : 0;
}

function showMessage(\$message, \$type = 'info') {
    if (\$type === 'error') {
        error_log(\$message);
    }
}
?>";
    
    if (file_put_contents('config/database.php', $database_content)) {
        echo "<p class='text-success'>✅ تم تحديث ملف database.php بكلمة المرور الصحيحة</p>";
    } else {
        echo "<p class='text-danger'>❌ فشل في تحديث ملف database.php</p>";
    }
    
    echo "<div class='text-center mt-3'>";
    echo "<a href='dashboard.php' class='btn btn-primary me-2' target='_blank'>جرب لوحة تحكم المرشح</a>";
    echo "<a href='modules/admin/dashboard.php' class='btn btn-success me-2' target='_blank'>جرب لوحة تحكم الإداريين</a>";
    echo "<a href='test_connection.php' class='btn btn-info' target='_blank'>اختبار الاتصال</a>";
    echo "</div>";
    
    echo "</div>";
} else {
    echo "<div class='alert alert-danger mt-4'>";
    echo "<h4>❌ لم يتم العثور على كلمة مرور صحيحة</h4>";
    echo "<p>جرب الحلول البديلة:</p>";
    echo "<div class='text-center'>";
    echo "<a href='demo_system.php' class='btn btn-warning me-2'>النظام التجريبي</a>";
    echo "<a href='create_database.php' class='btn btn-danger'>إنشاء قاعدة بيانات جديدة</a>";
    echo "</div>";
    echo "</div>";
}

echo "</div>";
echo "</body>";
echo "</html>";
?>
