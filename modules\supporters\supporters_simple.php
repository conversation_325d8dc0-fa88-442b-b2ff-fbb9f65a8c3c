<?php
// تعيين الترميز العربي
header('Content-Type: text/html; charset=utf-8');
ini_set('default_charset', 'utf-8');
mb_internal_encoding('UTF-8');

require_once '../../config/config.php';
require_once '../../config/database.php';

// التحقق من تسجيل الدخول
if (!isLoggedIn()) {
    redirect('../../login.php');
}

// معالجة إضافة مؤيد جديد
if ($_SERVER['REQUEST_METHOD'] == 'POST' && isset($_POST['action']) && $_POST['action'] == 'add') {
    $full_name = sanitize($_POST['full_name']);
    $gender = sanitize($_POST['gender']);
    $marital_status = sanitize($_POST['marital_status']);
    $birth_date = sanitize($_POST['birth_date']);
    $education = sanitize($_POST['education']);
    $profession = sanitize($_POST['profession']);
    $address = sanitize($_POST['address']);
    $phone = sanitize($_POST['phone']);
    $voter_number = sanitize($_POST['voter_number']);
    $voting_center = sanitize($_POST['voting_center']);
    $region_id = (int)$_POST['region_id'];
    $notes = sanitize($_POST['notes']);
    $added_by = $_SESSION['user_id'];

    // التحقق من صحة البيانات
    if (empty($full_name) || empty($phone) || empty($region_id) || empty($address) || empty($birth_date)) {
        showMessage('يرجى ملء جميع الحقول المطلوبة (الاسم، الهاتف، المنطقة، العنوان، تاريخ الميلاد)', 'error');
    } else {
        // التحقق من عدم تكرار رقم الناخب
        if (!empty($voter_number)) {
            $existing = fetchOne("SELECT id FROM supporters WHERE voter_number = ?", [$voter_number]);
            if ($existing) {
                showMessage('رقم الناخب موجود مسبقاً', 'error');
            } else {
                $insert_success = true;
            }
        } else {
            $insert_success = true;
        }

        if (isset($insert_success)) {
            $sql = "INSERT INTO supporters (full_name, gender, marital_status, birth_date, education, profession, address, phone, voter_number, voting_center, region_id, notes, added_by, created_at)
                    VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, NOW())";

            $result = executeQuery($sql, [
                $full_name, $gender, $marital_status, $birth_date, $education,
                $profession, $address, $phone, $voter_number, $voting_center,
                $region_id, $notes, $added_by
            ]);

            if ($result) {
                showMessage('تم إضافة المؤيد بنجاح', 'success');
                redirect('supporters_simple.php');
            } else {
                showMessage('حدث خطأ أثناء إضافة المؤيد', 'error');
            }
        }
    }
}

// معالجة حذف مؤيد
if ($_SERVER['REQUEST_METHOD'] == 'POST' && isset($_POST['action']) && $_POST['action'] == 'delete') {
    $id = (int)$_POST['id'];
    $result = executeQuery("DELETE FROM supporters WHERE id = ?", [$id]);

    if ($result) {
        showMessage('تم حذف المؤيد بنجاح', 'success');
    } else {
        showMessage('حدث خطأ أثناء حذف المؤيد', 'error');
    }
    redirect('supporters_simple.php');
}

// جلب المؤيدين
$search = isset($_GET['search']) ? sanitize($_GET['search']) : '';
$region_filter = isset($_GET['region']) ? (int)$_GET['region'] : 0;

$sql = "SELECT s.*, r.name as region_name, u.full_name as added_by_name,
               YEAR(CURDATE()) - YEAR(s.birth_date) as age
        FROM supporters s
        LEFT JOIN regions r ON s.region_id = r.id
        LEFT JOIN users u ON s.added_by = u.id
        WHERE 1=1";

$params = [];

if (!empty($search)) {
    $sql .= " AND (s.full_name LIKE ? OR s.phone LIKE ? OR s.voter_number LIKE ?)";
    $search_term = '%' . $search . '%';
    $params = array_merge($params, [$search_term, $search_term, $search_term]);
}

if ($region_filter > 0) {
    $sql .= " AND s.region_id = ?";
    $params[] = $region_filter;
}

$sql .= " ORDER BY s.created_at DESC";

$supporters = fetchAll($sql, $params);

// جلب المناطق
$regions = fetchAll("SELECT * FROM regions ORDER BY name");

// إحصائيات
$total_supporters = count($supporters);
$male_supporters = count(array_filter($supporters, function($s) { return $s['gender'] == 'male'; }));
$female_supporters = count(array_filter($supporters, function($s) { return $s['gender'] == 'female'; }));
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إدارة المؤيدين - <?php echo SITE_NAME; ?></title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        body { font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; }
        .main-content { margin-top: 80px; }
        .sidebar { background: #f8f9fa; min-height: calc(100vh - 80px); padding-top: 20px; }
        .supporter-photo { width: 50px; height: 50px; object-fit: cover; border-radius: 50%; }
    </style>
</head>
<body>
    <!-- شريط التنقل العلوي -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary fixed-top">
        <div class="container-fluid">
            <a class="navbar-brand" href="../../dashboard.php">
                <i class="fas fa-vote-yea me-2"></i>
                نظام إدارة الحملة الانتخابية
            </a>

            <div class="navbar-nav ms-auto">
                <div class="nav-item dropdown">
                    <a class="nav-link dropdown-toggle" href="#" id="userDropdown" role="button" data-bs-toggle="dropdown">
                        <i class="fas fa-user-circle me-1"></i>
                        <?php echo htmlspecialchars($_SESSION['full_name']); ?>
                    </a>
                    <ul class="dropdown-menu dropdown-menu-end">
                        <li><a class="dropdown-item" href="../../logout.php"><i class="fas fa-sign-out-alt me-2"></i>تسجيل الخروج</a></li>
                    </ul>
                </div>
            </div>
        </div>
    </nav>

    <!-- المحتوى الرئيسي -->
    <div class="container-fluid main-content">
        <div class="row">
            <!-- الشريط الجانبي -->
            <nav class="col-md-3 col-lg-2 d-md-block sidebar">
                <div class="position-sticky pt-3">
                    <ul class="nav flex-column">
                        <li class="nav-item">
                            <a class="nav-link" href="../../dashboard.php">
                                <i class="fas fa-tachometer-alt me-2"></i>
                                لوحة التحكم
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link active" href="supporters_simple.php">
                                <i class="fas fa-users me-2"></i>
                                المؤيدين
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="../regions/regions.php">
                                <i class="fas fa-map-marked-alt me-2"></i>
                                المناطق
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="../admins/admins_new.php">
                                <i class="fas fa-user-tie me-2"></i>
                                الإداريين
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="../expenses/expenses.php">
                                <i class="fas fa-money-bill-wave me-2"></i>
                                المصروفات
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="../events/events.php">
                                <i class="fas fa-calendar-alt me-2"></i>
                                الفعاليات
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="../competitors/competitors.php">
                                <i class="fas fa-chess me-2"></i>
                                المنافسين
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="../reports/reports.php">
                                <i class="fas fa-chart-bar me-2"></i>
                                التقارير
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="../settings/settings.php">
                                <i class="fas fa-cog me-2"></i>
                                الإعدادات
                            </a>
                        </li>
                    </ul>
                </div>
            </nav>

            <!-- المحتوى الرئيسي -->
            <main class="col-md-9 ms-sm-auto col-lg-10 px-md-4">
                <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                    <h1 class="h2">
                        <i class="fas fa-users me-2"></i>
                        إدارة المؤيدين
                    </h1>
                    <div class="btn-toolbar mb-2 mb-md-0">
                        <div class="btn-group me-2">
                            <button type="button" class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#addSupporterModal">
                                <i class="fas fa-plus"></i> إضافة مؤيد
                            </button>
                            <button type="button" class="btn btn-success" onclick="exportToExcel()">
                                <i class="fas fa-file-excel"></i> تصدير Excel
                            </button>
                        </div>
                    </div>
                </div>

                <?php displayMessage(); ?>

                <!-- الإحصائيات -->
                <div class="row mb-4">
                    <div class="col-md-3">
                        <div class="card bg-primary text-white">
                            <div class="card-body">
                                <div class="d-flex justify-content-between">
                                    <div>
                                        <h4><?php echo number_format($total_supporters); ?></h4>
                                        <p class="mb-0">إجمالي المؤيدين</p>
                                    </div>
                                    <div class="align-self-center">
                                        <i class="fas fa-users fa-2x"></i>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="card bg-info text-white">
                            <div class="card-body">
                                <div class="d-flex justify-content-between">
                                    <div>
                                        <h4><?php echo number_format($male_supporters); ?></h4>
                                        <p class="mb-0">الذكور</p>
                                    </div>
                                    <div class="align-self-center">
                                        <i class="fas fa-male fa-2x"></i>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="card bg-warning text-white">
                            <div class="card-body">
                                <div class="d-flex justify-content-between">
                                    <div>
                                        <h4><?php echo number_format($female_supporters); ?></h4>
                                        <p class="mb-0">الإناث</p>
                                    </div>
                                    <div class="align-self-center">
                                        <i class="fas fa-female fa-2x"></i>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="card bg-success text-white">
                            <div class="card-body">
                                <div class="d-flex justify-content-between">
                                    <div>
                                        <h4><?php echo $total_supporters > 0 ? round(($male_supporters / $total_supporters) * 100, 1) : 0; ?>%</h4>
                                        <p class="mb-0">نسبة الذكور</p>
                                    </div>
                                    <div class="align-self-center">
                                        <i class="fas fa-chart-pie fa-2x"></i>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- فلاتر البحث -->
                <div class="card mb-4">
                    <div class="card-body">
                        <form method="GET" action="" class="row g-3">
                            <div class="col-md-4">
                                <label for="search" class="form-label">البحث</label>
                                <input type="text" class="form-control" id="search" name="search"
                                       value="<?php echo htmlspecialchars($search); ?>"
                                       placeholder="ابحث بالاسم أو الهاتف أو رقم الناخب">
                            </div>
                            <div class="col-md-3">
                                <label for="region" class="form-label">المنطقة</label>
                                <select class="form-select" id="region" name="region">
                                    <option value="0">جميع المناطق</option>
                                    <?php foreach ($regions as $region): ?>
                                    <option value="<?php echo $region['id']; ?>"
                                            <?php echo $region_filter == $region['id'] ? 'selected' : ''; ?>>
                                        <?php echo htmlspecialchars($region['name']); ?>
                                    </option>
                                    <?php endforeach; ?>
                                </select>
                            </div>
                            <div class="col-md-2">
                                <label class="form-label">&nbsp;</label>
                                <div class="d-grid">
                                    <button type="submit" class="btn btn-primary">
                                        <i class="fas fa-search"></i> بحث
                                    </button>
                                </div>
                            </div>
                            <div class="col-md-2">
                                <label class="form-label">&nbsp;</label>
                                <div class="d-grid">
                                    <a href="supporters_simple.php" class="btn btn-secondary">
                                        <i class="fas fa-times"></i> مسح
                                    </a>
                                </div>
                            </div>
                        </form>
                    </div>
                </div>

                <!-- جدول المؤيدين -->
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="fas fa-list me-2"></i>
                            قائمة المؤيدين (<?php echo number_format($total_supporters); ?>)
                        </h5>
                    </div>
                    <div class="card-body">
                        <?php if (empty($supporters)): ?>
                            <div class="alert alert-info text-center">
                                <i class="fas fa-info-circle fa-2x mb-3"></i>
                                <h5>لا يوجد مؤيدين</h5>
                                <p>لم يتم العثور على أي مؤيدين. يمكنك إضافة مؤيد جديد من الزر أعلاه.</p>
                            </div>
                        <?php else: ?>
                            <div class="table-responsive">
                                <table class="table table-striped table-hover">
                                    <thead class="table-dark">
                                        <tr>
                                            <th>الاسم الكامل</th>
                                            <th>الجنس</th>
                                            <th>العمر</th>
                                            <th>رقم الهاتف</th>
                                            <th>رقم الناخب</th>
                                            <th>المنطقة</th>
                                            <th>تاريخ الإضافة</th>
                                            <th>الإجراءات</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <?php foreach ($supporters as $supporter): ?>
                                        <tr>
                                            <td>
                                                <strong><?php echo htmlspecialchars($supporter['full_name']); ?></strong>
                                                <br>
                                                <small class="text-muted"><?php echo htmlspecialchars($supporter['profession'] ?: 'غير محدد'); ?></small>
                                            </td>
                                            <td>
                                                <span class="badge <?php echo $supporter['gender'] == 'male' ? 'bg-primary' : 'bg-pink'; ?>">
                                                    <?php echo $supporter['gender'] == 'male' ? 'ذكر' : 'أنثى'; ?>
                                                </span>
                                            </td>
                                            <td><?php echo $supporter['age']; ?> سنة</td>
                                            <td>
                                                <i class="fas fa-phone text-success me-1"></i>
                                                <?php echo htmlspecialchars($supporter['phone']); ?>
                                            </td>
                                            <td><?php echo htmlspecialchars($supporter['voter_number'] ?: 'غير محدد'); ?></td>
                                            <td>
                                                <i class="fas fa-map-marker-alt text-danger me-1"></i>
                                                <?php echo htmlspecialchars($supporter['region_name']); ?>
                                            </td>
                                            <td><?php echo date('Y-m-d', strtotime($supporter['created_at'])); ?></td>
                                            <td>
                                                <div class="btn-group btn-group-sm">
                                                    <button type="button" class="btn btn-outline-info" onclick="viewSupporter(<?php echo $supporter['id']; ?>)">
                                                        <i class="fas fa-eye"></i>
                                                    </button>
                                                    <button type="button" class="btn btn-outline-warning" onclick="editSupporter(<?php echo $supporter['id']; ?>)">
                                                        <i class="fas fa-edit"></i>
                                                    </button>
                                                    <button type="button" class="btn btn-outline-danger" onclick="deleteSupporter(<?php echo $supporter['id']; ?>, '<?php echo htmlspecialchars($supporter['full_name']); ?>')">
                                                        <i class="fas fa-trash"></i>
                                                    </button>
                                                </div>
                                            </td>
                                        </tr>
                                        <?php endforeach; ?>
                                    </tbody>
                                </table>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>
            </main>
        </div>
    </div>

    <!-- نافذة إضافة مؤيد -->
    <div class="modal fade" id="addSupporterModal" tabindex="-1">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">إضافة مؤيد جديد</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <form method="POST" action="">
                    <input type="hidden" name="action" value="add">
                    <div class="modal-body">
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="full_name" class="form-label">الاسم الكامل *</label>
                                    <input type="text" class="form-control" id="full_name" name="full_name" required>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="phone" class="form-label">رقم الهاتف *</label>
                                    <input type="tel" class="form-control" id="phone" name="phone" required>
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-4">
                                <div class="mb-3">
                                    <label for="gender" class="form-label">الجنس *</label>
                                    <select class="form-select" id="gender" name="gender" required>
                                        <option value="">اختر الجنس</option>
                                        <option value="male">ذكر</option>
                                        <option value="female">أنثى</option>
                                    </select>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="mb-3">
                                    <label for="marital_status" class="form-label">الحالة الاجتماعية</label>
                                    <select class="form-select" id="marital_status" name="marital_status">
                                        <option value="single">أعزب</option>
                                        <option value="married">متزوج</option>
                                        <option value="divorced">مطلق</option>
                                        <option value="widowed">أرمل</option>
                                    </select>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="mb-3">
                                    <label for="birth_date" class="form-label">تاريخ الميلاد *</label>
                                    <input type="date" class="form-control" id="birth_date" name="birth_date" required>
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="education" class="form-label">المؤهل العلمي</label>
                                    <input type="text" class="form-control" id="education" name="education">
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="profession" class="form-label">المهنة</label>
                                    <input type="text" class="form-control" id="profession" name="profession">
                                </div>
                            </div>
                        </div>
                        <div class="mb-3">
                            <label for="address" class="form-label">العنوان *</label>
                            <textarea class="form-control" id="address" name="address" rows="2" required></textarea>
                        </div>
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="voter_number" class="form-label">رقم الناخب</label>
                                    <input type="text" class="form-control" id="voter_number" name="voter_number">
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="voting_center" class="form-label">مركز الاقتراع</label>
                                    <input type="text" class="form-control" id="voting_center" name="voting_center">
                                </div>
                            </div>
                        </div>
                        <div class="mb-3">
                            <label for="region_id" class="form-label">المنطقة *</label>
                            <select class="form-select" id="region_id" name="region_id" required>
                                <option value="">اختر المنطقة</option>
                                <?php foreach ($regions as $region): ?>
                                <option value="<?php echo $region['id']; ?>"><?php echo htmlspecialchars($region['name']); ?></option>
                                <?php endforeach; ?>
                            </select>
                        </div>
                        <div class="mb-3">
                            <label for="notes" class="form-label">ملاحظات</label>
                            <textarea class="form-control" id="notes" name="notes" rows="2"></textarea>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                        <button type="submit" class="btn btn-primary">إضافة المؤيد</button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        function viewSupporter(id) {
            alert('عرض تفاصيل المؤيد رقم: ' + id);
        }

        function editSupporter(id) {
            alert('تعديل المؤيد رقم: ' + id);
        }

        function deleteSupporter(id, name) {
            if (confirm('هل أنت متأكد من حذف المؤيد "' + name + '"؟\nهذا الإجراء لا يمكن التراجع عنه.')) {
                const form = document.createElement('form');
                form.method = 'POST';
                form.innerHTML = `
                    <input type="hidden" name="action" value="delete">
                    <input type="hidden" name="id" value="${id}">
                `;
                document.body.appendChild(form);
                form.submit();
            }
        }

        function exportToExcel() {
            window.location.href = 'export_supporters.php';
        }

        // تعيين الحد الأقصى لتاريخ الميلاد (18 سنة)
        document.addEventListener('DOMContentLoaded', function() {
            const birthDateInput = document.getElementById('birth_date');
            if (birthDateInput) {
                const today = new Date();
                const maxDate = new Date(today.getFullYear() - 18, today.getMonth(), today.getDate());
                birthDateInput.max = maxDate.toISOString().split('T')[0];
            }
        });
    </script>
</body>
</html>
