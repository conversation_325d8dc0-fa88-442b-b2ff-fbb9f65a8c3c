# نظام إدارة الحملة الانتخابية

نظام شامل لإدارة الحملات الانتخابية مع دعم كامل للغة العربية وميزات متقدمة لإدارة المؤيدين والمناطق والفعاليات.

## المميزات الرئيسية

### 🗳️ إدارة شاملة للحملة
- إدارة المؤيدين مع بيانات كاملة وصور
- إدارة المناطق الانتخابية
- إدارة الإداريين مع صلاحيات محددة
- تتبع المصروفات والفعاليات
- مراقبة المنافسين

### 📊 تقارير وإحصائيات
- إحصائيات فورية ومفصلة
- تقارير قابلة للتصدير (Excel, PDF)
- رسوم بيانية تفاعلية
- تحليل البيانات الديموغرافية

### 🔐 نظام أمان متقدم
- تسجيل دخول آمن مع التحقق من رقم الهاتف
- صلاحيات مخصصة لكل إداري
- تشفير كلمات المرور
- حماية من محاولات الاختراق

### 📱 تصميم متجاوب
- واجهة متجاوبة مع جميع الأجهزة
- دعم كامل للجوالات والأجهزة اللوحية
- تصميم عصري وسهل الاستخدام

### 🌐 دعم اللغة العربية
- واجهة باللغة العربية بالكامل
- دعم الكتابة من اليمين إلى اليسار
- تصدير البيانات باللغة العربية

## متطلبات النظام

### متطلبات الخادم
- PHP 7.4 أو أحدث
- MySQL 5.7 أو أحدث
- Apache أو Nginx
- مساحة تخزين: 500 ميجابايت على الأقل

### الإضافات المطلوبة
- PDO MySQL
- GD Library
- FileInfo
- cURL
- OpenSSL

## التثبيت

### 1. رفع الملفات
```bash
# رفع جميع ملفات النظام إلى مجلد الموقع
# تأكد من رفع جميع المجلدات والملفات
```

### 2. إعداد قاعدة البيانات
```sql
-- إنشاء قاعدة البيانات
CREATE DATABASE irjnpfzw_mr CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- إنشاء مستخدم قاعدة البيانات
CREATE USER 'irjnpfzw_mr'@'localhost' IDENTIFIED BY 'irjnpfzw_mr';
GRANT ALL PRIVILEGES ON irjnpfzw_mr.* TO 'irjnpfzw_mr'@'localhost';
FLUSH PRIVILEGES;
```

### 3. تشغيل معالج التثبيت
1. افتح المتصفح وانتقل إلى: `http://yoursite.com/install.php`
2. اتبع خطوات التثبيت:
   - التحقق من متطلبات النظام
   - إنشاء قاعدة البيانات
   - إنهاء التثبيت

### 4. تسجيل الدخول
استخدم البيانات التالية لتسجيل الدخول:
- **اسم المستخدم:** abd
- **كلمة المرور:** abdabd
- **رقم الهاتف:** 07719992716

## هيكل النظام

```
├── config/                 # ملفات الإعدادات
│   ├── config.php         # الإعدادات العامة
│   └── database.php       # إعدادات قاعدة البيانات
├── database/              # ملفات قاعدة البيانات
│   └── schema.sql         # هيكل قاعدة البيانات
├── assets/                # الملفات الثابتة
│   ├── css/              # ملفات التصميم
│   ├── js/               # ملفات JavaScript
│   └── images/           # الصور
├── modules/               # وحدات النظام
│   ├── supporters/       # إدارة المؤيدين
│   ├── regions/          # إدارة المناطق
│   ├── admins/           # إدارة الإداريين
│   ├── expenses/         # إدارة المصروفات
│   ├── events/           # إدارة الفعاليات
│   ├── competitors/      # إدارة المنافسين
│   ├── reports/          # التقارير
│   └── settings/         # الإعدادات
├── uploads/               # ملفات المستخدمين
└── exports/               # ملفات التصدير
```

## الوحدات الرئيسية

### 👥 إدارة المؤيدين
- إضافة وتعديل بيانات المؤيدين
- رفع الصور الشخصية
- البحث والتصفية المتقدمة
- استيراد من ملفات Excel
- تصدير البيانات
- طباعة بطاقات المؤيدين

### 🗺️ إدارة المناطق
- إضافة وإدارة المناطق الانتخابية
- عرض إحصائيات كل منطقة
- ربط المؤيدين بالمناطق

### 👨‍💼 إدارة الإداريين
- إضافة إداريين جدد
- تحديد الصلاحيات
- ربط الإداريين بالمناطق
- إنشاء حسابات منفصلة

### 💰 إدارة المصروفات
- تسجيل جميع المصروفات
- تصنيف المصروفات
- رفع المرفقات
- تقارير مالية مفصلة

### 📅 إدارة الفعاليات
- جدولة الفعاليات
- تقويم تفاعلي
- رفع صور الفعاليات
- إشعار الإداريين

### ⚔️ إدارة المنافسين
- قاعدة بيانات المنافسين
- تحليل نقاط القوة والضعف
- تقارير مقارنة

## الأمان

### حماية الملفات
- ملفات `.htaccess` لحماية المجلدات الحساسة
- منع الوصول المباشر لملفات الإعدادات
- تشفير كلمات المرور

### حماية قاعدة البيانات
- استخدام Prepared Statements
- تنظيف البيانات المدخلة
- حماية من SQL Injection

### حماية الجلسات
- انتهاء صلاحية الجلسات
- التحقق من IP Address
- حماية من Session Hijacking

## النسخ الاحتياطي

### نسخ احتياطي تلقائي
```php
// يمكن إعداد نسخ احتياطي تلقائي عبر Cron Job
0 2 * * * php /path/to/backup.php
```

### نسخ احتياطي يدوي
- من لوحة التحكم > الإعدادات > النسخ الاحتياطي
- تصدير قاعدة البيانات
- تصدير الملفات المرفوعة

## الدعم الفني

### المشاكل الشائعة

#### خطأ في الاتصال بقاعدة البيانات
```php
// تحقق من إعدادات قاعدة البيانات في config/database.php
define('DB_HOST', 'localhost');
define('DB_NAME', 'irjnpfzw_mr');
define('DB_USER', 'irjnpfzw_mr');
define('DB_PASS', 'irjnpfzw_mr');
```

#### مشكلة في رفع الملفات
```php
// تحقق من صلاحيات المجلدات
chmod 755 uploads/
chmod 755 exports/
```

#### مشكلة في عرض الخطوط العربية
```html
<!-- تأكد من وجود meta charset في جميع الصفحات -->
<meta charset="UTF-8">
```

## التطوير والتخصيص

### إضافة وحدات جديدة
1. إنشاء مجلد جديد في `modules/`
2. إنشاء ملفات PHP, CSS, JS منفصلة
3. إضافة الجداول المطلوبة في قاعدة البيانات
4. تحديث القائمة الجانبية

### تخصيص التصميم
- تعديل ملفات CSS في `assets/css/`
- استخدام متغيرات CSS للألوان
- تخصيص الشعار والألوان من الإعدادات

## الترخيص

هذا النظام مطور خصيصاً لإدارة الحملات الانتخابية ويمكن استخدامه وتطويره حسب الحاجة.

## معلومات الاتصال

للدعم الفني أو الاستفسارات، يرجى التواصل مع فريق التطوير.

---

**ملاحظة:** تأكد من تحديث كلمات المرور الافتراضية بعد التثبيت لضمان الأمان.
