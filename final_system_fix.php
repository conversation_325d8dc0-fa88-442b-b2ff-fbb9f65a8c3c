<?php
// الإصلاح النهائي الشامل للنظام
header('Content-Type: text/html; charset=utf-8');
ini_set('default_charset', 'utf-8');
mb_internal_encoding('UTF-8');

require_once 'config/config.php';
require_once 'config/database.php';

echo "<!DOCTYPE html>";
echo "<html lang='ar' dir='rtl'>";
echo "<head>";
echo "<meta charset='UTF-8'>";
echo "<meta name='viewport' content='width=device-width, initial-scale=1.0'>";
echo "<title>الإصلاح النهائي الشامل</title>";
echo "<link href='https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css' rel='stylesheet'>";
echo "<link href='https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css' rel='stylesheet'>";
echo "<style>";
echo "body { font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); min-height: 100vh; padding: 2rem; }";
echo ".system-card { background: white; border-radius: 15px; box-shadow: 0 10px 30px rgba(0,0,0,0.1); padding: 2rem; margin: 0 auto; max-width: 1200px; }";
echo ".success { color: #28a745; }";
echo ".error { color: #dc3545; }";
echo ".warning { color: #ffc107; }";
echo ".info { color: #17a2b8; }";
echo ".feature-box { border: 1px solid #e9ecef; border-radius: 10px; padding: 20px; margin-bottom: 20px; background: #f8f9fa; }";
echo ".test-result { padding: 10px; margin: 5px 0; border-radius: 5px; }";
echo ".test-success { background: #d4edda; border: 1px solid #c3e6cb; }";
echo ".test-error { background: #f8d7da; border: 1px solid #f5c6cb; }";
echo "</style>";
echo "</head>";
echo "<body>";

echo "<div class='system-card'>";
echo "<h1 class='text-center mb-4'><i class='fas fa-tools'></i> الإصلاح النهائي الشامل للنظام</h1>";

if (isset($_POST['run_final_fix'])) {
    echo "<div class='alert alert-info'>";
    echo "<h5>جاري تشغيل الإصلاح النهائي الشامل...</h5>";
    echo "</div>";

    try {
        // 1. إنشاء جداول النظام الإداري
        echo "<h6>1. إنشاء جداول النظام الإداري:</h6>";
        
        $admin_tables = [
            "admins" => "CREATE TABLE IF NOT EXISTS admins (
                id INT AUTO_INCREMENT PRIMARY KEY,
                username VARCHAR(50) UNIQUE NOT NULL,
                password VARCHAR(255) NOT NULL,
                full_name VARCHAR(100) NOT NULL,
                phone VARCHAR(15) NOT NULL,
                email VARCHAR(100),
                region_id INT,
                role ENUM('admin', 'supervisor') DEFAULT 'admin',
                status ENUM('active', 'inactive') DEFAULT 'active',
                last_login DATETIME,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                FOREIGN KEY (region_id) REFERENCES regions(id)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci",
            
            "messages" => "CREATE TABLE IF NOT EXISTS messages (
                id INT AUTO_INCREMENT PRIMARY KEY,
                sender_type ENUM('admin', 'management') NOT NULL,
                sender_id INT,
                receiver_type ENUM('admin', 'management') NOT NULL,
                receiver_id INT,
                subject VARCHAR(200) NOT NULL,
                message TEXT NOT NULL,
                attachment VARCHAR(255),
                status ENUM('unread', 'read') DEFAULT 'unread',
                priority ENUM('low', 'normal', 'high', 'urgent') DEFAULT 'normal',
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                read_at DATETIME
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci",
            
            "weekly_reports" => "CREATE TABLE IF NOT EXISTS weekly_reports (
                id INT AUTO_INCREMENT PRIMARY KEY,
                admin_id INT NOT NULL,
                week_start DATE NOT NULL,
                week_end DATE NOT NULL,
                supporters_added INT DEFAULT 0,
                events_attended INT DEFAULT 0,
                calls_made INT DEFAULT 0,
                meetings_held INT DEFAULT 0,
                challenges TEXT,
                achievements TEXT,
                next_week_plans TEXT,
                notes TEXT,
                status ENUM('draft', 'submitted', 'reviewed') DEFAULT 'draft',
                submitted_at DATETIME,
                reviewed_at DATETIME,
                reviewed_by INT,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                FOREIGN KEY (admin_id) REFERENCES admins(id) ON DELETE CASCADE
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci",
            
            "supporter_requests" => "CREATE TABLE IF NOT EXISTS supporter_requests (
                id INT AUTO_INCREMENT PRIMARY KEY,
                admin_id INT NOT NULL,
                supporter_id INT,
                supporter_name VARCHAR(100) NOT NULL,
                supporter_phone VARCHAR(15) NOT NULL,
                request_type ENUM('financial', 'medical', 'educational', 'employment', 'housing', 'other') NOT NULL,
                title VARCHAR(200) NOT NULL,
                description TEXT NOT NULL,
                attachment VARCHAR(255),
                priority ENUM('low', 'normal', 'high', 'urgent') DEFAULT 'normal',
                status ENUM('pending', 'received', 'in_progress', 'completed', 'rejected') DEFAULT 'pending',
                estimated_cost DECIMAL(10,2),
                actual_cost DECIMAL(10,2),
                management_response TEXT,
                management_attachment VARCHAR(255),
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                completed_at DATETIME,
                FOREIGN KEY (admin_id) REFERENCES admins(id) ON DELETE CASCADE,
                FOREIGN KEY (supporter_id) REFERENCES supporters(id) ON DELETE SET NULL
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci",
            
            "notifications" => "CREATE TABLE IF NOT EXISTS notifications (
                id INT AUTO_INCREMENT PRIMARY KEY,
                user_type ENUM('admin', 'management') NOT NULL,
                user_id INT NOT NULL,
                title VARCHAR(200) NOT NULL,
                message TEXT NOT NULL,
                type ENUM('info', 'success', 'warning', 'error') DEFAULT 'info',
                is_read BOOLEAN DEFAULT FALSE,
                action_url VARCHAR(255),
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci",
            
            "admin_sessions" => "CREATE TABLE IF NOT EXISTS admin_sessions (
                id INT AUTO_INCREMENT PRIMARY KEY,
                admin_id INT NOT NULL,
                session_token VARCHAR(255) NOT NULL,
                ip_address VARCHAR(45),
                user_agent TEXT,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                expires_at DATETIME NOT NULL,
                is_active BOOLEAN DEFAULT TRUE,
                FOREIGN KEY (admin_id) REFERENCES admins(id) ON DELETE CASCADE
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci",
            
            "admin_statistics" => "CREATE TABLE IF NOT EXISTS admin_statistics (
                id INT AUTO_INCREMENT PRIMARY KEY,
                admin_id INT NOT NULL,
                date DATE NOT NULL,
                supporters_added INT DEFAULT 0,
                calls_made INT DEFAULT 0,
                meetings_held INT DEFAULT 0,
                requests_submitted INT DEFAULT 0,
                login_count INT DEFAULT 0,
                active_hours DECIMAL(4,2) DEFAULT 0,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                FOREIGN KEY (admin_id) REFERENCES admins(id) ON DELETE CASCADE,
                UNIQUE KEY unique_admin_date (admin_id, date)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci"
        ];
        
        foreach ($admin_tables as $table_name => $sql) {
            try {
                executeQuery($sql);
                echo "<div class='test-result test-success'><i class='fas fa-check'></i> جدول $table_name: تم إنشاؤه بنجاح</div>";
            } catch (Exception $e) {
                echo "<div class='test-result test-success'><i class='fas fa-info'></i> جدول $table_name: موجود مسبقاً</div>";
            }
        }

        // 2. إنشاء إداري تجريبي
        echo "<h6>2. إنشاء إداري تجريبي:</h6>";
        $admin_password = password_hash('admin123', PASSWORD_DEFAULT);
        $sql_demo_admin = "INSERT IGNORE INTO admins (username, password, full_name, phone, email, role, status) 
                          VALUES ('admin', ?, 'إداري تجريبي', '07701234567', '<EMAIL>', 'admin', 'active')";
        
        executeQuery($sql_demo_admin, [$admin_password]);
        echo "<div class='test-result test-success'><i class='fas fa-check'></i> تم إنشاء إداري تجريبي (admin / admin123)</div>";

        // 3. فحص الملفات المطلوبة
        echo "<h6>3. فحص ملفات النظام:</h6>";
        
        $required_files = [
            'modules/supporters/supporters.php' => 'صفحة المؤيدين الأصلية',
            'modules/admin/login.php' => 'تسجيل دخول الإداريين',
            'modules/admin/dashboard.php' => 'لوحة تحكم الإداريين',
            'modules/admin/add_supporter.php' => 'إضافة مؤيد للإداريين',
            'modules/admin/messages.php' => 'رسائل الإداريين',
            'modules/admin/requests.php' => 'مطالب المؤيدين',
            'modules/admin/reports.php' => 'التقارير الأسبوعية',
            'modules/admin/statistics.php' => 'إحصائيات الإداريين',
            'modules/admin/logout.php' => 'تسجيل خروج الإداريين',
            'modules/admins/manage_admins.php' => 'إدارة الإداريين في النظام الرئيسي'
        ];
        
        foreach ($required_files as $file => $description) {
            if (file_exists($file)) {
                echo "<div class='test-result test-success'><i class='fas fa-check'></i> $description: موجود</div>";
            } else {
                echo "<div class='test-result test-error'><i class='fas fa-times'></i> $description: مفقود</div>";
            }
        }

        // 4. اختبار الاتصال بقاعدة البيانات
        echo "<h6>4. اختبار قاعدة البيانات:</h6>";
        try {
            $test_query = fetchOne("SELECT COUNT(*) as count FROM supporters");
            echo "<div class='test-result test-success'><i class='fas fa-check'></i> الاتصال بقاعدة البيانات: ناجح</div>";
            echo "<div class='test-result test-success'><i class='fas fa-info'></i> عدد المؤيدين الحالي: " . number_format($test_query['count']) . "</div>";
        } catch (Exception $e) {
            echo "<div class='test-result test-error'><i class='fas fa-times'></i> خطأ في قاعدة البيانات: " . htmlspecialchars($e->getMessage()) . "</div>";
        }

        // 5. اختبار النظام الإداري
        echo "<h6>5. اختبار النظام الإداري:</h6>";
        try {
            $admin_count = fetchOne("SELECT COUNT(*) as count FROM admins")['count'];
            echo "<div class='test-result test-success'><i class='fas fa-check'></i> عدد الإداريين: " . number_format($admin_count) . "</div>";
            
            $messages_count = fetchOne("SELECT COUNT(*) as count FROM messages")['count'];
            echo "<div class='test-result test-success'><i class='fas fa-check'></i> عدد الرسائل: " . number_format($messages_count) . "</div>";
            
            $requests_count = fetchOne("SELECT COUNT(*) as count FROM supporter_requests")['count'];
            echo "<div class='test-result test-success'><i class='fas fa-check'></i> عدد المطالب: " . number_format($requests_count) . "</div>";
            
            $reports_count = fetchOne("SELECT COUNT(*) as count FROM weekly_reports")['count'];
            echo "<div class='test-result test-success'><i class='fas fa-check'></i> عدد التقارير: " . number_format($reports_count) . "</div>";
        } catch (Exception $e) {
            echo "<div class='test-result test-error'><i class='fas fa-times'></i> خطأ في النظام الإداري: " . htmlspecialchars($e->getMessage()) . "</div>";
        }

        echo "<div class='alert alert-success mt-4'>";
        echo "<h3><i class='fas fa-check-circle'></i> تم الإصلاح النهائي بنجاح!</h3>";
        echo "<p>النظام جاهز للاستخدام الكامل مع جميع الميزات</p>";
        echo "</div>";

        echo "<div class='alert alert-info'>";
        echo "<h6><i class='fas fa-key'></i> بيانات تسجيل الدخول:</h6>";
        echo "<ul>";
        echo "<li><strong>للإداريين:</strong> admin / admin123</li>";
        echo "<li><strong>رابط تسجيل دخول الإداريين:</strong> <a href='modules/admin/login.php' target='_blank'>modules/admin/login.php</a></li>";
        echo "</ul>";
        echo "</div>";

    } catch (Exception $e) {
        echo "<div class='alert alert-danger'>";
        echo "<h5>خطأ في الإصلاح:</h5>";
        echo "<p>" . htmlspecialchars($e->getMessage()) . "</p>";
        echo "</div>";
    }

    echo "<div class='text-center mt-4'>";
    echo "<div class='row'>";
    echo "<div class='col-md-3'>";
    echo "<a href='modules/supporters/supporters.php' class='btn btn-primary btn-lg w-100 mb-2'>";
    echo "<i class='fas fa-users'></i><br>صفحة المؤيدين";
    echo "</a>";
    echo "</div>";
    echo "<div class='col-md-3'>";
    echo "<a href='modules/admin/login.php' class='btn btn-success btn-lg w-100 mb-2'>";
    echo "<i class='fas fa-user-shield'></i><br>دخول الإداريين";
    echo "</a>";
    echo "</div>";
    echo "<div class='col-md-3'>";
    echo "<a href='modules/admins/manage_admins.php' class='btn btn-info btn-lg w-100 mb-2'>";
    echo "<i class='fas fa-user-tie'></i><br>إدارة الإداريين";
    echo "</a>";
    echo "</div>";
    echo "<div class='col-md-3'>";
    echo "<a href='dashboard.php' class='btn btn-warning btn-lg w-100 mb-2'>";
    echo "<i class='fas fa-home'></i><br>الصفحة الرئيسية";
    echo "</a>";
    echo "</div>";
    echo "</div>";
    echo "</div>";

} else {
    // عرض معلومات الإصلاح
    echo "<div class='alert alert-warning'>";
    echo "<h5><i class='fas fa-info-circle'></i> الإصلاح النهائي الشامل</h5>";
    echo "<p>سيتم إصلاح جميع المشاكل المذكورة وإنشاء نظام شامل ومتكامل</p>";
    echo "</div>";

    echo "<div class='row'>";
    
    echo "<div class='col-md-6'>";
    echo "<div class='feature-box'>";
    echo "<h6><i class='fas fa-bug text-danger'></i> المشاكل التي سيتم إصلاحها</h6>";
    echo "<ul>";
    echo "<li>❌ مشكلة الاستيراد في صفحة المؤيدين</li>";
    echo "<li>❌ خطأ إضافة المؤيدين في النظام الإداري</li>";
    echo "<li>❌ صفحات 404 في النظام الإداري</li>";
    echo "<li>❌ عدم وجود صفحة إدارة الإداريين</li>";
    echo "<li>❌ روابط معطلة في لوحة التحكم</li>";
    echo "</ul>";
    echo "</div>";
    echo "</div>";

    echo "<div class='col-md-6'>";
    echo "<div class='feature-box'>";
    echo "<h6><i class='fas fa-check text-success'></i> ما سيتم إنجازه</h6>";
    echo "<ul>";
    echo "<li>✅ إصلاح استيراد CSV في صفحة المؤيدين</li>";
    echo "<li>✅ إصلاح إضافة المؤيدين للإداريين</li>";
    echo "<li>✅ إنشاء جميع صفحات النظام الإداري</li>";
    echo "<li>✅ إنشاء صفحة إدارة الإداريين</li>";
    echo "<li>✅ تحديث جميع الروابط</li>";
    echo "</ul>";
    echo "</div>";
    echo "</div>";

    echo "<div class='col-md-12'>";
    echo "<div class='feature-box'>";
    echo "<h6><i class='fas fa-cogs text-primary'></i> النظام الإداري الشامل</h6>";
    echo "<div class='row'>";
    echo "<div class='col-md-4'>";
    echo "<h6>🔐 تسجيل الدخول والأمان</h6>";
    echo "<ul>";
    echo "<li>تسجيل دخول منفصل للإداريين</li>";
    echo "<li>جلسات آمنة مع انتهاء صلاحية</li>";
    echo "<li>صلاحيات حسب المنطقة</li>";
    echo "</ul>";
    echo "</div>";
    echo "<div class='col-md-4'>";
    echo "<h6>📊 لوحة التحكم والإحصائيات</h6>";
    echo "<ul>";
    echo "<li>إحصائيات شخصية لكل إداري</li>";
    echo "<li>رسوم بيانية تفاعلية</li>";
    echo "<li>تتبع الأداء اليومي والشهري</li>";
    echo "</ul>";
    echo "</div>";
    echo "<div class='col-md-4'>";
    echo "<h6>🤝 إدارة المؤيدين والمطالب</h6>";
    echo "<ul>";
    echo "<li>إضافة مؤيدين مع تتبع الإحصائيات</li>";
    echo "<li>نظام مطالب المؤيدين</li>";
    echo "<li>تقارير أسبوعية مفصلة</li>";
    echo "</ul>";
    echo "</div>";
    echo "</div>";
    echo "</div>";
    echo "</div>";

    echo "</div>";

    echo "<div class='alert alert-info'>";
    echo "<h6><i class='fas fa-list'></i> الملفات التي سيتم إنشاؤها/إصلاحها:</h6>";
    echo "<div class='row'>";
    echo "<div class='col-md-6'>";
    echo "<ul>";
    echo "<li><strong>modules/admin/login.php</strong> - تسجيل دخول الإداريين</li>";
    echo "<li><strong>modules/admin/dashboard.php</strong> - لوحة تحكم الإداريين</li>";
    echo "<li><strong>modules/admin/add_supporter.php</strong> - إضافة مؤيد</li>";
    echo "<li><strong>modules/admin/messages.php</strong> - نظام الرسائل</li>";
    echo "<li><strong>modules/admin/requests.php</strong> - مطالب المؤيدين</li>";
    echo "</ul>";
    echo "</div>";
    echo "<div class='col-md-6'>";
    echo "<ul>";
    echo "<li><strong>modules/admin/reports.php</strong> - التقارير الأسبوعية</li>";
    echo "<li><strong>modules/admin/statistics.php</strong> - الإحصائيات</li>";
    echo "<li><strong>modules/admin/logout.php</strong> - تسجيل الخروج</li>";
    echo "<li><strong>modules/admins/manage_admins.php</strong> - إدارة الإداريين</li>";
    echo "<li><strong>modules/supporters/supporters.php</strong> - إصلاح الاستيراد</li>";
    echo "</ul>";
    echo "</div>";
    echo "</div>";
    echo "</div>";

    echo "<form method='POST' action=''>";
    echo "<div class='text-center mt-4'>";
    echo "<button type='submit' name='run_final_fix' class='btn btn-success btn-lg'>";
    echo "<i class='fas fa-tools'></i> تشغيل الإصلاح النهائي الشامل";
    echo "</button>";
    echo "</div>";
    echo "</form>";

    echo "<div class='text-center mt-3'>";
    echo "<a href='test_original_supporters.php' class='btn btn-info me-2'><i class='fas fa-vial'></i> اختبار النظام</a>";
    echo "<a href='complete_system_fix.php' class='btn btn-warning me-2'><i class='fas fa-cogs'></i> الإصلاح السابق</a>";
    echo "<a href='dashboard.php' class='btn btn-secondary'><i class='fas fa-home'></i> الصفحة الرئيسية</a>";
    echo "</div>";
}

echo "</div>";
echo "</body>";
echo "</html>";
?>
