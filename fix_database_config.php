<?php
// إصلاح إعدادات قاعدة البيانات
error_reporting(E_ALL);
ini_set('display_errors', 1);
header('Content-Type: text/html; charset=utf-8');

echo "<!DOCTYPE html>";
echo "<html lang='ar' dir='rtl'>";
echo "<head>";
echo "<meta charset='UTF-8'>";
echo "<meta name='viewport' content='width=device-width, initial-scale=1.0'>";
echo "<title>إصلاح إعدادات قاعدة البيانات</title>";
echo "<link href='https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css' rel='stylesheet'>";
echo "<style>body { font-family: Arial, sans-serif; padding: 20px; background: #f8f9fa; }</style>";
echo "</head>";
echo "<body>";

echo "<div class='container'>";
echo "<div class='row justify-content-center'>";
echo "<div class='col-md-8'>";

echo "<div class='card'>";
echo "<div class='card-header bg-warning text-dark'>";
echo "<h3><i class='fas fa-database'></i> إصلاح إعدادات قاعدة البيانات</h3>";
echo "</div>";
echo "<div class='card-body'>";

// معالجة النموذج
if ($_POST) {
    $host = $_POST['host'] ?? 'localhost';
    $dbname = $_POST['dbname'] ?? '';
    $username = $_POST['username'] ?? '';
    $password = $_POST['password'] ?? '';
    
    echo "<h5>اختبار الإعدادات الجديدة:</h5>";
    
    try {
        $pdo = new PDO("mysql:host=$host;dbname=$dbname;charset=utf8mb4", $username, $password);
        $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
        
        echo "<div class='alert alert-success'>";
        echo "<h6>✅ نجح الاتصال بقاعدة البيانات!</h6>";
        echo "<p>المعلومات صحيحة. سيتم الآن تحديث ملف الإعدادات.</p>";
        echo "</div>";
        
        // إنشاء محتوى ملف database.php الجديد
        $database_content = "<?php
// إعدادات قاعدة البيانات - محدثة
define('DB_HOST', '$host');
define('DB_NAME', '$dbname');
define('DB_USER', '$username');
define('DB_PASS', '$password');
define('DB_CHARSET', 'utf8mb4');

class Database {
    private \$host = DB_HOST;
    private \$db_name = DB_NAME;
    private \$username = DB_USER;
    private \$password = DB_PASS;
    private \$charset = DB_CHARSET;
    public \$conn;

    public function getConnection() {
        \$this->conn = null;
        try {
            \$dsn = \"mysql:host=\" . \$this->host . \";dbname=\" . \$this->db_name . \";charset=utf8mb4\";
            \$this->conn = new PDO(\$dsn, \$this->username, \$this->password, array(
                PDO::MYSQL_ATTR_INIT_COMMAND => \"SET NAMES utf8mb4 COLLATE utf8mb4_unicode_ci\",
                PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
                PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
                PDO::ATTR_EMULATE_PREPARES => false
            ));
        } catch(PDOException \$exception) {
            echo \"خطأ في الاتصال: \" . \$exception->getMessage();
        }
        return \$this->conn;
    }
}

// دالة للحصول على اتصال قاعدة البيانات
function getDBConnection() {
    \$database = new Database();
    return \$database->getConnection();
}

// دالة لتنفيذ استعلام آمن
function executeQuery(\$sql, \$params = []) {
    try {
        \$conn = getDBConnection();
        \$stmt = \$conn->prepare(\$sql);
        \$result = \$stmt->execute(\$params);
        return \$result;
    } catch(PDOException \$e) {
        error_log(\"Database Error: \" . \$e->getMessage());
        return false;
    }
}

// دالة للحصول على صف واحد
function fetchOne(\$sql, \$params = []) {
    try {
        \$conn = getDBConnection();
        \$stmt = \$conn->prepare(\$sql);
        \$stmt->execute(\$params);
        return \$stmt->fetch(PDO::FETCH_ASSOC);
    } catch(PDOException \$e) {
        error_log(\"Database Error: \" . \$e->getMessage());
        return false;
    }
}

// دالة للحصول على عدة صفوف
function fetchAll(\$sql, \$params = []) {
    try {
        \$conn = getDBConnection();
        \$stmt = \$conn->prepare(\$sql);
        \$stmt->execute(\$params);
        return \$stmt->fetchAll(PDO::FETCH_ASSOC);
    } catch(PDOException \$e) {
        error_log(\"Database Error: \" . \$e->getMessage());
        return [];
    }
}

// دالة للحصول على آخر ID مدرج
function getLastInsertId() {
    \$conn = getDBConnection();
    return \$conn->lastInsertId();
}

// دالة لعرض الرسائل
function showMessage(\$message, \$type = 'info') {
    // يمكن تطوير هذه الدالة لاحقاً لعرض الرسائل بشكل أفضل
    if (\$type === 'error') {
        error_log(\$message);
    }
}

// متغير عام لاتصال قاعدة البيانات
\$GLOBALS['pdo'] = getDBConnection();
?>";
        
        // حفظ الملف
        if (file_put_contents('config/database.php', $database_content)) {
            echo "<div class='alert alert-success'>";
            echo "<h6>✅ تم تحديث ملف database.php بنجاح!</h6>";
            echo "<p>يمكنك الآن تجربة النظام.</p>";
            echo "</div>";
            
            echo "<div class='text-center'>";
            echo "<a href='dashboard.php' class='btn btn-primary me-2'>جرب لوحة تحكم المرشح</a>";
            echo "<a href='modules/admin/dashboard.php' class='btn btn-success me-2'>جرب لوحة تحكم الإداريين</a>";
            echo "<a href='test_system.php' class='btn btn-info'>اختبار النظام</a>";
            echo "</div>";
            
        } else {
            echo "<div class='alert alert-danger'>";
            echo "<h6>❌ فشل في حفظ الملف</h6>";
            echo "<p>تأكد من صلاحيات الكتابة في مجلد config</p>";
            echo "</div>";
        }
        
    } catch (Exception $e) {
        echo "<div class='alert alert-danger'>";
        echo "<h6>❌ فشل الاتصال بقاعدة البيانات</h6>";
        echo "<p>الخطأ: " . $e->getMessage() . "</p>";
        echo "<p>تأكد من صحة المعلومات وحاول مرة أخرى.</p>";
        echo "</div>";
    }
}

// عرض النموذج
echo "<h5>أدخل معلومات قاعدة البيانات الصحيحة:</h5>";

echo "<form method='POST'>";
echo "<div class='mb-3'>";
echo "<label for='host' class='form-label'>عنوان الخادم (Host):</label>";
echo "<input type='text' class='form-control' id='host' name='host' value='localhost' required>";
echo "<small class='form-text text-muted'>عادة ما يكون localhost</small>";
echo "</div>";

echo "<div class='mb-3'>";
echo "<label for='dbname' class='form-label'>اسم قاعدة البيانات:</label>";
echo "<input type='text' class='form-control' id='dbname' name='dbname' placeholder='irjnpfzw_mr' required>";
echo "<small class='form-text text-muted'>اسم قاعدة البيانات من cPanel</small>";
echo "</div>";

echo "<div class='mb-3'>";
echo "<label for='username' class='form-label'>اسم المستخدم:</label>";
echo "<input type='text' class='form-control' id='username' name='username' placeholder='irjnpfzw_mr' required>";
echo "<small class='form-text text-muted'>اسم مستخدم قاعدة البيانات</small>";
echo "</div>";

echo "<div class='mb-3'>";
echo "<label for='password' class='form-label'>كلمة المرور:</label>";
echo "<input type='password' class='form-control' id='password' name='password' placeholder='كلمة مرور قاعدة البيانات' required>";
echo "<small class='form-text text-muted'>كلمة مرور قاعدة البيانات</small>";
echo "</div>";

echo "<button type='submit' class='btn btn-primary'>اختبار وحفظ الإعدادات</button>";
echo "</form>";

echo "<hr>";

echo "<div class='alert alert-info'>";
echo "<h6><i class='fas fa-info-circle'></i> كيفية الحصول على معلومات قاعدة البيانات:</h6>";
echo "<ol>";
echo "<li>ادخل إلى <strong>cPanel</strong> الخاص بك</li>";
echo "<li>ابحث عن <strong>MySQL Databases</strong> أو <strong>قواعد البيانات</strong></li>";
echo "<li>ستجد قائمة بقواعد البيانات الموجودة</li>";
echo "<li>انسخ اسم قاعدة البيانات واسم المستخدم</li>";
echo "<li>إذا نسيت كلمة المرور، يمكنك إعادة تعيينها من cPanel</li>";
echo "</ol>";
echo "</div>";

echo "<div class='alert alert-warning'>";
echo "<h6><i class='fas fa-exclamation-triangle'></i> أسماء قواعد البيانات الشائعة:</h6>";
echo "<ul>";
echo "<li><code>irjnpfzw_mr</code> (الاسم الحالي المحفوظ)</li>";
echo "<li><code>cpses_irs7jkoxpg</code> (الاسم المستخدم في الاختبار)</li>";
echo "<li>أو أي اسم آخر تم إنشاؤه في cPanel</li>";
echo "</ul>";
echo "</div>";

echo "</div>";
echo "</div>";

echo "</div>";
echo "</div>";
echo "</div>";

echo "</body>";
echo "</html>";
?>
