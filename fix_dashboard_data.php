<?php
// ملف إصلاح بيانات لوحة التحكم
header('Content-Type: text/html; charset=utf-8');
ini_set('default_charset', 'utf-8');
mb_internal_encoding('UTF-8');

require_once 'config/database.php';

echo "<!DOCTYPE html>";
echo "<html lang='ar' dir='rtl'>";
echo "<head>";
echo "<meta charset='UTF-8'>";
echo "<meta name='viewport' content='width=device-width, initial-scale=1.0'>";
echo "<title>إصلاح بيانات لوحة التحكم</title>";
echo "<link href='https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css' rel='stylesheet'>";
echo "<link href='https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css' rel='stylesheet'>";
echo "<style>";
echo "body { font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); min-height: 100vh; padding: 2rem; }";
echo ".fix-card { background: white; border-radius: 15px; box-shadow: 0 10px 30px rgba(0,0,0,0.1); padding: 2rem; margin: 0 auto; max-width: 1200px; }";
echo ".status-success { color: #28a745; }";
echo ".status-error { color: #dc3545; }";
echo ".status-warning { color: #ffc107; }";
echo "</style>";
echo "</head>";
echo "<body>";

echo "<div class='fix-card'>";
echo "<h1 class='text-center mb-4'><i class='fas fa-tools'></i> إصلاح بيانات لوحة التحكم</h1>";

// فحص الجداول المطلوبة
$required_tables = [
    'supporters' => 'جدول المؤيدين',
    'supporter_requests' => 'جدول مطالب المؤيدين', 
    'messages' => 'جدول الرسائل',
    'weekly_reports' => 'جدول التقارير الأسبوعية',
    'notifications' => 'جدول الإشعارات',
    'admins' => 'جدول الإداريين',
    'regions' => 'جدول المناطق',
    'supporter_attachments' => 'جدول مرفقات المؤيدين'
];

echo "<div class='alert alert-info'>";
echo "<h5><i class='fas fa-info-circle'></i> فحص الجداول المطلوبة</h5>";
echo "</div>";

echo "<div class='row'>";

foreach ($required_tables as $table => $description) {
    echo "<div class='col-md-6 mb-3'>";
    echo "<div class='card'>";
    echo "<div class='card-body'>";
    
    try {
        $result = fetchOne("SELECT COUNT(*) as count FROM $table");
        $count = $result['count'] ?? 0;
        
        echo "<h6 class='card-title status-success'>";
        echo "<i class='fas fa-check-circle'></i> $description";
        echo "</h6>";
        echo "<p class='card-text'>عدد السجلات: <strong>$count</strong></p>";
        
    } catch (Exception $e) {
        echo "<h6 class='card-title status-error'>";
        echo "<i class='fas fa-times-circle'></i> $description";
        echo "</h6>";
        echo "<p class='card-text status-error'>خطأ: " . $e->getMessage() . "</p>";
    }
    
    echo "</div>";
    echo "</div>";
    echo "</div>";
}

echo "</div>";

// فحص البيانات التفصيلية
echo "<div class='alert alert-warning mt-4'>";
echo "<h5><i class='fas fa-search'></i> فحص البيانات التفصيلية</h5>";
echo "</div>";

echo "<div class='row'>";

// فحص المؤيدين
echo "<div class='col-md-4 mb-3'>";
echo "<div class='card'>";
echo "<div class='card-header bg-primary text-white'>";
echo "<h6><i class='fas fa-users'></i> المؤيدين</h6>";
echo "</div>";
echo "<div class='card-body'>";

try {
    $supporters_stats = fetchOne("
        SELECT 
            COUNT(*) as total,
            SUM(CASE WHEN gender = 'male' THEN 1 ELSE 0 END) as male_count,
            SUM(CASE WHEN gender = 'female' THEN 1 ELSE 0 END) as female_count,
            COUNT(DISTINCT added_by) as admins_count
        FROM supporters
    ");
    
    echo "<ul class='list-unstyled'>";
    echo "<li>إجمالي المؤيدين: <strong>" . ($supporters_stats['total'] ?? 0) . "</strong></li>";
    echo "<li>ذكور: <strong>" . ($supporters_stats['male_count'] ?? 0) . "</strong></li>";
    echo "<li>إناث: <strong>" . ($supporters_stats['female_count'] ?? 0) . "</strong></li>";
    echo "<li>عدد الإداريين النشطين: <strong>" . ($supporters_stats['admins_count'] ?? 0) . "</strong></li>";
    echo "</ul>";
    
    // آخر المؤيدين
    $recent_supporters = fetchAll("
        SELECT s.full_name, s.phone, a.full_name as admin_name, s.created_at
        FROM supporters s
        LEFT JOIN admins a ON s.added_by = a.id
        ORDER BY s.created_at DESC LIMIT 3
    ");
    
    if (!empty($recent_supporters)) {
        echo "<hr>";
        echo "<h6>آخر المؤيدين:</h6>";
        foreach ($recent_supporters as $supporter) {
            echo "<small>";
            echo "• " . htmlspecialchars($supporter['full_name']) . " ";
            echo "(" . htmlspecialchars($supporter['phone']) . ") ";
            echo "بواسطة: " . htmlspecialchars($supporter['admin_name'] ?? 'غير محدد');
            echo "</small><br>";
        }
    }
    
} catch (Exception $e) {
    echo "<p class='status-error'>خطأ: " . $e->getMessage() . "</p>";
}

echo "</div>";
echo "</div>";
echo "</div>";

// فحص المطالب
echo "<div class='col-md-4 mb-3'>";
echo "<div class='card'>";
echo "<div class='card-header bg-warning text-dark'>";
echo "<h6><i class='fas fa-hand-holding-heart'></i> المطالب</h6>";
echo "</div>";
echo "<div class='card-body'>";

try {
    $requests_stats = fetchOne("
        SELECT 
            COUNT(*) as total,
            SUM(CASE WHEN status = 'pending' THEN 1 ELSE 0 END) as pending_count,
            SUM(CASE WHEN status = 'completed' THEN 1 ELSE 0 END) as completed_count,
            SUM(CASE WHEN status = 'rejected' THEN 1 ELSE 0 END) as rejected_count
        FROM supporter_requests
    ");
    
    echo "<ul class='list-unstyled'>";
    echo "<li>إجمالي المطالب: <strong>" . ($requests_stats['total'] ?? 0) . "</strong></li>";
    echo "<li>في الانتظار: <strong>" . ($requests_stats['pending_count'] ?? 0) . "</strong></li>";
    echo "<li>مكتملة: <strong>" . ($requests_stats['completed_count'] ?? 0) . "</strong></li>";
    echo "<li>مرفوضة: <strong>" . ($requests_stats['rejected_count'] ?? 0) . "</strong></li>";
    echo "</ul>";
    
    // آخر المطالب
    $recent_requests = fetchAll("
        SELECT sr.title, sr.supporter_name, a.full_name as admin_name, sr.submitted_at
        FROM supporter_requests sr
        LEFT JOIN admins a ON sr.admin_id = a.id
        ORDER BY sr.submitted_at DESC LIMIT 3
    ");
    
    if (!empty($recent_requests)) {
        echo "<hr>";
        echo "<h6>آخر المطالب:</h6>";
        foreach ($recent_requests as $request) {
            echo "<small>";
            echo "• " . htmlspecialchars($request['title']) . " ";
            echo "من: " . htmlspecialchars($request['supporter_name']) . " ";
            echo "بواسطة: " . htmlspecialchars($request['admin_name'] ?? 'غير محدد');
            echo "</small><br>";
        }
    }
    
} catch (Exception $e) {
    echo "<p class='status-error'>خطأ: " . $e->getMessage() . "</p>";
}

echo "</div>";
echo "</div>";
echo "</div>";

// فحص الرسائل
echo "<div class='col-md-4 mb-3'>";
echo "<div class='card'>";
echo "<div class='card-header bg-info text-white'>";
echo "<h6><i class='fas fa-envelope'></i> الرسائل</h6>";
echo "</div>";
echo "<div class='card-body'>";

try {
    $messages_stats = fetchOne("
        SELECT 
            COUNT(*) as total,
            SUM(CASE WHEN status = 'unread' THEN 1 ELSE 0 END) as unread_count,
            SUM(CASE WHEN status = 'read' THEN 1 ELSE 0 END) as read_count
        FROM messages
    ");
    
    echo "<ul class='list-unstyled'>";
    echo "<li>إجمالي الرسائل: <strong>" . ($messages_stats['total'] ?? 0) . "</strong></li>";
    echo "<li>غير مقروءة: <strong>" . ($messages_stats['unread_count'] ?? 0) . "</strong></li>";
    echo "<li>مقروءة: <strong>" . ($messages_stats['read_count'] ?? 0) . "</strong></li>";
    echo "</ul>";
    
    // آخر الرسائل
    $recent_messages = fetchAll("
        SELECT m.subject, 
               CASE 
                   WHEN m.sender_type = 'admin' THEN a.full_name
                   WHEN m.sender_type = 'management' THEN u.full_name
                   ELSE 'النظام'
               END as sender_name,
               m.created_at
        FROM messages m
        LEFT JOIN admins a ON m.sender_id = a.id AND m.sender_type = 'admin'
        LEFT JOIN users u ON m.sender_id = u.id AND m.sender_type = 'management'
        ORDER BY m.created_at DESC LIMIT 3
    ");
    
    if (!empty($recent_messages)) {
        echo "<hr>";
        echo "<h6>آخر الرسائل:</h6>";
        foreach ($recent_messages as $message) {
            echo "<small>";
            echo "• " . htmlspecialchars($message['subject']) . " ";
            echo "من: " . htmlspecialchars($message['sender_name'] ?? 'غير محدد');
            echo "</small><br>";
        }
    }
    
} catch (Exception $e) {
    echo "<p class='status-error'>خطأ: " . $e->getMessage() . "</p>";
}

echo "</div>";
echo "</div>";
echo "</div>";

echo "</div>";

// روابط سريعة
echo "<div class='alert alert-success mt-4'>";
echo "<h5><i class='fas fa-link'></i> روابط سريعة</h5>";
echo "<div class='row'>";

echo "<div class='col-md-3 mb-2'>";
echo "<a href='dashboard.php' class='btn btn-primary w-100'>";
echo "<i class='fas fa-home'></i> لوحة التحكم الرئيسية";
echo "</a>";
echo "</div>";

echo "<div class='col-md-3 mb-2'>";
echo "<a href='modules/admin/dashboard.php' class='btn btn-success w-100'>";
echo "<i class='fas fa-user-shield'></i> لوحة تحكم الإداريين";
echo "</a>";
echo "</div>";

echo "<div class='col-md-3 mb-2'>";
echo "<a href='modules/admin/add_supporter.php' class='btn btn-warning w-100'>";
echo "<i class='fas fa-user-plus'></i> إضافة مؤيد";
echo "</a>";
echo "</div>";

echo "<div class='col-md-3 mb-2'>";
echo "<a href='campaign_tables_only.sql' class='btn btn-info w-100' download>";
echo "<i class='fas fa-download'></i> تحميل قاعدة البيانات";
echo "</a>";
echo "</div>";

echo "</div>";
echo "</div>";

echo "</div>";
echo "</body>";
echo "</html>";
?>
