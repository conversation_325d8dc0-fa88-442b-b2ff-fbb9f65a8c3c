<?php
// إصلاح طارئ للنظام
error_reporting(E_ALL);
ini_set('display_errors', 1);
header('Content-Type: text/html; charset=utf-8');

echo "<!DOCTYPE html>";
echo "<html lang='ar' dir='rtl'>";
echo "<head>";
echo "<meta charset='UTF-8'>";
echo "<meta name='viewport' content='width=device-width, initial-scale=1.0'>";
echo "<title>إصلاح طارئ للنظام</title>";
echo "<link href='https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css' rel='stylesheet'>";
echo "<style>body { font-family: Arial, sans-serif; padding: 20px; background: #f8f9fa; }</style>";
echo "</head>";
echo "<body>";

echo "<div class='container'>";
echo "<div class='row justify-content-center'>";
echo "<div class='col-md-8'>";

echo "<div class='card'>";
echo "<div class='card-header bg-danger text-white'>";
echo "<h3><i class='fas fa-exclamation-triangle'></i> إصلاح طارئ للنظام</h3>";
echo "</div>";
echo "<div class='card-body'>";

// فحص الملفات الأساسية
echo "<h5>1. فحص الملفات الأساسية:</h5>";

$critical_files = [
    'config/database.php' => 'ملف قاعدة البيانات',
    'config/config.php' => 'ملف الإعدادات',
    'dashboard.php' => 'لوحة تحكم المرشح',
    'modules/admin/login.php' => 'تسجيل دخول الإداريين'
];

$missing_files = [];
foreach ($critical_files as $file => $description) {
    if (file_exists($file)) {
        echo "<p class='text-success'>✅ $description - موجود</p>";
    } else {
        echo "<p class='text-danger'>❌ $description - مفقود</p>";
        $missing_files[] = $file;
    }
}

// إنشاء ملف config.php بسيط إذا كان مفقوداً
if (in_array('config/config.php', $missing_files)) {
    echo "<h5>2. إنشاء ملف config.php:</h5>";
    
    $config_content = '<?php
// بدء الجلسة
if (session_status() == PHP_SESSION_NONE) {
    session_start();
}

// إعدادات النظام العامة
define("SITE_NAME", "نظام إدارة الحملة الانتخابية");
define("SITE_URL", "http://localhost");

// دالة للتحقق من تسجيل الدخول
function isLoggedIn() {
    return isset($_SESSION["user_id"]) && isset($_SESSION["username"]);
}

// دالة للتحقق من صلاحيات الإداري
function isAdmin() {
    return isset($_SESSION["user_type"]) && $_SESSION["user_type"] === "admin";
}

// دالة للتحقق من صلاحيات المرشح
function isCandidate() {
    return isset($_SESSION["user_type"]) && $_SESSION["user_type"] === "candidate";
}

// دالة لإعادة التوجيه
function redirect($url) {
    header("Location: $url");
    exit();
}

// دالة لعرض الرسائل
function showMessage($message, $type = "info") {
    $_SESSION["message"] = $message;
    $_SESSION["message_type"] = $type;
}

// دالة لعرض الرسائل المحفوظة
function displayMessage() {
    if (isset($_SESSION["message"])) {
        $type = $_SESSION["message_type"] ?? "info";
        $message = $_SESSION["message"];
        unset($_SESSION["message"], $_SESSION["message_type"]);

        $alertClass = [
            "success" => "alert-success",
            "error" => "alert-danger",
            "warning" => "alert-warning",
            "info" => "alert-info"
        ][$type] ?? "alert-info";

        echo "<div class=\"alert $alertClass alert-dismissible fade show\" role=\"alert\">
                $message
                <button type=\"button\" class=\"btn-close\" data-bs-dismiss=\"alert\"></button>
              </div>";
    }
}
?>';
    
    if (!is_dir('config')) {
        mkdir('config', 0755, true);
    }
    
    if (file_put_contents('config/config.php', $config_content)) {
        echo "<p class='text-success'>✅ تم إنشاء ملف config.php بنجاح</p>";
    } else {
        echo "<p class='text-danger'>❌ فشل في إنشاء ملف config.php</p>";
    }
}

// إنشاء ملف database.php بسيط إذا كان مفقوداً
if (in_array('config/database.php', $missing_files)) {
    echo "<h5>3. إنشاء ملف database.php:</h5>";
    
    $database_content = '<?php
// إعدادات قاعدة البيانات
define("DB_HOST", "localhost");
define("DB_NAME", "cpses_irs7jkoxpg");
define("DB_USER", "cpses_irs7jkoxpg");
define("DB_PASS", "Zain@123456789");
define("DB_CHARSET", "utf8mb4");

class Database {
    private $host = DB_HOST;
    private $db_name = DB_NAME;
    private $username = DB_USER;
    private $password = DB_PASS;
    private $charset = DB_CHARSET;
    public $conn;

    public function getConnection() {
        $this->conn = null;
        try {
            $dsn = "mysql:host=" . $this->host . ";dbname=" . $this->db_name . ";charset=utf8mb4";
            $this->conn = new PDO($dsn, $this->username, $this->password, array(
                PDO::MYSQL_ATTR_INIT_COMMAND => "SET NAMES utf8mb4 COLLATE utf8mb4_unicode_ci",
                PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
                PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
                PDO::ATTR_EMULATE_PREPARES => false
            ));
        } catch(PDOException $exception) {
            echo "خطأ في الاتصال: " . $exception->getMessage();
        }
        return $this->conn;
    }
}

// دالة للحصول على اتصال قاعدة البيانات
function getDBConnection() {
    $database = new Database();
    return $database->getConnection();
}

// دالة لتنفيذ استعلام آمن
function executeQuery($sql, $params = []) {
    try {
        $conn = getDBConnection();
        $stmt = $conn->prepare($sql);
        $result = $stmt->execute($params);
        return $result;
    } catch(PDOException $e) {
        error_log("Database Error: " . $e->getMessage());
        return false;
    }
}

// دالة للحصول على صف واحد
function fetchOne($sql, $params = []) {
    try {
        $conn = getDBConnection();
        $stmt = $conn->prepare($sql);
        $stmt->execute($params);
        return $stmt->fetch(PDO::FETCH_ASSOC);
    } catch(PDOException $e) {
        error_log("Database Error: " . $e->getMessage());
        return false;
    }
}

// دالة للحصول على عدة صفوف
function fetchAll($sql, $params = []) {
    try {
        $conn = getDBConnection();
        $stmt = $conn->prepare($sql);
        $stmt->execute($params);
        return $stmt->fetchAll(PDO::FETCH_ASSOC);
    } catch(PDOException $e) {
        error_log("Database Error: " . $e->getMessage());
        return [];
    }
}
?>';
    
    if (file_put_contents('config/database.php', $database_content)) {
        echo "<p class='text-success'>✅ تم إنشاء ملف database.php بنجاح</p>";
    } else {
        echo "<p class='text-danger'>❌ فشل في إنشاء ملف database.php</p>";
    }
}

// اختبار الاتصال بقاعدة البيانات
echo "<h5>4. اختبار الاتصال بقاعدة البيانات:</h5>";

try {
    $pdo = new PDO("mysql:host=localhost;dbname=cpses_irs7jkoxpg;charset=utf8mb4", "cpses_irs7jkoxpg", "Zain@123456789");
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    echo "<p class='text-success'>✅ الاتصال بقاعدة البيانات نجح</p>";
    
    // اختبار الجداول الأساسية
    $tables = ['users', 'admins', 'supporters', 'regions'];
    foreach ($tables as $table) {
        try {
            $stmt = $pdo->query("SELECT COUNT(*) FROM $table");
            $count = $stmt->fetchColumn();
            echo "<p class='text-success'>✅ جدول $table - يحتوي على $count سجل</p>";
        } catch (Exception $e) {
            echo "<p class='text-warning'>⚠️ جدول $table - قد يحتاج إنشاء</p>";
        }
    }
    
} catch (Exception $e) {
    echo "<p class='text-danger'>❌ خطأ في الاتصال بقاعدة البيانات: " . $e->getMessage() . "</p>";
}

// روابط الاختبار
echo "<h5>5. روابط الاختبار:</h5>";
echo "<div class='row'>";

$test_links = [
    ['الصفحة الرئيسية', 'index.php', 'primary'],
    ['اختبار النظام', 'test_system.php', 'info'],
    ['لوحة تحكم المرشح', 'dashboard.php', 'success'],
    ['تسجيل دخول الإداريين', 'modules/admin/login.php', 'warning']
];

foreach ($test_links as $link) {
    echo "<div class='col-md-6 mb-2'>";
    echo "<a href='{$link[1]}' class='btn btn-{$link[2]} w-100' target='_blank'>";
    echo "{$link[0]}";
    echo "</a>";
    echo "</div>";
}

echo "</div>";

echo "</div>";
echo "</div>";

echo "</div>";
echo "</div>";
echo "</div>";

echo "</body>";
echo "</html>";
?>
