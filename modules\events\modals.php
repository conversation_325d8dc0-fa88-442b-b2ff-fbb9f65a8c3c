<!-- نافذة إضافة فعالية جديدة -->
<div class="modal fade" id="addEventModal" tabindex="-1" aria-labelledby="addEventModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="addEventModalLabel">
                    <i class="fas fa-plus me-2"></i>
                    إضافة فعالية جديدة
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="إغلاق"></button>
            </div>
            <form method="POST" action="" class="event-form needs-validation" enctype="multipart/form-data" novalidate>
                <input type="hidden" name="action" value="add">
                <div class="modal-body">
                    <div class="row">
                        <!-- اسم الفعالية -->
                        <div class="col-md-6 mb-3">
                            <label for="name" class="form-label">اسم الفعالية <span class="required">*</span></label>
                            <input type="text" class="form-control" id="name" name="name" required>
                            <div class="invalid-feedback">يرجى إدخال اسم الفعالية</div>
                        </div>
                        
                        <!-- حالة الفعالية -->
                        <div class="col-md-6 mb-3">
                            <label for="status" class="form-label">حالة الفعالية <span class="required">*</span></label>
                            <select class="form-select" id="status" name="status" required>
                                <option value="">اختر الحالة</option>
                                <option value="planned">مخططة</option>
                                <option value="ongoing">جارية</option>
                                <option value="completed">مكتملة</option>
                                <option value="cancelled">ملغية</option>
                            </select>
                            <div class="invalid-feedback">يرجى اختيار حالة الفعالية</div>
                        </div>
                        
                        <!-- تاريخ الفعالية -->
                        <div class="col-md-6 mb-3">
                            <label for="event_date" class="form-label">تاريخ الفعالية <span class="required">*</span></label>
                            <input type="date" class="form-control" id="event_date" name="event_date" required>
                            <div class="invalid-feedback">يرجى إدخال تاريخ الفعالية</div>
                        </div>
                        
                        <!-- وقت الفعالية -->
                        <div class="col-md-6 mb-3">
                            <label for="event_time" class="form-label">وقت الفعالية</label>
                            <input type="time" class="form-control" id="event_time" name="event_time">
                        </div>
                        
                        <!-- مكان الفعالية -->
                        <div class="col-md-12 mb-3">
                            <label for="location" class="form-label">مكان الفعالية <span class="required">*</span></label>
                            <input type="text" class="form-control" id="location" name="location" 
                                   placeholder="مثال: قاعة المؤتمرات - فندق بغداد" required>
                            <div class="invalid-feedback">يرجى إدخال مكان الفعالية</div>
                        </div>
                        
                        <!-- وصف الفعالية -->
                        <div class="col-md-12 mb-3">
                            <label for="description" class="form-label">وصف الفعالية</label>
                            <textarea class="form-control" id="description" name="description" rows="3" 
                                      placeholder="وصف مفصل عن الفعالية وأهدافها..."></textarea>
                        </div>
                        
                        <!-- رفع الصور -->
                        <div class="col-md-12 mb-3">
                            <label for="photos" class="form-label">صور الفعالية</label>
                            <div class="photo-upload-area">
                                <input type="file" class="form-control d-none" id="photos" name="photos[]" 
                                       multiple accept="image/*">
                                <div class="text-center">
                                    <i class="fas fa-cloud-upload-alt fa-3x text-muted mb-3"></i>
                                    <p class="mb-0">اضغط هنا أو اسحب الصور لرفعها</p>
                                    <small class="text-muted">يمكن رفع عدة صور (JPG, PNG, GIF - حد أقصى 5MB لكل صورة)</small>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="alert alert-info">
                        <i class="fas fa-info-circle me-2"></i>
                        <strong>ملاحظة:</strong> يمكنك تعديل تفاصيل الفعالية وإضافة المزيد من الصور لاحقاً.
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                        <i class="fas fa-times me-2"></i>إلغاء
                    </button>
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-save me-2"></i>حفظ الفعالية
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- نافذة تعديل الفعالية -->
<div class="modal fade" id="editEventModal" tabindex="-1" aria-labelledby="editEventModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="editEventModalLabel">
                    <i class="fas fa-edit me-2"></i>
                    تعديل الفعالية
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="إغلاق"></button>
            </div>
            <form method="POST" action="" class="event-form needs-validation" enctype="multipart/form-data" novalidate>
                <input type="hidden" name="action" value="edit">
                <input type="hidden" id="editId" name="id">
                <div class="modal-body">
                    <div class="row">
                        <!-- اسم الفعالية -->
                        <div class="col-md-6 mb-3">
                            <label for="editName" class="form-label">اسم الفعالية <span class="required">*</span></label>
                            <input type="text" class="form-control" id="editName" name="name" required>
                            <div class="invalid-feedback">يرجى إدخال اسم الفعالية</div>
                        </div>
                        
                        <!-- حالة الفعالية -->
                        <div class="col-md-6 mb-3">
                            <label for="editStatus" class="form-label">حالة الفعالية <span class="required">*</span></label>
                            <select class="form-select" id="editStatus" name="status" required>
                                <option value="">اختر الحالة</option>
                                <option value="planned">مخططة</option>
                                <option value="ongoing">جارية</option>
                                <option value="completed">مكتملة</option>
                                <option value="cancelled">ملغية</option>
                            </select>
                            <div class="invalid-feedback">يرجى اختيار حالة الفعالية</div>
                        </div>
                        
                        <!-- تاريخ الفعالية -->
                        <div class="col-md-6 mb-3">
                            <label for="editEventDate" class="form-label">تاريخ الفعالية <span class="required">*</span></label>
                            <input type="date" class="form-control" id="editEventDate" name="event_date" required>
                            <div class="invalid-feedback">يرجى إدخال تاريخ الفعالية</div>
                        </div>
                        
                        <!-- وقت الفعالية -->
                        <div class="col-md-6 mb-3">
                            <label for="editEventTime" class="form-label">وقت الفعالية</label>
                            <input type="time" class="form-control" id="editEventTime" name="event_time">
                        </div>
                        
                        <!-- مكان الفعالية -->
                        <div class="col-md-12 mb-3">
                            <label for="editLocation" class="form-label">مكان الفعالية <span class="required">*</span></label>
                            <input type="text" class="form-control" id="editLocation" name="location" required>
                            <div class="invalid-feedback">يرجى إدخال مكان الفعالية</div>
                        </div>
                        
                        <!-- وصف الفعالية -->
                        <div class="col-md-12 mb-3">
                            <label for="editDescription" class="form-label">وصف الفعالية</label>
                            <textarea class="form-control" id="editDescription" name="description" rows="3"></textarea>
                        </div>
                        
                        <!-- رفع صور جديدة -->
                        <div class="col-md-12 mb-3">
                            <label for="editPhotos" class="form-label">إضافة صور جديدة</label>
                            <div class="photo-upload-area">
                                <input type="file" class="form-control d-none" id="editPhotos" name="photos[]" 
                                       multiple accept="image/*">
                                <div class="text-center">
                                    <i class="fas fa-cloud-upload-alt fa-2x text-muted mb-2"></i>
                                    <p class="mb-0">اضغط هنا لإضافة صور جديدة</p>
                                    <small class="text-muted">الصور الجديدة ستضاف إلى الصور الموجودة</small>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="alert alert-warning">
                        <i class="fas fa-exclamation-triangle me-2"></i>
                        <strong>تنبيه:</strong> تعديل تفاصيل الفعالية سيؤثر على المعلومات المعروضة للجمهور.
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                        <i class="fas fa-times me-2"></i>إلغاء
                    </button>
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-save me-2"></i>حفظ التغييرات
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- نافذة عرض تفاصيل الفعالية -->
<div class="modal fade" id="viewEventModal" tabindex="-1" aria-labelledby="viewEventModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-xl">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="viewEventModalLabel">
                    <i class="fas fa-calendar-alt me-2"></i>
                    تفاصيل الفعالية
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="إغلاق"></button>
            </div>
            <div class="modal-body">
                <div class="row">
                    <div class="col-md-6">
                        <div class="event-details">
                            <div class="detail-item">
                                <span class="detail-label">اسم الفعالية:</span>
                                <span class="detail-value" id="viewEventName"></span>
                            </div>
                            <div class="detail-item">
                                <span class="detail-label">التاريخ:</span>
                                <span class="detail-value" id="viewEventDate"></span>
                            </div>
                            <div class="detail-item">
                                <span class="detail-label">الوقت:</span>
                                <span class="detail-value" id="viewEventTime"></span>
                            </div>
                            <div class="detail-item">
                                <span class="detail-label">المكان:</span>
                                <span class="detail-value" id="viewEventLocation"></span>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="event-details">
                            <div class="detail-item">
                                <span class="detail-label">الحالة:</span>
                                <span class="detail-value" id="viewEventStatus"></span>
                            </div>
                            <div class="detail-item">
                                <span class="detail-label">أضيف بواسطة:</span>
                                <span class="detail-value" id="viewEventAddedBy"></span>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="row mt-3">
                    <div class="col-12">
                        <h6>وصف الفعالية:</h6>
                        <p id="viewEventDescription" class="text-muted"></p>
                    </div>
                </div>
                
                <div class="row mt-3">
                    <div class="col-12">
                        <h6>صور الفعالية:</h6>
                        <div id="viewEventPhotos" class="photo-gallery"></div>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                    <i class="fas fa-times me-2"></i>إغلاق
                </button>
                <button type="button" class="btn btn-primary" onclick="printEventDetails()">
                    <i class="fas fa-print me-2"></i>طباعة
                </button>
            </div>
        </div>
    </div>
</div>

<!-- نافذة عرض الصورة بالحجم الكامل -->
<div class="modal fade" id="photoModal" tabindex="-1" aria-labelledby="photoModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="photoModalLabel">
                    <i class="fas fa-image me-2"></i>
                    عرض الصورة
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="إغلاق"></button>
            </div>
            <div class="modal-body text-center">
                <img id="modalPhoto" class="img-fluid" alt="صورة الفعالية" style="max-height: 70vh;">
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                    <i class="fas fa-times me-2"></i>إغلاق
                </button>
                <button type="button" class="btn btn-primary" onclick="downloadPhoto()">
                    <i class="fas fa-download me-2"></i>تحميل الصورة
                </button>
            </div>
        </div>
    </div>
</div>

<script>
// تنسيق تفاصيل الفعالية
document.addEventListener('DOMContentLoaded', function() {
    const detailItems = document.querySelectorAll('.detail-item');
    detailItems.forEach(item => {
        item.style.display = 'flex';
        item.style.justifyContent = 'space-between';
        item.style.alignItems = 'center';
        item.style.padding = '10px 0';
        item.style.borderBottom = '1px solid #eee';
    });
    
    const detailLabels = document.querySelectorAll('.detail-label');
    detailLabels.forEach(label => {
        label.style.fontWeight = '600';
        label.style.color = '#495057';
        label.style.minWidth = '120px';
    });
    
    const detailValues = document.querySelectorAll('.detail-value');
    detailValues.forEach(value => {
        value.style.color = '#6c757d';
        value.style.textAlign = 'left';
    });
});

// طباعة تفاصيل الفعالية
function printEventDetails() {
    const eventName = document.getElementById('viewEventName').textContent;
    const eventDate = document.getElementById('viewEventDate').textContent;
    const eventTime = document.getElementById('viewEventTime').textContent;
    const eventLocation = document.getElementById('viewEventLocation').textContent;
    const eventDescription = document.getElementById('viewEventDescription').textContent;
    
    const printContent = `
        <html>
        <head>
            <title>تفاصيل الفعالية - ${eventName}</title>
            <style>
                body { font-family: Arial, sans-serif; direction: rtl; }
                .header { text-align: center; margin-bottom: 30px; }
                .details { margin: 20px 0; }
                .detail-row { margin: 10px 0; }
                .label { font-weight: bold; }
            </style>
        </head>
        <body>
            <div class="header">
                <h1>تفاصيل الفعالية</h1>
                <h2>${eventName}</h2>
            </div>
            <div class="details">
                <div class="detail-row"><span class="label">التاريخ:</span> ${eventDate}</div>
                <div class="detail-row"><span class="label">الوقت:</span> ${eventTime}</div>
                <div class="detail-row"><span class="label">المكان:</span> ${eventLocation}</div>
                <div class="detail-row"><span class="label">الوصف:</span> ${eventDescription}</div>
            </div>
        </body>
        </html>
    `;
    
    const printWindow = window.open('', '_blank');
    printWindow.document.write(printContent);
    printWindow.document.close();
    printWindow.print();
}

// تحميل الصورة
function downloadPhoto() {
    const photoSrc = document.getElementById('modalPhoto').src;
    const link = document.createElement('a');
    link.href = photoSrc;
    link.download = 'event_photo_' + Date.now() + '.jpg';
    link.click();
}
</script>
