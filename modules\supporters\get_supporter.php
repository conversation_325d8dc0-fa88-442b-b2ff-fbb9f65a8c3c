<?php
require_once '../../config/config.php';
require_once '../../config/database.php';

// التحقق من تسجيل الدخول
if (!isLoggedIn()) {
    http_response_code(401);
    echo json_encode(['success' => false, 'message' => 'غير مصرح']);
    exit;
}

// التحقق من وجود معرف المؤيد
if (!isset($_GET['id']) || !is_numeric($_GET['id'])) {
    http_response_code(400);
    echo json_encode(['success' => false, 'message' => 'معرف المؤيد غير صحيح']);
    exit;
}

$supporter_id = (int)$_GET['id'];

// بناء الاستعلام مع التحقق من الصلاحيات
$where_conditions = ["s.id = ?"];
$params = [$supporter_id];

// تصفية حسب المنطقة للإداريين
if (isAdmin() && $_SESSION['region_id']) {
    $where_conditions[] = "s.region_id = ?";
    $params[] = $_SESSION['region_id'];
}

$where_clause = implode(' AND ', $where_conditions);

// جلب بيانات المؤيد
$sql = "SELECT s.*, r.name as region_name, u.full_name as added_by_name,
               YEAR(CURDATE()) - YEAR(s.birth_date) as age
        FROM supporters s 
        LEFT JOIN regions r ON s.region_id = r.id 
        LEFT JOIN users u ON s.added_by = u.id 
        WHERE $where_clause";

$supporter = fetchOne($sql, $params);

if (!$supporter) {
    http_response_code(404);
    echo json_encode(['success' => false, 'message' => 'المؤيد غير موجود']);
    exit;
}

// إرجاع البيانات
header('Content-Type: application/json; charset=utf-8');
echo json_encode([
    'success' => true,
    'supporter' => $supporter
]);
?>
