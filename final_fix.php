<?php
// الإصلاح النهائي الشامل لجميع مشاكل النظام

header('Content-Type: text/html; charset=utf-8');
ini_set('default_charset', 'utf-8');
mb_internal_encoding('UTF-8');

require_once 'config/config.php';
require_once 'config/database.php';

echo "<!DOCTYPE html>";
echo "<html lang='ar' dir='rtl'>";
echo "<head>";
echo "<meta charset='UTF-8'>";
echo "<meta name='viewport' content='width=device-width, initial-scale=1.0'>";
echo "<title>الإصلاح النهائي الشامل</title>";
echo "<link href='https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css' rel='stylesheet'>";
echo "<link href='https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css' rel='stylesheet'>";
echo "<style>";
echo "body { font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); min-height: 100vh; padding: 2rem; }";
echo ".fix-card { background: white; border-radius: 15px; box-shadow: 0 10px 30px rgba(0,0,0,0.1); padding: 2rem; margin: 0 auto; max-width: 1000px; }";
echo ".success { color: #28a745; }";
echo ".error { color: #dc3545; }";
echo ".warning { color: #ffc107; }";
echo ".step { margin: 1rem 0; padding: 1rem; border-radius: 10px; }";
echo "</style>";
echo "</head>";
echo "<body>";

echo "<div class='fix-card'>";
echo "<h1 class='text-center mb-4'><i class='fas fa-magic'></i> الإصلاح النهائي الشامل</h1>";

if (isset($_POST['final_fix'])) {
    try {
        echo "<div class='alert alert-info'>";
        echo "<h5>🚀 جاري الإصلاح الشامل لجميع مشاكل النظام...</h5>";
        echo "</div>";

        $database = new Database();
        $db = $database->getConnection();

        if (!$db) {
            throw new Exception("فشل الاتصال بقاعدة البيانات");
        }

        // الخطوة 1: إصلاح الترميز
        echo "<div class='step alert alert-primary'>";
        echo "<h6>🔧 الخطوة 1: إصلاح الترميز العربي</h6>";
        $db->exec("SET NAMES utf8mb4 COLLATE utf8mb4_unicode_ci");
        $db->exec("SET CHARACTER SET utf8mb4");
        echo "<p class='success'><i class='fas fa-check'></i> تم إصلاح الترميز العربي</p>";
        echo "</div>";

        // الخطوة 2: حذف الجداول القديمة وإنشاء جداول جديدة
        echo "<div class='step alert alert-warning'>";
        echo "<h6>🗃️ الخطوة 2: إعادة إنشاء قاعدة البيانات</h6>";
        
        // حذف الجداول القديمة
        $tables = ['notifications', 'messages', 'competitors', 'events', 'expenses', 'supporters', 'users', 'regions'];
        foreach ($tables as $table) {
            try {
                $db->exec("DROP TABLE IF EXISTS $table");
                echo "<p class='warning'><i class='fas fa-trash'></i> تم حذف جدول $table القديم</p>";
            } catch (Exception $e) {
                // تجاهل الأخطاء
            }
        }
        
        // إنشاء جدول المناطق
        $sql = "CREATE TABLE regions (
            id INT AUTO_INCREMENT PRIMARY KEY,
            name VARCHAR(100) NOT NULL,
            description TEXT,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci";
        $db->exec($sql);
        echo "<p class='success'><i class='fas fa-check'></i> تم إنشاء جدول المناطق</p>";
        
        // إنشاء جدول المستخدمين
        $sql = "CREATE TABLE users (
            id INT AUTO_INCREMENT PRIMARY KEY,
            username VARCHAR(50) UNIQUE NOT NULL,
            password VARCHAR(255) NOT NULL,
            phone VARCHAR(15) NOT NULL,
            full_name VARCHAR(100) NOT NULL,
            user_type ENUM('candidate', 'admin') DEFAULT 'admin',
            permissions TEXT,
            region_id INT DEFAULT NULL,
            status ENUM('active', 'inactive') DEFAULT 'active',
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci";
        $db->exec($sql);
        echo "<p class='success'><i class='fas fa-check'></i> تم إنشاء جدول المستخدمين</p>";
        
        // إنشاء جدول المؤيدين
        $sql = "CREATE TABLE supporters (
            id INT AUTO_INCREMENT PRIMARY KEY,
            full_name VARCHAR(100) NOT NULL,
            gender ENUM('male', 'female') NOT NULL,
            marital_status ENUM('single', 'married', 'divorced', 'widowed') NOT NULL,
            birth_date DATE NOT NULL,
            education VARCHAR(50),
            profession VARCHAR(100),
            address TEXT NOT NULL,
            phone VARCHAR(15) NOT NULL,
            voter_number VARCHAR(50),
            voting_center VARCHAR(100),
            region_id INT NOT NULL,
            photo VARCHAR(255),
            notes TEXT,
            added_by INT NOT NULL,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci";
        $db->exec($sql);
        echo "<p class='success'><i class='fas fa-check'></i> تم إنشاء جدول المؤيدين</p>";
        
        // إنشاء جدول المصروفات
        $sql = "CREATE TABLE expenses (
            id INT AUTO_INCREMENT PRIMARY KEY,
            description TEXT NOT NULL,
            amount DECIMAL(10,2) NOT NULL,
            category VARCHAR(100) NOT NULL,
            expense_date DATE NOT NULL,
            region_id INT,
            attachment VARCHAR(255),
            notes TEXT,
            added_by INT NOT NULL,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci";
        $db->exec($sql);
        echo "<p class='success'><i class='fas fa-check'></i> تم إنشاء جدول المصروفات</p>";
        
        // إنشاء جدول الفعاليات
        $sql = "CREATE TABLE events (
            id INT AUTO_INCREMENT PRIMARY KEY,
            name VARCHAR(200) NOT NULL,
            description TEXT,
            event_date DATE NOT NULL,
            event_time TIME,
            location VARCHAR(255) NOT NULL,
            photos TEXT,
            status ENUM('planned', 'ongoing', 'completed', 'cancelled') DEFAULT 'planned',
            added_by INT NOT NULL,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci";
        $db->exec($sql);
        echo "<p class='success'><i class='fas fa-check'></i> تم إنشاء جدول الفعاليات</p>";
        
        // إنشاء جدول المنافسين
        $sql = "CREATE TABLE competitors (
            id INT AUTO_INCREMENT PRIMARY KEY,
            name VARCHAR(100) NOT NULL,
            party VARCHAR(100),
            region_id INT,
            strengths TEXT,
            weaknesses TEXT,
            photos TEXT,
            notes TEXT,
            added_by INT NOT NULL,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci";
        $db->exec($sql);
        echo "<p class='success'><i class='fas fa-check'></i> تم إنشاء جدول المنافسين</p>";
        
        // إنشاء جدول الرسائل
        $sql = "CREATE TABLE messages (
            id INT AUTO_INCREMENT PRIMARY KEY,
            sender_id INT NOT NULL,
            recipient_id INT,
            recipient_type ENUM('user', 'all_admins', 'active_admins') DEFAULT 'user',
            subject VARCHAR(255) NOT NULL,
            content TEXT NOT NULL,
            attachment VARCHAR(255),
            is_read TINYINT(1) DEFAULT 0,
            sent_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci";
        $db->exec($sql);
        echo "<p class='success'><i class='fas fa-check'></i> تم إنشاء جدول الرسائل</p>";
        
        // إنشاء جدول الإشعارات
        $sql = "CREATE TABLE notifications (
            id INT AUTO_INCREMENT PRIMARY KEY,
            user_id INT NOT NULL,
            title VARCHAR(255) NOT NULL,
            message TEXT NOT NULL,
            type ENUM('info', 'success', 'warning', 'error') DEFAULT 'info',
            is_read TINYINT(1) DEFAULT 0,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci";
        $db->exec($sql);
        echo "<p class='success'><i class='fas fa-check'></i> تم إنشاء جدول الإشعارات</p>";
        
        echo "</div>";

        // الخطوة 3: إدراج البيانات الأساسية
        echo "<div class='step alert alert-success'>";
        echo "<h6>📊 الخطوة 3: إدراج البيانات الأساسية</h6>";
        
        // إدراج المناطق العراقية
        $regions_data = [
            ['بغداد - الكرخ', 'منطقة الكرخ في بغداد'],
            ['بغداد - الرصافة', 'منطقة الرصافة في بغداد'],
            ['البصرة', 'محافظة البصرة'],
            ['النجف الأشرف', 'محافظة النجف الأشرف'],
            ['كربلاء المقدسة', 'محافظة كربلاء المقدسة'],
            ['الموصل', 'محافظة نينوى - الموصل'],
            ['أربيل', 'محافظة أربيل'],
            ['السليمانية', 'محافظة السليمانية'],
            ['دهوك', 'محافظة دهوك'],
            ['الأنبار', 'محافظة الأنبار']
        ];
        
        $stmt = $db->prepare("INSERT INTO regions (name, description) VALUES (?, ?)");
        foreach ($regions_data as $region) {
            $stmt->execute($region);
            echo "<p class='success'><i class='fas fa-map-marker-alt'></i> تم إدراج منطقة: {$region[0]}</p>";
        }
        
        // إدراج المستخدم الافتراضي (المرشح)
        $password_hash = password_hash('123456', PASSWORD_DEFAULT);
        $stmt = $db->prepare("INSERT INTO users (username, password, phone, full_name, user_type, status) VALUES (?, ?, ?, ?, ?, ?)");
        $stmt->execute(['abd', $password_hash, '07719992716', 'المرشح الرئيسي', 'candidate', 'active']);
        echo "<p class='success'><i class='fas fa-user-tie'></i> تم إدراج المرشح الرئيسي</p>";
        
        // إدراج إداري تجريبي
        $admin_password = password_hash('admin123', PASSWORD_DEFAULT);
        $stmt = $db->prepare("INSERT INTO users (username, password, phone, full_name, user_type, region_id, status) VALUES (?, ?, ?, ?, ?, ?, ?)");
        $stmt->execute(['admin1', $admin_password, '07701234567', 'الإداري الأول', 'admin', 1, 'active']);
        echo "<p class='success'><i class='fas fa-user-cog'></i> تم إدراج إداري تجريبي</p>";
        
        echo "</div>";

        // الخطوة 4: إنشاء المجلدات
        echo "<div class='step alert alert-info'>";
        echo "<h6>📁 الخطوة 4: إنشاء المجلدات المطلوبة</h6>";
        
        $folders = [
            'uploads',
            'uploads/supporters',
            'uploads/events',
            'uploads/competitors',
            'uploads/expenses',
            'uploads/messages',
            'exports',
            'modules',
            'modules/supporters',
            'modules/regions',
            'modules/admins',
            'modules/expenses',
            'modules/events',
            'modules/competitors',
            'modules/reports',
            'modules/settings'
        ];
        
        foreach ($folders as $folder) {
            if (!file_exists($folder)) {
                mkdir($folder, 0755, true);
                echo "<p class='success'><i class='fas fa-folder-plus'></i> تم إنشاء مجلد: $folder</p>";
            } else {
                echo "<p class='warning'><i class='fas fa-folder'></i> مجلد موجود: $folder</p>";
            }
        }
        
        echo "</div>";

        // الخطوة 5: اختبار النظام
        echo "<div class='step alert alert-primary'>";
        echo "<h6>🧪 الخطوة 5: اختبار النظام</h6>";
        
        // اختبار الترميز العربي
        $test_region = $db->query("SELECT name FROM regions WHERE id = 1")->fetch();
        if ($test_region && !strpos($test_region['name'], '?')) {
            echo "<p class='success'><i class='fas fa-language'></i> الترميز العربي يعمل بشكل صحيح: " . $test_region['name'] . "</p>";
        } else {
            echo "<p class='error'><i class='fas fa-exclamation-triangle'></i> مشكلة في الترميز العربي</p>";
        }
        
        // اختبار تسجيل الدخول
        $user = $db->query("SELECT * FROM users WHERE username = 'abd'")->fetch();
        if ($user && password_verify('123456', $user['password'])) {
            echo "<p class='success'><i class='fas fa-sign-in-alt'></i> تسجيل الدخول يعمل بشكل صحيح</p>";
        } else {
            echo "<p class='error'><i class='fas fa-exclamation-triangle'></i> مشكلة في تسجيل الدخول</p>";
        }
        
        // عرض الإحصائيات
        $regions_count = $db->query("SELECT COUNT(*) as count FROM regions")->fetch()['count'];
        $users_count = $db->query("SELECT COUNT(*) as count FROM users")->fetch()['count'];
        
        echo "<p class='success'><i class='fas fa-chart-bar'></i> عدد المناطق: $regions_count</p>";
        echo "<p class='success'><i class='fas fa-users'></i> عدد المستخدمين: $users_count</p>";
        
        echo "</div>";

        echo "<div class='alert alert-success'>";
        echo "<h3><i class='fas fa-check-circle'></i> 🎉 تم الإصلاح الشامل بنجاح!</h3>";
        echo "<div class='row'>";
        echo "<div class='col-md-6'>";
        echo "<h5>🔑 بيانات المرشح الرئيسي:</h5>";
        echo "<p><strong>اسم المستخدم:</strong> abd</p>";
        echo "<p><strong>كلمة المرور:</strong> 123456</p>";
        echo "<p><strong>رقم الهاتف:</strong> 07719992716</p>";
        echo "</div>";
        echo "<div class='col-md-6'>";
        echo "<h5>🔑 بيانات الإداري التجريبي:</h5>";
        echo "<p><strong>اسم المستخدم:</strong> admin1</p>";
        echo "<p><strong>كلمة المرور:</strong> admin123</p>";
        echo "<p><strong>رقم الهاتف:</strong> 07701234567</p>";
        echo "</div>";
        echo "</div>";
        echo "</div>";
        
    } catch (Exception $e) {
        echo "<div class='alert alert-danger'>";
        echo "<h5>❌ خطأ في الإصلاح:</h5>";
        echo "<p>" . htmlspecialchars($e->getMessage()) . "</p>";
        echo "</div>";
    }
    
    echo "<div class='text-center mt-4'>";
    echo "<a href='login.php' class='btn btn-primary btn-lg me-2'><i class='fas fa-sign-in-alt'></i> تسجيل الدخول</a>";
    echo "<a href='dashboard.php' class='btn btn-success btn-lg me-2'><i class='fas fa-tachometer-alt'></i> لوحة التحكم</a>";
    echo "<a href='search.php' class='btn btn-info btn-lg'><i class='fas fa-search'></i> البحث الشامل</a>";
    echo "</div>";
    
} else {
    // عرض نموذج التأكيد
    echo "<div class='alert alert-danger'>";
    echo "<h5><i class='fas fa-exclamation-triangle'></i> تحذير مهم!</h5>";
    echo "<p>هذا الإجراء سيقوم بـ:</p>";
    echo "<ul>";
    echo "<li>🗑️ <strong>حذف جميع البيانات الموجودة</strong></li>";
    echo "<li>🔧 إصلاح الترميز العربي نهائياً</li>";
    echo "<li>🗃️ إعادة إنشاء قاعدة البيانات من الصفر</li>";
    echo "<li>📊 إدراج 10 مناطق عراقية</li>";
    echo "<li>👤 إنشاء مستخدم افتراضي + إداري تجريبي</li>";
    echo "<li>📁 إنشاء جميع المجلدات المطلوبة</li>";
    echo "<li>✅ حل جميع مشاكل النظام</li>";
    echo "</ul>";
    echo "<p><strong>⚠️ تحذير:</strong> سيتم فقدان جميع البيانات الحالية!</p>";
    echo "</div>";
    
    echo "<form method='POST' action=''>";
    echo "<div class='text-center'>";
    echo "<button type='submit' name='final_fix' class='btn btn-danger btn-lg'>";
    echo "<i class='fas fa-magic'></i> تأكيد الإصلاح الشامل";
    echo "</button>";
    echo "</div>";
    echo "</form>";
    
    echo "<div class='text-center mt-4'>";
    echo "<a href='test.php' class='btn btn-info me-2'><i class='fas fa-cog'></i> اختبار النظام</a>";
    echo "<a href='login.php' class='btn btn-secondary'><i class='fas fa-sign-in-alt'></i> تسجيل الدخول</a>";
    echo "</div>";
}

echo "</div>";
echo "</body>";
echo "</html>";
?>
