<?php
// ملف إعادة تعيين قاعدة البيانات

require_once 'config/config.php';
require_once 'config/database.php';

echo "<!DOCTYPE html>";
echo "<html lang='ar' dir='rtl'>";
echo "<head>";
echo "<meta charset='UTF-8'>";
echo "<meta name='viewport' content='width=device-width, initial-scale=1.0'>";
echo "<title>إعادة تعيين قاعدة البيانات</title>";
echo "<link href='https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css' rel='stylesheet'>";
echo "<link href='https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css' rel='stylesheet'>";
echo "<style>";
echo "body { font-family: '<PERSON><PERSON><PERSON> U<PERSON>', Tahoma, Geneva, Verdana, sans-serif; background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); min-height: 100vh; padding: 2rem; }";
echo ".reset-card { background: white; border-radius: 15px; box-shadow: 0 10px 30px rgba(0,0,0,0.1); padding: 2rem; margin: 0 auto; max-width: 800px; }";
echo ".success { color: #28a745; }";
echo ".error { color: #dc3545; }";
echo "</style>";
echo "</head>";
echo "<body>";

echo "<div class='reset-card'>";
echo "<h1 class='text-center mb-4'><i class='fas fa-database'></i> إعادة تعيين قاعدة البيانات</h1>";

if (isset($_POST['confirm_reset'])) {
    try {
        echo "<div class='alert alert-warning'>";
        echo "<h5>جاري إعادة تعيين قاعدة البيانات...</h5>";
        
        // حذف الجداول الموجودة
        $tables = ['notifications', 'reports', 'competitors', 'events', 'expenses', 'supporters', 'users', 'regions', 'system_settings'];
        
        foreach ($tables as $table) {
            try {
                executeQuery("DROP TABLE IF EXISTS $table", []);
                echo "<p class='success'><i class='fas fa-check'></i> تم حذف جدول $table</p>";
            } catch (Exception $e) {
                echo "<p class='error'><i class='fas fa-times'></i> خطأ في حذف جدول $table: " . $e->getMessage() . "</p>";
            }
        }
        
        echo "</div>";
        
        echo "<div class='alert alert-info'>";
        echo "<h5>جاري إنشاء الجداول الجديدة...</h5>";
        
        // إنشاء جدول المناطق
        $sql = "CREATE TABLE regions (
            id INT AUTO_INCREMENT PRIMARY KEY,
            name VARCHAR(100) NOT NULL,
            description TEXT,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
        )";
        executeQuery($sql, []);
        echo "<p class='success'><i class='fas fa-check'></i> تم إنشاء جدول المناطق</p>";
        
        // إنشاء جدول المستخدمين
        $sql = "CREATE TABLE users (
            id INT AUTO_INCREMENT PRIMARY KEY,
            username VARCHAR(50) UNIQUE NOT NULL,
            password VARCHAR(255) NOT NULL,
            phone VARCHAR(15) NOT NULL,
            full_name VARCHAR(100) NOT NULL,
            user_type ENUM('candidate', 'admin') DEFAULT 'admin',
            permissions JSON,
            region_id INT,
            status ENUM('active', 'inactive') DEFAULT 'active',
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
        )";
        executeQuery($sql, []);
        echo "<p class='success'><i class='fas fa-check'></i> تم إنشاء جدول المستخدمين</p>";
        
        // إنشاء جدول المؤيدين
        $sql = "CREATE TABLE supporters (
            id INT AUTO_INCREMENT PRIMARY KEY,
            full_name VARCHAR(100) NOT NULL,
            gender ENUM('male', 'female') NOT NULL,
            marital_status ENUM('single', 'married', 'divorced', 'widowed') NOT NULL,
            birth_date DATE NOT NULL,
            education VARCHAR(50),
            profession VARCHAR(100),
            address TEXT NOT NULL,
            phone VARCHAR(15) NOT NULL,
            voter_number VARCHAR(50) UNIQUE,
            voting_center VARCHAR(100),
            region_id INT NOT NULL,
            photo VARCHAR(255),
            notes TEXT,
            added_by INT NOT NULL,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            FOREIGN KEY (region_id) REFERENCES regions(id) ON DELETE CASCADE,
            FOREIGN KEY (added_by) REFERENCES users(id) ON DELETE CASCADE
        )";
        executeQuery($sql, []);
        echo "<p class='success'><i class='fas fa-check'></i> تم إنشاء جدول المؤيدين</p>";
        
        // إنشاء جدول المصروفات
        $sql = "CREATE TABLE expenses (
            id INT AUTO_INCREMENT PRIMARY KEY,
            description TEXT NOT NULL,
            amount DECIMAL(10,2) NOT NULL,
            category VARCHAR(100) NOT NULL,
            expense_date DATE NOT NULL,
            region_id INT,
            attachment VARCHAR(255),
            notes TEXT,
            added_by INT NOT NULL,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            FOREIGN KEY (region_id) REFERENCES regions(id) ON DELETE SET NULL,
            FOREIGN KEY (added_by) REFERENCES users(id) ON DELETE CASCADE
        )";
        executeQuery($sql, []);
        echo "<p class='success'><i class='fas fa-check'></i> تم إنشاء جدول المصروفات</p>";
        
        echo "</div>";
        
        echo "<div class='alert alert-success'>";
        echo "<h5>جاري إدراج البيانات الأساسية...</h5>";
        
        // إدراج المناطق الافتراضية
        $regions = [
            ['المنطقة الأولى', 'وصف المنطقة الأولى'],
            ['المنطقة الثانية', 'وصف المنطقة الثانية'],
            ['المنطقة الثالثة', 'وصف المنطقة الثالثة']
        ];
        
        foreach ($regions as $region) {
            $sql = "INSERT INTO regions (name, description) VALUES (?, ?)";
            executeQuery($sql, $region);
        }
        echo "<p class='success'><i class='fas fa-check'></i> تم إدراج المناطق الافتراضية</p>";
        
        // إدراج المستخدم الافتراضي
        $password_hash = password_hash('123456', PASSWORD_DEFAULT);
        $sql = "INSERT INTO users (username, password, phone, full_name, user_type, status) 
                VALUES (?, ?, ?, ?, ?, ?)";
        
        executeQuery($sql, [
            'abd',
            $password_hash,
            '07719992716',
            'المرشح الرئيسي',
            'candidate',
            'active'
        ]);
        echo "<p class='success'><i class='fas fa-check'></i> تم إدراج المستخدم الافتراضي</p>";
        
        echo "</div>";
        
        echo "<div class='alert alert-success'>";
        echo "<h3><i class='fas fa-check-circle'></i> تم إعادة تعيين قاعدة البيانات بنجاح!</h3>";
        echo "<h5>بيانات تسجيل الدخول الجديدة:</h5>";
        echo "<p><strong>اسم المستخدم:</strong> abd</p>";
        echo "<p><strong>كلمة المرور:</strong> 123456</p>";
        echo "<p><strong>رقم الهاتف:</strong> 07719992716</p>";
        echo "</div>";
        
    } catch (Exception $e) {
        echo "<div class='alert alert-danger'>";
        echo "<h5>خطأ في إعادة تعيين قاعدة البيانات:</h5>";
        echo "<p>" . htmlspecialchars($e->getMessage()) . "</p>";
        echo "</div>";
    }
    
    echo "<div class='text-center mt-4'>";
    echo "<a href='login.php' class='btn btn-primary me-2'><i class='fas fa-sign-in-alt'></i> تسجيل الدخول</a>";
    echo "<a href='test.php' class='btn btn-info'><i class='fas fa-cog'></i> اختبار النظام</a>";
    echo "</div>";
    
} else {
    // عرض نموذج التأكيد
    echo "<div class='alert alert-danger'>";
    echo "<h5><i class='fas fa-exclamation-triangle'></i> تحذير!</h5>";
    echo "<p>هذا الإجراء سيحذف جميع البيانات الموجودة في قاعدة البيانات ويعيد إنشاءها من جديد.</p>";
    echo "<p><strong>لا يمكن التراجع عن هذا الإجراء!</strong></p>";
    echo "</div>";
    
    echo "<form method='POST' action=''>";
    echo "<div class='text-center'>";
    echo "<button type='submit' name='confirm_reset' class='btn btn-danger btn-lg'>";
    echo "<i class='fas fa-database'></i> تأكيد إعادة تعيين قاعدة البيانات";
    echo "</button>";
    echo "</div>";
    echo "</form>";
    
    echo "<div class='text-center mt-4'>";
    echo "<a href='fix_password.php' class='btn btn-warning me-2'><i class='fas fa-key'></i> إصلاح كلمة المرور فقط</a>";
    echo "<a href='test.php' class='btn btn-info'><i class='fas fa-cog'></i> اختبار النظام</a>";
    echo "</div>";
}

echo "</div>";
echo "</body>";
echo "</html>";
?>
