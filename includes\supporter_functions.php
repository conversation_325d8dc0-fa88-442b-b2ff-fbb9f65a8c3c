<?php
// دوال المؤيدين
require_once __DIR__ . '/../config/database.php';
require_once __DIR__ . '/notification_functions.php';

/**
 * إضافة مؤيد جديد
 */
function addSupporter($data, $admin_id) {
    $sql = "INSERT INTO supporters (
        full_name, gender, marital_status, birth_date, education, profession,
        address, phone, alternative_phone, email, voter_number, voting_center,
        region_id, district, neighborhood, family_members, support_level,
        contact_method, notes, tags, added_by
    ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)";
    
    $params = [
        $data['full_name'],
        $data['gender'],
        $data['marital_status'] ?? 'single',
        $data['birth_date'],
        $data['education'] ?? 'secondary',
        $data['profession'] ?? null,
        $data['address'],
        $data['phone'],
        $data['alternative_phone'] ?? null,
        $data['email'] ?? null,
        $data['voter_number'] ?? null,
        $data['voting_center'] ?? null,
        $data['region_id'] ?? null,
        $data['district'] ?? null,
        $data['neighborhood'] ?? null,
        $data['family_members'] ?? 1,
        $data['support_level'] ?? 'moderate',
        $data['contact_method'] ?? 'phone',
        $data['notes'] ?? null,
        $data['tags'] ?? null,
        $admin_id
    ];
    
    try {
        $result = executeQuery($sql, $params);
        $supporter_id = $GLOBALS['pdo']->lastInsertId();
        
        // تسجيل النشاط
        logActivity([
            'user_type' => 'admin',
            'user_id' => $admin_id,
            'action_type' => 'create',
            'target_type' => 'supporter',
            'target_id' => $supporter_id,
            'target_name' => $data['full_name'],
            'description' => 'تم إضافة مؤيد جديد: ' . $data['full_name']
        ]);
        
        // إنشاء إشعار
        notifyNewSupporter($supporter_id, $admin_id);
        
        // تحديث إحصائيات الإداري
        updateAdminDailyStats($admin_id, 'supporters_added', 1);
        
        return $supporter_id;
    } catch (Exception $e) {
        error_log("خطأ في إضافة المؤيد: " . $e->getMessage());
        return false;
    }
}

/**
 * تحديث بيانات مؤيد
 */
function updateSupporter($supporter_id, $data, $admin_id) {
    // جلب البيانات القديمة للمقارنة
    $old_data = fetchOne("SELECT * FROM supporters WHERE id = ?", [$supporter_id]);
    
    $sql = "UPDATE supporters SET 
        full_name = ?, gender = ?, marital_status = ?, birth_date = ?, 
        education = ?, profession = ?, address = ?, phone = ?, 
        alternative_phone = ?, email = ?, voter_number = ?, voting_center = ?,
        region_id = ?, district = ?, neighborhood = ?, family_members = ?, 
        support_level = ?, contact_method = ?, notes = ?, tags = ?,
        updated_at = NOW()
        WHERE id = ?";
    
    $params = [
        $data['full_name'],
        $data['gender'],
        $data['marital_status'],
        $data['birth_date'],
        $data['education'],
        $data['profession'],
        $data['address'],
        $data['phone'],
        $data['alternative_phone'],
        $data['email'],
        $data['voter_number'],
        $data['voting_center'],
        $data['region_id'],
        $data['district'],
        $data['neighborhood'],
        $data['family_members'],
        $data['support_level'],
        $data['contact_method'],
        $data['notes'],
        $data['tags'],
        $supporter_id
    ];
    
    try {
        $result = executeQuery($sql, $params);
        
        // تسجيل النشاط
        logActivity([
            'user_type' => 'admin',
            'user_id' => $admin_id,
            'action_type' => 'update',
            'target_type' => 'supporter',
            'target_id' => $supporter_id,
            'target_name' => $data['full_name'],
            'description' => 'تم تحديث بيانات المؤيد: ' . $data['full_name'],
            'old_values' => $old_data,
            'new_values' => $data
        ]);
        
        return true;
    } catch (Exception $e) {
        error_log("خطأ في تحديث المؤيد: " . $e->getMessage());
        return false;
    }
}

/**
 * حذف مؤيد
 */
function deleteSupporter($supporter_id, $admin_id) {
    $supporter = fetchOne("SELECT full_name FROM supporters WHERE id = ?", [$supporter_id]);
    
    if (!$supporter) {
        return false;
    }
    
    try {
        $result = executeQuery("DELETE FROM supporters WHERE id = ?", [$supporter_id]);
        
        // تسجيل النشاط
        logActivity([
            'user_type' => 'admin',
            'user_id' => $admin_id,
            'action_type' => 'delete',
            'target_type' => 'supporter',
            'target_id' => $supporter_id,
            'target_name' => $supporter['full_name'],
            'description' => 'تم حذف المؤيد: ' . $supporter['full_name']
        ]);
        
        return true;
    } catch (Exception $e) {
        error_log("خطأ في حذف المؤيد: " . $e->getMessage());
        return false;
    }
}

/**
 * البحث في المؤيدين
 */
function searchSupporters($search_term, $filters = [], $limit = 50, $offset = 0) {
    $sql = "SELECT s.*, r.name as region_name, a.full_name as added_by_name 
            FROM supporters s 
            LEFT JOIN regions r ON s.region_id = r.id 
            LEFT JOIN admins a ON s.added_by = a.id 
            WHERE 1=1";
    $params = [];
    
    // البحث النصي
    if (!empty($search_term)) {
        $sql .= " AND (MATCH(s.full_name, s.address, s.profession, s.notes) AGAINST(? IN NATURAL LANGUAGE MODE) 
                  OR s.phone LIKE ? OR s.voter_number LIKE ?)";
        $params[] = $search_term;
        $params[] = "%$search_term%";
        $params[] = "%$search_term%";
    }
    
    // فلاتر إضافية
    if (!empty($filters['region_id'])) {
        $sql .= " AND s.region_id = ?";
        $params[] = $filters['region_id'];
    }
    
    if (!empty($filters['gender'])) {
        $sql .= " AND s.gender = ?";
        $params[] = $filters['gender'];
    }
    
    if (!empty($filters['support_level'])) {
        $sql .= " AND s.support_level = ?";
        $params[] = $filters['support_level'];
    }
    
    if (!empty($filters['added_by'])) {
        $sql .= " AND s.added_by = ?";
        $params[] = $filters['added_by'];
    }
    
    if (!empty($filters['status'])) {
        $sql .= " AND s.status = ?";
        $params[] = $filters['status'];
    }
    
    // ترتيب وحد
    $sql .= " ORDER BY s.created_at DESC LIMIT ? OFFSET ?";
    $params[] = $limit;
    $params[] = $offset;
    
    return fetchAll($sql, $params);
}

/**
 * عدد المؤيدين حسب الفلاتر
 */
function countSupporters($search_term = '', $filters = []) {
    $sql = "SELECT COUNT(*) as count FROM supporters s WHERE 1=1";
    $params = [];
    
    // البحث النصي
    if (!empty($search_term)) {
        $sql .= " AND (MATCH(s.full_name, s.address, s.profession, s.notes) AGAINST(? IN NATURAL LANGUAGE MODE) 
                  OR s.phone LIKE ? OR s.voter_number LIKE ?)";
        $params[] = $search_term;
        $params[] = "%$search_term%";
        $params[] = "%$search_term%";
    }
    
    // فلاتر إضافية
    if (!empty($filters['region_id'])) {
        $sql .= " AND s.region_id = ?";
        $params[] = $filters['region_id'];
    }
    
    if (!empty($filters['gender'])) {
        $sql .= " AND s.gender = ?";
        $params[] = $filters['gender'];
    }
    
    if (!empty($filters['support_level'])) {
        $sql .= " AND s.support_level = ?";
        $params[] = $filters['support_level'];
    }
    
    if (!empty($filters['added_by'])) {
        $sql .= " AND s.added_by = ?";
        $params[] = $filters['added_by'];
    }
    
    if (!empty($filters['status'])) {
        $sql .= " AND s.status = ?";
        $params[] = $filters['status'];
    }
    
    $result = fetchOne($sql, $params);
    return $result['count'] ?? 0;
}

/**
 * إضافة مرفق لمؤيد
 */
function addSupporterAttachment($supporter_id, $file_data, $admin_id) {
    $sql = "INSERT INTO supporter_attachments (
        supporter_id, attachment_type, file_name, original_name, file_path,
        file_size, file_type, mime_type, uploaded_by, notes
    ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)";
    
    $params = [
        $supporter_id,
        $file_data['attachment_type'],
        $file_data['file_name'],
        $file_data['original_name'],
        $file_data['file_path'],
        $file_data['file_size'],
        $file_data['file_type'],
        $file_data['mime_type'],
        $admin_id,
        $file_data['notes'] ?? null
    ];
    
    try {
        $result = executeQuery($sql, $params);
        $attachment_id = $GLOBALS['pdo']->lastInsertId();
        
        // تسجيل النشاط
        logActivity([
            'user_type' => 'admin',
            'user_id' => $admin_id,
            'action_type' => 'create',
            'target_type' => 'file',
            'target_id' => $attachment_id,
            'description' => 'تم رفع مرفق للمؤيد: ' . $file_data['original_name']
        ]);
        
        return $attachment_id;
    } catch (Exception $e) {
        error_log("خطأ في إضافة المرفق: " . $e->getMessage());
        return false;
    }
}

/**
 * جلب مرفقات المؤيد
 */
function getSupporterAttachments($supporter_id) {
    return fetchAll("SELECT * FROM supporter_attachments WHERE supporter_id = ? ORDER BY upload_date DESC", [$supporter_id]);
}

/**
 * تسجيل اتصال مع مؤيد
 */
function logSupporterContact($supporter_id, $admin_id, $contact_data) {
    $sql = "INSERT INTO supporter_contacts (
        supporter_id, admin_id, contact_type, contact_date, duration_minutes,
        result, notes, follow_up_required, follow_up_date
    ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)";
    
    $params = [
        $supporter_id,
        $admin_id,
        $contact_data['contact_type'],
        $contact_data['contact_date'],
        $contact_data['duration_minutes'] ?? 0,
        $contact_data['result'] ?? 'successful',
        $contact_data['notes'] ?? null,
        $contact_data['follow_up_required'] ?? false,
        $contact_data['follow_up_date'] ?? null
    ];
    
    try {
        $result = executeQuery($sql, $params);
        
        // تحديث تاريخ آخر اتصال
        executeQuery("UPDATE supporters SET last_contact_date = ? WHERE id = ?", 
                    [$contact_data['contact_date'], $supporter_id]);
        
        // تحديث إحصائيات الإداري
        updateAdminDailyStats($admin_id, 'supporters_contacted', 1);
        
        return $GLOBALS['pdo']->lastInsertId();
    } catch (Exception $e) {
        error_log("خطأ في تسجيل الاتصال: " . $e->getMessage());
        return false;
    }
}

/**
 * جلب تاريخ الاتصالات مع المؤيد
 */
function getSupporterContacts($supporter_id, $limit = 20) {
    return fetchAll("
        SELECT sc.*, a.full_name as admin_name 
        FROM supporter_contacts sc 
        JOIN admins a ON sc.admin_id = a.id 
        WHERE sc.supporter_id = ? 
        ORDER BY sc.contact_date DESC 
        LIMIT ?
    ", [$supporter_id, $limit]);
}

/**
 * إضافة عضو عائلة للمؤيد
 */
function addFamilyMember($supporter_id, $family_data) {
    $sql = "INSERT INTO supporter_families (
        supporter_id, family_member_name, relationship, age, gender,
        is_voter, voter_number, support_level, notes
    ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)";
    
    $params = [
        $supporter_id,
        $family_data['family_member_name'],
        $family_data['relationship'],
        $family_data['age'] ?? null,
        $family_data['gender'] ?? null,
        $family_data['is_voter'] ?? false,
        $family_data['voter_number'] ?? null,
        $family_data['support_level'] ?? 'undecided',
        $family_data['notes'] ?? null
    ];
    
    try {
        return executeQuery($sql, $params);
    } catch (Exception $e) {
        error_log("خطأ في إضافة عضو العائلة: " . $e->getMessage());
        return false;
    }
}

/**
 * جلب أعضاء عائلة المؤيد
 */
function getSupporterFamily($supporter_id) {
    return fetchAll("SELECT * FROM supporter_families WHERE supporter_id = ? ORDER BY relationship", [$supporter_id]);
}

/**
 * تحديث الإحصائيات اليومية للإداري
 */
function updateAdminDailyStats($admin_id, $field, $increment = 1) {
    $today = date('Y-m-d');
    
    // التحقق من وجود سجل لليوم
    $existing = fetchOne("SELECT id FROM admin_daily_stats WHERE admin_id = ? AND date = ?", [$admin_id, $today]);
    
    if ($existing) {
        // تحديث السجل الموجود
        $sql = "UPDATE admin_daily_stats SET $field = $field + ? WHERE admin_id = ? AND date = ?";
        executeQuery($sql, [$increment, $admin_id, $today]);
    } else {
        // إنشاء سجل جديد
        $sql = "INSERT INTO admin_daily_stats (admin_id, date, $field) VALUES (?, ?, ?)";
        executeQuery($sql, [$admin_id, $today, $increment]);
    }
}

/**
 * إحصائيات المؤيدين
 */
function getSupportersStatistics($filters = []) {
    $sql = "SELECT 
        COUNT(*) as total,
        SUM(CASE WHEN gender = 'male' THEN 1 ELSE 0 END) as male_count,
        SUM(CASE WHEN gender = 'female' THEN 1 ELSE 0 END) as female_count,
        SUM(CASE WHEN support_level = 'strong' THEN 1 ELSE 0 END) as strong_support,
        SUM(CASE WHEN support_level = 'moderate' THEN 1 ELSE 0 END) as moderate_support,
        SUM(CASE WHEN support_level = 'weak' THEN 1 ELSE 0 END) as weak_support,
        AVG(family_members) as avg_family_size,
        COUNT(DISTINCT region_id) as regions_covered
    FROM supporters WHERE status = 'active'";
    
    $params = [];
    
    if (!empty($filters['region_id'])) {
        $sql .= " AND region_id = ?";
        $params[] = $filters['region_id'];
    }
    
    if (!empty($filters['added_by'])) {
        $sql .= " AND added_by = ?";
        $params[] = $filters['added_by'];
    }
    
    return fetchOne($sql, $params);
}
?>
