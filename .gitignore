# نظام إدارة الحملة الانتخابية - ملفات مستبعدة من Git

# ملفات الإعدادات الحساسة
config/local_config.php
config/production_config.php
.env
.env.local
.env.production

# ملفات المستخدمين المرفوعة
uploads/*
!uploads/.gitkeep
!uploads/index.php

# ملفات التصدير
exports/*
!exports/.gitkeep
!exports/index.php

# ملفات النسخ الاحتياطي
backups/*
!backups/.gitkeep

# ملفات السجلات
logs/*
!logs/.gitkeep
*.log

# ملفات مؤقتة
tmp/*
temp/*
cache/*
!tmp/.gitkeep
!temp/.gitkeep
!cache/.gitkeep

# مجلدات المكتبات
vendor/
node_modules/

# ملفات Composer
composer.lock
composer.phar

# ملفات NPM
package-lock.json
yarn.lock

# ملفات IDE
.vscode/
.idea/
*.swp
*.swo
*~

# ملفات نظام التشغيل
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# ملفات PHP
*.php~
*.php.bak
*.php.orig

# ملفات قواعد البيانات المؤقتة
*.sql.bak
*.sql.tmp
*.db
*.sqlite

# ملفات الجلسات
sessions/*
!sessions/.gitkeep

# ملفات التخزين المؤقت
storage/cache/*
storage/logs/*
storage/sessions/*
!storage/cache/.gitkeep
!storage/logs/.gitkeep
!storage/sessions/.gitkeep

# ملفات الاختبار
tests/coverage/
phpunit.xml
.phpunit.result.cache

# ملفات التطوير
.sass-cache/
*.css.map
*.js.map

# ملفات الأمان
.htpasswd
ssl/
certificates/

# ملفات التكوين المحلية
local_settings.php
development.php
staging.php

# ملفات الإحصائيات
analytics/
stats/

# ملفات التحديث
updates/
patches/

# ملفات الصيانة
maintenance.php
maintenance.html

# ملفات خاصة بالمشروع
install_complete.flag
setup_done.txt

# استثناءات مهمة
!.gitkeep
!.htaccess
!index.php
!README.md
