<?php
require_once '../../config/config.php';
require_once '../../config/database.php';

// التحقق من تسجيل الدخول
if (!isLoggedIn()) {
    redirect('../../login.php');
}

// التحقق من الصلاحيات - الإداريين للمرشح فقط
if (!isCandidate()) {
    showMessage('ليس لديك صلاحية للوصول إلى هذه الصفحة', 'error');
    redirect('../../dashboard.php');
}

// معالجة الطلبات
if ($_SERVER['REQUEST_METHOD'] == 'POST') {
    if (isset($_POST['action'])) {
        switch ($_POST['action']) {
            case 'add':
                addAdmin();
                break;
            case 'edit':
                editAdmin();
                break;
            case 'delete':
                deleteAdmin();
                break;
            case 'toggle_status':
                toggleAdminStatus();
                break;
        }
    }
}

// جلب الإداريين مع بيانات المناطق
$sql = "SELECT u.*, r.name as region_name,
               COUNT(s.id) as supporters_count
        FROM users u 
        LEFT JOIN regions r ON u.region_id = r.id 
        LEFT JOIN supporters s ON u.region_id = s.region_id
        WHERE u.user_type = 'admin'
        GROUP BY u.id 
        ORDER BY u.created_at DESC";

$admins = fetchAll($sql);

// جلب المناطق للتصفية
$regions = fetchAll("SELECT * FROM regions ORDER BY name");

// إحصائيات
$total_admins = count($admins);
$active_admins = count(array_filter($admins, function($admin) { return $admin['is_active']; }));
$inactive_admins = $total_admins - $active_admins;

function addAdmin() {
    $data = [
        'username' => sanitize($_POST['username']),
        'password' => password_hash(sanitize($_POST['password']), PASSWORD_DEFAULT),
        'phone' => sanitize($_POST['phone']),
        'full_name' => sanitize($_POST['full_name']),
        'region_id' => !empty($_POST['region_id']) ? (int)$_POST['region_id'] : null,
        'user_type' => 'admin',
        'is_active' => 1
    ];
    
    // التحقق من صحة البيانات
    if (empty($data['username']) || empty($data['phone']) || empty($data['full_name'])) {
        showMessage('يرجى ملء جميع الحقول المطلوبة', 'error');
        return;
    }
    
    // التحقق من رقم الهاتف العراقي
    if (!validateIraqiPhone($data['phone'])) {
        showMessage('رقم الهاتف غير صحيح', 'error');
        return;
    }
    
    // التحقق من تكرار اسم المستخدم
    $existing = fetchOne("SELECT id FROM users WHERE username = ?", [$data['username']]);
    if ($existing) {
        showMessage('اسم المستخدم موجود مسبقاً', 'error');
        return;
    }
    
    // التحقق من تكرار رقم الهاتف
    $existing = fetchOne("SELECT id FROM users WHERE phone = ?", [$data['phone']]);
    if ($existing) {
        showMessage('رقم الهاتف موجود مسبقاً', 'error');
        return;
    }
    
    $sql = "INSERT INTO users (username, password, phone, full_name, region_id, user_type, is_active) 
            VALUES (?, ?, ?, ?, ?, ?, ?)";
    
    $result = executeQuery($sql, array_values($data));
    
    if ($result) {
        showMessage('تم إضافة الإداري بنجاح', 'success');
        redirect('admins.php');
    } else {
        showMessage('حدث خطأ أثناء إضافة الإداري', 'error');
    }
}

function editAdmin() {
    $id = (int)$_POST['id'];
    $data = [
        'username' => sanitize($_POST['username']),
        'phone' => sanitize($_POST['phone']),
        'full_name' => sanitize($_POST['full_name']),
        'region_id' => !empty($_POST['region_id']) ? (int)$_POST['region_id'] : null
    ];
    
    // التحقق من صحة البيانات
    if (empty($data['username']) || empty($data['phone']) || empty($data['full_name'])) {
        showMessage('يرجى ملء جميع الحقول المطلوبة', 'error');
        return;
    }
    
    // التحقق من رقم الهاتف العراقي
    if (!validateIraqiPhone($data['phone'])) {
        showMessage('رقم الهاتف غير صحيح', 'error');
        return;
    }
    
    // التحقق من تكرار اسم المستخدم
    $existing = fetchOne("SELECT id FROM users WHERE username = ? AND id != ?", [$data['username'], $id]);
    if ($existing) {
        showMessage('اسم المستخدم موجود مسبقاً', 'error');
        return;
    }
    
    // التحقق من تكرار رقم الهاتف
    $existing = fetchOne("SELECT id FROM users WHERE phone = ? AND id != ?", [$data['phone'], $id]);
    if ($existing) {
        showMessage('رقم الهاتف موجود مسبقاً', 'error');
        return;
    }
    
    // تحديث كلمة المرور إذا تم إدخالها
    if (!empty($_POST['password'])) {
        $data['password'] = password_hash(sanitize($_POST['password']), PASSWORD_DEFAULT);
    }
    
    $set_clause = implode(', ', array_map(function($key) { return "$key = ?"; }, array_keys($data)));
    $sql = "UPDATE users SET $set_clause WHERE id = ?";
    
    $params = array_values($data);
    $params[] = $id;
    
    $result = executeQuery($sql, $params);
    
    if ($result) {
        showMessage('تم تحديث بيانات الإداري بنجاح', 'success');
        redirect('admins.php');
    } else {
        showMessage('حدث خطأ أثناء تحديث بيانات الإداري', 'error');
    }
}

function deleteAdmin() {
    $id = (int)$_POST['id'];
    
    // التحقق من عدم حذف المرشح الرئيسي
    $admin = fetchOne("SELECT user_type FROM users WHERE id = ?", [$id]);
    if ($admin && $admin['user_type'] == 'candidate') {
        showMessage('لا يمكن حذف المرشح الرئيسي', 'error');
        return;
    }
    
    $result = executeQuery("DELETE FROM users WHERE id = ? AND user_type = 'admin'", [$id]);
    
    if ($result) {
        showMessage('تم حذف الإداري بنجاح', 'success');
    } else {
        showMessage('حدث خطأ أثناء حذف الإداري', 'error');
    }
}

function toggleAdminStatus() {
    $id = (int)$_POST['id'];
    $status = (int)$_POST['status'];
    
    $result = executeQuery("UPDATE users SET is_active = ? WHERE id = ? AND user_type = 'admin'", [$status, $id]);
    
    if ($result) {
        $message = $status ? 'تم تفعيل الإداري بنجاح' : 'تم إلغاء تفعيل الإداري بنجاح';
        showMessage($message, 'success');
    } else {
        showMessage('حدث خطأ أثناء تغيير حالة الإداري', 'error');
    }
}
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إدارة الإداريين - <?php echo SITE_NAME; ?></title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="https://cdn.datatables.net/1.13.6/css/dataTables.bootstrap5.min.css" rel="stylesheet">
    <link href="../../assets/css/dashboard.css" rel="stylesheet">
    <link href="../../assets/css/admins.css" rel="stylesheet">
</head>
<body>
    <!-- شريط التنقل العلوي -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary fixed-top">
        <div class="container-fluid">
            <a class="navbar-brand" href="../../dashboard.php">
                <i class="fas fa-vote-yea me-2"></i>
                نظام إدارة الحملة الانتخابية
            </a>
            
            <div class="navbar-nav ms-auto">
                <div class="nav-item dropdown">
                    <a class="nav-link dropdown-toggle" href="#" id="userDropdown" role="button" data-bs-toggle="dropdown">
                        <i class="fas fa-user-circle me-1"></i>
                        <?php echo htmlspecialchars($_SESSION['full_name']); ?>
                    </a>
                    <ul class="dropdown-menu dropdown-menu-end">
                        <li><a class="dropdown-item" href="../../logout.php"><i class="fas fa-sign-out-alt me-2"></i>تسجيل الخروج</a></li>
                    </ul>
                </div>
            </div>
        </div>
    </nav>

    <!-- المحتوى الرئيسي -->
    <div class="container-fluid main-content">
        <div class="row">
            <!-- الشريط الجانبي -->
            <nav class="col-md-3 col-lg-2 d-md-block sidebar">
                <div class="position-sticky pt-3">
                    <ul class="nav flex-column">
                        <li class="nav-item">
                            <a class="nav-link" href="../../dashboard.php">
                                <i class="fas fa-tachometer-alt me-2"></i>
                                لوحة التحكم
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="../supporters/supporters.php">
                                <i class="fas fa-users me-2"></i>
                                المؤيدين
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="../regions/regions.php">
                                <i class="fas fa-map-marked-alt me-2"></i>
                                المناطق
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link active" href="admins.php">
                                <i class="fas fa-user-tie me-2"></i>
                                الإداريين
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="../expenses/expenses.php">
                                <i class="fas fa-money-bill-wave me-2"></i>
                                المصروفات
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="../events/events.php">
                                <i class="fas fa-calendar-alt me-2"></i>
                                الفعاليات
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="../competitors/competitors.php">
                                <i class="fas fa-chess me-2"></i>
                                المنافسين
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="../reports/reports.php">
                                <i class="fas fa-chart-bar me-2"></i>
                                التقارير
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="../settings/settings.php">
                                <i class="fas fa-cog me-2"></i>
                                الإعدادات
                            </a>
                        </li>
                    </ul>
                </div>
            </nav>

            <!-- المحتوى الرئيسي -->
            <main class="col-md-9 ms-sm-auto col-lg-10 px-md-4">
                <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                    <h1 class="h2">
                        <i class="fas fa-user-tie me-2"></i>
                        إدارة الإداريين
                    </h1>
                    <div class="btn-toolbar mb-2 mb-md-0">
                        <div class="btn-group me-2">
                            <button type="button" class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#addAdminModal">
                                <i class="fas fa-plus"></i> إضافة إداري
                            </button>
                            <button type="button" class="btn btn-success" onclick="exportAdmins()">
                                <i class="fas fa-file-excel"></i> تصدير Excel
                            </button>
                            <button type="button" class="btn btn-info" onclick="sendBulkMessage()">
                                <i class="fas fa-envelope"></i> رسالة جماعية
                            </button>
                        </div>
                    </div>
                </div>

                <?php displayMessage(); ?>

                <!-- الإحصائيات -->
                <div class="row mb-4">
                    <div class="col-md-4">
                        <div class="card bg-primary text-white">
                            <div class="card-body">
                                <div class="d-flex justify-content-between">
                                    <div>
                                        <h4><?php echo number_format($total_admins); ?></h4>
                                        <p class="mb-0">إجمالي الإداريين</p>
                                    </div>
                                    <div class="align-self-center">
                                        <i class="fas fa-user-tie fa-2x"></i>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="card bg-success text-white">
                            <div class="card-body">
                                <div class="d-flex justify-content-between">
                                    <div>
                                        <h4><?php echo number_format($active_admins); ?></h4>
                                        <p class="mb-0">الإداريين النشطين</p>
                                    </div>
                                    <div class="align-self-center">
                                        <i class="fas fa-user-check fa-2x"></i>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="card bg-warning text-white">
                            <div class="card-body">
                                <div class="d-flex justify-content-between">
                                    <div>
                                        <h4><?php echo number_format($inactive_admins); ?></h4>
                                        <p class="mb-0">الإداريين غير النشطين</p>
                                    </div>
                                    <div class="align-self-center">
                                        <i class="fas fa-user-times fa-2x"></i>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- جدول الإداريين -->
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="fas fa-list me-2"></i>
                            قائمة الإداريين (<?php echo number_format($total_admins); ?>)
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="table-responsive">
                            <table class="table table-striped table-hover" id="adminsTable">
                                <thead class="table-dark">
                                    <tr>
                                        <th>الاسم الكامل</th>
                                        <th>اسم المستخدم</th>
                                        <th>رقم الهاتف</th>
                                        <th>المنطقة</th>
                                        <th>عدد المؤيدين</th>
                                        <th>الحالة</th>
                                        <th>تاريخ الإضافة</th>
                                        <th>الإجراءات</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php foreach ($admins as $admin): ?>
                                    <tr>
                                        <td><strong><?php echo htmlspecialchars($admin['full_name']); ?></strong></td>
                                        <td><?php echo htmlspecialchars($admin['username']); ?></td>
                                        <td>
                                            <a href="tel:<?php echo $admin['phone']; ?>" class="text-decoration-none">
                                                <?php echo htmlspecialchars($admin['phone']); ?>
                                            </a>
                                        </td>
                                        <td><?php echo htmlspecialchars($admin['region_name']) ?: 'جميع المناطق'; ?></td>
                                        <td>
                                            <span class="badge bg-info"><?php echo $admin['supporters_count']; ?></span>
                                        </td>
                                        <td>
                                            <?php if ($admin['is_active']): ?>
                                                <span class="badge bg-success">نشط</span>
                                            <?php else: ?>
                                                <span class="badge bg-danger">غير نشط</span>
                                            <?php endif; ?>
                                        </td>
                                        <td><?php echo formatArabicDate($admin['created_at']); ?></td>
                                        <td>
                                            <div class="btn-group" role="group">
                                                <button type="button" class="btn btn-sm btn-info" 
                                                        onclick="viewAdmin(<?php echo $admin['id']; ?>)" 
                                                        title="عرض">
                                                    <i class="fas fa-eye"></i>
                                                </button>
                                                <button type="button" class="btn btn-sm btn-warning" 
                                                        onclick="editAdmin(<?php echo $admin['id']; ?>)" 
                                                        title="تعديل">
                                                    <i class="fas fa-edit"></i>
                                                </button>
                                                <button type="button" class="btn btn-sm <?php echo $admin['is_active'] ? 'btn-secondary' : 'btn-success'; ?>" 
                                                        onclick="toggleAdminStatus(<?php echo $admin['id']; ?>, <?php echo $admin['is_active'] ? 0 : 1; ?>)" 
                                                        title="<?php echo $admin['is_active'] ? 'إلغاء التفعيل' : 'تفعيل'; ?>">
                                                    <i class="fas fa-<?php echo $admin['is_active'] ? 'user-times' : 'user-check'; ?>"></i>
                                                </button>
                                                <button type="button" class="btn btn-sm btn-danger" 
                                                        onclick="deleteAdmin(<?php echo $admin['id']; ?>)" 
                                                        title="حذف">
                                                    <i class="fas fa-trash"></i>
                                                </button>
                                            </div>
                                        </td>
                                    </tr>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </main>
        </div>
    </div>

    <!-- النوافذ المنبثقة -->
    <?php include 'modals.php'; ?>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://code.jquery.com/jquery-3.7.0.min.js"></script>
    <script src="https://cdn.datatables.net/1.13.6/js/jquery.dataTables.min.js"></script>
    <script src="https://cdn.datatables.net/1.13.6/js/dataTables.bootstrap5.min.js"></script>
    <script src="../../assets/js/admins.js"></script>
</body>
</html>
