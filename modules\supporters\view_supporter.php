<?php
// عرض تفاصيل المؤيد مع المرفقات
require_once '../../config/config.php';
require_once '../../config/database.php';

// التحقق من تسجيل الدخول
if (!isLoggedIn()) {
    redirect('../../login.php');
}

// جلب معرف المؤيد
$supporter_id = isset($_GET['id']) ? (int)$_GET['id'] : 0;

if (!$supporter_id) {
    redirect('supporters.php');
}

// جلب بيانات المؤيد
$supporter = fetchOne("
    SELECT s.*, r.name as region_name, a.full_name as admin_name 
    FROM supporters s 
    LEFT JOIN regions r ON s.region_id = r.id 
    LEFT JOIN admins a ON s.added_by = a.id 
    WHERE s.id = ?
", [$supporter_id]);

if (!$supporter) {
    redirect('supporters.php');
}

// جلب المرفقات
$attachments = fetchAll("SELECT * FROM supporter_attachments WHERE supporter_id = ? ORDER BY attachment_type, upload_date", [$supporter_id]);

// تجميع المرفقات حسب النوع
$grouped_attachments = [];
foreach ($attachments as $attachment) {
    $grouped_attachments[$attachment['attachment_type']][] = $attachment;
}

// أسماء أنواع المرفقات
$attachment_types = [
    'voter_id_front' => 'هوية الناخب (الوجه)',
    'voter_id_back' => 'هوية الناخب (الظهر)',
    'national_id_front' => 'البطاقة الوطنية (الوجه)',
    'national_id_back' => 'البطاقة الوطنية (الظهر)',
    'residence_card' => 'بطاقة السكن',
    'other' => 'مرفقات أخرى'
];
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>تفاصيل المؤيد - <?php echo htmlspecialchars($supporter['full_name']); ?></title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: #f8f9fa;
        }
        .main-content {
            margin-top: 80px;
            padding: 15px;
        }
        .supporter-card {
            border-radius: 15px;
            border: none;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        .attachment-card {
            border-radius: 10px;
            border: 1px solid #dee2e6;
            margin-bottom: 15px;
            transition: transform 0.2s;
        }
        .attachment-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(0,0,0,0.15);
        }
        .attachment-image {
            max-width: 100%;
            max-height: 200px;
            border-radius: 8px;
            cursor: pointer;
        }
        .attachment-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 15px;
            margin-top: 20px;
        }
        .info-section {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 20px;
            margin-bottom: 20px;
        }
        
        /* تصميم متجاوب */
        @media (max-width: 768px) {
            .main-content {
                margin-top: 70px;
                padding: 10px;
            }
            .attachment-grid {
                grid-template-columns: 1fr;
                gap: 10px;
            }
            .supporter-card {
                margin: 0 0 15px 0;
                border-radius: 10px;
            }
        }
        
        @media (min-width: 769px) and (max-width: 1024px) {
            .attachment-grid {
                grid-template-columns: repeat(2, 1fr);
            }
        }
    </style>
</head>
<body>
    <!-- شريط التنقل العلوي -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary fixed-top">
        <div class="container-fluid">
            <a class="navbar-brand" href="../../dashboard.php">
                <i class="fas fa-vote-yea me-2"></i>
                نظام إدارة الحملة الانتخابية
            </a>
            
            <div class="navbar-nav ms-auto">
                <div class="nav-item dropdown">
                    <a class="nav-link dropdown-toggle" href="#" id="userDropdown" role="button" data-bs-toggle="dropdown">
                        <i class="fas fa-user-circle me-1"></i>
                        <?php echo htmlspecialchars($_SESSION['full_name']); ?>
                    </a>
                    <ul class="dropdown-menu dropdown-menu-end">
                        <li><a class="dropdown-item" href="../../logout.php"><i class="fas fa-sign-out-alt me-2"></i>تسجيل الخروج</a></li>
                    </ul>
                </div>
            </div>
        </div>
    </nav>

    <!-- المحتوى الرئيسي -->
    <div class="container-fluid main-content">
        <div class="row">
            <div class="col-12">
                <!-- العنوان والأزرار -->
                <div class="d-flex justify-content-between align-items-center mb-4">
                    <h2>
                        <i class="fas fa-user me-2"></i>
                        تفاصيل المؤيد: <?php echo htmlspecialchars($supporter['full_name']); ?>
                    </h2>
                    <div>
                        <a href="supporters.php" class="btn btn-secondary">
                            <i class="fas fa-arrow-left me-2"></i>العودة للقائمة
                        </a>
                        <button type="button" class="btn btn-primary" onclick="window.print()">
                            <i class="fas fa-print me-2"></i>طباعة
                        </button>
                    </div>
                </div>

                <!-- بيانات المؤيد الأساسية -->
                <div class="card supporter-card">
                    <div class="card-header bg-primary text-white">
                        <h5 class="mb-0">
                            <i class="fas fa-info-circle me-2"></i>
                            البيانات الأساسية
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6">
                                <div class="info-section">
                                    <h6 class="text-primary mb-3">المعلومات الشخصية</h6>
                                    <table class="table table-borderless">
                                        <tr>
                                            <td><strong>الاسم الكامل:</strong></td>
                                            <td><?php echo htmlspecialchars($supporter['full_name']); ?></td>
                                        </tr>
                                        <tr>
                                            <td><strong>الجنس:</strong></td>
                                            <td><?php echo $supporter['gender'] == 'male' ? 'ذكر' : 'أنثى'; ?></td>
                                        </tr>
                                        <tr>
                                            <td><strong>الحالة الاجتماعية:</strong></td>
                                            <td>
                                                <?php 
                                                $marital_status = [
                                                    'single' => 'أعزب',
                                                    'married' => 'متزوج',
                                                    'divorced' => 'مطلق',
                                                    'widowed' => 'أرمل'
                                                ];
                                                echo $marital_status[$supporter['marital_status']] ?? $supporter['marital_status'];
                                                ?>
                                            </td>
                                        </tr>
                                        <tr>
                                            <td><strong>تاريخ الميلاد:</strong></td>
                                            <td><?php echo date('Y-m-d', strtotime($supporter['birth_date'])); ?></td>
                                        </tr>
                                        <tr>
                                            <td><strong>المؤهل العلمي:</strong></td>
                                            <td><?php echo htmlspecialchars($supporter['education'] ?: 'غير محدد'); ?></td>
                                        </tr>
                                        <tr>
                                            <td><strong>المهنة:</strong></td>
                                            <td><?php echo htmlspecialchars($supporter['profession'] ?: 'غير محدد'); ?></td>
                                        </tr>
                                    </table>
                                </div>
                            </div>
                            
                            <div class="col-md-6">
                                <div class="info-section">
                                    <h6 class="text-success mb-3">معلومات الاتصال والموقع</h6>
                                    <table class="table table-borderless">
                                        <tr>
                                            <td><strong>رقم الهاتف:</strong></td>
                                            <td>
                                                <a href="tel:<?php echo $supporter['phone']; ?>" class="text-decoration-none">
                                                    <i class="fas fa-phone me-1"></i>
                                                    <?php echo htmlspecialchars($supporter['phone']); ?>
                                                </a>
                                            </td>
                                        </tr>
                                        <tr>
                                            <td><strong>العنوان:</strong></td>
                                            <td><?php echo htmlspecialchars($supporter['address']); ?></td>
                                        </tr>
                                        <tr>
                                            <td><strong>المنطقة:</strong></td>
                                            <td><?php echo htmlspecialchars($supporter['region_name'] ?: 'غير محدد'); ?></td>
                                        </tr>
                                        <tr>
                                            <td><strong>رقم الناخب:</strong></td>
                                            <td><?php echo htmlspecialchars($supporter['voter_number'] ?: 'غير محدد'); ?></td>
                                        </tr>
                                        <tr>
                                            <td><strong>مركز الاقتراع:</strong></td>
                                            <td><?php echo htmlspecialchars($supporter['voting_center'] ?: 'غير محدد'); ?></td>
                                        </tr>
                                    </table>
                                </div>
                                
                                <div class="info-section">
                                    <h6 class="text-info mb-3">معلومات النظام</h6>
                                    <table class="table table-borderless">
                                        <tr>
                                            <td><strong>أضيف بواسطة:</strong></td>
                                            <td><?php echo htmlspecialchars($supporter['admin_name'] ?: 'النظام'); ?></td>
                                        </tr>
                                        <tr>
                                            <td><strong>تاريخ الإضافة:</strong></td>
                                            <td><?php echo date('Y-m-d H:i', strtotime($supporter['created_at'])); ?></td>
                                        </tr>
                                        <?php if ($supporter['updated_at'] != $supporter['created_at']): ?>
                                        <tr>
                                            <td><strong>آخر تحديث:</strong></td>
                                            <td><?php echo date('Y-m-d H:i', strtotime($supporter['updated_at'])); ?></td>
                                        </tr>
                                        <?php endif; ?>
                                    </table>
                                </div>
                            </div>
                        </div>
                        
                        <?php if ($supporter['notes']): ?>
                        <div class="info-section">
                            <h6 class="text-warning mb-3">ملاحظات</h6>
                            <p class="mb-0"><?php echo nl2br(htmlspecialchars($supporter['notes'])); ?></p>
                        </div>
                        <?php endif; ?>
                    </div>
                </div>

                <!-- المرفقات -->
                <?php if (!empty($attachments)): ?>
                <div class="card supporter-card">
                    <div class="card-header bg-success text-white">
                        <h5 class="mb-0">
                            <i class="fas fa-paperclip me-2"></i>
                            المرفقات (<?php echo count($attachments); ?>)
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="attachment-grid">
                            <?php foreach ($attachment_types as $type => $type_name): ?>
                                <?php if (isset($grouped_attachments[$type])): ?>
                                    <div class="attachment-card">
                                        <div class="card-header bg-light">
                                            <h6 class="mb-0">
                                                <i class="fas fa-<?php 
                                                    echo strpos($type, 'voter_id') !== false ? 'id-card' : 
                                                        (strpos($type, 'national_id') !== false ? 'address-card' : 
                                                        ($type == 'residence_card' ? 'home' : 'file')); 
                                                ?> me-2"></i>
                                                <?php echo $type_name; ?>
                                            </h6>
                                        </div>
                                        <div class="card-body">
                                            <?php foreach ($grouped_attachments[$type] as $attachment): ?>
                                                <div class="attachment-item mb-3">
                                                    <?php if (strpos($attachment['file_type'], 'image/') === 0): ?>
                                                        <img src="../../<?php echo htmlspecialchars($attachment['file_path']); ?>" 
                                                             class="attachment-image" 
                                                             alt="<?php echo htmlspecialchars($attachment['file_name']); ?>"
                                                             onclick="showImageModal('../../<?php echo htmlspecialchars($attachment['file_path']); ?>', '<?php echo htmlspecialchars($attachment['file_name']); ?>')">
                                                    <?php else: ?>
                                                        <div class="text-center p-3">
                                                            <i class="fas fa-file-pdf fa-3x text-danger mb-2"></i>
                                                            <br>
                                                            <a href="../../<?php echo htmlspecialchars($attachment['file_path']); ?>" 
                                                               target="_blank" class="btn btn-outline-primary btn-sm">
                                                                <i class="fas fa-eye me-1"></i>عرض PDF
                                                            </a>
                                                        </div>
                                                    <?php endif; ?>
                                                    
                                                    <div class="attachment-info mt-2">
                                                        <small class="text-muted">
                                                            <i class="fas fa-file me-1"></i>
                                                            <?php echo htmlspecialchars($attachment['file_name']); ?>
                                                            <br>
                                                            <i class="fas fa-weight me-1"></i>
                                                            <?php echo formatFileSize($attachment['file_size']); ?>
                                                            <br>
                                                            <i class="fas fa-clock me-1"></i>
                                                            <?php echo date('Y-m-d H:i', strtotime($attachment['upload_date'])); ?>
                                                        </small>
                                                    </div>
                                                </div>
                                            <?php endforeach; ?>
                                        </div>
                                    </div>
                                <?php endif; ?>
                            <?php endforeach; ?>
                        </div>
                    </div>
                </div>
                <?php else: ?>
                <div class="card supporter-card">
                    <div class="card-body text-center">
                        <i class="fas fa-paperclip fa-3x text-muted mb-3"></i>
                        <h5 class="text-muted">لا توجد مرفقات</h5>
                        <p class="text-muted">لم يتم رفع أي مرفقات لهذا المؤيد</p>
                    </div>
                </div>
                <?php endif; ?>
            </div>
        </div>
    </div>

    <!-- نافذة عرض الصور -->
    <div class="modal fade" id="imageModal" tabindex="-1">
        <div class="modal-dialog modal-lg modal-dialog-centered">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="imageModalTitle">عرض الصورة</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body text-center">
                    <img id="modalImage" src="" class="img-fluid" alt="">
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // عرض الصور في نافذة منبثقة
        function showImageModal(imageSrc, imageTitle) {
            document.getElementById('modalImage').src = imageSrc;
            document.getElementById('imageModalTitle').textContent = imageTitle;
            new bootstrap.Modal(document.getElementById('imageModal')).show();
        }

        // تنسيق حجم الملف
        function formatFileSize(bytes) {
            if (bytes === 0) return '0 Bytes';
            const k = 1024;
            const sizes = ['Bytes', 'KB', 'MB', 'GB'];
            const i = Math.floor(Math.log(bytes) / Math.log(k));
            return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
        }
    </script>
</body>
</html>

<?php
// دالة تنسيق حجم الملف
function formatFileSize($bytes) {
    if ($bytes == 0) return '0 Bytes';
    $k = 1024;
    $sizes = ['Bytes', 'KB', 'MB', 'GB'];
    $i = floor(log($bytes) / log($k));
    return round(($bytes / pow($k, $i)), 2) . ' ' . $sizes[$i];
}
?>
