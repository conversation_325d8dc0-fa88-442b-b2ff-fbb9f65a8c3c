<?php
// صفحة معالجة الأخطاء
$error_code = $_GET['code'] ?? '404';
$error_messages = [
    '400' => [
        'title' => 'طلب غير صحيح',
        'message' => 'الطلب الذي أرسلته غير صحيح أو غير مكتمل.',
        'icon' => 'fas fa-exclamation-triangle'
    ],
    '401' => [
        'title' => 'غير مصرح',
        'message' => 'ليس لديك صلاحية للوصول إلى هذه الصفحة.',
        'icon' => 'fas fa-lock'
    ],
    '403' => [
        'title' => 'ممنوع',
        'message' => 'الوصول إلى هذه الصفحة ممنوع.',
        'icon' => 'fas fa-ban'
    ],
    '404' => [
        'title' => 'الصفحة غير موجودة',
        'message' => 'الصفحة التي تبحث عنها غير موجودة أو تم نقلها.',
        'icon' => 'fas fa-search'
    ],
    '500' => [
        'title' => 'خطأ في الخادم',
        'message' => 'حدث خطأ داخلي في الخادم. يرجى المحاولة لاحقاً.',
        'icon' => 'fas fa-server'
    ]
];

$error = $error_messages[$error_code] ?? $error_messages['404'];
http_response_code((int)$error_code);
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $error['title']; ?> - نظام إدارة الحملة الانتخابية</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        
        .error-container {
            text-align: center;
            color: white;
            max-width: 600px;
            padding: 2rem;
        }
        
        .error-code {
            font-size: 8rem;
            font-weight: bold;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
            margin-bottom: 1rem;
            animation: pulse 2s infinite;
        }
        
        .error-icon {
            font-size: 4rem;
            margin-bottom: 2rem;
            opacity: 0.8;
        }
        
        .error-title {
            font-size: 2.5rem;
            font-weight: bold;
            margin-bottom: 1rem;
            text-shadow: 1px 1px 2px rgba(0,0,0,0.3);
        }
        
        .error-message {
            font-size: 1.2rem;
            margin-bottom: 2rem;
            opacity: 0.9;
            line-height: 1.6;
        }
        
        .error-actions {
            margin-top: 2rem;
        }
        
        .btn-home {
            background: rgba(255,255,255,0.2);
            border: 2px solid rgba(255,255,255,0.3);
            color: white;
            padding: 12px 30px;
            border-radius: 25px;
            text-decoration: none;
            display: inline-block;
            margin: 0 10px;
            transition: all 0.3s ease;
            backdrop-filter: blur(10px);
        }
        
        .btn-home:hover {
            background: rgba(255,255,255,0.3);
            border-color: rgba(255,255,255,0.5);
            color: white;
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0,0,0,0.2);
        }
        
        .floating-shapes {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            pointer-events: none;
            z-index: -1;
        }
        
        .shape {
            position: absolute;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 50%;
            animation: float 6s ease-in-out infinite;
        }
        
        .shape:nth-child(1) {
            width: 80px;
            height: 80px;
            top: 20%;
            left: 10%;
            animation-delay: 0s;
        }
        
        .shape:nth-child(2) {
            width: 120px;
            height: 120px;
            top: 60%;
            right: 10%;
            animation-delay: 2s;
        }
        
        .shape:nth-child(3) {
            width: 60px;
            height: 60px;
            bottom: 20%;
            left: 20%;
            animation-delay: 4s;
        }
        
        .shape:nth-child(4) {
            width: 100px;
            height: 100px;
            top: 10%;
            right: 30%;
            animation-delay: 1s;
        }
        
        .shape:nth-child(5) {
            width: 70px;
            height: 70px;
            bottom: 30%;
            right: 20%;
            animation-delay: 3s;
        }
        
        @keyframes float {
            0%, 100% { 
                transform: translateY(0px) rotate(0deg); 
            }
            50% { 
                transform: translateY(-20px) rotate(180deg); 
            }
        }
        
        @keyframes pulse {
            0%, 100% { 
                transform: scale(1); 
            }
            50% { 
                transform: scale(1.05); 
            }
        }
        
        .error-details {
            background: rgba(255,255,255,0.1);
            border-radius: 10px;
            padding: 1rem;
            margin-top: 2rem;
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255,255,255,0.2);
        }
        
        .error-details small {
            opacity: 0.7;
        }
        
        @media (max-width: 768px) {
            .error-code {
                font-size: 5rem;
            }
            
            .error-title {
                font-size: 2rem;
            }
            
            .error-message {
                font-size: 1rem;
            }
            
            .error-container {
                padding: 1rem;
            }
        }
    </style>
</head>
<body>
    <div class="floating-shapes">
        <div class="shape"></div>
        <div class="shape"></div>
        <div class="shape"></div>
        <div class="shape"></div>
        <div class="shape"></div>
    </div>

    <div class="error-container">
        <div class="error-code"><?php echo $error_code; ?></div>
        <div class="error-icon">
            <i class="<?php echo $error['icon']; ?>"></i>
        </div>
        <h1 class="error-title"><?php echo $error['title']; ?></h1>
        <p class="error-message"><?php echo $error['message']; ?></p>
        
        <div class="error-actions">
            <a href="/" class="btn-home">
                <i class="fas fa-home me-2"></i>
                العودة للرئيسية
            </a>
            <a href="javascript:history.back()" class="btn-home">
                <i class="fas fa-arrow-right me-2"></i>
                العودة للخلف
            </a>
        </div>
        
        <div class="error-details">
            <small>
                <i class="fas fa-info-circle me-1"></i>
                إذا كنت تعتقد أن هذا خطأ، يرجى التواصل مع الدعم الفني
                <br>
                كود الخطأ: <?php echo $error_code; ?> | 
                الوقت: <?php echo date('Y-m-d H:i:s'); ?> |
                IP: <?php echo $_SERVER['REMOTE_ADDR'] ?? 'غير معروف'; ?>
            </small>
        </div>
    </div>

    <script>
        // تسجيل الخطأ (اختياري)
        if (typeof gtag !== 'undefined') {
            gtag('event', 'page_view', {
                page_title: 'Error <?php echo $error_code; ?>',
                page_location: window.location.href
            });
        }
        
        // إضافة تأثيرات تفاعلية
        document.addEventListener('DOMContentLoaded', function() {
            const shapes = document.querySelectorAll('.shape');
            shapes.forEach((shape, index) => {
                shape.addEventListener('mouseenter', function() {
                    this.style.animationDuration = '2s';
                });
                
                shape.addEventListener('mouseleave', function() {
                    this.style.animationDuration = '6s';
                });
            });
            
            // تأثير الماوس على الخلفية
            document.addEventListener('mousemove', function(e) {
                const x = e.clientX / window.innerWidth;
                const y = e.clientY / window.innerHeight;
                
                document.body.style.background = `linear-gradient(${45 + x * 90}deg, 
                    hsl(${220 + x * 40}, 70%, ${60 + y * 20}%) 0%, 
                    hsl(${280 + y * 40}, 60%, ${50 + x * 20}%) 100%)`;
            });
        });
        
        // إعادة تحميل الصفحة بعد 30 ثانية للأخطاء المؤقتة
        <?php if ($error_code == '500'): ?>
        setTimeout(function() {
            if (confirm('هل تريد إعادة تحميل الصفحة؟')) {
                window.location.reload();
            }
        }, 30000);
        <?php endif; ?>
    </script>
</body>
</html>
