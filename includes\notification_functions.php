<?php
// دوال الإشعارات
require_once __DIR__ . '/../config/database.php';

/**
 * إنشاء إشعار جديد
 */
function createNotification($data) {
    $notification_uuid = generateUUID();
    
    $sql = "INSERT INTO notifications (
        notification_uuid, user_type, user_id, title, message, short_message,
        type, category, priority, related_type, related_id, related_data,
        action_url, action_text, icon, color, sender_type, sender_id
    ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)";
    
    $params = [
        $notification_uuid,
        $data['user_type'],
        $data['user_id'] ?? null,
        $data['title'],
        $data['message'],
        $data['short_message'] ?? substr($data['message'], 0, 255),
        $data['type'] ?? 'info',
        $data['category'] ?? 'general',
        $data['priority'] ?? 'normal',
        $data['related_type'] ?? null,
        $data['related_id'] ?? null,
        isset($data['related_data']) ? json_encode($data['related_data']) : null,
        $data['action_url'] ?? null,
        $data['action_text'] ?? null,
        $data['icon'] ?? 'fas fa-bell',
        $data['color'] ?? '#007bff',
        $data['sender_type'] ?? 'system',
        $data['sender_id'] ?? null
    ];
    
    try {
        executeQuery($sql, $params);
        
        // تسجيل النشاط
        logActivity([
            'user_type' => $data['sender_type'] ?? 'system',
            'user_id' => $data['sender_id'] ?? null,
            'action_type' => 'create',
            'target_type' => 'notification',
            'description' => 'تم إنشاء إشعار جديد: ' . $data['title']
        ]);
        
        return $notification_uuid;
    } catch (Exception $e) {
        error_log("خطأ في إنشاء الإشعار: " . $e->getMessage());
        return false;
    }
}

/**
 * إنشاء إشعار من قالب
 */
function createNotificationFromTemplate($template_key, $variables, $user_type, $user_id = null) {
    // جلب القالب
    $template = fetchOne("SELECT * FROM notification_templates WHERE template_key = ? AND is_active = 1", [$template_key]);
    
    if (!$template) {
        return false;
    }
    
    // استبدال المتغيرات
    $title = replaceTemplateVariables($template['title_template'], $variables);
    $message = replaceTemplateVariables($template['message_template'], $variables);
    $short_message = replaceTemplateVariables($template['short_message_template'] ?? '', $variables);
    
    $data = [
        'user_type' => $user_type,
        'user_id' => $user_id,
        'title' => $title,
        'message' => $message,
        'short_message' => $short_message,
        'type' => $template['type'],
        'category' => $template['category'],
        'priority' => $template['priority'],
        'icon' => $template['icon'],
        'color' => $template['color']
    ];
    
    return createNotification($data);
}

/**
 * استبدال المتغيرات في النص
 */
function replaceTemplateVariables($text, $variables) {
    foreach ($variables as $key => $value) {
        $text = str_replace('{' . $key . '}', $value, $text);
    }
    return $text;
}

/**
 * جلب الإشعارات للمستخدم
 */
function getUserNotifications($user_type, $user_id = null, $limit = 20, $unread_only = false) {
    $sql = "SELECT * FROM notifications WHERE user_type = ?";
    $params = [$user_type];
    
    if ($user_id !== null) {
        $sql .= " AND (user_id = ? OR user_id IS NULL)";
        $params[] = $user_id;
    }
    
    if ($unread_only) {
        $sql .= " AND is_read = 0";
    }
    
    $sql .= " AND (expires_at IS NULL OR expires_at > NOW())";
    $sql .= " ORDER BY priority DESC, created_at DESC LIMIT ?";
    $params[] = $limit;
    
    return fetchAll($sql, $params);
}

/**
 * عدد الإشعارات غير المقروءة
 */
function getUnreadNotificationsCount($user_type, $user_id = null) {
    $sql = "SELECT COUNT(*) as count FROM notifications WHERE user_type = ? AND is_read = 0";
    $params = [$user_type];
    
    if ($user_id !== null) {
        $sql .= " AND (user_id = ? OR user_id IS NULL)";
        $params[] = $user_id;
    }
    
    $sql .= " AND (expires_at IS NULL OR expires_at > NOW())";
    
    $result = fetchOne($sql, $params);
    return $result['count'] ?? 0;
}

/**
 * تحديد الإشعار كمقروء
 */
function markNotificationAsRead($notification_id, $user_type, $user_id = null) {
    $sql = "UPDATE notifications SET is_read = 1, read_at = NOW() WHERE id = ?";
    $params = [$notification_id];
    
    if ($user_id !== null) {
        $sql .= " AND user_type = ? AND (user_id = ? OR user_id IS NULL)";
        $params[] = $user_type;
        $params[] = $user_id;
    }
    
    return executeQuery($sql, $params);
}

/**
 * تحديد جميع الإشعارات كمقروءة
 */
function markAllNotificationsAsRead($user_type, $user_id = null) {
    $sql = "UPDATE notifications SET is_read = 1, read_at = NOW() WHERE user_type = ? AND is_read = 0";
    $params = [$user_type];
    
    if ($user_id !== null) {
        $sql .= " AND (user_id = ? OR user_id IS NULL)";
        $params[] = $user_id;
    }
    
    return executeQuery($sql, $params);
}

/**
 * حذف الإشعارات المنتهية الصلاحية
 */
function cleanupExpiredNotifications() {
    $sql = "DELETE FROM notifications WHERE expires_at IS NOT NULL AND expires_at < NOW()";
    return executeQuery($sql);
}

/**
 * إنشاء إشعار لمؤيد جديد
 */
function notifyNewSupporter($supporter_id, $admin_id) {
    $supporter = fetchOne("SELECT full_name FROM supporters WHERE id = ?", [$supporter_id]);
    $admin = fetchOne("SELECT full_name FROM admins WHERE id = ?", [$admin_id]);
    
    if ($supporter && $admin) {
        return createNotificationFromTemplate('new_supporter', [
            'supporter_name' => $supporter['full_name'],
            'admin_name' => $admin['full_name']
        ], 'candidate', 1);
    }
    
    return false;
}

/**
 * إنشاء إشعار لمطلب جديد
 */
function notifyNewRequest($request_id) {
    $request = fetchOne("
        SELECT sr.title, sr.supporter_name, a.full_name as admin_name 
        FROM supporter_requests sr 
        JOIN admins a ON sr.admin_id = a.id 
        WHERE sr.id = ?
    ", [$request_id]);
    
    if ($request) {
        return createNotificationFromTemplate('new_request', [
            'request_title' => $request['title'],
            'supporter_name' => $request['supporter_name'],
            'admin_name' => $request['admin_name']
        ], 'candidate', 1);
    }
    
    return false;
}

/**
 * إنشاء إشعار لرسالة جديدة
 */
function notifyNewMessage($message_id) {
    $message = fetchOne("
        SELECT m.subject, a.full_name as sender_name 
        FROM messages m 
        LEFT JOIN admins a ON m.sender_id = a.id AND m.sender_type = 'admin'
        WHERE m.id = ?
    ", [$message_id]);
    
    if ($message) {
        return createNotificationFromTemplate('new_message', [
            'sender_name' => $message['sender_name'] ?? 'النظام',
            'message_subject' => $message['subject']
        ], 'candidate', 1);
    }
    
    return false;
}

/**
 * إنشاء إشعار لتقرير جديد
 */
function notifyNewReport($report_id) {
    $report = fetchOne("
        SELECT wr.report_period, a.full_name as admin_name 
        FROM weekly_reports wr 
        JOIN admins a ON wr.admin_id = a.id 
        WHERE wr.id = ?
    ", [$report_id]);
    
    if ($report) {
        return createNotificationFromTemplate('new_report', [
            'admin_name' => $report['admin_name'],
            'week_period' => $report['report_period']
        ], 'candidate', 1);
    }
    
    return false;
}

/**
 * إنشاء إشعار لإداري جديد
 */
function notifyNewAdmin($admin_id) {
    $admin = fetchOne("
        SELECT a.full_name, r.name as region_name 
        FROM admins a 
        LEFT JOIN regions r ON a.region_id = r.id 
        WHERE a.id = ?
    ", [$admin_id]);
    
    if ($admin) {
        return createNotificationFromTemplate('new_admin', [
            'admin_name' => $admin['full_name'],
            'region_name' => $admin['region_name'] ?? 'غير محدد'
        ], 'candidate', 1);
    }
    
    return false;
}

/**
 * تسجيل نشاط في السجل
 */
function logActivity($data) {
    $activity_uuid = generateUUID();
    
    $sql = "INSERT INTO activity_log (
        activity_uuid, user_type, user_id, user_name, action_type, target_type, 
        target_id, target_name, description, details, session_id, ip_address, 
        user_agent, severity, category
    ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)";
    
    $params = [
        $activity_uuid,
        $data['user_type'],
        $data['user_id'] ?? null,
        $data['user_name'] ?? null,
        $data['action_type'],
        $data['target_type'],
        $data['target_id'] ?? null,
        $data['target_name'] ?? null,
        $data['description'],
        isset($data['details']) ? json_encode($data['details']) : null,
        session_id(),
        $_SERVER['REMOTE_ADDR'] ?? null,
        $_SERVER['HTTP_USER_AGENT'] ?? null,
        $data['severity'] ?? 'medium',
        $data['category'] ?? 'data_modification'
    ];
    
    try {
        return executeQuery($sql, $params);
    } catch (Exception $e) {
        error_log("خطأ في تسجيل النشاط: " . $e->getMessage());
        return false;
    }
}

/**
 * توليد UUID فريد
 */
function generateUUID() {
    return sprintf('%04x%04x-%04x-%04x-%04x-%04x%04x%04x',
        mt_rand(0, 0xffff), mt_rand(0, 0xffff),
        mt_rand(0, 0xffff),
        mt_rand(0, 0x0fff) | 0x4000,
        mt_rand(0, 0x3fff) | 0x8000,
        mt_rand(0, 0xffff), mt_rand(0, 0xffff), mt_rand(0, 0xffff)
    );
}

/**
 * جلب الأنشطة الأخيرة
 */
function getRecentActivities($limit = 20, $user_type = null, $user_id = null) {
    $sql = "SELECT * FROM activity_log WHERE 1=1";
    $params = [];
    
    if ($user_type) {
        $sql .= " AND user_type = ?";
        $params[] = $user_type;
    }
    
    if ($user_id) {
        $sql .= " AND user_id = ?";
        $params[] = $user_id;
    }
    
    $sql .= " ORDER BY created_at DESC LIMIT ?";
    $params[] = $limit;
    
    return fetchAll($sql, $params);
}

/**
 * إحصائيات الإشعارات
 */
function getNotificationStatistics($user_type, $user_id = null, $days = 30) {
    $sql = "SELECT 
        COUNT(*) as total,
        SUM(CASE WHEN is_read = 1 THEN 1 ELSE 0 END) as read_count,
        SUM(CASE WHEN is_read = 0 THEN 1 ELSE 0 END) as unread_count,
        SUM(CASE WHEN type = 'urgent' THEN 1 ELSE 0 END) as urgent_count,
        SUM(CASE WHEN category = 'supporter' THEN 1 ELSE 0 END) as supporter_notifications,
        SUM(CASE WHEN category = 'request' THEN 1 ELSE 0 END) as request_notifications,
        SUM(CASE WHEN category = 'message' THEN 1 ELSE 0 END) as message_notifications,
        SUM(CASE WHEN category = 'report' THEN 1 ELSE 0 END) as report_notifications
    FROM notifications 
    WHERE user_type = ? AND created_at >= DATE_SUB(NOW(), INTERVAL ? DAY)";
    
    $params = [$user_type, $days];
    
    if ($user_id !== null) {
        $sql .= " AND (user_id = ? OR user_id IS NULL)";
        $params[] = $user_id;
    }
    
    return fetchOne($sql, $params);
}
?>
