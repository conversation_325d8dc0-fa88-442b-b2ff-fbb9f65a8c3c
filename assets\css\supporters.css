/* تصميم صفحة المؤيدين */

/* صور المؤيدين */
.supporter-photo {
    width: 50px;
    height: 50px;
    border-radius: 50%;
    object-fit: cover;
    cursor: pointer;
    transition: all 0.3s ease;
    border: 2px solid #dee2e6;
}

.supporter-photo:hover {
    transform: scale(1.1);
    border-color: #007bff;
    box-shadow: 0 4px 8px rgba(0,123,255,0.3);
}

.no-photo {
    width: 50px;
    height: 50px;
    border-radius: 50%;
    background-color: #f8f9fa;
    display: flex;
    align-items: center;
    justify-content: center;
    border: 2px solid #dee2e6;
    color: #6c757d;
}

/* بطاقات الإحصائيات */
.card {
    transition: all 0.3s ease;
    border: none;
    border-radius: 10px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
}

.card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 15px rgba(0,0,0,0.15);
}

/* جدول المؤيدين */
#supportersTable {
    font-size: 0.9rem;
}

#supportersTable th {
    background-color: #343a40;
    color: white;
    font-weight: 600;
    border: none;
    padding: 12px 8px;
    text-align: center;
    vertical-align: middle;
}

#supportersTable td {
    padding: 10px 8px;
    vertical-align: middle;
    text-align: center;
    border-bottom: 1px solid #dee2e6;
}

#supportersTable tbody tr {
    transition: all 0.3s ease;
}

#supportersTable tbody tr:hover {
    background-color: #f8f9fa;
    transform: scale(1.01);
}

/* أزرار الإجراءات */
.btn-group .btn {
    margin: 0 1px;
    border-radius: 4px;
}

.btn-sm {
    padding: 0.25rem 0.5rem;
    font-size: 0.875rem;
}

/* شارات الجنس */
.badge.bg-pink {
    background-color: #e91e63 !important;
}

/* فلاتر البحث */
.card-header {
    background: linear-gradient(135deg, #007bff, #0056b3);
    color: white;
    border-radius: 10px 10px 0 0;
    border: none;
}

.form-control, .form-select {
    border-radius: 8px;
    border: 2px solid #e9ecef;
    transition: all 0.3s ease;
}

.form-control:focus, .form-select:focus {
    border-color: #007bff;
    box-shadow: 0 0 0 0.2rem rgba(0,123,255,0.25);
    transform: translateY(-1px);
}

/* النوافذ المنبثقة */
.modal-content {
    border-radius: 15px;
    border: none;
    box-shadow: 0 10px 30px rgba(0,0,0,0.3);
}

.modal-header {
    background: linear-gradient(135deg, #007bff, #0056b3);
    color: white;
    border-radius: 15px 15px 0 0;
    border: none;
}

.modal-footer {
    border: none;
    border-radius: 0 0 15px 15px;
}

/* نموذج إضافة/تعديل المؤيد */
.supporter-form .form-label {
    font-weight: 600;
    color: #495057;
    margin-bottom: 0.5rem;
}

.supporter-form .form-control,
.supporter-form .form-select {
    margin-bottom: 1rem;
}

.supporter-form .required {
    color: #dc3545;
}

/* معاينة الصورة */
.photo-preview {
    max-width: 200px;
    max-height: 200px;
    border-radius: 10px;
    margin-top: 10px;
    border: 2px solid #dee2e6;
}

.photo-upload-area {
    border: 2px dashed #dee2e6;
    border-radius: 10px;
    padding: 20px;
    text-align: center;
    transition: all 0.3s ease;
    cursor: pointer;
}

.photo-upload-area:hover {
    border-color: #007bff;
    background-color: #f8f9fa;
}

.photo-upload-area.dragover {
    border-color: #28a745;
    background-color: #d4edda;
}

/* تفاصيل المؤيد */
.supporter-details {
    padding: 20px;
}

.supporter-details .detail-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 10px 0;
    border-bottom: 1px solid #eee;
}

.supporter-details .detail-item:last-child {
    border-bottom: none;
}

.supporter-details .detail-label {
    font-weight: 600;
    color: #495057;
    min-width: 120px;
}

.supporter-details .detail-value {
    color: #6c757d;
    text-align: left;
}

/* بطاقة المؤيد للطباعة */
.supporter-card {
    width: 350px;
    height: 220px;
    border: 2px solid #007bff;
    border-radius: 15px;
    padding: 15px;
    background: linear-gradient(135deg, #f8f9fa, #ffffff);
    position: relative;
    overflow: hidden;
}

.supporter-card::before {
    content: '';
    position: absolute;
    top: -50%;
    right: -50%;
    width: 200%;
    height: 200%;
    background: linear-gradient(45deg, transparent, rgba(0,123,255,0.1), transparent);
    transform: rotate(45deg);
    animation: shine 3s infinite;
}

@keyframes shine {
    0% { transform: translateX(-100%) translateY(-100%) rotate(45deg); }
    100% { transform: translateX(100%) translateY(100%) rotate(45deg); }
}

.supporter-card .card-header {
    text-align: center;
    margin-bottom: 10px;
    background: none;
    color: #007bff;
    font-weight: bold;
}

.supporter-card .supporter-info {
    display: flex;
    align-items: center;
    gap: 15px;
}

.supporter-card .supporter-photo-large {
    width: 80px;
    height: 80px;
    border-radius: 50%;
    object-fit: cover;
    border: 3px solid #007bff;
}

.supporter-card .supporter-details-card {
    flex: 1;
}

.supporter-card .detail-row {
    display: flex;
    justify-content: space-between;
    margin-bottom: 5px;
    font-size: 0.9rem;
}

.supporter-card .detail-label-card {
    font-weight: 600;
    color: #495057;
}

.supporter-card .detail-value-card {
    color: #6c757d;
}

/* تحسينات للجوال */
@media (max-width: 768px) {
    .supporter-photo {
        width: 40px;
        height: 40px;
    }
    
    .no-photo {
        width: 40px;
        height: 40px;
    }
    
    #supportersTable {
        font-size: 0.8rem;
    }
    
    #supportersTable th,
    #supportersTable td {
        padding: 6px 4px;
    }
    
    .btn-group .btn {
        padding: 0.2rem 0.4rem;
        font-size: 0.75rem;
    }
    
    .supporter-card {
        width: 100%;
        height: auto;
        min-height: 200px;
    }
    
    .supporter-card .supporter-info {
        flex-direction: column;
        text-align: center;
    }
    
    .supporter-card .supporter-photo-large {
        width: 60px;
        height: 60px;
    }
}

/* تحسينات إضافية */
.table-responsive {
    border-radius: 10px;
    overflow: hidden;
}

.dataTables_wrapper .dataTables_length,
.dataTables_wrapper .dataTables_filter,
.dataTables_wrapper .dataTables_info,
.dataTables_wrapper .dataTables_paginate {
    margin-bottom: 1rem;
}

.dataTables_wrapper .dataTables_filter input {
    border-radius: 20px;
    border: 2px solid #e9ecef;
    padding: 8px 15px;
    margin-left: 10px;
}

.dataTables_wrapper .dataTables_length select {
    border-radius: 8px;
    border: 2px solid #e9ecef;
    padding: 5px 10px;
    margin: 0 10px;
}

/* أزرار التصدير */
.btn-toolbar .btn-group .btn {
    margin-left: 5px;
    border-radius: 8px;
    font-weight: 600;
    transition: all 0.3s ease;
}

.btn-toolbar .btn-group .btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0,0,0,0.2);
}

/* مؤشر التحميل */
.loading-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(255,255,255,0.8);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 9999;
}

.loading-spinner {
    width: 50px;
    height: 50px;
    border: 5px solid #f3f3f3;
    border-top: 5px solid #007bff;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* تحسين الألوان */
.bg-pink {
    background-color: #e91e63 !important;
}

.text-pink {
    color: #e91e63 !important;
}

/* تحسين الحدود */
.border-primary {
    border-color: #007bff !important;
}

.border-success {
    border-color: #28a745 !important;
}

.border-warning {
    border-color: #ffc107 !important;
}

.border-danger {
    border-color: #dc3545 !important;
}

/* تحسين الظلال */
.shadow-sm {
    box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075) !important;
}

.shadow {
    box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15) !important;
}

.shadow-lg {
    box-shadow: 0 1rem 3rem rgba(0, 0, 0, 0.175) !important;
}

/* تحسين الانتقالات */
.fade-in {
    animation: fadeIn 0.5s ease-in;
}

@keyframes fadeIn {
    from { opacity: 0; transform: translateY(20px); }
    to { opacity: 1; transform: translateY(0); }
}

.slide-in {
    animation: slideIn 0.5s ease-out;
}

@keyframes slideIn {
    from { transform: translateX(-100%); }
    to { transform: translateX(0); }
}

/* تحسين التمرير */
.smooth-scroll {
    scroll-behavior: smooth;
}

/* تحسين التركيز */
.form-control:focus,
.form-select:focus,
.btn:focus {
    outline: none;
    box-shadow: 0 0 0 0.2rem rgba(0,123,255,0.25);
}

/* تحسين النصوص */
.text-truncate {
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

.text-wrap {
    word-wrap: break-word;
    word-break: break-word;
}
