<?php
// إصلاح شامل لجميع مشاكل النظام

header('Content-Type: text/html; charset=utf-8');
ini_set('default_charset', 'utf-8');
mb_internal_encoding('UTF-8');

require_once 'config/config.php';
require_once 'config/database.php';

echo "<!DOCTYPE html>";
echo "<html lang='ar' dir='rtl'>";
echo "<head>";
echo "<meta charset='UTF-8'>";
echo "<meta name='viewport' content='width=device-width, initial-scale=1.0'>";
echo "<title>إصلاح شامل للنظام</title>";
echo "<link href='https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css' rel='stylesheet'>";
echo "<link href='https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css' rel='stylesheet'>";
echo "<style>";
echo "body { font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); min-height: 100vh; padding: 2rem; }";
echo ".fix-card { background: white; border-radius: 15px; box-shadow: 0 10px 30px rgba(0,0,0,0.1); padding: 2rem; margin: 0 auto; max-width: 1200px; }";
echo ".success { color: #28a745; }";
echo ".error { color: #dc3545; }";
echo ".warning { color: #ffc107; }";
echo "</style>";
echo "</head>";
echo "<body>";

echo "<div class='fix-card'>";
echo "<h1 class='text-center mb-4'><i class='fas fa-tools'></i> إصلاح شامل لجميع مشاكل النظام</h1>";

if (isset($_POST['fix_all_system'])) {
    try {
        echo "<div class='alert alert-info'>";
        echo "<h5>جاري إصلاح جميع مشاكل النظام...</h5>";
        echo "</div>";

        $database = new Database();
        $db = $database->getConnection();

        if (!$db) {
            throw new Exception("فشل الاتصال بقاعدة البيانات");
        }

        // 1. إصلاح قاعدة البيانات والترميز
        echo "<div class='alert alert-primary'>";
        echo "<h6>1. إصلاح قاعدة البيانات والترميز</h6>";
        
        // تحديث إعدادات قاعدة البيانات
        $db->exec("SET NAMES utf8mb4 COLLATE utf8mb4_unicode_ci");
        $db->exec("ALTER DATABASE " . DB_NAME . " CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci");
        echo "<p class='success'><i class='fas fa-check'></i> تم تحديث إعدادات الترميز</p>";
        echo "</div>";

        // 2. إنشاء/تحديث جميع الجداول
        echo "<div class='alert alert-warning'>";
        echo "<h6>2. إنشاء/تحديث جميع الجداول</h6>";
        
        // جدول المناطق
        $sql = "CREATE TABLE IF NOT EXISTS regions (
            id INT AUTO_INCREMENT PRIMARY KEY,
            name VARCHAR(100) NOT NULL,
            description TEXT,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci";
        $db->exec($sql);
        echo "<p class='success'><i class='fas fa-check'></i> جدول المناطق</p>";
        
        // جدول المستخدمين
        $sql = "CREATE TABLE IF NOT EXISTS users (
            id INT AUTO_INCREMENT PRIMARY KEY,
            username VARCHAR(50) UNIQUE NOT NULL,
            password VARCHAR(255) NOT NULL,
            phone VARCHAR(15) NOT NULL,
            full_name VARCHAR(100) NOT NULL,
            user_type ENUM('candidate', 'admin') DEFAULT 'admin',
            permissions JSON,
            region_id INT,
            is_active TINYINT(1) DEFAULT 1,
            last_login TIMESTAMP NULL,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            FOREIGN KEY (region_id) REFERENCES regions(id) ON DELETE SET NULL
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci";
        $db->exec($sql);
        echo "<p class='success'><i class='fas fa-check'></i> جدول المستخدمين</p>";
        
        // جدول المؤيدين
        $sql = "CREATE TABLE IF NOT EXISTS supporters (
            id INT AUTO_INCREMENT PRIMARY KEY,
            full_name VARCHAR(100) NOT NULL,
            gender ENUM('male', 'female') NOT NULL,
            marital_status ENUM('single', 'married', 'divorced', 'widowed') NOT NULL,
            birth_date DATE NOT NULL,
            education VARCHAR(50),
            profession VARCHAR(100),
            address TEXT NOT NULL,
            phone VARCHAR(15) NOT NULL,
            voter_number VARCHAR(50) UNIQUE,
            voting_center VARCHAR(100),
            region_id INT NOT NULL,
            photo VARCHAR(255),
            notes TEXT,
            added_by INT NOT NULL,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            FOREIGN KEY (region_id) REFERENCES regions(id) ON DELETE CASCADE,
            FOREIGN KEY (added_by) REFERENCES users(id) ON DELETE CASCADE
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci";
        $db->exec($sql);
        echo "<p class='success'><i class='fas fa-check'></i> جدول المؤيدين</p>";
        
        // جدول المصروفات
        $sql = "CREATE TABLE IF NOT EXISTS expenses (
            id INT AUTO_INCREMENT PRIMARY KEY,
            description TEXT NOT NULL,
            amount DECIMAL(10,2) NOT NULL,
            category VARCHAR(100) NOT NULL,
            expense_date DATE NOT NULL,
            region_id INT,
            attachment VARCHAR(255),
            notes TEXT,
            added_by INT NOT NULL,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            FOREIGN KEY (region_id) REFERENCES regions(id) ON DELETE SET NULL,
            FOREIGN KEY (added_by) REFERENCES users(id) ON DELETE CASCADE
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci";
        $db->exec($sql);
        echo "<p class='success'><i class='fas fa-check'></i> جدول المصروفات</p>";
        
        // جدول الفعاليات
        $sql = "CREATE TABLE IF NOT EXISTS events (
            id INT AUTO_INCREMENT PRIMARY KEY,
            name VARCHAR(200) NOT NULL,
            description TEXT,
            event_date DATE NOT NULL,
            event_time TIME,
            location VARCHAR(255) NOT NULL,
            photos JSON,
            status ENUM('planned', 'ongoing', 'completed', 'cancelled') DEFAULT 'planned',
            added_by INT NOT NULL,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            FOREIGN KEY (added_by) REFERENCES users(id) ON DELETE CASCADE
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci";
        $db->exec($sql);
        echo "<p class='success'><i class='fas fa-check'></i> جدول الفعاليات</p>";
        
        // جدول المنافسين
        $sql = "CREATE TABLE IF NOT EXISTS competitors (
            id INT AUTO_INCREMENT PRIMARY KEY,
            name VARCHAR(100) NOT NULL,
            party VARCHAR(100),
            region_id INT,
            strengths TEXT,
            weaknesses TEXT,
            photos JSON,
            notes TEXT,
            added_by INT NOT NULL,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            FOREIGN KEY (region_id) REFERENCES regions(id) ON DELETE SET NULL,
            FOREIGN KEY (added_by) REFERENCES users(id) ON DELETE CASCADE
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci";
        $db->exec($sql);
        echo "<p class='success'><i class='fas fa-check'></i> جدول المنافسين</p>";
        
        // جدول الرسائل
        $sql = "CREATE TABLE IF NOT EXISTS messages (
            id INT AUTO_INCREMENT PRIMARY KEY,
            sender_id INT NOT NULL,
            recipient_id INT,
            recipient_type ENUM('user', 'all_admins', 'active_admins') DEFAULT 'user',
            subject VARCHAR(255) NOT NULL,
            content TEXT NOT NULL,
            attachment VARCHAR(255),
            is_read TINYINT(1) DEFAULT 0,
            sent_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            read_at TIMESTAMP NULL,
            FOREIGN KEY (sender_id) REFERENCES users(id) ON DELETE CASCADE,
            FOREIGN KEY (recipient_id) REFERENCES users(id) ON DELETE CASCADE
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci";
        $db->exec($sql);
        echo "<p class='success'><i class='fas fa-check'></i> جدول الرسائل</p>";
        
        // جدول الإشعارات
        $sql = "CREATE TABLE IF NOT EXISTS notifications (
            id INT AUTO_INCREMENT PRIMARY KEY,
            user_id INT NOT NULL,
            title VARCHAR(255) NOT NULL,
            message TEXT NOT NULL,
            type ENUM('info', 'success', 'warning', 'error') DEFAULT 'info',
            is_read TINYINT(1) DEFAULT 0,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci";
        $db->exec($sql);
        echo "<p class='success'><i class='fas fa-check'></i> جدول الإشعارات</p>";
        
        echo "</div>";

        // 3. إدراج البيانات الأساسية
        echo "<div class='alert alert-success'>";
        echo "<h6>3. إدراج البيانات الأساسية</h6>";
        
        // حذف البيانات التالفة وإدراج مناطق جديدة
        $db->exec("DELETE FROM regions");
        
        $regions_data = [
            ['بغداد - الكرخ', 'منطقة الكرخ في بغداد'],
            ['بغداد - الرصافة', 'منطقة الرصافة في بغداد'],
            ['البصرة', 'محافظة البصرة'],
            ['النجف الأشرف', 'محافظة النجف الأشرف'],
            ['كربلاء المقدسة', 'محافظة كربلاء المقدسة'],
            ['الموصل', 'محافظة نينوى - الموصل'],
            ['أربيل', 'محافظة أربيل'],
            ['السليمانية', 'محافظة السليمانية'],
            ['دهوك', 'محافظة دهوك'],
            ['الأنبار', 'محافظة الأنبار']
        ];
        
        $stmt = $db->prepare("INSERT INTO regions (name, description) VALUES (?, ?)");
        foreach ($regions_data as $region) {
            $stmt->execute($region);
            echo "<p class='success'><i class='fas fa-check'></i> تم إدراج منطقة: {$region[0]}</p>";
        }
        
        // إدراج/تحديث المستخدم الافتراضي
        $existing_user = $db->query("SELECT id FROM users WHERE username = 'abd'")->fetch();
        if (!$existing_user) {
            $password_hash = password_hash('123456', PASSWORD_DEFAULT);
            $stmt = $db->prepare("INSERT INTO users (username, password, phone, full_name, user_type, is_active) VALUES (?, ?, ?, ?, ?, ?)");
            $stmt->execute(['abd', $password_hash, '07719992716', 'المرشح الرئيسي', 'candidate', 1]);
            echo "<p class='success'><i class='fas fa-check'></i> تم إدراج المستخدم الافتراضي</p>";
        } else {
            $password_hash = password_hash('123456', PASSWORD_DEFAULT);
            $stmt = $db->prepare("UPDATE users SET password = ?, full_name = ?, is_active = 1 WHERE username = 'abd'");
            $stmt->execute([$password_hash, 'المرشح الرئيسي']);
            echo "<p class='success'><i class='fas fa-check'></i> تم تحديث المستخدم الافتراضي</p>";
        }
        
        echo "</div>";

        // 4. إنشاء المجلدات المطلوبة
        echo "<div class='alert alert-info'>";
        echo "<h6>4. إنشاء المجلدات المطلوبة</h6>";
        
        $folders = [
            'uploads',
            'uploads/supporters',
            'uploads/events',
            'uploads/competitors',
            'uploads/expenses',
            'uploads/messages',
            'exports',
            'modules/reports',
            'modules/settings'
        ];
        
        foreach ($folders as $folder) {
            if (!file_exists($folder)) {
                mkdir($folder, 0755, true);
                echo "<p class='success'><i class='fas fa-check'></i> تم إنشاء مجلد: $folder</p>";
            } else {
                echo "<p class='warning'><i class='fas fa-info'></i> مجلد موجود: $folder</p>";
            }
        }
        
        echo "</div>";

        echo "<div class='alert alert-success'>";
        echo "<h3><i class='fas fa-check-circle'></i> تم إصلاح جميع مشاكل النظام بنجاح!</h3>";
        echo "<h5>بيانات تسجيل الدخول:</h5>";
        echo "<p><strong>اسم المستخدم:</strong> abd</p>";
        echo "<p><strong>كلمة المرور:</strong> 123456</p>";
        echo "<p><strong>رقم الهاتف:</strong> 07719992716</p>";
        echo "</div>";
        
    } catch (Exception $e) {
        echo "<div class='alert alert-danger'>";
        echo "<h5>خطأ في الإصلاح:</h5>";
        echo "<p>" . htmlspecialchars($e->getMessage()) . "</p>";
        echo "</div>";
    }
    
    echo "<div class='text-center mt-4'>";
    echo "<a href='login.php' class='btn btn-primary btn-lg me-2'><i class='fas fa-sign-in-alt'></i> تسجيل الدخول</a>";
    echo "<a href='dashboard.php' class='btn btn-success btn-lg me-2'><i class='fas fa-tachometer-alt'></i> لوحة التحكم</a>";
    echo "<a href='test_arabic.php' class='btn btn-info btn-lg'><i class='fas fa-language'></i> اختبار الترميز</a>";
    echo "</div>";
    
} else {
    // عرض نموذج التأكيد
    echo "<div class='alert alert-warning'>";
    echo "<h5><i class='fas fa-exclamation-triangle'></i> إصلاح شامل للنظام</h5>";
    echo "<p>هذا الإجراء سيقوم بإصلاح جميع المشاكل التالية:</p>";
    echo "<ul>";
    echo "<li>✅ إصلاح قاعدة البيانات والترميز العربي</li>";
    echo "<li>✅ إنشاء/تحديث جميع الجداول المطلوبة</li>";
    echo "<li>✅ إصلاح مشكلة إضافة الإداريين</li>";
    echo "<li>✅ إصلاح مشكلة إضافة المؤيدين</li>";
    echo "<li>✅ إنشاء نظام الرسائل والإشعارات</li>";
    echo "<li>✅ إنشاء المجلدات المطلوبة</li>";
    echo "<li>✅ إدراج البيانات الأساسية (10 مناطق عراقية)</li>";
    echo "<li>✅ إصلاح المستخدم الافتراضي</li>";
    echo "</ul>";
    echo "<p><strong>تحذير:</strong> سيتم حذف البيانات التالفة الموجودة</p>";
    echo "</div>";
    
    echo "<form method='POST' action=''>";
    echo "<div class='text-center'>";
    echo "<button type='submit' name='fix_all_system' class='btn btn-success btn-lg'>";
    echo "<i class='fas fa-tools'></i> إصلاح جميع مشاكل النظام";
    echo "</button>";
    echo "</div>";
    echo "</form>";
    
    echo "<div class='text-center mt-4'>";
    echo "<a href='test.php' class='btn btn-info me-2'><i class='fas fa-cog'></i> اختبار النظام</a>";
    echo "<a href='test_arabic.php' class='btn btn-warning'><i class='fas fa-language'></i> اختبار الترميز</a>";
    echo "</div>";
}

echo "</div>";
echo "</body>";
echo "</html>";
?>
