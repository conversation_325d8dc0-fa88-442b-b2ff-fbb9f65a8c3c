-- =============================================
-- نظام إدارة الحملة الانتخابية - قاعدة البيانات الكاملة
-- تاريخ الإنشاء: 2024
-- الإصدار: 2.0.0
-- =============================================

SET SQL_MODE = "NO_AUTO_VALUE_ON_ZERO";
START TRANSACTION;
SET time_zone = "+00:00";

-- إعدادات الترميز
/*!40101 SET @OLD_CHARACTER_SET_CLIENT=@@CHARACTER_SET_CLIENT */;
/*!40101 SET @OLD_CHARACTER_SET_RESULTS=@@CHARACTER_SET_RESULTS */;
/*!40101 SET @OLD_COLLATION_CONNECTION=@@COLLATION_CONNECTION */;
/*!40101 SET NAMES utf8mb4 */;

-- =============================================
-- إنشاء قاعدة البيانات
-- =============================================

CREATE DATABASE IF NOT EXISTS `campaign_db` DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
USE `campaign_db`;

-- =============================================
-- 1. جداول المناطق والمستخدمين
-- =============================================

-- جدول المناطق
CREATE TABLE `regions` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `name` varchar(100) NOT NULL,
  `description` text DEFAULT NULL,
  `admin_count` int(11) DEFAULT 0,
  `supporter_count` int(11) DEFAULT 0,
  `status` enum('active','inactive') DEFAULT 'active',
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
  PRIMARY KEY (`id`),
  KEY `idx_name` (`name`),
  KEY `idx_status` (`status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- جدول المستخدمين الرئيسيين
CREATE TABLE `users` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `username` varchar(50) NOT NULL,
  `password` varchar(255) NOT NULL,
  `full_name` varchar(100) NOT NULL,
  `email` varchar(100) DEFAULT NULL,
  `phone` varchar(15) DEFAULT NULL,
  `user_type` enum('candidate','manager','supervisor') DEFAULT 'manager',
  `status` enum('active','inactive') DEFAULT 'active',
  `last_login` datetime DEFAULT NULL,
  `login_attempts` int(11) DEFAULT 0,
  `locked_until` datetime DEFAULT NULL,
  `profile_image` varchar(255) DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
  PRIMARY KEY (`id`),
  UNIQUE KEY `username` (`username`),
  KEY `idx_username` (`username`),
  KEY `idx_user_type` (`user_type`),
  KEY `idx_status` (`status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- جدول الإداريين
CREATE TABLE `admins` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `username` varchar(50) NOT NULL,
  `password` varchar(255) NOT NULL,
  `full_name` varchar(100) NOT NULL,
  `phone` varchar(15) NOT NULL,
  `email` varchar(100) DEFAULT NULL,
  `region_id` int(11) DEFAULT NULL,
  `role` enum('admin','supervisor','coordinator') DEFAULT 'admin',
  `status` enum('active','inactive','suspended') DEFAULT 'active',
  `last_login` datetime DEFAULT NULL,
  `login_attempts` int(11) DEFAULT 0,
  `locked_until` datetime DEFAULT NULL,
  `profile_image` varchar(255) DEFAULT NULL,
  `hire_date` date DEFAULT NULL,
  `salary` decimal(10,2) DEFAULT NULL,
  `notes` text DEFAULT NULL,
  `created_by` int(11) DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
  PRIMARY KEY (`id`),
  UNIQUE KEY `username` (`username`),
  KEY `idx_username` (`username`),
  KEY `idx_region_id` (`region_id`),
  KEY `idx_role` (`role`),
  KEY `idx_status` (`status`),
  KEY `idx_phone` (`phone`),
  CONSTRAINT `admins_ibfk_1` FOREIGN KEY (`region_id`) REFERENCES `regions` (`id`) ON DELETE SET NULL,
  CONSTRAINT `admins_ibfk_2` FOREIGN KEY (`created_by`) REFERENCES `users` (`id`) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- جدول صلاحيات الإداريين
CREATE TABLE `admin_permissions` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `admin_id` int(11) NOT NULL,
  `permission_name` varchar(100) NOT NULL,
  `permission_value` tinyint(1) DEFAULT 1,
  `granted_by` int(11) DEFAULT NULL,
  `granted_at` timestamp NOT NULL DEFAULT current_timestamp(),
  PRIMARY KEY (`id`),
  UNIQUE KEY `unique_admin_permission` (`admin_id`,`permission_name`),
  KEY `idx_admin_id` (`admin_id`),
  KEY `idx_permission_name` (`permission_name`),
  CONSTRAINT `admin_permissions_ibfk_1` FOREIGN KEY (`admin_id`) REFERENCES `admins` (`id`) ON DELETE CASCADE,
  CONSTRAINT `admin_permissions_ibfk_2` FOREIGN KEY (`granted_by`) REFERENCES `users` (`id`) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- جدول جلسات الإداريين
CREATE TABLE `admin_sessions` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `admin_id` int(11) NOT NULL,
  `session_token` varchar(255) NOT NULL,
  `ip_address` varchar(45) DEFAULT NULL,
  `user_agent` text DEFAULT NULL,
  `device_info` varchar(255) DEFAULT NULL,
  `location` varchar(100) DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `expires_at` datetime NOT NULL,
  `is_active` tinyint(1) DEFAULT 1,
  `last_activity` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
  `logout_at` datetime DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `idx_session_token` (`session_token`),
  KEY `idx_admin_id` (`admin_id`),
  KEY `idx_expires_at` (`expires_at`),
  KEY `idx_is_active` (`is_active`),
  CONSTRAINT `admin_sessions_ibfk_1` FOREIGN KEY (`admin_id`) REFERENCES `admins` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- جدول إحصائيات الإداريين اليومية
CREATE TABLE `admin_daily_stats` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `admin_id` int(11) NOT NULL,
  `date` date NOT NULL,
  `supporters_added` int(11) DEFAULT 0,
  `calls_made` int(11) DEFAULT 0,
  `meetings_held` int(11) DEFAULT 0,
  `requests_submitted` int(11) DEFAULT 0,
  `messages_sent` int(11) DEFAULT 0,
  `reports_submitted` int(11) DEFAULT 0,
  `login_count` int(11) DEFAULT 0,
  `active_hours` decimal(4,2) DEFAULT 0.00,
  `first_login` time DEFAULT NULL,
  `last_logout` time DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
  PRIMARY KEY (`id`),
  UNIQUE KEY `unique_admin_date` (`admin_id`,`date`),
  KEY `idx_date` (`date`),
  KEY `idx_admin_id` (`admin_id`),
  CONSTRAINT `admin_daily_stats_ibfk_1` FOREIGN KEY (`admin_id`) REFERENCES `admins` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- =============================================
-- 2. جداول المؤيدين
-- =============================================

-- جدول المؤيدين
CREATE TABLE `supporters` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `full_name` varchar(100) NOT NULL,
  `gender` enum('male','female') NOT NULL,
  `marital_status` enum('single','married','divorced','widowed') DEFAULT 'single',
  `birth_date` date NOT NULL,
  `age` int(11) GENERATED ALWAYS AS (year(curdate()) - year(`birth_date`)) STORED,
  `education` enum('primary','secondary','diploma','bachelor','master','phd','other') DEFAULT 'secondary',
  `profession` varchar(100) DEFAULT NULL,
  `monthly_income` decimal(10,2) DEFAULT NULL,
  `address` text NOT NULL,
  `phone` varchar(15) NOT NULL,
  `alternative_phone` varchar(15) DEFAULT NULL,
  `email` varchar(100) DEFAULT NULL,
  `voter_number` varchar(50) DEFAULT NULL,
  `voting_center` varchar(100) DEFAULT NULL,
  `region_id` int(11) DEFAULT NULL,
  `district` varchar(100) DEFAULT NULL,
  `neighborhood` varchar(100) DEFAULT NULL,
  `family_members` int(11) DEFAULT 1,
  `support_level` enum('strong','moderate','weak','undecided') DEFAULT 'moderate',
  `contact_method` enum('phone','visit','social_media','event') DEFAULT 'phone',
  `last_contact_date` date DEFAULT NULL,
  `next_contact_date` date DEFAULT NULL,
  `notes` text DEFAULT NULL,
  `tags` varchar(255) DEFAULT NULL,
  `added_by` int(11) DEFAULT NULL,
  `status` enum('active','inactive','moved','deceased') DEFAULT 'active',
  `verification_status` enum('pending','verified','rejected') DEFAULT 'pending',
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
  PRIMARY KEY (`id`),
  KEY `idx_phone` (`phone`),
  KEY `idx_voter_number` (`voter_number`),
  KEY `idx_region_id` (`region_id`),
  KEY `idx_added_by` (`added_by`),
  KEY `idx_status` (`status`),
  KEY `idx_support_level` (`support_level`),
  KEY `idx_created_at` (`created_at`),
  FULLTEXT KEY `idx_fulltext_search` (`full_name`,`address`,`profession`,`notes`),
  CONSTRAINT `supporters_ibfk_1` FOREIGN KEY (`region_id`) REFERENCES `regions` (`id`) ON DELETE SET NULL,
  CONSTRAINT `supporters_ibfk_2` FOREIGN KEY (`added_by`) REFERENCES `admins` (`id`) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- جدول مرفقات المؤيدين
CREATE TABLE `supporter_attachments` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `supporter_id` int(11) NOT NULL,
  `attachment_type` enum('voter_id_front','voter_id_back','national_id_front','national_id_back','residence_card','passport','birth_certificate','other') NOT NULL,
  `file_name` varchar(255) NOT NULL,
  `original_name` varchar(255) NOT NULL,
  `file_path` varchar(500) NOT NULL,
  `file_size` int(11) NOT NULL,
  `file_type` varchar(100) NOT NULL,
  `mime_type` varchar(100) DEFAULT NULL,
  `uploaded_by` int(11) DEFAULT NULL,
  `upload_date` timestamp NOT NULL DEFAULT current_timestamp(),
  `notes` text DEFAULT NULL,
  `status` enum('pending','approved','rejected') DEFAULT 'pending',
  `reviewed_by` int(11) DEFAULT NULL,
  `reviewed_at` datetime DEFAULT NULL,
  `review_notes` text DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `idx_supporter_id` (`supporter_id`),
  KEY `idx_attachment_type` (`attachment_type`),
  KEY `idx_status` (`status`),
  KEY `idx_upload_date` (`upload_date`),
  CONSTRAINT `supporter_attachments_ibfk_1` FOREIGN KEY (`supporter_id`) REFERENCES `supporters` (`id`) ON DELETE CASCADE,
  CONSTRAINT `supporter_attachments_ibfk_2` FOREIGN KEY (`uploaded_by`) REFERENCES `admins` (`id`) ON DELETE SET NULL,
  CONSTRAINT `supporter_attachments_ibfk_3` FOREIGN KEY (`reviewed_by`) REFERENCES `users` (`id`) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- جدول تاريخ الاتصالات مع المؤيدين
CREATE TABLE `supporter_contacts` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `supporter_id` int(11) NOT NULL,
  `admin_id` int(11) NOT NULL,
  `contact_type` enum('phone_call','visit','message','event','social_media') NOT NULL,
  `contact_date` datetime NOT NULL,
  `duration_minutes` int(11) DEFAULT 0,
  `result` enum('successful','no_answer','busy','refused','wrong_number') DEFAULT 'successful',
  `notes` text DEFAULT NULL,
  `follow_up_required` tinyint(1) DEFAULT 0,
  `follow_up_date` date DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  PRIMARY KEY (`id`),
  KEY `idx_supporter_id` (`supporter_id`),
  KEY `idx_admin_id` (`admin_id`),
  KEY `idx_contact_date` (`contact_date`),
  KEY `idx_contact_type` (`contact_type`),
  CONSTRAINT `supporter_contacts_ibfk_1` FOREIGN KEY (`supporter_id`) REFERENCES `supporters` (`id`) ON DELETE CASCADE,
  CONSTRAINT `supporter_contacts_ibfk_2` FOREIGN KEY (`admin_id`) REFERENCES `admins` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- جدول عائلات المؤيدين
CREATE TABLE `supporter_families` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `supporter_id` int(11) NOT NULL,
  `family_member_name` varchar(100) NOT NULL,
  `relationship` enum('spouse','son','daughter','father','mother','brother','sister','other') NOT NULL,
  `age` int(11) DEFAULT NULL,
  `gender` enum('male','female') DEFAULT NULL,
  `is_voter` tinyint(1) DEFAULT 0,
  `voter_number` varchar(50) DEFAULT NULL,
  `support_level` enum('strong','moderate','weak','opposed','undecided') DEFAULT 'undecided',
  `notes` text DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  PRIMARY KEY (`id`),
  KEY `idx_supporter_id` (`supporter_id`),
  KEY `idx_is_voter` (`is_voter`),
  KEY `idx_support_level` (`support_level`),
  CONSTRAINT `supporter_families_ibfk_1` FOREIGN KEY (`supporter_id`) REFERENCES `supporters` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- جدول مجموعات المؤيدين
CREATE TABLE `supporter_groups` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `name` varchar(100) NOT NULL,
  `description` text DEFAULT NULL,
  `group_type` enum('region','profession','age','interest','custom') DEFAULT 'custom',
  `created_by` int(11) DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  PRIMARY KEY (`id`),
  KEY `idx_group_type` (`group_type`),
  KEY `idx_created_by` (`created_by`),
  CONSTRAINT `supporter_groups_ibfk_1` FOREIGN KEY (`created_by`) REFERENCES `admins` (`id`) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- جدول ربط المؤيدين بالمجموعات
CREATE TABLE `supporter_group_members` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `group_id` int(11) NOT NULL,
  `supporter_id` int(11) NOT NULL,
  `added_by` int(11) DEFAULT NULL,
  `added_at` timestamp NOT NULL DEFAULT current_timestamp(),
  PRIMARY KEY (`id`),
  UNIQUE KEY `unique_group_supporter` (`group_id`,`supporter_id`),
  KEY `idx_group_id` (`group_id`),
  KEY `idx_supporter_id` (`supporter_id`),
  CONSTRAINT `supporter_group_members_ibfk_1` FOREIGN KEY (`group_id`) REFERENCES `supporter_groups` (`id`) ON DELETE CASCADE,
  CONSTRAINT `supporter_group_members_ibfk_2` FOREIGN KEY (`supporter_id`) REFERENCES `supporters` (`id`) ON DELETE CASCADE,
  CONSTRAINT `supporter_group_members_ibfk_3` FOREIGN KEY (`added_by`) REFERENCES `admins` (`id`) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- =============================================
-- 3. جداول الرسائل
-- =============================================

-- جدول الرسائل
CREATE TABLE `messages` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `sender_type` enum('admin','management','candidate') NOT NULL,
  `sender_id` int(11) NOT NULL,
  `receiver_type` enum('admin','management','candidate','all_admins') NOT NULL,
  `receiver_id` int(11) DEFAULT NULL,
  `subject` varchar(200) NOT NULL,
  `message` text NOT NULL,
  `message_type` enum('personal','announcement','urgent','system') DEFAULT 'personal',
  `priority` enum('low','normal','high','urgent') DEFAULT 'normal',
  `status` enum('unread','read','archived','deleted') DEFAULT 'unread',
  `is_broadcast` tinyint(1) DEFAULT 0,
  `scheduled_at` datetime DEFAULT NULL,
  `expires_at` datetime DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `read_at` datetime DEFAULT NULL,
  `replied_at` datetime DEFAULT NULL,
  `parent_message_id` int(11) DEFAULT NULL,
  `thread_id` varchar(50) DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `idx_sender` (`sender_type`,`sender_id`),
  KEY `idx_receiver` (`receiver_type`,`receiver_id`),
  KEY `idx_status` (`status`),
  KEY `idx_priority` (`priority`),
  KEY `idx_created_at` (`created_at`),
  KEY `idx_thread_id` (`thread_id`),
  FULLTEXT KEY `idx_fulltext_search` (`subject`,`message`),
  CONSTRAINT `messages_ibfk_1` FOREIGN KEY (`parent_message_id`) REFERENCES `messages` (`id`) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- جدول مرفقات الرسائل
CREATE TABLE `message_attachments` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `message_id` int(11) NOT NULL,
  `file_name` varchar(255) NOT NULL,
  `original_name` varchar(255) NOT NULL,
  `file_path` varchar(500) NOT NULL,
  `file_size` int(11) NOT NULL,
  `file_type` varchar(100) NOT NULL,
  `mime_type` varchar(100) DEFAULT NULL,
  `attachment_type` enum('document','image','video','audio','archive','other') DEFAULT 'document',
  `is_inline` tinyint(1) DEFAULT 0,
  `download_count` int(11) DEFAULT 0,
  `upload_date` timestamp NOT NULL DEFAULT current_timestamp(),
  PRIMARY KEY (`id`),
  KEY `idx_message_id` (`message_id`),
  KEY `idx_file_type` (`file_type`),
  KEY `idx_attachment_type` (`attachment_type`),
  CONSTRAINT `message_attachments_ibfk_1` FOREIGN KEY (`message_id`) REFERENCES `messages` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- جدول قراءة الرسائل
CREATE TABLE `message_reads` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `message_id` int(11) NOT NULL,
  `reader_type` enum('admin','management','candidate') NOT NULL,
  `reader_id` int(11) NOT NULL,
  `read_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `ip_address` varchar(45) DEFAULT NULL,
  `user_agent` text DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `unique_message_reader` (`message_id`,`reader_type`,`reader_id`),
  KEY `idx_message_id` (`message_id`),
  KEY `idx_reader` (`reader_type`,`reader_id`),
  KEY `idx_read_at` (`read_at`),
  CONSTRAINT `message_reads_ibfk_1` FOREIGN KEY (`message_id`) REFERENCES `messages` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- جدول قوالب الرسائل
CREATE TABLE `message_templates` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `name` varchar(100) NOT NULL,
  `subject` varchar(200) NOT NULL,
  `content` text NOT NULL,
  `template_type` enum('welcome','reminder','announcement','report_request','custom') DEFAULT 'custom',
  `variables` json DEFAULT NULL,
  `is_active` tinyint(1) DEFAULT 1,
  `created_by` int(11) DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
  PRIMARY KEY (`id`),
  KEY `idx_template_type` (`template_type`),
  KEY `idx_is_active` (`is_active`),
  KEY `idx_created_by` (`created_by`),
  CONSTRAINT `message_templates_ibfk_1` FOREIGN KEY (`created_by`) REFERENCES `users` (`id`) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- جدول مجلدات الرسائل
CREATE TABLE `message_folders` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `user_type` enum('admin','management','candidate') NOT NULL,
  `user_id` int(11) NOT NULL,
  `folder_name` varchar(100) NOT NULL,
  `folder_type` enum('inbox','sent','drafts','archive','trash','custom') DEFAULT 'custom',
  `color` varchar(7) DEFAULT '#007bff',
  `icon` varchar(50) DEFAULT 'fas fa-folder',
  `message_count` int(11) DEFAULT 0,
  `unread_count` int(11) DEFAULT 0,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  PRIMARY KEY (`id`),
  KEY `idx_user` (`user_type`,`user_id`),
  KEY `idx_folder_type` (`folder_type`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- جدول ربط الرسائل بالمجلدات
CREATE TABLE `message_folder_items` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `folder_id` int(11) NOT NULL,
  `message_id` int(11) NOT NULL,
  `added_at` timestamp NOT NULL DEFAULT current_timestamp(),
  PRIMARY KEY (`id`),
  UNIQUE KEY `unique_folder_message` (`folder_id`,`message_id`),
  KEY `idx_folder_id` (`folder_id`),
  KEY `idx_message_id` (`message_id`),
  CONSTRAINT `message_folder_items_ibfk_1` FOREIGN KEY (`folder_id`) REFERENCES `message_folders` (`id`) ON DELETE CASCADE,
  CONSTRAINT `message_folder_items_ibfk_2` FOREIGN KEY (`message_id`) REFERENCES `messages` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- =============================================
-- 4. جداول المطالب
-- =============================================

-- جدول مطالب المؤيدين
CREATE TABLE `supporter_requests` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `request_number` varchar(20) NOT NULL,
  `admin_id` int(11) NOT NULL,
  `supporter_id` int(11) DEFAULT NULL,
  `supporter_name` varchar(100) NOT NULL,
  `supporter_phone` varchar(15) NOT NULL,
  `supporter_address` text DEFAULT NULL,
  `request_type` enum('financial','medical','educational','employment','housing','legal','social','infrastructure','documentation','other') NOT NULL,
  `category` enum('urgent','important','normal','low') DEFAULT 'normal',
  `title` varchar(200) NOT NULL,
  `description` text NOT NULL,
  `amount_requested` decimal(10,2) DEFAULT NULL,
  `currency` enum('IQD','USD') DEFAULT 'IQD',
  `priority` enum('low','normal','high','urgent') DEFAULT 'normal',
  `status` enum('pending','received','under_review','in_progress','completed','rejected','cancelled') DEFAULT 'pending',
  `urgency_level` enum('low','medium','high','critical') DEFAULT 'medium',
  `expected_completion_date` date DEFAULT NULL,
  `actual_completion_date` date DEFAULT NULL,
  `management_response` text DEFAULT NULL,
  `rejection_reason` text DEFAULT NULL,
  `management_notes` text DEFAULT NULL,
  `follow_up_required` tinyint(1) DEFAULT 0,
  `follow_up_date` date DEFAULT NULL,
  `beneficiary_count` int(11) DEFAULT 1,
  `location_details` text DEFAULT NULL,
  `contact_person` varchar(100) DEFAULT NULL,
  `contact_phone` varchar(15) DEFAULT NULL,
  `submitted_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `reviewed_at` datetime DEFAULT NULL,
  `completed_at` datetime DEFAULT NULL,
  `reviewed_by` int(11) DEFAULT NULL,
  `approved_by` int(11) DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
  PRIMARY KEY (`id`),
  UNIQUE KEY `request_number` (`request_number`),
  KEY `idx_admin_id` (`admin_id`),
  KEY `idx_supporter_id` (`supporter_id`),
  KEY `idx_status` (`status`),
  KEY `idx_request_type` (`request_type`),
  KEY `idx_priority` (`priority`),
  KEY `idx_submitted_at` (`submitted_at`),
  FULLTEXT KEY `idx_fulltext_search` (`title`,`description`,`supporter_name`),
  CONSTRAINT `supporter_requests_ibfk_1` FOREIGN KEY (`admin_id`) REFERENCES `admins` (`id`) ON DELETE CASCADE,
  CONSTRAINT `supporter_requests_ibfk_2` FOREIGN KEY (`supporter_id`) REFERENCES `supporters` (`id`) ON DELETE SET NULL,
  CONSTRAINT `supporter_requests_ibfk_3` FOREIGN KEY (`reviewed_by`) REFERENCES `users` (`id`) ON DELETE SET NULL,
  CONSTRAINT `supporter_requests_ibfk_4` FOREIGN KEY (`approved_by`) REFERENCES `users` (`id`) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- جدول مرفقات المطالب
CREATE TABLE `request_attachments` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `request_id` int(11) NOT NULL,
  `file_name` varchar(255) NOT NULL,
  `original_name` varchar(255) NOT NULL,
  `file_path` varchar(500) NOT NULL,
  `file_size` int(11) NOT NULL,
  `file_type` varchar(100) NOT NULL,
  `mime_type` varchar(100) DEFAULT NULL,
  `uploaded_by` int(11) DEFAULT NULL,
  `upload_date` timestamp NOT NULL DEFAULT current_timestamp(),
  `attachment_type` enum('document','image','medical_report','certificate','invoice','id_copy','proof','other') DEFAULT 'document',
  `description` text DEFAULT NULL,
  `is_required` tinyint(1) DEFAULT 0,
  `status` enum('pending','approved','rejected') DEFAULT 'pending',
  `reviewed_by` int(11) DEFAULT NULL,
  `reviewed_at` datetime DEFAULT NULL,
  `review_notes` text DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `idx_request_id` (`request_id`),
  KEY `idx_attachment_type` (`attachment_type`),
  KEY `idx_status` (`status`),
  KEY `idx_upload_date` (`upload_date`),
  CONSTRAINT `request_attachments_ibfk_1` FOREIGN KEY (`request_id`) REFERENCES `supporter_requests` (`id`) ON DELETE CASCADE,
  CONSTRAINT `request_attachments_ibfk_2` FOREIGN KEY (`uploaded_by`) REFERENCES `admins` (`id`) ON DELETE SET NULL,
  CONSTRAINT `request_attachments_ibfk_3` FOREIGN KEY (`reviewed_by`) REFERENCES `users` (`id`) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- جدول تاريخ حالات المطالب
CREATE TABLE `request_status_history` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `request_id` int(11) NOT NULL,
  `old_status` enum('pending','received','under_review','in_progress','completed','rejected','cancelled') DEFAULT NULL,
  `new_status` enum('pending','received','under_review','in_progress','completed','rejected','cancelled') NOT NULL,
  `changed_by` int(11) NOT NULL,
  `change_reason` text DEFAULT NULL,
  `notes` text DEFAULT NULL,
  `changed_at` timestamp NOT NULL DEFAULT current_timestamp(),
  PRIMARY KEY (`id`),
  KEY `idx_request_id` (`request_id`),
  KEY `idx_new_status` (`new_status`),
  KEY `idx_changed_at` (`changed_at`),
  CONSTRAINT `request_status_history_ibfk_1` FOREIGN KEY (`request_id`) REFERENCES `supporter_requests` (`id`) ON DELETE CASCADE,
  CONSTRAINT `request_status_history_ibfk_2` FOREIGN KEY (`changed_by`) REFERENCES `users` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- جدول تعليقات المطالب
CREATE TABLE `request_comments` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `request_id` int(11) NOT NULL,
  `commenter_type` enum('admin','management','candidate') NOT NULL,
  `commenter_id` int(11) NOT NULL,
  `comment` text NOT NULL,
  `comment_type` enum('note','question','update','approval','rejection') DEFAULT 'note',
  `is_internal` tinyint(1) DEFAULT 0,
  `parent_comment_id` int(11) DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
  PRIMARY KEY (`id`),
  KEY `idx_request_id` (`request_id`),
  KEY `idx_commenter` (`commenter_type`,`commenter_id`),
  KEY `idx_comment_type` (`comment_type`),
  KEY `idx_created_at` (`created_at`),
  CONSTRAINT `request_comments_ibfk_1` FOREIGN KEY (`request_id`) REFERENCES `supporter_requests` (`id`) ON DELETE CASCADE,
  CONSTRAINT `request_comments_ibfk_2` FOREIGN KEY (`parent_comment_id`) REFERENCES `request_comments` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- جدول فئات المطالب
CREATE TABLE `request_categories` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `name` varchar(100) NOT NULL,
  `description` text DEFAULT NULL,
  `parent_category_id` int(11) DEFAULT NULL,
  `icon` varchar(50) DEFAULT 'fas fa-folder',
  `color` varchar(7) DEFAULT '#007bff',
  `is_active` tinyint(1) DEFAULT 1,
  `sort_order` int(11) DEFAULT 0,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  PRIMARY KEY (`id`),
  KEY `idx_parent_category_id` (`parent_category_id`),
  KEY `idx_is_active` (`is_active`),
  KEY `idx_sort_order` (`sort_order`),
  CONSTRAINT `request_categories_ibfk_1` FOREIGN KEY (`parent_category_id`) REFERENCES `request_categories` (`id`) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- جدول إحصائيات المطالب
CREATE TABLE `request_statistics` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `date` date NOT NULL,
  `admin_id` int(11) DEFAULT NULL,
  `region_id` int(11) DEFAULT NULL,
  `total_requests` int(11) DEFAULT 0,
  `pending_requests` int(11) DEFAULT 0,
  `completed_requests` int(11) DEFAULT 0,
  `rejected_requests` int(11) DEFAULT 0,
  `total_amount` decimal(12,2) DEFAULT 0.00,
  `approved_amount` decimal(12,2) DEFAULT 0.00,
  `average_processing_days` decimal(5,2) DEFAULT 0.00,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
  PRIMARY KEY (`id`),
  UNIQUE KEY `unique_date_admin_region` (`date`,`admin_id`,`region_id`),
  KEY `idx_date` (`date`),
  KEY `idx_admin_id` (`admin_id`),
  KEY `idx_region_id` (`region_id`),
  CONSTRAINT `request_statistics_ibfk_1` FOREIGN KEY (`admin_id`) REFERENCES `admins` (`id`) ON DELETE SET NULL,
  CONSTRAINT `request_statistics_ibfk_2` FOREIGN KEY (`region_id`) REFERENCES `regions` (`id`) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- =============================================
-- 5. جداول التقارير
-- =============================================

-- جدول التقارير الأسبوعية
CREATE TABLE `weekly_reports` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `report_number` varchar(20) NOT NULL,
  `admin_id` int(11) NOT NULL,
  `week_start` date NOT NULL,
  `week_end` date NOT NULL,
  `report_period` varchar(50) NOT NULL,
  `supporters_added` int(11) DEFAULT 0,
  `supporters_contacted` int(11) DEFAULT 0,
  `supporters_visited` int(11) DEFAULT 0,
  `new_families_reached` int(11) DEFAULT 0,
  `events_attended` int(11) DEFAULT 0,
  `meetings_held` int(11) DEFAULT 0,
  `calls_made` int(11) DEFAULT 0,
  `visits_conducted` int(11) DEFAULT 0,
  `requests_submitted` int(11) DEFAULT 0,
  `requests_followed_up` int(11) DEFAULT 0,
  `requests_completed` int(11) DEFAULT 0,
  `messages_sent` int(11) DEFAULT 0,
  `messages_received` int(11) DEFAULT 0,
  `achievements` text DEFAULT NULL,
  `challenges` text DEFAULT NULL,
  `solutions_implemented` text DEFAULT NULL,
  `next_week_plans` text DEFAULT NULL,
  `recommendations` text DEFAULT NULL,
  `notes` text DEFAULT NULL,
  `working_hours` decimal(5,2) DEFAULT 0.00,
  `overtime_hours` decimal(5,2) DEFAULT 0.00,
  `travel_distance_km` decimal(8,2) DEFAULT 0.00,
  `fuel_cost` decimal(10,2) DEFAULT 0.00,
  `other_expenses` decimal(10,2) DEFAULT 0.00,
  `status` enum('draft','submitted','under_review','approved','rejected','revision_required') DEFAULT 'draft',
  `priority` enum('low','normal','high','urgent') DEFAULT 'normal',
  `submitted_at` datetime DEFAULT NULL,
  `reviewed_at` datetime DEFAULT NULL,
  `approved_at` datetime DEFAULT NULL,
  `reviewed_by` int(11) DEFAULT NULL,
  `approved_by` int(11) DEFAULT NULL,
  `review_notes` text DEFAULT NULL,
  `rejection_reason` text DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
  PRIMARY KEY (`id`),
  UNIQUE KEY `report_number` (`report_number`),
  KEY `idx_admin_id` (`admin_id`),
  KEY `idx_week_start` (`week_start`),
  KEY `idx_status` (`status`),
  UNIQUE KEY `unique_admin_week` (`admin_id`,`week_start`),
  CONSTRAINT `weekly_reports_ibfk_1` FOREIGN KEY (`admin_id`) REFERENCES `admins` (`id`) ON DELETE CASCADE,
  CONSTRAINT `weekly_reports_ibfk_2` FOREIGN KEY (`reviewed_by`) REFERENCES `users` (`id`) ON DELETE SET NULL,
  CONSTRAINT `weekly_reports_ibfk_3` FOREIGN KEY (`approved_by`) REFERENCES `users` (`id`) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- جدول مرفقات التقارير
CREATE TABLE `report_attachments` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `report_id` int(11) NOT NULL,
  `file_name` varchar(255) NOT NULL,
  `original_name` varchar(255) NOT NULL,
  `file_path` varchar(500) NOT NULL,
  `file_size` int(11) NOT NULL,
  `file_type` varchar(100) NOT NULL,
  `mime_type` varchar(100) DEFAULT NULL,
  `attachment_type` enum('photo','document','certificate','receipt','evidence','other') DEFAULT 'document',
  `description` text DEFAULT NULL,
  `upload_date` timestamp NOT NULL DEFAULT current_timestamp(),
  `uploaded_by` int(11) DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `idx_report_id` (`report_id`),
  KEY `idx_attachment_type` (`attachment_type`),
  KEY `idx_upload_date` (`upload_date`),
  CONSTRAINT `report_attachments_ibfk_1` FOREIGN KEY (`report_id`) REFERENCES `weekly_reports` (`id`) ON DELETE CASCADE,
  CONSTRAINT `report_attachments_ibfk_2` FOREIGN KEY (`uploaded_by`) REFERENCES `admins` (`id`) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- جدول قوالب التقارير
CREATE TABLE `report_templates` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `name` varchar(100) NOT NULL,
  `description` text DEFAULT NULL,
  `template_type` enum('weekly','monthly','quarterly','annual','special','custom') DEFAULT 'weekly',
  `template_content` json DEFAULT NULL,
  `fields_config` json DEFAULT NULL,
  `is_active` tinyint(1) DEFAULT 1,
  `is_default` tinyint(1) DEFAULT 0,
  `created_by` int(11) DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
  PRIMARY KEY (`id`),
  KEY `idx_template_type` (`template_type`),
  KEY `idx_is_active` (`is_active`),
  KEY `idx_created_by` (`created_by`),
  CONSTRAINT `report_templates_ibfk_1` FOREIGN KEY (`created_by`) REFERENCES `users` (`id`) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- جدول تعليقات التقارير
CREATE TABLE `report_comments` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `report_id` int(11) NOT NULL,
  `commenter_type` enum('admin','management','candidate') NOT NULL,
  `commenter_id` int(11) NOT NULL,
  `comment` text NOT NULL,
  `comment_type` enum('note','question','suggestion','approval','rejection','revision') DEFAULT 'note',
  `is_internal` tinyint(1) DEFAULT 0,
  `parent_comment_id` int(11) DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
  PRIMARY KEY (`id`),
  KEY `idx_report_id` (`report_id`),
  KEY `idx_commenter` (`commenter_type`,`commenter_id`),
  KEY `idx_comment_type` (`comment_type`),
  KEY `idx_created_at` (`created_at`),
  CONSTRAINT `report_comments_ibfk_1` FOREIGN KEY (`report_id`) REFERENCES `weekly_reports` (`id`) ON DELETE CASCADE,
  CONSTRAINT `report_comments_ibfk_2` FOREIGN KEY (`parent_comment_id`) REFERENCES `report_comments` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- جدول إحصائيات التقارير
CREATE TABLE `report_statistics` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `period_type` enum('weekly','monthly','quarterly','yearly') NOT NULL,
  `period_start` date NOT NULL,
  `period_end` date NOT NULL,
  `admin_id` int(11) DEFAULT NULL,
  `region_id` int(11) DEFAULT NULL,
  `total_reports` int(11) DEFAULT 0,
  `submitted_reports` int(11) DEFAULT 0,
  `approved_reports` int(11) DEFAULT 0,
  `rejected_reports` int(11) DEFAULT 0,
  `pending_reports` int(11) DEFAULT 0,
  `total_supporters_added` int(11) DEFAULT 0,
  `total_supporters_contacted` int(11) DEFAULT 0,
  `total_families_reached` int(11) DEFAULT 0,
  `total_events_attended` int(11) DEFAULT 0,
  `total_meetings_held` int(11) DEFAULT 0,
  `total_calls_made` int(11) DEFAULT 0,
  `total_visits_conducted` int(11) DEFAULT 0,
  `total_requests_submitted` int(11) DEFAULT 0,
  `total_requests_completed` int(11) DEFAULT 0,
  `total_working_hours` decimal(8,2) DEFAULT 0.00,
  `total_overtime_hours` decimal(8,2) DEFAULT 0.00,
  `total_travel_distance` decimal(10,2) DEFAULT 0.00,
  `total_expenses` decimal(12,2) DEFAULT 0.00,
  `avg_supporters_per_admin` decimal(8,2) DEFAULT 0.00,
  `avg_requests_per_admin` decimal(8,2) DEFAULT 0.00,
  `avg_working_hours_per_admin` decimal(8,2) DEFAULT 0.00,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
  PRIMARY KEY (`id`),
  UNIQUE KEY `unique_period_admin_region` (`period_type`,`period_start`,`admin_id`,`region_id`),
  KEY `idx_period` (`period_type`,`period_start`),
  KEY `idx_admin_id` (`admin_id`),
  KEY `idx_region_id` (`region_id`),
  CONSTRAINT `report_statistics_ibfk_1` FOREIGN KEY (`admin_id`) REFERENCES `admins` (`id`) ON DELETE SET NULL,
  CONSTRAINT `report_statistics_ibfk_2` FOREIGN KEY (`region_id`) REFERENCES `regions` (`id`) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- =============================================
-- 6. جداول الإشعارات وسجل الأنشطة
-- =============================================

-- جدول الإشعارات
CREATE TABLE `notifications` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `notification_uuid` varchar(36) NOT NULL,
  `user_type` enum('admin','management','candidate','all') NOT NULL,
  `user_id` int(11) DEFAULT NULL,
  `title` varchar(200) NOT NULL,
  `message` text NOT NULL,
  `short_message` varchar(255) DEFAULT NULL,
  `type` enum('info','success','warning','error','new_supporter','new_request','new_message','new_report','system','reminder','urgent') DEFAULT 'info',
  `category` enum('system','supporter','request','message','report','admin','general') DEFAULT 'general',
  `priority` enum('low','normal','high','urgent') DEFAULT 'normal',
  `related_type` enum('supporter','request','message','report','admin','user','system') DEFAULT NULL,
  `related_id` int(11) DEFAULT NULL,
  `related_data` json DEFAULT NULL,
  `is_read` tinyint(1) DEFAULT 0,
  `is_archived` tinyint(1) DEFAULT 0,
  `is_pinned` tinyint(1) DEFAULT 0,
  `requires_action` tinyint(1) DEFAULT 0,
  `action_url` varchar(255) DEFAULT NULL,
  `action_text` varchar(100) DEFAULT NULL,
  `secondary_action_url` varchar(255) DEFAULT NULL,
  `secondary_action_text` varchar(100) DEFAULT NULL,
  `icon` varchar(50) DEFAULT 'fas fa-bell',
  `color` varchar(7) DEFAULT '#007bff',
  `image_url` varchar(255) DEFAULT NULL,
  `scheduled_at` datetime DEFAULT NULL,
  `expires_at` datetime DEFAULT NULL,
  `read_at` datetime DEFAULT NULL,
  `archived_at` datetime DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `sender_type` enum('system','admin','management','candidate') DEFAULT 'system',
  `sender_id` int(11) DEFAULT NULL,
  `device_tokens` json DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `notification_uuid` (`notification_uuid`),
  KEY `idx_user` (`user_type`,`user_id`),
  KEY `idx_is_read` (`is_read`),
  KEY `idx_type` (`type`),
  KEY `idx_category` (`category`),
  KEY `idx_priority` (`priority`),
  KEY `idx_created_at` (`created_at`),
  KEY `idx_related` (`related_type`,`related_id`),
  KEY `idx_scheduled_at` (`scheduled_at`),
  KEY `idx_expires_at` (`expires_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- جدول سجل الأنشطة
CREATE TABLE `activity_log` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `activity_uuid` varchar(36) NOT NULL,
  `user_type` enum('admin','management','candidate','system') NOT NULL,
  `user_id` int(11) DEFAULT NULL,
  `user_name` varchar(100) DEFAULT NULL,
  `action_type` enum('create','update','delete','login','logout','view','export','import','approve','reject','send','receive') NOT NULL,
  `target_type` enum('supporter','request','message','report','admin','user','system','file','setting') NOT NULL,
  `target_id` int(11) DEFAULT NULL,
  `target_name` varchar(200) DEFAULT NULL,
  `description` text NOT NULL,
  `details` json DEFAULT NULL,
  `old_values` json DEFAULT NULL,
  `new_values` json DEFAULT NULL,
  `session_id` varchar(255) DEFAULT NULL,
  `ip_address` varchar(45) DEFAULT NULL,
  `user_agent` text DEFAULT NULL,
  `device_info` varchar(255) DEFAULT NULL,
  `browser_info` varchar(255) DEFAULT NULL,
  `location` varchar(100) DEFAULT NULL,
  `severity` enum('low','medium','high','critical') DEFAULT 'medium',
  `status` enum('success','failed','pending','cancelled') DEFAULT 'success',
  `duration_ms` int(11) DEFAULT 0,
  `category` enum('authentication','data_modification','file_operation','communication','system','security') DEFAULT 'data_modification',
  `tags` varchar(255) DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  PRIMARY KEY (`id`),
  UNIQUE KEY `activity_uuid` (`activity_uuid`),
  KEY `idx_user` (`user_type`,`user_id`),
  KEY `idx_action_type` (`action_type`),
  KEY `idx_target` (`target_type`,`target_id`),
  KEY `idx_created_at` (`created_at`),
  KEY `idx_ip_address` (`ip_address`),
  KEY `idx_severity` (`severity`),
  KEY `idx_category` (`category`),
  KEY `idx_session_id` (`session_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- جدول إعدادات الإشعارات للمستخدمين
CREATE TABLE `notification_settings` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `user_type` enum('admin','management','candidate') NOT NULL,
  `user_id` int(11) NOT NULL,
  `email_notifications` tinyint(1) DEFAULT 1,
  `sms_notifications` tinyint(1) DEFAULT 0,
  `push_notifications` tinyint(1) DEFAULT 1,
  `desktop_notifications` tinyint(1) DEFAULT 1,
  `new_supporter_notifications` tinyint(1) DEFAULT 1,
  `new_request_notifications` tinyint(1) DEFAULT 1,
  `new_message_notifications` tinyint(1) DEFAULT 1,
  `new_report_notifications` tinyint(1) DEFAULT 1,
  `system_notifications` tinyint(1) DEFAULT 1,
  `reminder_notifications` tinyint(1) DEFAULT 1,
  `urgent_notifications` tinyint(1) DEFAULT 1,
  `quiet_hours_start` time DEFAULT '22:00:00',
  `quiet_hours_end` time DEFAULT '08:00:00',
  `weekend_notifications` tinyint(1) DEFAULT 0,
  `digest_frequency` enum('immediate','hourly','daily','weekly') DEFAULT 'immediate',
  `max_notifications_per_hour` int(11) DEFAULT 10,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
  PRIMARY KEY (`id`),
  UNIQUE KEY `unique_user_settings` (`user_type`,`user_id`),
  KEY `idx_user` (`user_type`,`user_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- جدول قوالب الإشعارات
CREATE TABLE `notification_templates` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `name` varchar(100) NOT NULL,
  `description` text DEFAULT NULL,
  `template_key` varchar(100) NOT NULL,
  `title_template` varchar(200) NOT NULL,
  `message_template` text NOT NULL,
  `short_message_template` varchar(255) DEFAULT NULL,
  `type` enum('info','success','warning','error','new_supporter','new_request','new_message','new_report','system','reminder','urgent') DEFAULT 'info',
  `category` enum('system','supporter','request','message','report','admin','general') DEFAULT 'general',
  `priority` enum('low','normal','high','urgent') DEFAULT 'normal',
  `variables` json DEFAULT NULL,
  `icon` varchar(50) DEFAULT 'fas fa-bell',
  `color` varchar(7) DEFAULT '#007bff',
  `is_active` tinyint(1) DEFAULT 1,
  `is_system` tinyint(1) DEFAULT 0,
  `created_by` int(11) DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
  PRIMARY KEY (`id`),
  UNIQUE KEY `template_key` (`template_key`),
  KEY `idx_type` (`type`),
  KEY `idx_category` (`category`),
  KEY `idx_is_active` (`is_active`),
  CONSTRAINT `notification_templates_ibfk_1` FOREIGN KEY (`created_by`) REFERENCES `users` (`id`) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- جدول إحصائيات الإشعارات
CREATE TABLE `notification_statistics` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `date` date NOT NULL,
  `user_type` enum('admin','management','candidate','all') NOT NULL,
  `user_id` int(11) DEFAULT NULL,
  `total_sent` int(11) DEFAULT 0,
  `total_read` int(11) DEFAULT 0,
  `total_unread` int(11) DEFAULT 0,
  `total_archived` int(11) DEFAULT 0,
  `info_notifications` int(11) DEFAULT 0,
  `success_notifications` int(11) DEFAULT 0,
  `warning_notifications` int(11) DEFAULT 0,
  `error_notifications` int(11) DEFAULT 0,
  `urgent_notifications` int(11) DEFAULT 0,
  `supporter_notifications` int(11) DEFAULT 0,
  `request_notifications` int(11) DEFAULT 0,
  `message_notifications` int(11) DEFAULT 0,
  `report_notifications` int(11) DEFAULT 0,
  `system_notifications` int(11) DEFAULT 0,
  `read_rate` decimal(5,2) DEFAULT 0.00,
  `response_time_avg_minutes` decimal(8,2) DEFAULT 0.00,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
  PRIMARY KEY (`id`),
  UNIQUE KEY `unique_date_user` (`date`,`user_type`,`user_id`),
  KEY `idx_date` (`date`),
  KEY `idx_user` (`user_type`,`user_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- جدول إعدادات النظام
CREATE TABLE `system_settings` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `setting_key` varchar(100) NOT NULL,
  `setting_value` text DEFAULT NULL,
  `setting_type` enum('string','number','boolean','json','text') DEFAULT 'string',
  `description` text DEFAULT NULL,
  `category` varchar(50) DEFAULT 'general',
  `is_public` tinyint(1) DEFAULT 0,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
  PRIMARY KEY (`id`),
  UNIQUE KEY `setting_key` (`setting_key`),
  KEY `idx_category` (`category`),
  KEY `idx_is_public` (`is_public`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- =============================================
-- 7. إدراج البيانات الافتراضية
-- =============================================

-- إدراج المناطق الافتراضية
INSERT INTO `regions` (`name`, `description`, `status`) VALUES
('بغداد - الكرخ', 'منطقة الكرخ في بغداد', 'active'),
('بغداد - الرصافة', 'منطقة الرصافة في بغداد', 'active'),
('البصرة', 'محافظة البصرة', 'active'),
('أربيل', 'محافظة أربيل', 'active'),
('الموصل', 'محافظة نينوى - الموصل', 'active'),
('النجف', 'محافظة النجف الأشرف', 'active'),
('كربلاء', 'محافظة كربلاء المقدسة', 'active'),
('الأنبار', 'محافظة الأنبار', 'active'),
('بابل', 'محافظة بابل', 'active'),
('ديالى', 'محافظة ديالى', 'active'),
('ذي قار', 'محافظة ذي قار', 'active'),
('المثنى', 'محافظة المثنى', 'active'),
('القادسية', 'محافظة القادسية', 'active'),
('واسط', 'محافظة واسط', 'active'),
('صلاح الدين', 'محافظة صلاح الدين', 'active'),
('كركوك', 'محافظة كركوك', 'active'),
('دهوك', 'محافظة دهوك', 'active'),
('السليمانية', 'محافظة السليمانية', 'active');

-- إدراج المستخدم الرئيسي (المرشح)
INSERT INTO `users` (`username`, `password`, `full_name`, `email`, `phone`, `user_type`, `status`) VALUES
('candidate', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'المرشح الرئيسي', '<EMAIL>', '07701234567', 'candidate', 'active'),
('manager', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'مدير الحملة', '<EMAIL>', '07701234568', 'manager', 'active'),
('supervisor', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'المشرف العام', '<EMAIL>', '07701234569', 'supervisor', 'active');

-- إدراج إداري تجريبي
INSERT INTO `admins` (`username`, `password`, `full_name`, `phone`, `email`, `region_id`, `role`, `status`, `hire_date`, `created_by`) VALUES
('admin1', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'أحمد محمد علي', '07701234570', '<EMAIL>', 1, 'admin', 'active', CURDATE(), 1),
('admin2', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'فاطمة حسن محمود', '07701234571', '<EMAIL>', 2, 'admin', 'active', CURDATE(), 1),
('supervisor1', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'محمد عبدالله أحمد', '07701234572', '<EMAIL>', 1, 'supervisor', 'active', CURDATE(), 1);

-- إدراج الصلاحيات الافتراضية للإداريين
INSERT INTO `admin_permissions` (`admin_id`, `permission_name`, `granted_by`) VALUES
(1, 'add_supporters', 1),
(1, 'edit_supporters', 1),
(1, 'view_supporters', 1),
(1, 'send_messages', 1),
(1, 'submit_requests', 1),
(1, 'create_reports', 1),
(1, 'view_statistics', 1),
(2, 'add_supporters', 1),
(2, 'edit_supporters', 1),
(2, 'view_supporters', 1),
(2, 'send_messages', 1),
(2, 'submit_requests', 1),
(2, 'create_reports', 1),
(2, 'view_statistics', 1),
(3, 'add_supporters', 1),
(3, 'edit_supporters', 1),
(3, 'view_supporters', 1),
(3, 'send_messages', 1),
(3, 'submit_requests', 1),
(3, 'create_reports', 1),
(3, 'view_statistics', 1),
(3, 'manage_admins', 1),
(3, 'approve_reports', 1);

-- إدراج فئات المطالب الافتراضية
INSERT INTO `request_categories` (`name`, `description`, `icon`, `color`) VALUES
('مطالب مالية', 'المساعدات المالية والقروض', 'fas fa-money-bill-wave', '#28a745'),
('مطالب طبية', 'العلاج والأدوية والمساعدات الطبية', 'fas fa-heartbeat', '#dc3545'),
('مطالب تعليمية', 'الرسوم الدراسية والكتب والمستلزمات', 'fas fa-graduation-cap', '#007bff'),
('مطالب وظيفية', 'فرص العمل والتوظيف', 'fas fa-briefcase', '#6f42c1'),
('مطالب سكنية', 'السكن والإيجار والصيانة', 'fas fa-home', '#fd7e14'),
('مطالب قانونية', 'الاستشارات والمساعدات القانونية', 'fas fa-balance-scale', '#20c997'),
('مطالب اجتماعية', 'المساعدات الاجتماعية والأسرية', 'fas fa-users', '#e83e8c'),
('مطالب البنية التحتية', 'الطرق والكهرباء والماء', 'fas fa-tools', '#6c757d'),
('مطالب الوثائق', 'استخراج الوثائق الرسمية', 'fas fa-file-alt', '#17a2b8'),
('مطالب أخرى', 'مطالب متنوعة أخرى', 'fas fa-ellipsis-h', '#ffc107');

-- إدراج قوالب التقارير الافتراضية
INSERT INTO `report_templates` (`name`, `description`, `template_type`, `template_content`, `is_default`) VALUES
('التقرير الأسبوعي الأساسي', 'قالب التقرير الأسبوعي الافتراضي', 'weekly',
 '{"sections": ["achievements", "challenges", "supporters_added", "requests_submitted", "next_week_plans"]}',
 1),
('التقرير الأسبوعي المفصل', 'قالب تقرير أسبوعي مفصل مع جميع الحقول', 'weekly',
 '{"sections": ["achievements", "challenges", "supporters_added", "events_attended", "meetings_held", "calls_made", "requests_submitted", "messages_sent", "working_hours", "expenses", "next_week_plans", "recommendations"]}',
 0),
('التقرير الشهري', 'قالب التقرير الشهري', 'monthly',
 '{"sections": ["monthly_achievements", "monthly_challenges", "monthly_statistics", "monthly_plans"]}',
 0);

-- إدراج قوالب الإشعارات الافتراضية
INSERT INTO `notification_templates` (`name`, `template_key`, `title_template`, `message_template`, `type`, `category`, `variables`, `icon`, `color`, `is_system`) VALUES
('مؤيد جديد', 'new_supporter', 'تم إضافة مؤيد جديد', 'تم إضافة المؤيد {supporter_name} بواسطة {admin_name}', 'new_supporter', 'supporter', '{"supporter_name": "اسم المؤيد", "admin_name": "اسم الإداري"}', 'fas fa-user-plus', '#28a745', 1),
('مطلب جديد', 'new_request', 'مطلب جديد من مؤيد', 'تم تقديم مطلب جديد: {request_title} من المؤيد {supporter_name}', 'new_request', 'request', '{"request_title": "عنوان المطلب", "supporter_name": "اسم المؤيد"}', 'fas fa-hand-holding-heart', '#ffc107', 1),
('رسالة جديدة', 'new_message', 'رسالة جديدة', 'رسالة جديدة من {sender_name}: {message_subject}', 'new_message', 'message', '{"sender_name": "اسم المرسل", "message_subject": "موضوع الرسالة"}', 'fas fa-envelope', '#007bff', 1),
('تقرير جديد', 'new_report', 'تقرير أسبوعي جديد', 'تم تقديم تقرير أسبوعي جديد من {admin_name} للفترة {week_period}', 'new_report', 'report', '{"admin_name": "اسم الإداري", "week_period": "فترة الأسبوع"}', 'fas fa-file-alt', '#6f42c1', 1),
('إداري جديد', 'new_admin', 'إداري جديد انضم للفريق', 'انضم إداري جديد للفريق: {admin_name} في منطقة {region_name}', 'info', 'admin', '{"admin_name": "اسم الإداري", "region_name": "اسم المنطقة"}', 'fas fa-user-tie', '#17a2b8', 1),
('تذكير تقرير', 'report_reminder', 'تذكير: التقرير الأسبوعي', 'تذكير بضرورة تقديم التقرير الأسبوعي قبل نهاية اليوم', 'reminder', 'report', '{}', 'fas fa-clock', '#fd7e14', 1),
('طلب عاجل', 'urgent_request', 'مطلب عاجل يحتاج متابعة', 'مطلب عاجل من {supporter_name}: {request_title}', 'urgent', 'request', '{"supporter_name": "اسم المؤيد", "request_title": "عنوان المطلب"}', 'fas fa-exclamation-triangle', '#dc3545', 1);

-- إدراج قوالب الرسائل الافتراضية
INSERT INTO `message_templates` (`name`, `subject`, `content`, `template_type`, `variables`, `is_active`) VALUES
('رسالة ترحيب', 'مرحباً بك في فريق الحملة', 'مرحباً {admin_name}،\n\nنرحب بك في فريق الحملة الانتخابية. نتطلع للعمل معك لتحقيق أهدافنا المشتركة.\n\nمع أطيب التحيات،\nإدارة الحملة', 'welcome', '{"admin_name": "اسم الإداري"}', 1),
('تذكير بالتقرير', 'تذكير: موعد تقديم التقرير الأسبوعي', 'عزيزي {admin_name}،\n\nنذكرك بضرورة تقديم التقرير الأسبوعي للفترة من {week_start} إلى {week_end} قبل نهاية اليوم.\n\nشكراً لتعاونك', 'reminder', '{"admin_name": "اسم الإداري", "week_start": "بداية الأسبوع", "week_end": "نهاية الأسبوع"}', 1),
('إعلان عام', 'إعلان مهم للجميع', 'إلى جميع أعضاء الفريق،\n\n{announcement_content}\n\nمع التقدير،\nإدارة الحملة', 'announcement', '{"announcement_content": "محتوى الإعلان"}', 1);

-- إدراج إعدادات إشعارات افتراضية للمرشح
INSERT INTO `notification_settings` (`user_type`, `user_id`, `email_notifications`, `push_notifications`, `new_supporter_notifications`, `new_request_notifications`, `new_message_notifications`, `new_report_notifications`) VALUES
('candidate', 1, 1, 1, 1, 1, 1, 1);

-- إدراج إعدادات النظام الافتراضية
INSERT INTO `system_settings` (`setting_key`, `setting_value`, `setting_type`, `description`, `category`) VALUES
('system_name', 'نظام إدارة الحملة الانتخابية', 'string', 'اسم النظام', 'general'),
('system_version', '2.0.0', 'string', 'إصدار النظام', 'general'),
('default_language', 'ar', 'string', 'اللغة الافتراضية', 'general'),
('timezone', 'Asia/Baghdad', 'string', 'المنطقة الزمنية', 'general'),
('max_file_size', '10485760', 'number', 'الحد الأقصى لحجم الملف (بايت)', 'files'),
('allowed_file_types', 'jpg,jpeg,png,gif,pdf,doc,docx,xls,xlsx', 'string', 'أنواع الملفات المسموحة', 'files'),
('session_timeout', '3600', 'number', 'مهلة انتهاء الجلسة (ثانية)', 'security'),
('max_login_attempts', '5', 'number', 'عدد محاولات تسجيل الدخول القصوى', 'security'),
('backup_enabled', '1', 'boolean', 'تفعيل النسخ الاحتياطي', 'backup'),
('maintenance_mode', '0', 'boolean', 'وضع الصيانة', 'system');

COMMIT;

-- =============================================
-- انتهاء ملف قاعدة البيانات
-- =============================================

/*!40101 SET CHARACTER_SET_CLIENT=@OLD_CHARACTER_SET_CLIENT */;
/*!40101 SET CHARACTER_SET_RESULTS=@OLD_CHARACTER_SET_RESULTS */;
/*!40101 SET COLLATION_CONNECTION=@OLD_COLLATION_CONNECTION */;
