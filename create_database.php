<?php
// إنشاء قاعدة بيانات جديدة للنظام
error_reporting(E_ALL);
ini_set('display_errors', 1);
header('Content-Type: text/html; charset=utf-8');

echo "<!DOCTYPE html>";
echo "<html lang='ar' dir='rtl'>";
echo "<head>";
echo "<meta charset='UTF-8'>";
echo "<meta name='viewport' content='width=device-width, initial-scale=1.0'>";
echo "<title>إنشاء قاعدة بيانات جديدة</title>";
echo "<link href='https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css' rel='stylesheet'>";
echo "<style>body { font-family: Arial, sans-serif; padding: 20px; background: #f8f9fa; }</style>";
echo "</head>";
echo "<body>";

echo "<div class='container'>";
echo "<div class='row justify-content-center'>";
echo "<div class='col-md-10'>";

echo "<div class='card'>";
echo "<div class='card-header bg-primary text-white'>";
echo "<h3>🗄️ إنشاء قاعدة بيانات جديدة</h3>";
echo "</div>";
echo "<div class='card-body'>";

// معالجة النموذج
if ($_POST && isset($_POST['action']) && $_POST['action'] === 'create_tables') {
    $host = $_POST['host'] ?? 'localhost';
    $dbname = $_POST['dbname'] ?? '';
    $username = $_POST['username'] ?? '';
    $password = $_POST['password'] ?? '';
    
    try {
        $pdo = new PDO("mysql:host=$host;dbname=$dbname;charset=utf8mb4", $username, $password);
        $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
        
        echo "<div class='alert alert-success'>";
        echo "<h5>✅ نجح الاتصال بقاعدة البيانات!</h5>";
        echo "</div>";
        
        // إنشاء الجداول
        $tables_sql = [
            // جدول المستخدمين (المرشحين)
            "CREATE TABLE IF NOT EXISTS users (
                id INT AUTO_INCREMENT PRIMARY KEY,
                username VARCHAR(50) UNIQUE NOT NULL,
                password VARCHAR(255) NOT NULL,
                full_name VARCHAR(100) NOT NULL,
                email VARCHAR(100),
                phone VARCHAR(20),
                user_type ENUM('candidate', 'manager') DEFAULT 'candidate',
                status ENUM('active', 'inactive') DEFAULT 'active',
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci",
            
            // جدول المناطق
            "CREATE TABLE IF NOT EXISTS regions (
                id INT AUTO_INCREMENT PRIMARY KEY,
                name VARCHAR(100) NOT NULL,
                description TEXT,
                population INT DEFAULT 0,
                voters_count INT DEFAULT 0,
                status ENUM('active', 'inactive') DEFAULT 'active',
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci",
            
            // جدول الإداريين
            "CREATE TABLE IF NOT EXISTS admins (
                id INT AUTO_INCREMENT PRIMARY KEY,
                username VARCHAR(50) UNIQUE NOT NULL,
                password VARCHAR(255) NOT NULL,
                full_name VARCHAR(100) NOT NULL,
                email VARCHAR(100),
                phone VARCHAR(20),
                region_id INT,
                role ENUM('admin', 'supervisor', 'coordinator') DEFAULT 'admin',
                status ENUM('active', 'inactive') DEFAULT 'active',
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                FOREIGN KEY (region_id) REFERENCES regions(id) ON DELETE SET NULL
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci",
            
            // جدول المؤيدين
            "CREATE TABLE IF NOT EXISTS supporters (
                id INT AUTO_INCREMENT PRIMARY KEY,
                full_name VARCHAR(100) NOT NULL,
                phone VARCHAR(20) NOT NULL,
                gender ENUM('male', 'female') NOT NULL,
                birth_date DATE,
                marital_status ENUM('single', 'married', 'divorced', 'widowed') DEFAULT 'single',
                education VARCHAR(100),
                profession VARCHAR(100),
                address TEXT NOT NULL,
                voter_number VARCHAR(50),
                voting_center VARCHAR(100),
                region_id INT,
                added_by INT,
                notes TEXT,
                status ENUM('active', 'inactive') DEFAULT 'active',
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                FOREIGN KEY (region_id) REFERENCES regions(id) ON DELETE SET NULL,
                FOREIGN KEY (added_by) REFERENCES admins(id) ON DELETE SET NULL
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci",
            
            // جدول مرفقات المؤيدين
            "CREATE TABLE IF NOT EXISTS supporter_attachments (
                id INT AUTO_INCREMENT PRIMARY KEY,
                supporter_id INT NOT NULL,
                attachment_type ENUM('voter_id_front', 'voter_id_back', 'national_id_front', 'national_id_back', 'residence_card') NOT NULL,
                file_name VARCHAR(255) NOT NULL,
                original_name VARCHAR(255) NOT NULL,
                file_path VARCHAR(500) NOT NULL,
                file_size INT NOT NULL,
                mime_type VARCHAR(100) NOT NULL,
                uploaded_by INT,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (supporter_id) REFERENCES supporters(id) ON DELETE CASCADE,
                FOREIGN KEY (uploaded_by) REFERENCES admins(id) ON DELETE SET NULL
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci",
            
            // جدول مطالب المؤيدين
            "CREATE TABLE IF NOT EXISTS supporter_requests (
                id INT AUTO_INCREMENT PRIMARY KEY,
                request_number VARCHAR(50) UNIQUE NOT NULL,
                admin_id INT NOT NULL,
                supporter_name VARCHAR(100) NOT NULL,
                supporter_phone VARCHAR(20) NOT NULL,
                title VARCHAR(200) NOT NULL,
                description TEXT NOT NULL,
                request_type ENUM('medical', 'financial', 'educational', 'employment', 'housing', 'legal', 'other') NOT NULL,
                priority ENUM('low', 'medium', 'high', 'urgent') DEFAULT 'medium',
                status ENUM('pending', 'received', 'in_progress', 'completed', 'rejected') DEFAULT 'pending',
                management_response TEXT,
                response_date TIMESTAMP NULL,
                submitted_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                FOREIGN KEY (admin_id) REFERENCES admins(id) ON DELETE CASCADE
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci",
            
            // جدول الرسائل
            "CREATE TABLE IF NOT EXISTS messages (
                id INT AUTO_INCREMENT PRIMARY KEY,
                sender_type ENUM('admin', 'management', 'system') NOT NULL,
                sender_id INT,
                receiver_type ENUM('admin', 'all_admins', 'management', 'candidate') NOT NULL,
                receiver_id INT,
                subject VARCHAR(200) NOT NULL,
                message TEXT NOT NULL,
                status ENUM('unread', 'read') DEFAULT 'unread',
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                read_at TIMESTAMP NULL
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci",
            
            // جدول التقارير الأسبوعية
            "CREATE TABLE IF NOT EXISTS weekly_reports (
                id INT AUTO_INCREMENT PRIMARY KEY,
                admin_id INT NOT NULL,
                report_period VARCHAR(50) NOT NULL,
                supporters_added INT DEFAULT 0,
                requests_submitted INT DEFAULT 0,
                events_attended INT DEFAULT 0,
                notes TEXT,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                FOREIGN KEY (admin_id) REFERENCES admins(id) ON DELETE CASCADE
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci",
            
            // جدول الإشعارات
            "CREATE TABLE IF NOT EXISTS notifications (
                id INT AUTO_INCREMENT PRIMARY KEY,
                user_type ENUM('admin', 'candidate', 'all') NOT NULL,
                user_id INT,
                title VARCHAR(200) NOT NULL,
                message TEXT NOT NULL,
                type ENUM('info', 'success', 'warning', 'error') DEFAULT 'info',
                status ENUM('unread', 'read') DEFAULT 'unread',
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                read_at TIMESTAMP NULL
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci",
            
            // جدول إحصائيات الإداريين اليومية
            "CREATE TABLE IF NOT EXISTS admin_daily_stats (
                id INT AUTO_INCREMENT PRIMARY KEY,
                admin_id INT NOT NULL,
                date DATE NOT NULL,
                supporters_added INT DEFAULT 0,
                requests_submitted INT DEFAULT 0,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                FOREIGN KEY (admin_id) REFERENCES admins(id) ON DELETE CASCADE,
                UNIQUE KEY unique_admin_date (admin_id, date)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci"
        ];
        
        echo "<h5>إنشاء الجداول:</h5>";
        
        foreach ($tables_sql as $index => $sql) {
            try {
                $pdo->exec($sql);
                $table_names = [
                    'المستخدمين (users)',
                    'المناطق (regions)', 
                    'الإداريين (admins)',
                    'المؤيدين (supporters)',
                    'مرفقات المؤيدين (supporter_attachments)',
                    'مطالب المؤيدين (supporter_requests)',
                    'الرسائل (messages)',
                    'التقارير الأسبوعية (weekly_reports)',
                    'الإشعارات (notifications)',
                    'إحصائيات الإداريين (admin_daily_stats)'
                ];
                echo "<p class='text-success'>✅ تم إنشاء جدول: " . $table_names[$index] . "</p>";
            } catch (Exception $e) {
                echo "<p class='text-danger'>❌ خطأ في إنشاء الجدول " . ($index + 1) . ": " . $e->getMessage() . "</p>";
            }
        }
        
        // إدراج بيانات أساسية
        echo "<h5>إدراج البيانات الأساسية:</h5>";
        
        try {
            // إدراج مستخدم المرشح
            $pdo->exec("INSERT IGNORE INTO users (id, username, password, full_name, phone, user_type) VALUES 
                       (1, 'abd', '" . password_hash('abdabd', PASSWORD_DEFAULT) . "', 'زين العابدين', '07719992716', 'candidate')");
            echo "<p class='text-success'>✅ تم إنشاء حساب المرشح</p>";
            
            // إدراج مناطق أساسية
            $pdo->exec("INSERT IGNORE INTO regions (id, name, description) VALUES 
                       (1, 'بغداد - الكرخ', 'منطقة الكرخ في بغداد'),
                       (2, 'بغداد - الرصافة', 'منطقة الرصافة في بغداد'),
                       (3, 'النجف', 'محافظة النجف'),
                       (4, 'كربلاء', 'محافظة كربلاء'),
                       (5, 'البصرة', 'محافظة البصرة')");
            echo "<p class='text-success'>✅ تم إدراج المناطق الأساسية</p>";
            
            // إدراج إداري تجريبي
            $pdo->exec("INSERT IGNORE INTO admins (id, username, password, full_name, phone, region_id) VALUES 
                       (1, 'admin', '" . password_hash('admin123', PASSWORD_DEFAULT) . "', 'الإداري الرئيسي', '07700000000', 1)");
            echo "<p class='text-success'>✅ تم إنشاء حساب إداري تجريبي</p>";
            
        } catch (Exception $e) {
            echo "<p class='text-warning'>⚠️ تحذير في إدراج البيانات: " . $e->getMessage() . "</p>";
        }
        
        // تحديث ملف database.php
        $database_content = "<?php
// إعدادات قاعدة البيانات - تم إنشاؤها وتجهيزها
define('DB_HOST', '$host');
define('DB_NAME', '$dbname');
define('DB_USER', '$username');
define('DB_PASS', '$password');
define('DB_CHARSET', 'utf8mb4');

class Database {
    private \$host = DB_HOST;
    private \$db_name = DB_NAME;
    private \$username = DB_USER;
    private \$password = DB_PASS;
    private \$charset = DB_CHARSET;
    public \$conn;

    public function getConnection() {
        \$this->conn = null;
        try {
            \$dsn = \"mysql:host=\" . \$this->host . \";dbname=\" . \$this->db_name . \";charset=utf8mb4\";
            \$this->conn = new PDO(\$dsn, \$this->username, \$this->password, array(
                PDO::MYSQL_ATTR_INIT_COMMAND => \"SET NAMES utf8mb4 COLLATE utf8mb4_unicode_ci\",
                PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
                PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
                PDO::ATTR_EMULATE_PREPARES => false
            ));
        } catch(PDOException \$exception) {
            echo \"خطأ في الاتصال: \" . \$exception->getMessage();
        }
        return \$this->conn;
    }
}

// دالة للحصول على اتصال قاعدة البيانات
function getDBConnection() {
    \$database = new Database();
    return \$database->getConnection();
}

// دالة لتنفيذ استعلام آمن
function executeQuery(\$sql, \$params = []) {
    try {
        \$conn = getDBConnection();
        \$stmt = \$conn->prepare(\$sql);
        \$result = \$stmt->execute(\$params);
        return \$result;
    } catch(PDOException \$e) {
        error_log(\"Database Error: \" . \$e->getMessage());
        return false;
    }
}

// دالة للحصول على صف واحد
function fetchOne(\$sql, \$params = []) {
    try {
        \$conn = getDBConnection();
        \$stmt = \$conn->prepare(\$sql);
        \$stmt->execute(\$params);
        return \$stmt->fetch(PDO::FETCH_ASSOC);
    } catch(PDOException \$e) {
        error_log(\"Database Error: \" . \$e->getMessage());
        return false;
    }
}

// دالة للحصول على عدة صفوف
function fetchAll(\$sql, \$params = []) {
    try {
        \$conn = getDBConnection();
        \$stmt = \$conn->prepare(\$sql);
        \$stmt->execute(\$params);
        return \$stmt->fetchAll(PDO::FETCH_ASSOC);
    } catch(PDOException \$e) {
        error_log(\"Database Error: \" . \$e->getMessage());
        return [];
    }
}

// دالة للحصول على آخر ID مدرج
function getLastInsertId() {
    \$conn = getDBConnection();
    return \$conn->lastInsertId();
}

// دالة لعرض الرسائل
function showMessage(\$message, \$type = 'info') {
    if (\$type === 'error') {
        error_log(\$message);
    }
}

// متغير عام لاتصال قاعدة البيانات
\$GLOBALS['pdo'] = getDBConnection();
?>";
        
        if (file_put_contents('config/database.php', $database_content)) {
            echo "<p class='text-success'>✅ تم تحديث ملف database.php</p>";
        }
        
        echo "<div class='alert alert-success mt-4'>";
        echo "<h5>🎉 تم إنشاء قاعدة البيانات بنجاح!</h5>";
        echo "<p>يمكنك الآن استخدام النظام:</p>";
        echo "<div class='text-center'>";
        echo "<a href='dashboard.php' class='btn btn-primary me-2'>لوحة تحكم المرشح</a>";
        echo "<a href='modules/admin/login.php' class='btn btn-success me-2'>تسجيل دخول الإداريين</a>";
        echo "<a href='test_system.php' class='btn btn-info'>اختبار النظام</a>";
        echo "</div>";
        echo "</div>";
        
        echo "<div class='alert alert-info'>";
        echo "<h6>معلومات تسجيل الدخول:</h6>";
        echo "<ul>";
        echo "<li><strong>المرشح:</strong> abd / abdabd</li>";
        echo "<li><strong>الإداري:</strong> admin / admin123</li>";
        echo "</ul>";
        echo "</div>";
        
    } catch (Exception $e) {
        echo "<div class='alert alert-danger'>";
        echo "<h5>❌ فشل في الاتصال بقاعدة البيانات</h5>";
        echo "<p>الخطأ: " . $e->getMessage() . "</p>";
        echo "</div>";
    }
}

// عرض النموذج
if (!$_POST || !isset($_POST['action'])) {
    echo "<div class='alert alert-info'>";
    echo "<h5>📋 خطوات إنشاء قاعدة بيانات جديدة:</h5>";
    echo "<ol>";
    echo "<li>ادخل إلى cPanel</li>";
    echo "<li>أنشئ قاعدة بيانات جديدة (مثل: campaign_system)</li>";
    echo "<li>أنشئ مستخدم جديد وأعطه جميع الصلاحيات</li>";
    echo "<li>أدخل المعلومات أدناه</li>";
    echo "</ol>";
    echo "</div>";
    
    echo "<form method='POST'>";
    echo "<input type='hidden' name='action' value='create_tables'>";
    
    echo "<div class='row'>";
    echo "<div class='col-md-6'>";
    echo "<div class='mb-3'>";
    echo "<label for='host' class='form-label'>عنوان الخادم:</label>";
    echo "<input type='text' class='form-control' id='host' name='host' value='localhost' required>";
    echo "</div>";
    echo "</div>";
    echo "<div class='col-md-6'>";
    echo "<div class='mb-3'>";
    echo "<label for='dbname' class='form-label'>اسم قاعدة البيانات:</label>";
    echo "<input type='text' class='form-control' id='dbname' name='dbname' placeholder='campaign_system' required>";
    echo "</div>";
    echo "</div>";
    echo "</div>";
    
    echo "<div class='row'>";
    echo "<div class='col-md-6'>";
    echo "<div class='mb-3'>";
    echo "<label for='username' class='form-label'>اسم المستخدم:</label>";
    echo "<input type='text' class='form-control' id='username' name='username' placeholder='username_campaign' required>";
    echo "</div>";
    echo "</div>";
    echo "<div class='col-md-6'>";
    echo "<div class='mb-3'>";
    echo "<label for='password' class='form-label'>كلمة المرور:</label>";
    echo "<input type='password' class='form-control' id='password' name='password' placeholder='كلمة مرور قوية' required>";
    echo "</div>";
    echo "</div>";
    echo "</div>";
    
    echo "<div class='text-center'>";
    echo "<button type='submit' class='btn btn-primary btn-lg'>إنشاء قاعدة البيانات والجداول</button>";
    echo "</div>";
    echo "</form>";
}

echo "</div>";
echo "</div>";

echo "</div>";
echo "</div>";
echo "</div>";

echo "</body>";
echo "</html>";
?>
