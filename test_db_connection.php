<?php
// اختبار اتصال قاعدة البيانات

echo "<!DOCTYPE html>";
echo "<html lang='ar' dir='rtl'>";
echo "<head>";
echo "<meta charset='UTF-8'>";
echo "<meta name='viewport' content='width=device-width, initial-scale=1.0'>";
echo "<title>اختبار اتصال قاعدة البيانات</title>";
echo "<link href='https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css' rel='stylesheet'>";
echo "<link href='https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css' rel='stylesheet'>";
echo "<style>";
echo "body { font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); min-height: 100vh; padding: 2rem; }";
echo ".test-card { background: white; border-radius: 15px; box-shadow: 0 10px 30px rgba(0,0,0,0.1); padding: 2rem; margin: 0 auto; max-width: 800px; }";
echo ".success { color: #28a745; }";
echo ".error { color: #dc3545; }";
echo ".info { color: #17a2b8; }";
echo "</style>";
echo "</head>";
echo "<body>";

echo "<div class='test-card'>";
echo "<h1 class='text-center mb-4'><i class='fas fa-database'></i> اختبار اتصال قاعدة البيانات</h1>";

// معلومات قاعدة البيانات
$db_configs = [
    [
        'host' => 'localhost',
        'name' => 'irjnpfzw_mr',
        'user' => 'irjnpfzw_mr',
        'pass' => 'irjnpfzw_mr',
        'description' => 'كلمة المرور الافتراضية (نفس اسم المستخدم)'
    ],
    [
        'host' => 'localhost',
        'name' => 'irjnpfzw_mr',
        'user' => 'irjnpfzw_mr',
        'pass' => 'Zain@123456',
        'description' => 'كلمة المرور المخصصة'
    ],
    [
        'host' => 'localhost',
        'name' => 'irjnpfzw_mr',
        'user' => 'irjnpfzw_mr',
        'pass' => '',
        'description' => 'بدون كلمة مرور'
    ],
    [
        'host' => 'localhost',
        'name' => 'irjnpfzw_mr',
        'user' => 'root',
        'pass' => '',
        'description' => 'مستخدم root بدون كلمة مرور'
    ],
    [
        'host' => 'localhost',
        'name' => 'irjnpfzw_mr',
        'user' => 'root',
        'pass' => 'root',
        'description' => 'مستخدم root مع كلمة مرور root'
    ]
];

$working_config = null;

echo "<div class='alert alert-info'>";
echo "<h5>جاري اختبار إعدادات مختلفة لقاعدة البيانات...</h5>";
echo "</div>";

foreach ($db_configs as $index => $config) {
    echo "<div class='alert alert-secondary'>";
    echo "<h6>اختبار " . ($index + 1) . ": " . $config['description'] . "</h6>";
    echo "<p><strong>المضيف:</strong> " . $config['host'] . "</p>";
    echo "<p><strong>قاعدة البيانات:</strong> " . $config['name'] . "</p>";
    echo "<p><strong>المستخدم:</strong> " . $config['user'] . "</p>";
    echo "<p><strong>كلمة المرور:</strong> " . ($config['pass'] ? str_repeat('*', strlen($config['pass'])) : 'فارغة') . "</p>";
    
    try {
        $dsn = "mysql:host=" . $config['host'] . ";dbname=" . $config['name'] . ";charset=utf8mb4";
        $pdo = new PDO($dsn, $config['user'], $config['pass'], [
            PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
            PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
            PDO::MYSQL_ATTR_INIT_COMMAND => "SET NAMES utf8mb4 COLLATE utf8mb4_unicode_ci"
        ]);
        
        // اختبار استعلام بسيط
        $stmt = $pdo->query("SELECT DATABASE() as db_name, VERSION() as version");
        $result = $stmt->fetch();
        
        echo "<p class='success'><i class='fas fa-check-circle'></i> <strong>نجح الاتصال!</strong></p>";
        echo "<p class='success'>قاعدة البيانات: " . $result['db_name'] . "</p>";
        echo "<p class='success'>إصدار MySQL: " . $result['version'] . "</p>";
        
        if (!$working_config) {
            $working_config = $config;
        }
        
    } catch (PDOException $e) {
        echo "<p class='error'><i class='fas fa-times-circle'></i> <strong>فشل الاتصال:</strong> " . $e->getMessage() . "</p>";
    }
    
    echo "</div>";
}

if ($working_config) {
    echo "<div class='alert alert-success'>";
    echo "<h5><i class='fas fa-check-circle'></i> تم العثور على إعدادات صحيحة!</h5>";
    echo "<p><strong>المضيف:</strong> " . $working_config['host'] . "</p>";
    echo "<p><strong>قاعدة البيانات:</strong> " . $working_config['name'] . "</p>";
    echo "<p><strong>المستخدم:</strong> " . $working_config['user'] . "</p>";
    echo "<p><strong>كلمة المرور:</strong> " . ($working_config['pass'] ?: 'فارغة') . "</p>";
    echo "</div>";
    
    // تحديث ملف الإعدادات
    if (isset($_POST['update_config'])) {
        $config_content = "<?php\n";
        $config_content .= "// إعدادات قاعدة البيانات\n";
        $config_content .= "define('DB_HOST', '" . $working_config['host'] . "');\n";
        $config_content .= "define('DB_NAME', '" . $working_config['name'] . "');\n";
        $config_content .= "define('DB_USER', '" . $working_config['user'] . "');\n";
        $config_content .= "define('DB_PASS', '" . $working_config['pass'] . "');\n";
        $config_content .= "define('DB_CHARSET', 'utf8mb4');\n\n";
        
        // إضافة باقي محتوى الملف
        $current_content = file_get_contents('config/database.php');
        $class_start = strpos($current_content, 'class Database');
        if ($class_start !== false) {
            $config_content .= substr($current_content, $class_start);
        }
        
        file_put_contents('config/database.php', $config_content);
        
        echo "<div class='alert alert-info'>";
        echo "<h5>تم تحديث ملف الإعدادات!</h5>";
        echo "</div>";
    }
    
    echo "<form method='POST' action=''>";
    echo "<div class='text-center'>";
    echo "<button type='submit' name='update_config' class='btn btn-primary btn-lg me-2'>";
    echo "<i class='fas fa-save'></i> تحديث ملف الإعدادات";
    echo "</button>";
    echo "</div>";
    echo "</form>";
    
} else {
    echo "<div class='alert alert-danger'>";
    echo "<h5><i class='fas fa-exclamation-triangle'></i> لم يتم العثور على إعدادات صحيحة!</h5>";
    echo "<p>يرجى التحقق من:</p>";
    echo "<ul>";
    echo "<li>أن قاعدة البيانات موجودة</li>";
    echo "<li>أن المستخدم له صلاحيات الوصول</li>";
    echo "<li>أن كلمة المرور صحيحة</li>";
    echo "<li>أن خدمة MySQL تعمل</li>";
    echo "</ul>";
    echo "</div>";
}

// معلومات إضافية
echo "<div class='mt-4'>";
echo "<h6>معلومات إضافية:</h6>";
echo "<ul>";
echo "<li><strong>خادم الويب:</strong> " . $_SERVER['SERVER_SOFTWARE'] . "</li>";
echo "<li><strong>إصدار PHP:</strong> " . PHP_VERSION . "</li>";
echo "<li><strong>PDO MySQL:</strong> " . (extension_loaded('pdo_mysql') ? 'متوفر' : 'غير متوفر') . "</li>";
echo "<li><strong>MySQLi:</strong> " . (extension_loaded('mysqli') ? 'متوفر' : 'غير متوفر') . "</li>";
echo "</ul>";
echo "</div>";

echo "<div class='text-center mt-4'>";
echo "<a href='test.php' class='btn btn-info me-2'><i class='fas fa-cog'></i> اختبار النظام</a>";
echo "<a href='fix_all_issues.php' class='btn btn-warning me-2'><i class='fas fa-tools'></i> إصلاح المشاكل</a>";
echo "<a href='login.php' class='btn btn-success'><i class='fas fa-sign-in-alt'></i> تسجيل الدخول</a>";
echo "</div>";

echo "</div>";
echo "</body>";
echo "</html>";
?>
