<?php
require_once '../../config/config.php';
require_once '../../config/database.php';

// التحقق من تسجيل الدخول
if (!isLoggedIn()) {
    redirect('../../login.php');
}

// التحقق من الصلاحيات
$can_add = isCandidate() || (isAdmin() && in_array('add_events', $_SESSION['permissions'] ?? []));
$can_edit = isCandidate() || (isAdmin() && in_array('edit_events', $_SESSION['permissions'] ?? []));
$can_delete = isCandidate() || (isAdmin() && in_array('delete_events', $_SESSION['permissions'] ?? []));

// معالجة الطلبات
if ($_SERVER['REQUEST_METHOD'] == 'POST') {
    if (isset($_POST['action'])) {
        switch ($_POST['action']) {
            case 'add':
                if ($can_add) addEvent();
                break;
            case 'edit':
                if ($can_edit) editEvent();
                break;
            case 'delete':
                if ($can_delete) deleteEvent();
                break;
        }
    }
}

// بناء الاستعلام مع التصفية
$where_conditions = [];
$params = [];

// تطبيق فلاتر البحث
if (!empty($_GET['search'])) {
    $search = '%' . $_GET['search'] . '%';
    $where_conditions[] = "(e.name LIKE ? OR e.description LIKE ? OR e.location LIKE ?)";
    $params = array_merge($params, [$search, $search, $search]);
}

if (!empty($_GET['status'])) {
    $where_conditions[] = "e.status = ?";
    $params[] = $_GET['status'];
}

if (!empty($_GET['date_from'])) {
    $where_conditions[] = "e.event_date >= ?";
    $params[] = $_GET['date_from'];
}

if (!empty($_GET['date_to'])) {
    $where_conditions[] = "e.event_date <= ?";
    $params[] = $_GET['date_to'];
}

$where_clause = !empty($where_conditions) ? 'WHERE ' . implode(' AND ', $where_conditions) : '';

// جلب الفعاليات
$sql = "SELECT e.*, u.full_name as added_by_name
        FROM events e 
        LEFT JOIN users u ON e.added_by = u.id 
        $where_clause 
        ORDER BY e.event_date DESC, e.created_at DESC";

$events = fetchAll($sql, $params);

// إحصائيات
$total_events = count($events);
$planned_events = count(array_filter($events, function($event) { return $event['status'] == 'planned'; }));
$completed_events = count(array_filter($events, function($event) { return $event['status'] == 'completed'; }));
$ongoing_events = count(array_filter($events, function($event) { return $event['status'] == 'ongoing'; }));

function addEvent() {
    $data = [
        'name' => sanitize($_POST['name']),
        'description' => sanitize($_POST['description']),
        'event_date' => sanitize($_POST['event_date']),
        'event_time' => sanitize($_POST['event_time']),
        'location' => sanitize($_POST['location']),
        'status' => sanitize($_POST['status']),
        'added_by' => $_SESSION['user_id']
    ];
    
    // التحقق من صحة البيانات
    if (empty($data['name']) || empty($data['event_date']) || empty($data['location'])) {
        showMessage('يرجى ملء جميع الحقول المطلوبة', 'error');
        return;
    }
    
    // رفع الصور إن وجدت
    $photos = [];
    if (isset($_FILES['photos']) && !empty($_FILES['photos']['name'][0])) {
        $photos = uploadEventPhotos($_FILES['photos']);
    }
    
    if (!empty($photos)) {
        $data['photos'] = json_encode($photos);
    }
    
    $sql = "INSERT INTO events (name, description, event_date, event_time, location, photos, status, added_by) 
            VALUES (?, ?, ?, ?, ?, ?, ?, ?)";
    
    $result = executeQuery($sql, array_values($data));
    
    if ($result) {
        showMessage('تم إضافة الفعالية بنجاح', 'success');
        redirect('events.php');
    } else {
        showMessage('حدث خطأ أثناء إضافة الفعالية', 'error');
    }
}

function editEvent() {
    $id = (int)$_POST['id'];
    $data = [
        'name' => sanitize($_POST['name']),
        'description' => sanitize($_POST['description']),
        'event_date' => sanitize($_POST['event_date']),
        'event_time' => sanitize($_POST['event_time']),
        'location' => sanitize($_POST['location']),
        'status' => sanitize($_POST['status'])
    ];
    
    // التحقق من صحة البيانات
    if (empty($data['name']) || empty($data['event_date']) || empty($data['location'])) {
        showMessage('يرجى ملء جميع الحقول المطلوبة', 'error');
        return;
    }
    
    // رفع الصور الجديدة إن وجدت
    if (isset($_FILES['photos']) && !empty($_FILES['photos']['name'][0])) {
        $photos = uploadEventPhotos($_FILES['photos']);
        if (!empty($photos)) {
            $data['photos'] = json_encode($photos);
        }
    }
    
    $set_clause = implode(', ', array_map(function($key) { return "$key = ?"; }, array_keys($data)));
    $sql = "UPDATE events SET $set_clause WHERE id = ?";
    
    $params = array_values($data);
    $params[] = $id;
    
    $result = executeQuery($sql, $params);
    
    if ($result) {
        showMessage('تم تحديث الفعالية بنجاح', 'success');
        redirect('events.php');
    } else {
        showMessage('حدث خطأ أثناء تحديث الفعالية', 'error');
    }
}

function deleteEvent() {
    $id = (int)$_POST['id'];
    
    // حذف الصور المرتبطة
    $event = fetchOne("SELECT photos FROM events WHERE id = ?", [$id]);
    if ($event && $event['photos']) {
        $photos = json_decode($event['photos'], true);
        foreach ($photos as $photo) {
            if (file_exists('../../' . $photo)) {
                unlink('../../' . $photo);
            }
        }
    }
    
    $result = executeQuery("DELETE FROM events WHERE id = ?", [$id]);
    
    if ($result) {
        showMessage('تم حذف الفعالية بنجاح', 'success');
    } else {
        showMessage('حدث خطأ أثناء حذف الفعالية', 'error');
    }
}

function uploadEventPhotos($files) {
    $uploaded_photos = [];
    $allowed_types = ['image/jpeg', 'image/png', 'image/gif'];
    $max_size = 5 * 1024 * 1024; // 5MB
    
    $upload_dir = '../../uploads/events/';
    if (!file_exists($upload_dir)) {
        mkdir($upload_dir, 0755, true);
    }
    
    for ($i = 0; $i < count($files['name']); $i++) {
        if ($files['error'][$i] == 0) {
            if (!in_array($files['type'][$i], $allowed_types)) {
                continue;
            }
            
            if ($files['size'][$i] > $max_size) {
                continue;
            }
            
            $file_extension = pathinfo($files['name'][$i], PATHINFO_EXTENSION);
            $new_filename = uniqid() . '.' . $file_extension;
            $upload_path = $upload_dir . $new_filename;
            
            if (move_uploaded_file($files['tmp_name'][$i], $upload_path)) {
                $uploaded_photos[] = 'uploads/events/' . $new_filename;
            }
        }
    }
    
    return $uploaded_photos;
}
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إدارة الفعاليات - <?php echo SITE_NAME; ?></title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="https://cdn.datatables.net/1.13.6/css/dataTables.bootstrap5.min.css" rel="stylesheet">
    <link href="../../assets/css/dashboard.css" rel="stylesheet">
    <link href="../../assets/css/events.css" rel="stylesheet">
</head>
<body>
    <!-- شريط التنقل العلوي -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary fixed-top">
        <div class="container-fluid">
            <a class="navbar-brand" href="../../dashboard.php">
                <i class="fas fa-vote-yea me-2"></i>
                نظام إدارة الحملة الانتخابية
            </a>
            
            <div class="navbar-nav ms-auto">
                <div class="nav-item dropdown">
                    <a class="nav-link dropdown-toggle" href="#" id="userDropdown" role="button" data-bs-toggle="dropdown">
                        <i class="fas fa-user-circle me-1"></i>
                        <?php echo htmlspecialchars($_SESSION['full_name']); ?>
                    </a>
                    <ul class="dropdown-menu dropdown-menu-end">
                        <li><a class="dropdown-item" href="../../logout.php"><i class="fas fa-sign-out-alt me-2"></i>تسجيل الخروج</a></li>
                    </ul>
                </div>
            </div>
        </div>
    </nav>

    <!-- المحتوى الرئيسي -->
    <div class="container-fluid main-content">
        <div class="row">
            <!-- الشريط الجانبي -->
            <nav class="col-md-3 col-lg-2 d-md-block sidebar">
                <div class="position-sticky pt-3">
                    <ul class="nav flex-column">
                        <li class="nav-item">
                            <a class="nav-link" href="../../dashboard.php">
                                <i class="fas fa-tachometer-alt me-2"></i>
                                لوحة التحكم
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="../supporters/supporters.php">
                                <i class="fas fa-users me-2"></i>
                                المؤيدين
                            </a>
                        </li>
                        <?php if (isCandidate()): ?>
                        <li class="nav-item">
                            <a class="nav-link" href="../regions/regions.php">
                                <i class="fas fa-map-marked-alt me-2"></i>
                                المناطق
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="../admins/admins.php">
                                <i class="fas fa-user-tie me-2"></i>
                                الإداريين
                            </a>
                        </li>
                        <?php endif; ?>
                        <li class="nav-item">
                            <a class="nav-link" href="../expenses/expenses.php">
                                <i class="fas fa-money-bill-wave me-2"></i>
                                المصروفات
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link active" href="events.php">
                                <i class="fas fa-calendar-alt me-2"></i>
                                الفعاليات
                            </a>
                        </li>
                        <?php if (isCandidate()): ?>
                        <li class="nav-item">
                            <a class="nav-link" href="../competitors/competitors.php">
                                <i class="fas fa-chess me-2"></i>
                                المنافسين
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="../reports/reports.php">
                                <i class="fas fa-chart-bar me-2"></i>
                                التقارير
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="../settings/settings.php">
                                <i class="fas fa-cog me-2"></i>
                                الإعدادات
                            </a>
                        </li>
                        <?php endif; ?>
                    </ul>
                </div>
            </nav>

            <!-- المحتوى الرئيسي -->
            <main class="col-md-9 ms-sm-auto col-lg-10 px-md-4">
                <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                    <h1 class="h2">
                        <i class="fas fa-calendar-alt me-2"></i>
                        إدارة الفعاليات
                    </h1>
                    <div class="btn-toolbar mb-2 mb-md-0">
                        <div class="btn-group me-2">
                            <?php if ($can_add): ?>
                            <button type="button" class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#addEventModal">
                                <i class="fas fa-plus"></i> إضافة فعالية
                            </button>
                            <?php endif; ?>
                            <button type="button" class="btn btn-success" onclick="exportEvents()">
                                <i class="fas fa-file-excel"></i> تصدير Excel
                            </button>
                        </div>
                    </div>
                </div>

                <?php displayMessage(); ?>

                <!-- الإحصائيات -->
                <div class="row mb-4">
                    <div class="col-md-3">
                        <div class="card bg-primary text-white">
                            <div class="card-body">
                                <div class="d-flex justify-content-between">
                                    <div>
                                        <h4><?php echo number_format($total_events); ?></h4>
                                        <p class="mb-0">إجمالي الفعاليات</p>
                                    </div>
                                    <div class="align-self-center">
                                        <i class="fas fa-calendar-alt fa-2x"></i>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="card bg-warning text-white">
                            <div class="card-body">
                                <div class="d-flex justify-content-between">
                                    <div>
                                        <h4><?php echo number_format($planned_events); ?></h4>
                                        <p class="mb-0">مخططة</p>
                                    </div>
                                    <div class="align-self-center">
                                        <i class="fas fa-clock fa-2x"></i>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="card bg-info text-white">
                            <div class="card-body">
                                <div class="d-flex justify-content-between">
                                    <div>
                                        <h4><?php echo number_format($ongoing_events); ?></h4>
                                        <p class="mb-0">جارية</p>
                                    </div>
                                    <div class="align-self-center">
                                        <i class="fas fa-play fa-2x"></i>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="card bg-success text-white">
                            <div class="card-body">
                                <div class="d-flex justify-content-between">
                                    <div>
                                        <h4><?php echo number_format($completed_events); ?></h4>
                                        <p class="mb-0">مكتملة</p>
                                    </div>
                                    <div class="align-self-center">
                                        <i class="fas fa-check fa-2x"></i>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- فلاتر البحث -->
                <div class="card mb-4">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="fas fa-filter me-2"></i>
                            فلاتر البحث والتصفية
                        </h5>
                    </div>
                    <div class="card-body">
                        <form method="GET" action="">
                            <div class="row">
                                <div class="col-md-3 mb-3">
                                    <label for="search" class="form-label">البحث</label>
                                    <input type="text" class="form-control" id="search" name="search" 
                                           value="<?php echo htmlspecialchars($_GET['search'] ?? ''); ?>" 
                                           placeholder="اسم الفعالية، الوصف، المكان...">
                                </div>
                                <div class="col-md-2 mb-3">
                                    <label for="status" class="form-label">الحالة</label>
                                    <select class="form-select" id="status" name="status">
                                        <option value="">جميع الحالات</option>
                                        <option value="planned" <?php echo ($_GET['status'] ?? '') == 'planned' ? 'selected' : ''; ?>>مخططة</option>
                                        <option value="ongoing" <?php echo ($_GET['status'] ?? '') == 'ongoing' ? 'selected' : ''; ?>>جارية</option>
                                        <option value="completed" <?php echo ($_GET['status'] ?? '') == 'completed' ? 'selected' : ''; ?>>مكتملة</option>
                                        <option value="cancelled" <?php echo ($_GET['status'] ?? '') == 'cancelled' ? 'selected' : ''; ?>>ملغية</option>
                                    </select>
                                </div>
                                <div class="col-md-2 mb-3">
                                    <label for="date_from" class="form-label">من تاريخ</label>
                                    <input type="date" class="form-control" id="date_from" name="date_from" 
                                           value="<?php echo htmlspecialchars($_GET['date_from'] ?? ''); ?>">
                                </div>
                                <div class="col-md-2 mb-3">
                                    <label for="date_to" class="form-label">إلى تاريخ</label>
                                    <input type="date" class="form-control" id="date_to" name="date_to" 
                                           value="<?php echo htmlspecialchars($_GET['date_to'] ?? ''); ?>">
                                </div>
                                <div class="col-md-1 mb-3 d-flex align-items-end">
                                    <button type="submit" class="btn btn-primary w-100">
                                        <i class="fas fa-search"></i>
                                    </button>
                                </div>
                            </div>
                        </form>
                    </div>
                </div>

                <!-- جدول الفعاليات -->
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="fas fa-list me-2"></i>
                            قائمة الفعاليات (<?php echo number_format($total_events); ?>)
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="table-responsive">
                            <table class="table table-striped table-hover" id="eventsTable">
                                <thead class="table-dark">
                                    <tr>
                                        <th>اسم الفعالية</th>
                                        <th>التاريخ</th>
                                        <th>الوقت</th>
                                        <th>المكان</th>
                                        <th>الحالة</th>
                                        <th>الصور</th>
                                        <th>أضيف بواسطة</th>
                                        <th>الإجراءات</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php foreach ($events as $event): ?>
                                    <tr>
                                        <td><strong><?php echo htmlspecialchars($event['name']); ?></strong></td>
                                        <td><?php echo formatArabicDate($event['event_date']); ?></td>
                                        <td><?php echo date('H:i', strtotime($event['event_time'])); ?></td>
                                        <td><?php echo htmlspecialchars($event['location']); ?></td>
                                        <td>
                                            <?php
                                            $status_colors = [
                                                'planned' => 'warning',
                                                'ongoing' => 'info',
                                                'completed' => 'success',
                                                'cancelled' => 'danger'
                                            ];
                                            $status_names = [
                                                'planned' => 'مخططة',
                                                'ongoing' => 'جارية',
                                                'completed' => 'مكتملة',
                                                'cancelled' => 'ملغية'
                                            ];
                                            $color = $status_colors[$event['status']] ?? 'secondary';
                                            $name = $status_names[$event['status']] ?? $event['status'];
                                            ?>
                                            <span class="badge bg-<?php echo $color; ?>"><?php echo $name; ?></span>
                                        </td>
                                        <td>
                                            <?php if ($event['photos']): ?>
                                                <?php $photos = json_decode($event['photos'], true); ?>
                                                <span class="badge bg-info"><?php echo count($photos); ?> صورة</span>
                                            <?php else: ?>
                                                <span class="text-muted">لا توجد</span>
                                            <?php endif; ?>
                                        </td>
                                        <td><?php echo htmlspecialchars($event['added_by_name']); ?></td>
                                        <td>
                                            <div class="btn-group" role="group">
                                                <button type="button" class="btn btn-sm btn-info" 
                                                        onclick="viewEvent(<?php echo $event['id']; ?>)" 
                                                        title="عرض">
                                                    <i class="fas fa-eye"></i>
                                                </button>
                                                <?php if ($can_edit): ?>
                                                <button type="button" class="btn btn-sm btn-warning" 
                                                        onclick="editEvent(<?php echo $event['id']; ?>)" 
                                                        title="تعديل">
                                                    <i class="fas fa-edit"></i>
                                                </button>
                                                <?php endif; ?>
                                                <?php if ($can_delete): ?>
                                                <button type="button" class="btn btn-sm btn-danger" 
                                                        onclick="deleteEvent(<?php echo $event['id']; ?>)" 
                                                        title="حذف">
                                                    <i class="fas fa-trash"></i>
                                                </button>
                                                <?php endif; ?>
                                            </div>
                                        </td>
                                    </tr>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </main>
        </div>
    </div>

    <!-- النوافذ المنبثقة -->
    <?php include 'modals.php'; ?>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://code.jquery.com/jquery-3.7.0.min.js"></script>
    <script src="https://cdn.datatables.net/1.13.6/js/jquery.dataTables.min.js"></script>
    <script src="https://cdn.datatables.net/1.13.6/js/dataTables.bootstrap5.min.js"></script>
    <script src="../../assets/js/events.js"></script>
</body>
</html>
