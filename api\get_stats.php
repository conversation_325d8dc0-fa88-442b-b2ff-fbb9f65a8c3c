<?php
// API للحصول على الإحصائيات
header('Content-Type: application/json; charset=utf-8');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET');
header('Access-Control-Allow-Headers: Content-Type');

require_once '../config/database.php';

try {
    // جلب الإحصائيات
    $stats = [];
    
    // عدد المؤيدين
    $result = fetchOne("SELECT COUNT(*) as count FROM supporters");
    $stats['supporters'] = $result ? $result['count'] : 0;

    // عدد المناطق
    $result = fetchOne("SELECT COUNT(*) as count FROM regions");
    $stats['regions'] = $result ? $result['count'] : 0;

    // عدد الإداريين
    $result = fetchOne("SELECT COUNT(*) as count FROM admins");
    $stats['admins'] = $result ? $result['count'] : 0;

    // عدد المطالب
    $result = fetchOne("SELECT COUNT(*) as count FROM supporter_requests");
    $stats['requests'] = $result ? $result['count'] : 0;

    // عدد المطالب المعلقة
    $result = fetchOne("SELECT COUNT(*) as count FROM supporter_requests WHERE status = 'pending'");
    $stats['pending_requests'] = $result ? $result['count'] : 0;

    // عدد الرسائل
    $result = fetchOne("SELECT COUNT(*) as count FROM messages");
    $stats['messages'] = $result ? $result['count'] : 0;

    // عدد التقارير
    $result = fetchOne("SELECT COUNT(*) as count FROM weekly_reports");
    $stats['reports'] = $result ? $result['count'] : 0;

    // إحصائيات المرفقات
    $result = fetchOne("SELECT COUNT(*) as count FROM supporter_attachments");
    $stats['attachments'] = $result ? $result['count'] : 0;

    // إحصائيات إضافية
    $result = fetchOne("SELECT COUNT(*) as count FROM supporters WHERE DATE(created_at) = CURDATE()");
    $stats['supporters_today'] = $result ? $result['count'] : 0;

    $result = fetchOne("SELECT COUNT(*) as count FROM supporter_requests WHERE DATE(submitted_at) = CURDATE()");
    $stats['requests_today'] = $result ? $result['count'] : 0;

    // آخر نشاط
    $result = fetchOne("SELECT MAX(created_at) as last_activity FROM supporters");
    $stats['last_supporter_activity'] = $result ? $result['last_activity'] : null;

    $result = fetchOne("SELECT MAX(submitted_at) as last_activity FROM supporter_requests");
    $stats['last_request_activity'] = $result ? $result['last_activity'] : null;

    echo json_encode([
        'success' => true,
        'stats' => $stats,
        'timestamp' => date('Y-m-d H:i:s'),
        'message' => 'تم جلب الإحصائيات بنجاح'
    ], JSON_UNESCAPED_UNICODE);

} catch (Exception $e) {
    echo json_encode([
        'success' => false,
        'error' => $e->getMessage(),
        'timestamp' => date('Y-m-d H:i:s')
    ], JSON_UNESCAPED_UNICODE);
}
?>
