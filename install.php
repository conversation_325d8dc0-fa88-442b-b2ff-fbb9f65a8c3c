<?php
// ملف تثبيت النظام
error_reporting(E_ALL);
ini_set('display_errors', 1);

// التحقق من وجود ملف الإعدادات
if (file_exists('config/config.php')) {
    require_once 'config/config.php';
    if (isLoggedIn()) {
        redirect('dashboard.php');
    }
}

$message = '';
$step = $_GET['step'] ?? 1;

if ($_SERVER['REQUEST_METHOD'] == 'POST') {
    if ($step == 1) {
        // التحقق من متطلبات النظام
        $requirements_met = checkSystemRequirements();
        if ($requirements_met) {
            header('Location: install.php?step=2');
            exit;
        } else {
            $message = 'بعض المتطلبات غير متوفرة. يرجى التحقق من الخادم.';
        }
    } elseif ($step == 2) {
        // إنشاء قاعدة البيانات
        $result = createDatabase();
        if ($result['success']) {
            header('Location: install.php?step=3');
            exit;
        } else {
            $message = $result['message'];
        }
    } elseif ($step == 3) {
        // إنهاء التثبيت
        $result = finishInstallation();
        if ($result['success']) {
            header('Location: login.php');
            exit;
        } else {
            $message = $result['message'];
        }
    }
}

function checkSystemRequirements() {
    $requirements = [
        'PHP >= 7.4' => version_compare(PHP_VERSION, '7.4.0', '>='),
        'PDO Extension' => extension_loaded('pdo'),
        'PDO MySQL' => extension_loaded('pdo_mysql'),
        'GD Extension' => extension_loaded('gd'),
        'FileInfo Extension' => extension_loaded('fileinfo'),
        'Writable uploads/' => is_writable('uploads/') || mkdir('uploads/', 0755, true),
        'Writable exports/' => is_writable('exports/') || mkdir('exports/', 0755, true),
    ];
    
    return !in_array(false, $requirements);
}

function createDatabase() {
    try {
        // الاتصال بـ MySQL بدون تحديد قاعدة بيانات
        $dsn = "mysql:host=localhost;charset=utf8mb4";
        $pdo = new PDO($dsn, 'irjnpfzw_mr', 'irjnpfzw_mr');
        $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
        
        // قراءة ملف SQL
        $sql_file = 'database/schema.sql';
        if (!file_exists($sql_file)) {
            return ['success' => false, 'message' => 'ملف قاعدة البيانات غير موجود'];
        }
        
        $sql = file_get_contents($sql_file);
        
        // تنفيذ الاستعلامات
        $statements = explode(';', $sql);
        foreach ($statements as $statement) {
            $statement = trim($statement);
            if (!empty($statement)) {
                $pdo->exec($statement);
            }
        }
        
        return ['success' => true, 'message' => 'تم إنشاء قاعدة البيانات بنجاح'];
        
    } catch (PDOException $e) {
        return ['success' => false, 'message' => 'خطأ في إنشاء قاعدة البيانات: ' . $e->getMessage()];
    }
}

function finishInstallation() {
    try {
        // إنشاء ملف .htaccess للأمان
        $htaccess_content = "
# منع الوصول للملفات الحساسة
<Files ~ \"^\.ht\">
    Order allow,deny
    Deny from all
</Files>

<Files ~ \"\.sql$\">
    Order allow,deny
    Deny from all
</Files>

<Files ~ \"config\.php$\">
    Order allow,deny
    Deny from all
</Files>

# إعادة توجيه الأخطاء
ErrorDocument 404 /404.php
ErrorDocument 403 /403.php

# ضغط الملفات
<IfModule mod_deflate.c>
    AddOutputFilterByType DEFLATE text/plain
    AddOutputFilterByType DEFLATE text/html
    AddOutputFilterByType DEFLATE text/xml
    AddOutputFilterByType DEFLATE text/css
    AddOutputFilterByType DEFLATE application/xml
    AddOutputFilterByType DEFLATE application/xhtml+xml
    AddOutputFilterByType DEFLATE application/rss+xml
    AddOutputFilterByType DEFLATE application/javascript
    AddOutputFilterByType DEFLATE application/x-javascript
</IfModule>

# تخزين مؤقت للملفات الثابتة
<IfModule mod_expires.c>
    ExpiresActive on
    ExpiresByType text/css \"access plus 1 year\"
    ExpiresByType application/javascript \"access plus 1 year\"
    ExpiresByType image/png \"access plus 1 year\"
    ExpiresByType image/jpg \"access plus 1 year\"
    ExpiresByType image/jpeg \"access plus 1 year\"
    ExpiresByType image/gif \"access plus 1 year\"
</IfModule>
";
        
        file_put_contents('.htaccess', $htaccess_content);
        
        // إنشاء ملف index.php في مجلد uploads للأمان
        $index_content = "<?php\n// منع الوصول المباشر\nheader('HTTP/1.0 403 Forbidden');\nexit('Access Denied');\n?>";
        file_put_contents('uploads/index.php', $index_content);
        file_put_contents('exports/index.php', $index_content);
        
        // إنشاء مجلدات فرعية
        $folders = [
            'uploads/supporters',
            'uploads/events',
            'uploads/competitors',
            'uploads/imports',
            'exports/supporters',
            'exports/reports'
        ];
        
        foreach ($folders as $folder) {
            if (!file_exists($folder)) {
                mkdir($folder, 0755, true);
                file_put_contents($folder . '/index.php', $index_content);
            }
        }
        
        return ['success' => true, 'message' => 'تم تثبيت النظام بنجاح'];
        
    } catch (Exception $e) {
        return ['success' => false, 'message' => 'خطأ في إنهاء التثبيت: ' . $e->getMessage()];
    }
}
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>تثبيت نظام إدارة الحملة الانتخابية</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }
        
        .install-container {
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 20px;
        }
        
        .install-card {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.2);
            overflow: hidden;
            max-width: 600px;
            width: 100%;
        }
        
        .install-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 2rem;
            text-align: center;
        }
        
        .install-header i {
            font-size: 3rem;
            margin-bottom: 1rem;
        }
        
        .install-body {
            padding: 2rem;
        }
        
        .step-indicator {
            display: flex;
            justify-content: center;
            margin-bottom: 2rem;
        }
        
        .step {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            background-color: #e9ecef;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 10px;
            font-weight: bold;
            position: relative;
        }
        
        .step.active {
            background-color: #007bff;
            color: white;
        }
        
        .step.completed {
            background-color: #28a745;
            color: white;
        }
        
        .step::after {
            content: '';
            position: absolute;
            top: 50%;
            left: 100%;
            width: 20px;
            height: 2px;
            background-color: #e9ecef;
            transform: translateY(-50%);
        }
        
        .step:last-child::after {
            display: none;
        }
        
        .step.completed::after {
            background-color: #28a745;
        }
        
        .requirement-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 10px 0;
            border-bottom: 1px solid #eee;
        }
        
        .requirement-item:last-child {
            border-bottom: none;
        }
        
        .requirement-status {
            font-weight: bold;
        }
        
        .requirement-status.success {
            color: #28a745;
        }
        
        .requirement-status.error {
            color: #dc3545;
        }
    </style>
</head>
<body>
    <div class="install-container">
        <div class="install-card">
            <div class="install-header">
                <i class="fas fa-vote-yea"></i>
                <h3>تثبيت نظام إدارة الحملة الانتخابية</h3>
                <p class="mb-0">مرحباً بك في معالج التثبيت</p>
            </div>
            
            <div class="install-body">
                <!-- مؤشر الخطوات -->
                <div class="step-indicator">
                    <div class="step <?php echo $step >= 1 ? ($step > 1 ? 'completed' : 'active') : ''; ?>">1</div>
                    <div class="step <?php echo $step >= 2 ? ($step > 2 ? 'completed' : 'active') : ''; ?>">2</div>
                    <div class="step <?php echo $step >= 3 ? 'active' : ''; ?>">3</div>
                </div>
                
                <?php if (!empty($message)): ?>
                    <div class="alert alert-danger" role="alert">
                        <i class="fas fa-exclamation-triangle me-2"></i>
                        <?php echo $message; ?>
                    </div>
                <?php endif; ?>
                
                <?php if ($step == 1): ?>
                    <!-- الخطوة 1: التحقق من المتطلبات -->
                    <h4 class="mb-3">الخطوة 1: التحقق من متطلبات النظام</h4>
                    
                    <div class="requirements-list">
                        <?php
                        $requirements = [
                            'PHP >= 7.4' => version_compare(PHP_VERSION, '7.4.0', '>='),
                            'PDO Extension' => extension_loaded('pdo'),
                            'PDO MySQL' => extension_loaded('pdo_mysql'),
                            'GD Extension' => extension_loaded('gd'),
                            'FileInfo Extension' => extension_loaded('fileinfo'),
                            'مجلد uploads قابل للكتابة' => is_writable('uploads/') || mkdir('uploads/', 0755, true),
                            'مجلد exports قابل للكتابة' => is_writable('exports/') || mkdir('exports/', 0755, true),
                        ];
                        
                        $all_met = true;
                        foreach ($requirements as $requirement => $met):
                            if (!$met) $all_met = false;
                        ?>
                            <div class="requirement-item">
                                <span><?php echo $requirement; ?></span>
                                <span class="requirement-status <?php echo $met ? 'success' : 'error'; ?>">
                                    <i class="fas fa-<?php echo $met ? 'check' : 'times'; ?>"></i>
                                    <?php echo $met ? 'متوفر' : 'غير متوفر'; ?>
                                </span>
                            </div>
                        <?php endforeach; ?>
                    </div>
                    
                    <div class="text-center mt-4">
                        <?php if ($all_met): ?>
                            <form method="POST">
                                <button type="submit" class="btn btn-primary btn-lg">
                                    <i class="fas fa-arrow-left me-2"></i>
                                    المتابعة إلى الخطوة التالية
                                </button>
                            </form>
                        <?php else: ?>
                            <div class="alert alert-warning">
                                يرجى التأكد من توفر جميع المتطلبات قبل المتابعة
                            </div>
                        <?php endif; ?>
                    </div>
                    
                <?php elseif ($step == 2): ?>
                    <!-- الخطوة 2: إنشاء قاعدة البيانات -->
                    <h4 class="mb-3">الخطوة 2: إنشاء قاعدة البيانات</h4>
                    
                    <div class="alert alert-info">
                        <h6><i class="fas fa-info-circle me-2"></i>معلومات قاعدة البيانات:</h6>
                        <ul class="mb-0">
                            <li><strong>اسم قاعدة البيانات:</strong> irjnpfzw_mr</li>
                            <li><strong>اسم المستخدم:</strong> irjnpfzw_mr</li>
                            <li><strong>كلمة المرور:</strong> irjnpfzw_mr</li>
                        </ul>
                    </div>
                    
                    <div class="alert alert-warning">
                        <strong>تنبيه:</strong> سيتم إنشاء قاعدة البيانات والجداول المطلوبة. تأكد من صحة بيانات الاتصال.
                    </div>
                    
                    <div class="text-center">
                        <form method="POST">
                            <button type="submit" class="btn btn-primary btn-lg">
                                <i class="fas fa-database me-2"></i>
                                إنشاء قاعدة البيانات
                            </button>
                        </form>
                    </div>
                    
                <?php elseif ($step == 3): ?>
                    <!-- الخطوة 3: إنهاء التثبيت -->
                    <h4 class="mb-3">الخطوة 3: إنهاء التثبيت</h4>
                    
                    <div class="alert alert-success">
                        <h6><i class="fas fa-check-circle me-2"></i>تم إنشاء قاعدة البيانات بنجاح!</h6>
                        <p class="mb-0">الآن سيتم إنهاء عملية التثبيت وإعداد الملفات الأمنية.</p>
                    </div>
                    
                    <div class="alert alert-info">
                        <h6><i class="fas fa-user me-2"></i>بيانات تسجيل الدخول الافتراضية:</h6>
                        <ul class="mb-0">
                            <li><strong>اسم المستخدم:</strong> abd</li>
                            <li><strong>كلمة المرور:</strong> abdabd</li>
                            <li><strong>رقم الهاتف:</strong> 07719992716</li>
                        </ul>
                    </div>
                    
                    <div class="text-center">
                        <form method="POST">
                            <button type="submit" class="btn btn-success btn-lg">
                                <i class="fas fa-check me-2"></i>
                                إنهاء التثبيت
                            </button>
                        </form>
                    </div>
                    
                <?php endif; ?>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
