<?php
// صفحة إضافة مؤيد للإداريين
session_start();
header('Content-Type: text/html; charset=utf-8');
ini_set('default_charset', 'utf-8');
mb_internal_encoding('UTF-8');

require_once '../../config/config.php';
require_once '../../config/database.php';

// التحقق من تسجيل الدخول
if (!isset($_SESSION['admin_id'])) {
    redirect('login.php');
}

$admin_id = $_SESSION['admin_id'];
$admin_name = $_SESSION['admin_name'];
$admin_region_id = $_SESSION['admin_region_id'];

// معالجة إضافة مؤيد جديد
if ($_SERVER['REQUEST_METHOD'] == 'POST' && isset($_POST['action']) && $_POST['action'] == 'add') {
    $full_name = sanitize($_POST['full_name']);
    $gender = sanitize($_POST['gender']);
    $marital_status = sanitize($_POST['marital_status']);
    $birth_date = sanitize($_POST['birth_date']);
    $education = sanitize($_POST['education']);
    $profession = sanitize($_POST['profession']);
    $address = sanitize($_POST['address']);
    $phone = sanitize($_POST['phone']);
    $voter_number = sanitize($_POST['voter_number']);
    $voting_center = sanitize($_POST['voting_center']);
    $region_id = $admin_region_id ?: (int)$_POST['region_id'];
    $notes = sanitize($_POST['notes']);

    // التحقق من صحة البيانات
    if (empty($full_name) || empty($phone) || empty($region_id) || empty($address) || empty($birth_date)) {
        showMessage('يرجى ملء جميع الحقول المطلوبة (الاسم، الهاتف، المنطقة، العنوان، تاريخ الميلاد)', 'error');
    } else {
        // التحقق من عدم تكرار رقم الناخب
        $insert_success = true;
        if (!empty($voter_number)) {
            $existing = fetchOne("SELECT id FROM supporters WHERE voter_number = ?", [$voter_number]);
            if ($existing) {
                showMessage('رقم الناخب موجود مسبقاً', 'error');
                $insert_success = false;
            }
        }

        if ($insert_success) {
            $sql = "INSERT INTO supporters (full_name, gender, marital_status, birth_date, education, profession, address, phone, voter_number, voting_center, region_id, notes, added_by, admin_id, created_at) 
                    VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, NOW())";
            
            $result = executeQuery($sql, [
                $full_name, $gender, $marital_status, $birth_date, $education, 
                $profession, $address, $phone, $voter_number, $voting_center, 
                $region_id, $notes, $admin_id, $admin_id
            ]);

            if ($result) {
                // تحديث إحصائيات الإداري
                $today = date('Y-m-d');
                $stat_sql = "INSERT INTO admin_statistics (admin_id, date, supporters_added) 
                            VALUES (?, ?, 1) 
                            ON DUPLICATE KEY UPDATE supporters_added = supporters_added + 1";
                executeQuery($stat_sql, [$admin_id, $today]);

                showMessage('تم إضافة المؤيد بنجاح', 'success');
                redirect('dashboard.php');
            } else {
                showMessage('حدث خطأ أثناء إضافة المؤيد', 'error');
            }
        }
    }
}

// جلب المناطق
$regions = fetchAll("SELECT * FROM regions ORDER BY name");
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إضافة مؤيد جديد - لوحة تحكم الإداريين</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: #f8f9fa;
        }
        .main-content {
            margin-top: 80px;
        }
        .form-card {
            border-radius: 15px;
            border: none;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        }
    </style>
</head>
<body>
    <!-- شريط التنقل العلوي -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-dark fixed-top">
        <div class="container-fluid">
            <a class="navbar-brand" href="dashboard.php">
                <i class="fas fa-user-shield me-2"></i>
                لوحة تحكم الإداريين
            </a>
            
            <div class="navbar-nav ms-auto">
                <div class="nav-item dropdown">
                    <a class="nav-link dropdown-toggle" href="#" id="userDropdown" role="button" data-bs-toggle="dropdown">
                        <i class="fas fa-user-circle me-1"></i>
                        <?php echo htmlspecialchars($admin_name); ?>
                    </a>
                    <ul class="dropdown-menu dropdown-menu-end">
                        <li><a class="dropdown-item" href="dashboard.php"><i class="fas fa-tachometer-alt me-2"></i>لوحة التحكم</a></li>
                        <li><a class="dropdown-item" href="logout.php"><i class="fas fa-sign-out-alt me-2"></i>تسجيل الخروج</a></li>
                    </ul>
                </div>
            </div>
        </div>
    </nav>

    <!-- المحتوى الرئيسي -->
    <div class="container main-content">
        <div class="row justify-content-center">
            <div class="col-md-8">
                <div class="card form-card">
                    <div class="card-header bg-primary text-white">
                        <h4 class="mb-0">
                            <i class="fas fa-user-plus me-2"></i>
                            إضافة مؤيد جديد
                        </h4>
                    </div>
                    
                    <div class="card-body">
                        <?php displayMessage(); ?>
                        
                        <form method="POST" action="">
                            <input type="hidden" name="action" value="add">
                            
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="full_name" class="form-label">الاسم الكامل *</label>
                                        <input type="text" class="form-control" id="full_name" name="full_name" required>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="phone" class="form-label">رقم الهاتف *</label>
                                        <input type="tel" class="form-control" id="phone" name="phone" 
                                               pattern="07[0-9]{9}" placeholder="07XXXXXXXXX" required>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="row">
                                <div class="col-md-4">
                                    <div class="mb-3">
                                        <label for="gender" class="form-label">الجنس *</label>
                                        <select class="form-select" id="gender" name="gender" required>
                                            <option value="">اختر الجنس</option>
                                            <option value="male">ذكر</option>
                                            <option value="female">أنثى</option>
                                        </select>
                                    </div>
                                </div>
                                <div class="col-md-4">
                                    <div class="mb-3">
                                        <label for="marital_status" class="form-label">الحالة الاجتماعية</label>
                                        <select class="form-select" id="marital_status" name="marital_status">
                                            <option value="single">أعزب</option>
                                            <option value="married">متزوج</option>
                                            <option value="divorced">مطلق</option>
                                            <option value="widowed">أرمل</option>
                                        </select>
                                    </div>
                                </div>
                                <div class="col-md-4">
                                    <div class="mb-3">
                                        <label for="birth_date" class="form-label">تاريخ الميلاد *</label>
                                        <input type="date" class="form-control" id="birth_date" name="birth_date" required>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="education" class="form-label">المؤهل العلمي</label>
                                        <input type="text" class="form-control" id="education" name="education">
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="profession" class="form-label">المهنة</label>
                                        <input type="text" class="form-control" id="profession" name="profession">
                                    </div>
                                </div>
                            </div>
                            
                            <div class="mb-3">
                                <label for="address" class="form-label">العنوان *</label>
                                <textarea class="form-control" id="address" name="address" rows="2" required></textarea>
                            </div>
                            
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="voter_number" class="form-label">رقم الناخب</label>
                                        <input type="text" class="form-control" id="voter_number" name="voter_number">
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="voting_center" class="form-label">مركز الاقتراع</label>
                                        <input type="text" class="form-control" id="voting_center" name="voting_center">
                                    </div>
                                </div>
                            </div>
                            
                            <?php if (!$admin_region_id): ?>
                            <div class="mb-3">
                                <label for="region_id" class="form-label">المنطقة *</label>
                                <select class="form-select" id="region_id" name="region_id" required>
                                    <option value="">اختر المنطقة</option>
                                    <?php foreach ($regions as $region): ?>
                                    <option value="<?php echo $region['id']; ?>"><?php echo htmlspecialchars($region['name']); ?></option>
                                    <?php endforeach; ?>
                                </select>
                            </div>
                            <?php else: ?>
                            <div class="mb-3">
                                <label class="form-label">المنطقة</label>
                                <input type="text" class="form-control" value="<?php 
                                    $admin_region = fetchOne("SELECT name FROM regions WHERE id = ?", [$admin_region_id]);
                                    echo htmlspecialchars($admin_region['name']);
                                ?>" readonly>
                            </div>
                            <?php endif; ?>
                            
                            <div class="mb-3">
                                <label for="notes" class="form-label">ملاحظات</label>
                                <textarea class="form-control" id="notes" name="notes" rows="2"></textarea>
                            </div>
                            
                            <div class="d-flex justify-content-between">
                                <a href="dashboard.php" class="btn btn-secondary">
                                    <i class="fas fa-arrow-left me-2"></i>العودة
                                </a>
                                <button type="submit" class="btn btn-primary">
                                    <i class="fas fa-save me-2"></i>إضافة المؤيد
                                </button>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // تعيين الحد الأقصى لتاريخ الميلاد (18 سنة)
        document.addEventListener('DOMContentLoaded', function() {
            const birthDateInput = document.getElementById('birth_date');
            if (birthDateInput) {
                const today = new Date();
                const maxDate = new Date(today.getFullYear() - 18, today.getMonth(), today.getDate());
                birthDateInput.max = maxDate.toISOString().split('T')[0];
            }
        });
    </script>
</body>
</html>
