<?php
// صفحة إضافة مؤيد للإداريين
session_start();
header('Content-Type: text/html; charset=utf-8');
ini_set('default_charset', 'utf-8');
mb_internal_encoding('UTF-8');

require_once '../../config/config.php';
require_once '../../config/database.php';

// التحقق من تسجيل الدخول
if (!isset($_SESSION['admin_id'])) {
    redirect('login.php');
}

$admin_id = $_SESSION['admin_id'];
$admin_name = $_SESSION['admin_name'];
$admin_region_id = $_SESSION['admin_region_id'];

// معالجة إضافة مؤيد جديد
if ($_SERVER['REQUEST_METHOD'] == 'POST' && isset($_POST['action']) && $_POST['action'] == 'add') {
    $full_name = sanitize($_POST['full_name']);
    $gender = sanitize($_POST['gender']);
    $marital_status = sanitize($_POST['marital_status']);
    $birth_date = sanitize($_POST['birth_date']);
    $education = sanitize($_POST['education']);
    $profession = sanitize($_POST['profession']);
    $address = sanitize($_POST['address']);
    $phone = sanitize($_POST['phone']);
    $voter_number = sanitize($_POST['voter_number']);
    $voting_center = sanitize($_POST['voting_center']);
    $region_id = $admin_region_id ?: (int)$_POST['region_id'];
    $notes = sanitize($_POST['notes']);

    // التحقق من صحة البيانات
    if (empty($full_name) || empty($phone) || empty($region_id) || empty($address) || empty($birth_date)) {
        showMessage('يرجى ملء جميع الحقول المطلوبة (الاسم، الهاتف، المنطقة، العنوان، تاريخ الميلاد)', 'error');
    } else {
        // التحقق من عدم تكرار رقم الناخب
        $insert_success = true;
        if (!empty($voter_number)) {
            $existing = fetchOne("SELECT id FROM supporters WHERE voter_number = ?", [$voter_number]);
            if ($existing) {
                showMessage('رقم الناخب موجود مسبقاً', 'error');
                $insert_success = false;
            }
        }

        if ($insert_success) {
            $sql = "INSERT INTO supporters (full_name, gender, marital_status, birth_date, education, profession, address, phone, voter_number, voting_center, region_id, notes, added_by, created_at)
                    VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, NOW())";

            $result = executeQuery($sql, [
                $full_name, $gender, $marital_status, $birth_date, $education,
                $profession, $address, $phone, $voter_number, $voting_center,
                $region_id, $notes, $admin_id
            ]);

            if ($result) {
                $supporter_id = $pdo->lastInsertId();

                // معالجة رفع المرفقات
                $attachment_types = ['voter_id_front', 'voter_id_back', 'national_id_front', 'national_id_back', 'residence_card'];
                $upload_success = true;

                foreach ($attachment_types as $type) {
                    if (isset($_FILES[$type]) && $_FILES[$type]['error'] == 0) {
                        $file = $_FILES[$type];

                        // التحقق من نوع الملف
                        $allowed_types = ['image/jpeg', 'image/jpg', 'image/png', 'image/gif', 'application/pdf'];
                        if (!in_array($file['type'], $allowed_types)) {
                            continue;
                        }

                        // التحقق من حجم الملف (5MB)
                        if ($file['size'] > 5 * 1024 * 1024) {
                            continue;
                        }

                        // تحديد مجلد التحميل
                        $upload_dir = "../../uploads/supporters/";
                        if (strpos($type, 'voter_id') !== false) {
                            $upload_dir .= "voter_ids/";
                        } elseif (strpos($type, 'national_id') !== false) {
                            $upload_dir .= "national_ids/";
                        } elseif ($type == 'residence_card') {
                            $upload_dir .= "residence_cards/";
                        } else {
                            $upload_dir .= "other/";
                        }

                        // إنشاء المجلد إذا لم يكن موجوداً
                        if (!file_exists($upload_dir)) {
                            mkdir($upload_dir, 0755, true);
                        }

                        // إنشاء اسم ملف فريد
                        $file_extension = pathinfo($file['name'], PATHINFO_EXTENSION);
                        $file_name = $supporter_id . "_" . $type . "_" . time() . "." . $file_extension;
                        $file_path = $upload_dir . $file_name;

                        // رفع الملف
                        if (move_uploaded_file($file['tmp_name'], $file_path)) {
                            // حفظ في قاعدة البيانات
                            $attachment_sql = "INSERT INTO supporter_attachments (supporter_id, attachment_type, file_name, file_path, file_size, file_type, uploaded_by)
                                             VALUES (?, ?, ?, ?, ?, ?, ?)";

                            executeQuery($attachment_sql, [
                                $supporter_id, $type, $file['name'], $file_path,
                                $file['size'], $file['type'], $admin_id
                            ]);
                        }
                    }
                }

                // تحديث إحصائيات الإداري
                $today = date('Y-m-d');
                $stat_sql = "INSERT INTO admin_statistics (admin_id, date, supporters_added)
                            VALUES (?, ?, 1)
                            ON DUPLICATE KEY UPDATE supporters_added = supporters_added + 1";
                executeQuery($stat_sql, [$admin_id, $today]);

                showMessage('تم إضافة المؤيد والمرفقات بنجاح', 'success');
                redirect('dashboard.php');
            } else {
                showMessage('حدث خطأ أثناء إضافة المؤيد', 'error');
            }
        }
    }
}

// جلب المناطق
$regions = fetchAll("SELECT * FROM regions ORDER BY name");
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إضافة مؤيد جديد - لوحة تحكم الإداريين</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: #f8f9fa;
        }
        .main-content {
            margin-top: 80px;
            padding: 15px;
        }
        .form-card {
            border-radius: 15px;
            border: none;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        }
        .attachment-section {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 20px;
            margin: 20px 0;
            border: 2px dashed #dee2e6;
        }
        .file-upload-box {
            border: 2px dashed #007bff;
            border-radius: 10px;
            padding: 20px;
            text-align: center;
            background: #f8f9ff;
            transition: all 0.3s;
            cursor: pointer;
            margin-bottom: 15px;
        }
        .file-upload-box:hover {
            border-color: #0056b3;
            background: #e7f3ff;
        }
        .file-upload-box.dragover {
            border-color: #28a745;
            background: #f0fff4;
        }
        .file-preview {
            max-width: 100px;
            max-height: 100px;
            border-radius: 5px;
            margin: 5px;
        }
        .attachment-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 15px;
            margin-top: 20px;
        }

        /* تصميم متجاوب للهواتف */
        @media (max-width: 768px) {
            .main-content {
                margin-top: 70px;
                padding: 10px;
            }
            .form-card {
                margin: 0;
                border-radius: 10px;
            }
            .attachment-grid {
                grid-template-columns: 1fr;
                gap: 10px;
            }
            .file-upload-box {
                padding: 15px;
            }
            .btn {
                width: 100%;
                margin-bottom: 10px;
            }
            .row .col-md-6 {
                margin-bottom: 15px;
            }
        }

        /* تصميم متجاوب للأيباد */
        @media (min-width: 769px) and (max-width: 1024px) {
            .attachment-grid {
                grid-template-columns: repeat(2, 1fr);
            }
        }

        .required-field::after {
            content: " *";
            color: #dc3545;
        }
    </style>
</head>
<body>
    <!-- شريط التنقل العلوي -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-dark fixed-top">
        <div class="container-fluid">
            <a class="navbar-brand" href="dashboard.php">
                <i class="fas fa-user-shield me-2"></i>
                لوحة تحكم الإداريين
            </a>

            <div class="navbar-nav ms-auto">
                <div class="nav-item dropdown">
                    <a class="nav-link dropdown-toggle" href="#" id="userDropdown" role="button" data-bs-toggle="dropdown">
                        <i class="fas fa-user-circle me-1"></i>
                        <?php echo htmlspecialchars($admin_name); ?>
                    </a>
                    <ul class="dropdown-menu dropdown-menu-end">
                        <li><a class="dropdown-item" href="dashboard.php"><i class="fas fa-tachometer-alt me-2"></i>لوحة التحكم</a></li>
                        <li><a class="dropdown-item" href="logout.php"><i class="fas fa-sign-out-alt me-2"></i>تسجيل الخروج</a></li>
                    </ul>
                </div>
            </div>
        </div>
    </nav>

    <!-- المحتوى الرئيسي -->
    <div class="container main-content">
        <div class="row justify-content-center">
            <div class="col-md-8">
                <div class="card form-card">
                    <div class="card-header bg-primary text-white">
                        <h4 class="mb-0">
                            <i class="fas fa-user-plus me-2"></i>
                            إضافة مؤيد جديد
                        </h4>
                    </div>

                    <div class="card-body">
                        <?php displayMessage(); ?>

                        <form method="POST" action="" enctype="multipart/form-data">
                            <input type="hidden" name="action" value="add">

                            <div class="row">
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="full_name" class="form-label">الاسم الكامل *</label>
                                        <input type="text" class="form-control" id="full_name" name="full_name" required>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="phone" class="form-label">رقم الهاتف *</label>
                                        <input type="tel" class="form-control" id="phone" name="phone"
                                               pattern="07[0-9]{9}" placeholder="07XXXXXXXXX" required>
                                    </div>
                                </div>
                            </div>

                            <div class="row">
                                <div class="col-md-4">
                                    <div class="mb-3">
                                        <label for="gender" class="form-label">الجنس *</label>
                                        <select class="form-select" id="gender" name="gender" required>
                                            <option value="">اختر الجنس</option>
                                            <option value="male">ذكر</option>
                                            <option value="female">أنثى</option>
                                        </select>
                                    </div>
                                </div>
                                <div class="col-md-4">
                                    <div class="mb-3">
                                        <label for="marital_status" class="form-label">الحالة الاجتماعية</label>
                                        <select class="form-select" id="marital_status" name="marital_status">
                                            <option value="single">أعزب</option>
                                            <option value="married">متزوج</option>
                                            <option value="divorced">مطلق</option>
                                            <option value="widowed">أرمل</option>
                                        </select>
                                    </div>
                                </div>
                                <div class="col-md-4">
                                    <div class="mb-3">
                                        <label for="birth_date" class="form-label">تاريخ الميلاد *</label>
                                        <input type="date" class="form-control" id="birth_date" name="birth_date" required>
                                    </div>
                                </div>
                            </div>

                            <div class="row">
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="education" class="form-label">المؤهل العلمي</label>
                                        <input type="text" class="form-control" id="education" name="education">
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="profession" class="form-label">المهنة</label>
                                        <input type="text" class="form-control" id="profession" name="profession">
                                    </div>
                                </div>
                            </div>

                            <div class="mb-3">
                                <label for="address" class="form-label">العنوان *</label>
                                <textarea class="form-control" id="address" name="address" rows="2" required></textarea>
                            </div>

                            <div class="row">
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="voter_number" class="form-label">رقم الناخب</label>
                                        <input type="text" class="form-control" id="voter_number" name="voter_number">
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="voting_center" class="form-label">مركز الاقتراع</label>
                                        <input type="text" class="form-control" id="voting_center" name="voting_center">
                                    </div>
                                </div>
                            </div>

                            <?php if (!$admin_region_id): ?>
                            <div class="mb-3">
                                <label for="region_id" class="form-label">المنطقة *</label>
                                <select class="form-select" id="region_id" name="region_id" required>
                                    <option value="">اختر المنطقة</option>
                                    <?php foreach ($regions as $region): ?>
                                    <option value="<?php echo $region['id']; ?>"><?php echo htmlspecialchars($region['name']); ?></option>
                                    <?php endforeach; ?>
                                </select>
                            </div>
                            <?php else: ?>
                            <div class="mb-3">
                                <label class="form-label">المنطقة</label>
                                <input type="text" class="form-control" value="<?php
                                    $admin_region = fetchOne("SELECT name FROM regions WHERE id = ?", [$admin_region_id]);
                                    echo htmlspecialchars($admin_region['name']);
                                ?>" readonly>
                            </div>
                            <?php endif; ?>

                            <!-- قسم المرفقات -->
                            <div class="attachment-section">
                                <h5 class="mb-4">
                                    <i class="fas fa-paperclip me-2"></i>
                                    المرفقات المطلوبة
                                </h5>

                                <div class="attachment-grid">
                                    <!-- هوية الناخب - الوجه -->
                                    <div class="file-upload-container">
                                        <label class="form-label required-field">هوية الناخب (الوجه)</label>
                                        <div class="file-upload-box" onclick="document.getElementById('voter_id_front').click()">
                                            <i class="fas fa-id-card fa-2x text-primary mb-2"></i>
                                            <p class="mb-0">اضغط لرفع صورة وجه هوية الناخب</p>
                                            <small class="text-muted">JPG, PNG, PDF - حد أقصى 5MB</small>
                                        </div>
                                        <input type="file" id="voter_id_front" name="voter_id_front" accept="image/*,application/pdf" style="display: none;" onchange="previewFile(this, 'voter_id_front_preview')">
                                        <div id="voter_id_front_preview" class="file-preview-container"></div>
                                    </div>

                                    <!-- هوية الناخب - الظهر -->
                                    <div class="file-upload-container">
                                        <label class="form-label required-field">هوية الناخب (الظهر)</label>
                                        <div class="file-upload-box" onclick="document.getElementById('voter_id_back').click()">
                                            <i class="fas fa-id-card fa-2x text-primary mb-2"></i>
                                            <p class="mb-0">اضغط لرفع صورة ظهر هوية الناخب</p>
                                            <small class="text-muted">JPG, PNG, PDF - حد أقصى 5MB</small>
                                        </div>
                                        <input type="file" id="voter_id_back" name="voter_id_back" accept="image/*,application/pdf" style="display: none;" onchange="previewFile(this, 'voter_id_back_preview')">
                                        <div id="voter_id_back_preview" class="file-preview-container"></div>
                                    </div>

                                    <!-- البطاقة الوطنية - الوجه -->
                                    <div class="file-upload-container">
                                        <label class="form-label required-field">البطاقة الوطنية (الوجه)</label>
                                        <div class="file-upload-box" onclick="document.getElementById('national_id_front').click()">
                                            <i class="fas fa-address-card fa-2x text-success mb-2"></i>
                                            <p class="mb-0">اضغط لرفع صورة وجه البطاقة الوطنية</p>
                                            <small class="text-muted">JPG, PNG, PDF - حد أقصى 5MB</small>
                                        </div>
                                        <input type="file" id="national_id_front" name="national_id_front" accept="image/*,application/pdf" style="display: none;" onchange="previewFile(this, 'national_id_front_preview')">
                                        <div id="national_id_front_preview" class="file-preview-container"></div>
                                    </div>

                                    <!-- البطاقة الوطنية - الظهر -->
                                    <div class="file-upload-container">
                                        <label class="form-label required-field">البطاقة الوطنية (الظهر)</label>
                                        <div class="file-upload-box" onclick="document.getElementById('national_id_back').click()">
                                            <i class="fas fa-address-card fa-2x text-success mb-2"></i>
                                            <p class="mb-0">اضغط لرفع صورة ظهر البطاقة الوطنية</p>
                                            <small class="text-muted">JPG, PNG, PDF - حد أقصى 5MB</small>
                                        </div>
                                        <input type="file" id="national_id_back" name="national_id_back" accept="image/*,application/pdf" style="display: none;" onchange="previewFile(this, 'national_id_back_preview')">
                                        <div id="national_id_back_preview" class="file-preview-container"></div>
                                    </div>

                                    <!-- بطاقة السكن -->
                                    <div class="file-upload-container">
                                        <label class="form-label required-field">بطاقة السكن</label>
                                        <div class="file-upload-box" onclick="document.getElementById('residence_card').click()">
                                            <i class="fas fa-home fa-2x text-warning mb-2"></i>
                                            <p class="mb-0">اضغط لرفع صورة بطاقة السكن</p>
                                            <small class="text-muted">JPG, PNG, PDF - حد أقصى 5MB</small>
                                        </div>
                                        <input type="file" id="residence_card" name="residence_card" accept="image/*,application/pdf" style="display: none;" onchange="previewFile(this, 'residence_card_preview')">
                                        <div id="residence_card_preview" class="file-preview-container"></div>
                                    </div>
                                </div>

                                <div class="alert alert-info mt-3">
                                    <i class="fas fa-info-circle me-2"></i>
                                    <strong>تعليمات المرفقات:</strong>
                                    <ul class="mb-0 mt-2">
                                        <li>تأكد من وضوح الصور وقابليتها للقراءة</li>
                                        <li>الحد الأقصى لحجم كل ملف هو 5 ميجابايت</li>
                                        <li>الصيغ المدعومة: JPG, PNG, PDF</li>
                                        <li>يفضل التصوير في إضاءة جيدة</li>
                                    </ul>
                                </div>
                            </div>

                            <div class="mb-3">
                                <label for="notes" class="form-label">ملاحظات</label>
                                <textarea class="form-control" id="notes" name="notes" rows="2"></textarea>
                            </div>

                            <div class="d-flex justify-content-between">
                                <a href="dashboard.php" class="btn btn-secondary">
                                    <i class="fas fa-arrow-left me-2"></i>العودة
                                </a>
                                <button type="submit" class="btn btn-primary">
                                    <i class="fas fa-save me-2"></i>إضافة المؤيد
                                </button>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // تعيين الحد الأقصى لتاريخ الميلاد (18 سنة)
        document.addEventListener('DOMContentLoaded', function() {
            const birthDateInput = document.getElementById('birth_date');
            if (birthDateInput) {
                const today = new Date();
                const maxDate = new Date(today.getFullYear() - 18, today.getMonth(), today.getDate());
                birthDateInput.max = maxDate.toISOString().split('T')[0];
            }
        });

        // معاينة الملفات
        function previewFile(input, previewId) {
            const file = input.files[0];
            const previewContainer = document.getElementById(previewId);

            if (file) {
                const reader = new FileReader();

                reader.onload = function(e) {
                    const fileType = file.type;
                    let previewHTML = '';

                    if (fileType.startsWith('image/')) {
                        previewHTML = `
                            <div class="file-preview-item">
                                <img src="${e.target.result}" class="file-preview" alt="معاينة">
                                <div class="file-info">
                                    <small class="text-success">
                                        <i class="fas fa-check-circle"></i> ${file.name}
                                        <br>الحجم: ${formatFileSize(file.size)}
                                    </small>
                                </div>
                            </div>
                        `;
                    } else if (fileType === 'application/pdf') {
                        previewHTML = `
                            <div class="file-preview-item">
                                <div class="pdf-preview">
                                    <i class="fas fa-file-pdf fa-3x text-danger"></i>
                                    <div class="file-info">
                                        <small class="text-success">
                                            <i class="fas fa-check-circle"></i> ${file.name}
                                            <br>الحجم: ${formatFileSize(file.size)}
                                        </small>
                                    </div>
                                </div>
                            </div>
                        `;
                    }

                    previewContainer.innerHTML = previewHTML;
                };

                reader.readAsDataURL(file);

                // تغيير لون صندوق الرفع
                const uploadBox = input.parentElement.querySelector('.file-upload-box');
                uploadBox.style.borderColor = '#28a745';
                uploadBox.style.backgroundColor = '#f0fff4';
            }
        }

        // تنسيق حجم الملف
        function formatFileSize(bytes) {
            if (bytes === 0) return '0 Bytes';
            const k = 1024;
            const sizes = ['Bytes', 'KB', 'MB', 'GB'];
            const i = Math.floor(Math.log(bytes) / Math.log(k));
            return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
        }

        // دعم السحب والإفلات
        document.querySelectorAll('.file-upload-box').forEach(box => {
            box.addEventListener('dragover', function(e) {
                e.preventDefault();
                this.classList.add('dragover');
            });

            box.addEventListener('dragleave', function(e) {
                e.preventDefault();
                this.classList.remove('dragover');
            });

            box.addEventListener('drop', function(e) {
                e.preventDefault();
                this.classList.remove('dragover');

                const files = e.dataTransfer.files;
                if (files.length > 0) {
                    const input = this.parentElement.querySelector('input[type="file"]');
                    input.files = files;

                    // تشغيل معاينة الملف
                    const previewId = input.getAttribute('onchange').match(/'([^']+)'/)[1];
                    previewFile(input, previewId);
                }
            });
        });

        // التحقق من صحة النموذج قبل الإرسال
        document.querySelector('form').addEventListener('submit', function(e) {
            const requiredFields = ['full_name', 'phone', 'address', 'birth_date', 'gender'];
            let isValid = true;

            requiredFields.forEach(fieldName => {
                const field = document.querySelector(`[name="${fieldName}"]`);
                if (!field.value.trim()) {
                    field.classList.add('is-invalid');
                    isValid = false;
                } else {
                    field.classList.remove('is-invalid');
                }
            });

            if (!isValid) {
                e.preventDefault();
                alert('يرجى ملء جميع الحقول المطلوبة');
                return false;
            }

            // إظهار مؤشر التحميل
            const submitBtn = document.querySelector('button[type="submit"]');
            submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>جاري الحفظ...';
            submitBtn.disabled = true;
        });
    </script>
</body>
</html>
