<?php
// إنشاء جداول التقارير والإحصائيات
header('Content-Type: text/html; charset=utf-8');
ini_set('default_charset', 'utf-8');
mb_internal_encoding('UTF-8');

require_once '../config/config.php';
require_once '../config/database.php';

function createReportsTables() {
    $tables = [];
    
    // جدول التقارير الأسبوعية
    $tables['weekly_reports'] = "CREATE TABLE IF NOT EXISTS weekly_reports (
        id INT AUTO_INCREMENT PRIMARY KEY,
        report_number VARCHAR(20) UNIQUE NOT NULL,
        admin_id INT NOT NULL,
        week_start DATE NOT NULL,
        week_end DATE NOT NULL,
        report_period VARCHAR(50) NOT NULL,
        
        -- إحصائيات المؤيدين
        supporters_added INT DEFAULT 0,
        supporters_contacted INT DEFAULT 0,
        supporters_visited INT DEFAULT 0,
        new_families_reached INT DEFAULT 0,
        
        -- إحصائيات الأنشطة
        events_attended INT DEFAULT 0,
        meetings_held INT DEFAULT 0,
        calls_made INT DEFAULT 0,
        visits_conducted INT DEFAULT 0,
        
        -- إحصائيات المطالب
        requests_submitted INT DEFAULT 0,
        requests_followed_up INT DEFAULT 0,
        requests_completed INT DEFAULT 0,
        
        -- إحصائيات الرسائل
        messages_sent INT DEFAULT 0,
        messages_received INT DEFAULT 0,
        
        -- المحتوى النصي
        achievements TEXT,
        challenges TEXT,
        solutions_implemented TEXT,
        next_week_plans TEXT,
        recommendations TEXT,
        notes TEXT,
        
        -- معلومات إضافية
        working_hours DECIMAL(5,2) DEFAULT 0,
        overtime_hours DECIMAL(5,2) DEFAULT 0,
        travel_distance_km DECIMAL(8,2) DEFAULT 0,
        fuel_cost DECIMAL(10,2) DEFAULT 0,
        other_expenses DECIMAL(10,2) DEFAULT 0,
        
        -- حالة التقرير
        status ENUM('draft', 'submitted', 'under_review', 'approved', 'rejected', 'revision_required') DEFAULT 'draft',
        priority ENUM('low', 'normal', 'high', 'urgent') DEFAULT 'normal',
        
        -- تواريخ مهمة
        submitted_at DATETIME,
        reviewed_at DATETIME,
        approved_at DATETIME,
        
        -- المراجعة والموافقة
        reviewed_by INT,
        approved_by INT,
        review_notes TEXT,
        rejection_reason TEXT,
        
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        
        FOREIGN KEY (admin_id) REFERENCES admins(id) ON DELETE CASCADE,
        FOREIGN KEY (reviewed_by) REFERENCES users(id) ON DELETE SET NULL,
        FOREIGN KEY (approved_by) REFERENCES users(id) ON DELETE SET NULL,
        
        INDEX idx_admin_id (admin_id),
        INDEX idx_week_start (week_start),
        INDEX idx_status (status),
        INDEX idx_report_number (report_number),
        UNIQUE KEY unique_admin_week (admin_id, week_start)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci";

    // جدول مرفقات التقارير
    $tables['report_attachments'] = "CREATE TABLE IF NOT EXISTS report_attachments (
        id INT AUTO_INCREMENT PRIMARY KEY,
        report_id INT NOT NULL,
        file_name VARCHAR(255) NOT NULL,
        original_name VARCHAR(255) NOT NULL,
        file_path VARCHAR(500) NOT NULL,
        file_size INT NOT NULL,
        file_type VARCHAR(100) NOT NULL,
        mime_type VARCHAR(100),
        attachment_type ENUM('photo', 'document', 'certificate', 'receipt', 'evidence', 'other') DEFAULT 'document',
        description TEXT,
        upload_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        uploaded_by INT,
        
        FOREIGN KEY (report_id) REFERENCES weekly_reports(id) ON DELETE CASCADE,
        FOREIGN KEY (uploaded_by) REFERENCES admins(id) ON DELETE SET NULL,
        
        INDEX idx_report_id (report_id),
        INDEX idx_attachment_type (attachment_type),
        INDEX idx_upload_date (upload_date)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci";

    // جدول قوالب التقارير
    $tables['report_templates'] = "CREATE TABLE IF NOT EXISTS report_templates (
        id INT AUTO_INCREMENT PRIMARY KEY,
        name VARCHAR(100) NOT NULL,
        description TEXT,
        template_type ENUM('weekly', 'monthly', 'quarterly', 'annual', 'special', 'custom') DEFAULT 'weekly',
        template_content JSON,
        fields_config JSON,
        is_active BOOLEAN DEFAULT TRUE,
        is_default BOOLEAN DEFAULT FALSE,
        created_by INT,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        
        FOREIGN KEY (created_by) REFERENCES users(id) ON DELETE SET NULL,
        
        INDEX idx_template_type (template_type),
        INDEX idx_is_active (is_active),
        INDEX idx_created_by (created_by)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci";

    // جدول تعليقات التقارير
    $tables['report_comments'] = "CREATE TABLE IF NOT EXISTS report_comments (
        id INT AUTO_INCREMENT PRIMARY KEY,
        report_id INT NOT NULL,
        commenter_type ENUM('admin', 'management', 'candidate') NOT NULL,
        commenter_id INT NOT NULL,
        comment TEXT NOT NULL,
        comment_type ENUM('note', 'question', 'suggestion', 'approval', 'rejection', 'revision') DEFAULT 'note',
        is_internal BOOLEAN DEFAULT FALSE,
        parent_comment_id INT NULL,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        
        FOREIGN KEY (report_id) REFERENCES weekly_reports(id) ON DELETE CASCADE,
        FOREIGN KEY (parent_comment_id) REFERENCES report_comments(id) ON DELETE CASCADE,
        
        INDEX idx_report_id (report_id),
        INDEX idx_commenter (commenter_type, commenter_id),
        INDEX idx_comment_type (comment_type),
        INDEX idx_created_at (created_at)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci";

    // جدول إحصائيات التقارير
    $tables['report_statistics'] = "CREATE TABLE IF NOT EXISTS report_statistics (
        id INT AUTO_INCREMENT PRIMARY KEY,
        period_type ENUM('weekly', 'monthly', 'quarterly', 'yearly') NOT NULL,
        period_start DATE NOT NULL,
        period_end DATE NOT NULL,
        admin_id INT,
        region_id INT,
        
        -- إحصائيات عامة
        total_reports INT DEFAULT 0,
        submitted_reports INT DEFAULT 0,
        approved_reports INT DEFAULT 0,
        rejected_reports INT DEFAULT 0,
        pending_reports INT DEFAULT 0,
        
        -- إحصائيات المؤيدين
        total_supporters_added INT DEFAULT 0,
        total_supporters_contacted INT DEFAULT 0,
        total_families_reached INT DEFAULT 0,
        
        -- إحصائيات الأنشطة
        total_events_attended INT DEFAULT 0,
        total_meetings_held INT DEFAULT 0,
        total_calls_made INT DEFAULT 0,
        total_visits_conducted INT DEFAULT 0,
        
        -- إحصائيات المطالب
        total_requests_submitted INT DEFAULT 0,
        total_requests_completed INT DEFAULT 0,
        
        -- إحصائيات الوقت والتكاليف
        total_working_hours DECIMAL(8,2) DEFAULT 0,
        total_overtime_hours DECIMAL(8,2) DEFAULT 0,
        total_travel_distance DECIMAL(10,2) DEFAULT 0,
        total_expenses DECIMAL(12,2) DEFAULT 0,
        
        -- متوسطات
        avg_supporters_per_admin DECIMAL(8,2) DEFAULT 0,
        avg_requests_per_admin DECIMAL(8,2) DEFAULT 0,
        avg_working_hours_per_admin DECIMAL(8,2) DEFAULT 0,
        
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        
        FOREIGN KEY (admin_id) REFERENCES admins(id) ON DELETE SET NULL,
        FOREIGN KEY (region_id) REFERENCES regions(id) ON DELETE SET NULL,
        
        UNIQUE KEY unique_period_admin_region (period_type, period_start, admin_id, region_id),
        INDEX idx_period (period_type, period_start),
        INDEX idx_admin_id (admin_id),
        INDEX idx_region_id (region_id)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci";

    return $tables;
}

function insertDefaultReportData() {
    $queries = [];
    
    // إدراج قوالب التقارير الافتراضية
    $queries[] = "INSERT IGNORE INTO report_templates (name, description, template_type, template_content, is_default) VALUES 
        ('التقرير الأسبوعي الأساسي', 'قالب التقرير الأسبوعي الافتراضي', 'weekly', 
         '{\"sections\": [\"achievements\", \"challenges\", \"supporters_added\", \"requests_submitted\", \"next_week_plans\"]}', 
         TRUE),
        ('التقرير الأسبوعي المفصل', 'قالب تقرير أسبوعي مفصل مع جميع الحقول', 'weekly', 
         '{\"sections\": [\"achievements\", \"challenges\", \"supporters_added\", \"events_attended\", \"meetings_held\", \"calls_made\", \"requests_submitted\", \"messages_sent\", \"working_hours\", \"expenses\", \"next_week_plans\", \"recommendations\"]}', 
         FALSE),
        ('التقرير الشهري', 'قالب التقرير الشهري', 'monthly', 
         '{\"sections\": [\"monthly_achievements\", \"monthly_challenges\", \"monthly_statistics\", \"monthly_plans\"]}', 
         FALSE)";

    return $queries;
}

// تشغيل الدوال إذا تم استدعاء الملف مباشرة
if (basename($_SERVER['PHP_SELF']) == 'create_reports_tables.php') {
    echo "<!DOCTYPE html>";
    echo "<html lang='ar' dir='rtl'>";
    echo "<head>";
    echo "<meta charset='UTF-8'>";
    echo "<meta name='viewport' content='width=device-width, initial-scale=1.0'>";
    echo "<title>إنشاء جداول التقارير</title>";
    echo "<link href='https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css' rel='stylesheet'>";
    echo "<link href='https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css' rel='stylesheet'>";
    echo "<style>";
    echo "body { font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); min-height: 100vh; padding: 2rem; }";
    echo ".system-card { background: white; border-radius: 15px; box-shadow: 0 10px 30px rgba(0,0,0,0.1); padding: 2rem; margin: 0 auto; max-width: 1000px; }";
    echo ".test-result { padding: 10px; margin: 5px 0; border-radius: 5px; }";
    echo ".test-success { background: #d4edda; border: 1px solid #c3e6cb; }";
    echo ".test-error { background: #f8d7da; border: 1px solid #f5c6cb; }";
    echo "</style>";
    echo "</head>";
    echo "<body>";

    echo "<div class='system-card'>";
    echo "<h1 class='text-center mb-4'><i class='fas fa-file-alt'></i> إنشاء جداول التقارير</h1>";

    if (isset($_POST['create_reports_tables'])) {
        echo "<div class='alert alert-info'>";
        echo "<h5>جاري إنشاء جداول التقارير...</h5>";
        echo "</div>";

        try {
            // إنشاء الجداول
            $tables = createReportsTables();
            foreach ($tables as $table_name => $sql) {
                try {
                    executeQuery($sql);
                    echo "<div class='test-result test-success'><i class='fas fa-check'></i> جدول $table_name: تم إنشاؤه بنجاح</div>";
                } catch (Exception $e) {
                    echo "<div class='test-result test-success'><i class='fas fa-info'></i> جدول $table_name: موجود مسبقاً</div>";
                }
            }

            // إدراج البيانات الافتراضية
            echo "<h6 class='mt-4'>إدراج البيانات الافتراضية:</h6>";
            $queries = insertDefaultReportData();
            foreach ($queries as $query) {
                try {
                    executeQuery($query);
                    echo "<div class='test-result test-success'><i class='fas fa-check'></i> تم إدراج قوالب التقارير الافتراضية</div>";
                } catch (Exception $e) {
                    echo "<div class='test-result test-success'><i class='fas fa-info'></i> القوالب موجودة مسبقاً</div>";
                }
            }

            echo "<div class='alert alert-success mt-4'>";
            echo "<h3><i class='fas fa-check-circle'></i> تم إنشاء جداول التقارير بنجاح!</h3>";
            echo "</div>";

        } catch (Exception $e) {
            echo "<div class='alert alert-danger'>";
            echo "<h5>خطأ في الإنشاء:</h5>";
            echo "<p>" . htmlspecialchars($e->getMessage()) . "</p>";
            echo "</div>";
        }

        echo "<div class='text-center mt-4'>";
        echo "<a href='create_notifications_tables.php' class='btn btn-primary btn-lg me-2'><i class='fas fa-bell'></i> إنشاء جداول الإشعارات</a>";
        echo "<a href='../dashboard.php' class='btn btn-success btn-lg'><i class='fas fa-home'></i> الصفحة الرئيسية</a>";
        echo "</div>";

    } else {
        echo "<div class='alert alert-warning'>";
        echo "<h5><i class='fas fa-info-circle'></i> إنشاء جداول التقارير</h5>";
        echo "<p>سيتم إنشاء جميع الجداول المتعلقة بالتقارير والإحصائيات</p>";
        echo "</div>";

        echo "<div class='card mb-4'>";
        echo "<div class='card-header bg-secondary text-white'>";
        echo "<h6><i class='fas fa-table'></i> الجداول التي سيتم إنشاؤها</h6>";
        echo "</div>";
        echo "<div class='card-body'>";
        echo "<div class='row'>";
        echo "<div class='col-md-6'>";
        echo "<ul>";
        echo "<li><strong>weekly_reports:</strong> التقارير الأسبوعية</li>";
        echo "<li><strong>report_attachments:</strong> مرفقات التقارير</li>";
        echo "<li><strong>report_templates:</strong> قوالب التقارير</li>";
        echo "</ul>";
        echo "</div>";
        echo "<div class='col-md-6'>";
        echo "<ul>";
        echo "<li><strong>report_comments:</strong> تعليقات التقارير</li>";
        echo "<li><strong>report_statistics:</strong> إحصائيات التقارير</li>";
        echo "</ul>";
        echo "</div>";
        echo "</div>";
        echo "</div>";
        echo "</div>";

        echo "<div class='alert alert-info'>";
        echo "<h6><i class='fas fa-star'></i> الميزات المتضمنة:</h6>";
        echo "<ul>";
        echo "<li>📊 <strong>تقارير شاملة:</strong> أسبوعية وشهرية وسنوية</li>";
        echo "<li>📎 <strong>مرفقات متنوعة:</strong> صور ووثائق</li>";
        echo "<li>📋 <strong>قوالب جاهزة:</strong> قوالب معدة مسبقاً</li>";
        echo "<li>💬 <strong>نظام تعليقات:</strong> مراجعة وتفاعل</li>";
        echo "<li>📈 <strong>إحصائيات متقدمة:</strong> تحليلات شاملة</li>";
        echo "<li>⏰ <strong>تتبع الوقت:</strong> ساعات العمل والإضافي</li>";
        echo "</ul>";
        echo "</div>";

        echo "<form method='POST' action=''>";
        echo "<div class='text-center'>";
        echo "<button type='submit' name='create_reports_tables' class='btn btn-success btn-lg'>";
        echo "<i class='fas fa-database'></i> إنشاء جداول التقارير";
        echo "</button>";
        echo "</div>";
        echo "</form>";

        echo "<div class='text-center mt-3'>";
        echo "<a href='create_requests_tables.php' class='btn btn-secondary me-2'><i class='fas fa-arrow-right'></i> جداول المطالب</a>";
        echo "<a href='../dashboard.php' class='btn btn-secondary'><i class='fas fa-home'></i> الصفحة الرئيسية</a>";
        echo "</div>";
    }

    echo "</div>";
    echo "</body>";
    echo "</html>";
}
?>
