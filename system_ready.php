<?php
// اختبار جاهزية النظام
error_reporting(E_ALL);
ini_set('display_errors', 1);
header('Content-Type: text/html; charset=utf-8');

echo "<!DOCTYPE html>";
echo "<html lang='ar' dir='rtl'>";
echo "<head>";
echo "<meta charset='UTF-8'>";
echo "<meta name='viewport' content='width=device-width, initial-scale=1.0'>";
echo "<title>النظام جاهز للاستخدام</title>";
echo "<link href='https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css' rel='stylesheet'>";
echo "<link href='https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css' rel='stylesheet'>";
echo "<style>";
echo "body { font-family: Arial, sans-serif; padding: 20px; background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); min-height: 100vh; }";
echo ".main-card { background: white; border-radius: 20px; box-shadow: 0 15px 35px rgba(0,0,0,0.1); padding: 3rem; max-width: 1200px; margin: 0 auto; }";
echo ".success-icon { color: #28a745; font-size: 4rem; }";
echo ".test-card { border-radius: 15px; margin-bottom: 20px; transition: transform 0.3s ease; }";
echo ".test-card:hover { transform: translateY(-5px); }";
echo "</style>";
echo "</head>";
echo "<body>";

echo "<div class='main-card'>";
echo "<div class='text-center mb-5'>";
echo "<i class='fas fa-check-circle success-icon mb-3'></i>";
echo "<h1 class='text-success'>🎉 النظام جاهز للاستخدام!</h1>";
echo "<p class='lead'>تم إصلاح جميع المشاكل وإعداد النظام بنجاح</p>";
echo "</div>";

// اختبار قاعدة البيانات
echo "<div class='alert alert-success'>";
echo "<h5><i class='fas fa-database me-2'></i>حالة قاعدة البيانات</h5>";

try {
    require_once 'config/database.php';
    $conn = getDBConnection();
    
    if ($conn) {
        echo "<p class='mb-0'>✅ الاتصال بقاعدة البيانات نجح</p>";
        
        // فحص الجداول
        $tables = ['users', 'admins', 'supporters', 'regions', 'messages', 'supporter_requests', 'weekly_reports'];
        $table_status = [];
        
        foreach ($tables as $table) {
            try {
                $result = fetchOne("SELECT COUNT(*) as count FROM $table");
                $count = $result ? $result['count'] : 0;
                $table_status[$table] = $count;
                echo "<p class='mb-0'>✅ جدول $table: $count سجل</p>";
            } catch (Exception $e) {
                echo "<p class='mb-0'>⚠️ جدول $table: قد يحتاج إعداد</p>";
            }
        }
    } else {
        echo "<p class='mb-0'>❌ فشل الاتصال بقاعدة البيانات</p>";
    }
} catch (Exception $e) {
    echo "<p class='mb-0'>❌ خطأ: " . $e->getMessage() . "</p>";
}

echo "</div>";

// اختبار الصفحات الرئيسية
echo "<h5><i class='fas fa-globe me-2'></i>اختبار الصفحات الرئيسية</h5>";
echo "<div class='row'>";

$pages_to_test = [
    ['لوحة تحكم المرشح', 'dashboard.php', 'primary', 'fas fa-crown'],
    ['لوحة تحكم الإداريين', 'modules/admin/dashboard.php', 'success', 'fas fa-user-shield'],
    ['تسجيل دخول الإداريين', 'modules/admin/login.php', 'info', 'fas fa-sign-in-alt'],
    ['إضافة مؤيد', 'modules/admin/add_supporter.php', 'warning', 'fas fa-user-plus'],
    ['النظام التجريبي', 'demo_system.php', 'secondary', 'fas fa-flask'],
    ['لوحة تحكم بسيطة', 'simple_dashboard.php', 'dark', 'fas fa-tachometer-alt']
];

foreach ($pages_to_test as $page) {
    echo "<div class='col-md-6 col-lg-4 mb-3'>";
    echo "<div class='card test-card border-{$page[2]}'>";
    echo "<div class='card-body text-center'>";
    echo "<i class='{$page[3]} fa-2x text-{$page[2]} mb-3'></i>";
    echo "<h6 class='card-title'>{$page[0]}</h6>";
    echo "<a href='{$page[1]}' class='btn btn-{$page[2]} btn-sm' target='_blank'>";
    echo "<i class='fas fa-external-link-alt me-1'></i>فتح الصفحة";
    echo "</a>";
    echo "</div>";
    echo "</div>";
    echo "</div>";
}

echo "</div>";

// معلومات تسجيل الدخول
echo "<div class='alert alert-info'>";
echo "<h5><i class='fas fa-key me-2'></i>معلومات تسجيل الدخول</h5>";
echo "<div class='row'>";

echo "<div class='col-md-6'>";
echo "<h6>حساب المرشح:</h6>";
echo "<ul>";
echo "<li><strong>اسم المستخدم:</strong> abd</li>";
echo "<li><strong>كلمة المرور:</strong> abdabd</li>";
echo "<li><strong>الرابط:</strong> <a href='dashboard.php' target='_blank'>لوحة تحكم المرشح</a></li>";
echo "</ul>";
echo "</div>";

echo "<div class='col-md-6'>";
echo "<h6>حساب الإداري:</h6>";
echo "<ul>";
echo "<li><strong>اسم المستخدم:</strong> admin</li>";
echo "<li><strong>كلمة المرور:</strong> admin123</li>";
echo "<li><strong>الرابط:</strong> <a href='modules/admin/login.php' target='_blank'>تسجيل دخول الإداريين</a></li>";
echo "</ul>";
echo "</div>";

echo "</div>";
echo "</div>";

// الميزات المتاحة
echo "<h5><i class='fas fa-star me-2'></i>الميزات المتاحة في النظام</h5>";
echo "<div class='row'>";

$features = [
    ['إدارة المؤيدين', 'إضافة وتعديل وحذف المؤيدين مع المرفقات', 'fas fa-users', 'primary'],
    ['مطالب المؤيدين', 'استقبال ومتابعة مطالب المؤيدين', 'fas fa-hand-holding-heart', 'warning'],
    ['نظام الرسائل', 'تبادل الرسائل بين الإدارة والإداريين', 'fas fa-envelope', 'info'],
    ['التقارير الأسبوعية', 'إنشاء ومتابعة التقارير الدورية', 'fas fa-file-alt', 'success'],
    ['إدارة المناطق', 'تنظيم المؤيدين حسب المناطق الجغرافية', 'fas fa-map-marker-alt', 'secondary'],
    ['الإحصائيات', 'عرض إحصائيات شاملة للحملة', 'fas fa-chart-bar', 'dark']
];

foreach ($features as $feature) {
    echo "<div class='col-md-6 col-lg-4 mb-3'>";
    echo "<div class='card test-card border-{$feature[3]}'>";
    echo "<div class='card-body'>";
    echo "<div class='d-flex align-items-center mb-2'>";
    echo "<i class='{$feature[2]} fa-lg text-{$feature[3]} me-3'></i>";
    echo "<h6 class='card-title mb-0'>{$feature[0]}</h6>";
    echo "</div>";
    echo "<p class='card-text small text-muted'>{$feature[1]}</p>";
    echo "</div>";
    echo "</div>";
    echo "</div>";
}

echo "</div>";

// إحصائيات النظام
if (isset($table_status)) {
    echo "<div class='alert alert-light'>";
    echo "<h5><i class='fas fa-chart-pie me-2'></i>إحصائيات النظام الحالية</h5>";
    echo "<div class='row text-center'>";
    
    $stats_display = [
        ['المؤيدين', $table_status['supporters'] ?? 0, 'fas fa-users', 'primary'],
        ['الإداريين', $table_status['admins'] ?? 0, 'fas fa-user-shield', 'success'],
        ['المناطق', $table_status['regions'] ?? 0, 'fas fa-map-marker-alt', 'info'],
        ['المطالب', $table_status['supporter_requests'] ?? 0, 'fas fa-hand-holding-heart', 'warning'],
        ['الرسائل', $table_status['messages'] ?? 0, 'fas fa-envelope', 'secondary'],
        ['التقارير', $table_status['weekly_reports'] ?? 0, 'fas fa-file-alt', 'dark']
    ];
    
    foreach ($stats_display as $stat) {
        echo "<div class='col-md-4 col-lg-2 mb-3'>";
        echo "<div class='card border-{$stat[3]}'>";
        echo "<div class='card-body'>";
        echo "<i class='{$stat[2]} fa-2x text-{$stat[3]} mb-2'></i>";
        echo "<h4 class='text-{$stat[3]}'>" . number_format($stat[1]) . "</h4>";
        echo "<small>{$stat[0]}</small>";
        echo "</div>";
        echo "</div>";
        echo "</div>";
    }
    
    echo "</div>";
    echo "</div>";
}

// روابط سريعة
echo "<div class='text-center mt-5'>";
echo "<h5><i class='fas fa-rocket me-2'></i>ابدأ استخدام النظام الآن</h5>";
echo "<div class='btn-group-vertical btn-group-lg' role='group'>";
echo "<a href='dashboard.php' class='btn btn-primary btn-lg mb-2'>";
echo "<i class='fas fa-crown me-2'></i>لوحة تحكم المرشح";
echo "</a>";
echo "<a href='modules/admin/dashboard.php' class='btn btn-success btn-lg mb-2'>";
echo "<i class='fas fa-user-shield me-2'></i>لوحة تحكم الإداريين";
echo "</a>";
echo "<a href='modules/admin/add_supporter.php' class='btn btn-warning btn-lg'>";
echo "<i class='fas fa-user-plus me-2'></i>إضافة مؤيد جديد";
echo "</a>";
echo "</div>";
echo "</div>";

echo "<div class='text-center mt-4'>";
echo "<small class='text-muted'>";
echo "تم إنشاء وإعداد النظام بواسطة Augment Agent | ";
echo "آخر تحديث: " . date('Y-m-d H:i:s');
echo "</small>";
echo "</div>";

echo "</div>";
echo "</body>";
echo "</html>";
?>
