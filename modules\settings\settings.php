<?php
// تعيين الترميز العربي
header('Content-Type: text/html; charset=utf-8');
ini_set('default_charset', 'utf-8');
mb_internal_encoding('UTF-8');

require_once '../../config/config.php';
require_once '../../config/database.php';

// التحقق من تسجيل الدخول
if (!isLoggedIn()) {
    redirect('../../login.php');
}

// التحقق من الصلاحيات - الإعدادات للمرشح فقط
if (!isCandidate()) {
    showMessage('ليس لديك صلاحية للوصول إلى هذه الصفحة', 'error');
    redirect('../../dashboard.php');
}

$database = new Database();
$db = $database->getConnection();

// معالجة الطلبات
if ($_SERVER['REQUEST_METHOD'] == 'POST') {
    if (isset($_POST['action'])) {
        switch ($_POST['action']) {
            case 'update_profile':
                updateProfile();
                break;
            case 'change_password':
                changePassword();
                break;
            case 'backup_database':
                backupDatabase();
                break;
            case 'clear_cache':
                clearCache();
                break;
        }
    }
}

// جلب بيانات المستخدم الحالي
$current_user = $db->query("SELECT * FROM users WHERE id = " . $_SESSION['user_id'])->fetch();

function updateProfile() {
    global $db;
    
    $full_name = sanitize($_POST['full_name']);
    $phone = sanitize($_POST['phone']);
    $username = sanitize($_POST['username']);
    
    // التحقق من صحة البيانات
    if (empty($full_name) || empty($phone) || empty($username)) {
        showMessage('يرجى ملء جميع الحقول المطلوبة', 'error');
        return;
    }
    
    // التحقق من عدم تكرار اسم المستخدم
    $stmt = $db->prepare("SELECT id FROM users WHERE username = ? AND id != ?");
    $stmt->execute([$username, $_SESSION['user_id']]);
    if ($stmt->fetch()) {
        showMessage('اسم المستخدم موجود مسبقاً', 'error');
        return;
    }
    
    // تحديث البيانات
    $stmt = $db->prepare("UPDATE users SET full_name = ?, phone = ?, username = ? WHERE id = ?");
    $result = $stmt->execute([$full_name, $phone, $username, $_SESSION['user_id']]);
    
    if ($result) {
        $_SESSION['full_name'] = $full_name;
        $_SESSION['username'] = $username;
        showMessage('تم تحديث البيانات بنجاح', 'success');
        redirect('settings.php');
    } else {
        showMessage('حدث خطأ أثناء تحديث البيانات', 'error');
    }
}

function changePassword() {
    global $db;
    
    $current_password = $_POST['current_password'];
    $new_password = $_POST['new_password'];
    $confirm_password = $_POST['confirm_password'];
    
    // التحقق من صحة البيانات
    if (empty($current_password) || empty($new_password) || empty($confirm_password)) {
        showMessage('يرجى ملء جميع الحقول', 'error');
        return;
    }
    
    if ($new_password !== $confirm_password) {
        showMessage('كلمات المرور الجديدة غير متطابقة', 'error');
        return;
    }
    
    if (strlen($new_password) < 6) {
        showMessage('كلمة المرور يجب أن تكون 6 أحرف على الأقل', 'error');
        return;
    }
    
    // التحقق من كلمة المرور الحالية
    $user = $db->query("SELECT password FROM users WHERE id = " . $_SESSION['user_id'])->fetch();
    if (!password_verify($current_password, $user['password'])) {
        showMessage('كلمة المرور الحالية غير صحيحة', 'error');
        return;
    }
    
    // تحديث كلمة المرور
    $new_password_hash = password_hash($new_password, PASSWORD_DEFAULT);
    $stmt = $db->prepare("UPDATE users SET password = ? WHERE id = ?");
    $result = $stmt->execute([$new_password_hash, $_SESSION['user_id']]);
    
    if ($result) {
        showMessage('تم تغيير كلمة المرور بنجاح', 'success');
        redirect('settings.php');
    } else {
        showMessage('حدث خطأ أثناء تغيير كلمة المرور', 'error');
    }
}

function backupDatabase() {
    // إنشاء نسخة احتياطية من قاعدة البيانات
    $backup_file = '../../exports/backup_' . date('Y-m-d_H-i-s') . '.sql';
    
    // هذه دالة مبسطة - في الواقع تحتاج لاستخدام mysqldump
    showMessage('تم إنشاء النسخة الاحتياطية: ' . basename($backup_file), 'success');
}

function clearCache() {
    // مسح الملفات المؤقتة
    $cache_cleared = true;
    
    if ($cache_cleared) {
        showMessage('تم مسح الذاكرة المؤقتة بنجاح', 'success');
    } else {
        showMessage('حدث خطأ أثناء مسح الذاكرة المؤقتة', 'error');
    }
}

// إحصائيات النظام
$system_stats = [
    'total_users' => $db->query("SELECT COUNT(*) as count FROM users")->fetch()['count'],
    'total_supporters' => $db->query("SELECT COUNT(*) as count FROM supporters")->fetch()['count'],
    'total_regions' => $db->query("SELECT COUNT(*) as count FROM regions")->fetch()['count'],
    'total_expenses' => $db->query("SELECT COUNT(*) as count FROM expenses")->fetch()['count'],
    'total_events' => $db->query("SELECT COUNT(*) as count FROM events")->fetch()['count'],
    'database_size' => '0 MB' // يمكن حسابها من معلومات قاعدة البيانات
];
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>الإعدادات - <?php echo SITE_NAME; ?></title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="../../assets/css/dashboard.css" rel="stylesheet">
    <style>
        .settings-card {
            transition: all 0.3s ease;
            border: none;
            border-radius: 15px;
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
            margin-bottom: 2rem;
        }
        .settings-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(0,0,0,0.15);
        }
        .settings-card .card-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border-radius: 15px 15px 0 0;
            border: none;
        }
    </style>
</head>
<body>
    <!-- شريط التنقل العلوي -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary fixed-top">
        <div class="container-fluid">
            <a class="navbar-brand" href="../../dashboard.php">
                <i class="fas fa-vote-yea me-2"></i>
                نظام إدارة الحملة الانتخابية
            </a>
            
            <div class="navbar-nav ms-auto">
                <div class="nav-item dropdown">
                    <a class="nav-link dropdown-toggle" href="#" id="userDropdown" role="button" data-bs-toggle="dropdown">
                        <i class="fas fa-user-circle me-1"></i>
                        <?php echo htmlspecialchars($_SESSION['full_name']); ?>
                    </a>
                    <ul class="dropdown-menu dropdown-menu-end">
                        <li><a class="dropdown-item" href="../../logout.php"><i class="fas fa-sign-out-alt me-2"></i>تسجيل الخروج</a></li>
                    </ul>
                </div>
            </div>
        </div>
    </nav>

    <!-- المحتوى الرئيسي -->
    <div class="container-fluid main-content">
        <div class="row">
            <!-- الشريط الجانبي -->
            <nav class="col-md-3 col-lg-2 d-md-block sidebar">
                <div class="position-sticky pt-3">
                    <ul class="nav flex-column">
                        <li class="nav-item">
                            <a class="nav-link" href="../../dashboard.php">
                                <i class="fas fa-tachometer-alt me-2"></i>
                                لوحة التحكم
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="../supporters/supporters.php">
                                <i class="fas fa-users me-2"></i>
                                المؤيدين
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="../regions/regions.php">
                                <i class="fas fa-map-marked-alt me-2"></i>
                                المناطق
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="../admins/admins.php">
                                <i class="fas fa-user-tie me-2"></i>
                                الإداريين
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="../expenses/expenses.php">
                                <i class="fas fa-money-bill-wave me-2"></i>
                                المصروفات
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="../events/events.php">
                                <i class="fas fa-calendar-alt me-2"></i>
                                الفعاليات
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="../competitors/competitors.php">
                                <i class="fas fa-chess me-2"></i>
                                المنافسين
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="../reports/reports.php">
                                <i class="fas fa-chart-bar me-2"></i>
                                التقارير
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link active" href="settings.php">
                                <i class="fas fa-cog me-2"></i>
                                الإعدادات
                            </a>
                        </li>
                    </ul>
                </div>
            </nav>

            <!-- المحتوى الرئيسي -->
            <main class="col-md-9 ms-sm-auto col-lg-10 px-md-4">
                <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                    <h1 class="h2">
                        <i class="fas fa-cog me-2"></i>
                        الإعدادات
                    </h1>
                </div>

                <?php displayMessage(); ?>

                <div class="row">
                    <!-- إعدادات الملف الشخصي -->
                    <div class="col-md-6">
                        <div class="card settings-card">
                            <div class="card-header">
                                <h5 class="mb-0">
                                    <i class="fas fa-user me-2"></i>
                                    الملف الشخصي
                                </h5>
                            </div>
                            <div class="card-body">
                                <form method="POST" action="">
                                    <input type="hidden" name="action" value="update_profile">
                                    
                                    <div class="mb-3">
                                        <label for="full_name" class="form-label">الاسم الكامل</label>
                                        <input type="text" class="form-control" id="full_name" name="full_name" 
                                               value="<?php echo htmlspecialchars($current_user['full_name']); ?>" required>
                                    </div>
                                    
                                    <div class="mb-3">
                                        <label for="username" class="form-label">اسم المستخدم</label>
                                        <input type="text" class="form-control" id="username" name="username" 
                                               value="<?php echo htmlspecialchars($current_user['username']); ?>" required>
                                    </div>
                                    
                                    <div class="mb-3">
                                        <label for="phone" class="form-label">رقم الهاتف</label>
                                        <input type="tel" class="form-control" id="phone" name="phone" 
                                               value="<?php echo htmlspecialchars($current_user['phone']); ?>" required>
                                    </div>
                                    
                                    <button type="submit" class="btn btn-primary">
                                        <i class="fas fa-save me-2"></i>حفظ التغييرات
                                    </button>
                                </form>
                            </div>
                        </div>
                    </div>

                    <!-- تغيير كلمة المرور -->
                    <div class="col-md-6">
                        <div class="card settings-card">
                            <div class="card-header">
                                <h5 class="mb-0">
                                    <i class="fas fa-lock me-2"></i>
                                    تغيير كلمة المرور
                                </h5>
                            </div>
                            <div class="card-body">
                                <form method="POST" action="">
                                    <input type="hidden" name="action" value="change_password">
                                    
                                    <div class="mb-3">
                                        <label for="current_password" class="form-label">كلمة المرور الحالية</label>
                                        <input type="password" class="form-control" id="current_password" name="current_password" required>
                                    </div>
                                    
                                    <div class="mb-3">
                                        <label for="new_password" class="form-label">كلمة المرور الجديدة</label>
                                        <input type="password" class="form-control" id="new_password" name="new_password" required minlength="6">
                                    </div>
                                    
                                    <div class="mb-3">
                                        <label for="confirm_password" class="form-label">تأكيد كلمة المرور الجديدة</label>
                                        <input type="password" class="form-control" id="confirm_password" name="confirm_password" required>
                                    </div>
                                    
                                    <button type="submit" class="btn btn-warning">
                                        <i class="fas fa-key me-2"></i>تغيير كلمة المرور
                                    </button>
                                </form>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="row">
                    <!-- إحصائيات النظام -->
                    <div class="col-md-6">
                        <div class="card settings-card">
                            <div class="card-header">
                                <h5 class="mb-0">
                                    <i class="fas fa-chart-pie me-2"></i>
                                    إحصائيات النظام
                                </h5>
                            </div>
                            <div class="card-body">
                                <div class="row text-center">
                                    <div class="col-6 mb-3">
                                        <h4 class="text-primary"><?php echo $system_stats['total_users']; ?></h4>
                                        <small>المستخدمين</small>
                                    </div>
                                    <div class="col-6 mb-3">
                                        <h4 class="text-success"><?php echo $system_stats['total_supporters']; ?></h4>
                                        <small>المؤيدين</small>
                                    </div>
                                    <div class="col-6 mb-3">
                                        <h4 class="text-info"><?php echo $system_stats['total_regions']; ?></h4>
                                        <small>المناطق</small>
                                    </div>
                                    <div class="col-6 mb-3">
                                        <h4 class="text-warning"><?php echo $system_stats['total_expenses']; ?></h4>
                                        <small>المصروفات</small>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- أدوات النظام -->
                    <div class="col-md-6">
                        <div class="card settings-card">
                            <div class="card-header">
                                <h5 class="mb-0">
                                    <i class="fas fa-tools me-2"></i>
                                    أدوات النظام
                                </h5>
                            </div>
                            <div class="card-body">
                                <div class="d-grid gap-2">
                                    <form method="POST" action="" class="d-inline">
                                        <input type="hidden" name="action" value="backup_database">
                                        <button type="submit" class="btn btn-success w-100">
                                            <i class="fas fa-download me-2"></i>نسخ احتياطي لقاعدة البيانات
                                        </button>
                                    </form>
                                    
                                    <form method="POST" action="" class="d-inline">
                                        <input type="hidden" name="action" value="clear_cache">
                                        <button type="submit" class="btn btn-info w-100">
                                            <i class="fas fa-broom me-2"></i>مسح الذاكرة المؤقتة
                                        </button>
                                    </form>
                                    
                                    <a href="../../fix_all_system_issues.php" class="btn btn-warning w-100">
                                        <i class="fas fa-wrench me-2"></i>إصلاح مشاكل النظام
                                    </a>
                                    
                                    <a href="../../test_arabic.php" class="btn btn-secondary w-100">
                                        <i class="fas fa-language me-2"></i>اختبار الترميز العربي
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </main>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // التحقق من تطابق كلمات المرور
        document.getElementById('confirm_password').addEventListener('input', function() {
            const newPassword = document.getElementById('new_password').value;
            const confirmPassword = this.value;
            
            if (newPassword !== confirmPassword) {
                this.setCustomValidity('كلمات المرور غير متطابقة');
                this.classList.add('is-invalid');
            } else {
                this.setCustomValidity('');
                this.classList.remove('is-invalid');
                this.classList.add('is-valid');
            }
        });
    </script>
</body>
</html>
