<?php
// تعيين الترميز العربي
header('Content-Type: text/html; charset=utf-8');
ini_set('default_charset', 'utf-8');
mb_internal_encoding('UTF-8');

require_once '../../config/config.php';
require_once '../../config/database.php';

// التحقق من تسجيل الدخول
if (!isLoggedIn()) {
    redirect('../../login.php');
}

// التحقق من الصلاحيات
$can_add = isCandidate() || (isAdmin() && in_array('add_supporters', $_SESSION['permissions'] ?? []));
$can_edit = isCandidate() || (isAdmin() && in_array('edit_supporters', $_SESSION['permissions'] ?? []));
$can_delete = isCandidate() || (isAdmin() && in_array('delete_supporters', $_SESSION['permissions'] ?? []));

// معالجة الطلبات
if ($_SERVER['REQUEST_METHOD'] == 'POST') {
    if (isset($_POST['action'])) {
        switch ($_POST['action']) {
            case 'add':
                if ($can_add) {
                    addSupporter();
                }
                break;
            case 'edit':
                if ($can_edit) {
                    editSupporter();
                }
                break;
            case 'delete':
                if ($can_delete) {
                    deleteSupporter();
                }
                break;
        }
    }
}

// معالجة الاستيراد المباشر
if ($_SERVER['REQUEST_METHOD'] == 'POST' && isset($_FILES['import_file'])) {
    $file = $_FILES['import_file'];

    // التحقق من نوع الملف
    $file_extension = strtolower(pathinfo($file['name'], PATHINFO_EXTENSION));
    if (!in_array($file_extension, ['csv', 'txt'])) {
        showMessage('نوع الملف غير مدعوم. يرجى رفع ملف CSV', 'error');
    } else {
        // قراءة الملف
        $file_content = file_get_contents($file['tmp_name']);

        // تحويل الترميز إلى UTF-8
        if (!mb_check_encoding($file_content, 'UTF-8')) {
            $file_content = mb_convert_encoding($file_content, 'UTF-8', 'auto');
        }

        // تقسيم الملف إلى أسطر
        $lines = explode("\n", $file_content);

        // إزالة السطر الأول (العناوين)
        if (count($lines) > 1) {
            array_shift($lines);
        }

        $success_count = 0;
        $error_count = 0;

        foreach ($lines as $line) {
            $line = trim($line);
            if (empty($line)) continue;

            $data = str_getcsv($line);

            if (count($data) >= 4) {
                $full_name = trim($data[0] ?? '');
                $gender = strtolower(trim($data[1] ?? ''));
                $phone = trim($data[7] ?? '');
                $address = trim($data[6] ?? '');

                // تحويل الجنس
                if ($gender == 'ذكر' || $gender == 'male') $gender = 'male';
                else $gender = 'female';

                if (!empty($full_name) && !empty($phone) && !empty($address)) {
                    $sql = "INSERT INTO supporters (full_name, gender, marital_status, birth_date, education, profession, address, phone, voter_number, voting_center, region_id, notes, added_by, created_at)
                            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, NOW())";

                    $result = executeQuery($sql, [
                        $full_name, $gender, trim($data[2] ?? 'single'), trim($data[3] ?? '1990-01-01'),
                        trim($data[4] ?? ''), trim($data[5] ?? ''), $address, $phone,
                        trim($data[8] ?? ''), trim($data[9] ?? ''), 1, trim($data[11] ?? ''), $_SESSION['user_id']
                    ]);

                    if ($result) {
                        $success_count++;
                    } else {
                        $error_count++;
                    }
                } else {
                    $error_count++;
                }
            } else {
                $error_count++;
            }
        }

        if ($success_count > 0) {
            showMessage("تم استيراد $success_count مؤيد بنجاح", 'success');
        }
        if ($error_count > 0) {
            showMessage("فشل في استيراد $error_count سجل", 'warning');
        }
    }
}

// جلب المؤيدين مع التصفية
$where_conditions = [];
$params = [];

// تصفية حسب المنطقة للإداريين
if (isAdmin() && $_SESSION['region_id']) {
    $where_conditions[] = "s.region_id = ?";
    $params[] = $_SESSION['region_id'];
}

// تصفية حسب البحث
if (!empty($_GET['search'])) {
    $search = '%' . $_GET['search'] . '%';
    $where_conditions[] = "(s.full_name LIKE ? OR s.phone LIKE ? OR s.voter_number LIKE ?)";
    $params = array_merge($params, [$search, $search, $search]);
}

// تصفية حسب المنطقة
if (!empty($_GET['region_id'])) {
    $where_conditions[] = "s.region_id = ?";
    $params[] = $_GET['region_id'];
}

// تصفية حسب الجنس
if (!empty($_GET['gender'])) {
    $where_conditions[] = "s.gender = ?";
    $params[] = $_GET['gender'];
}

// تصفية حسب العمر
if (!empty($_GET['age_from']) || !empty($_GET['age_to'])) {
    if (!empty($_GET['age_from'])) {
        $where_conditions[] = "YEAR(CURDATE()) - YEAR(s.birth_date) >= ?";
        $params[] = $_GET['age_from'];
    }
    if (!empty($_GET['age_to'])) {
        $where_conditions[] = "YEAR(CURDATE()) - YEAR(s.birth_date) <= ?";
        $params[] = $_GET['age_to'];
    }
}

$where_clause = !empty($where_conditions) ? 'WHERE ' . implode(' AND ', $where_conditions) : '';

// جلب المؤيدين
$sql = "SELECT s.*, r.name as region_name, u.full_name as added_by_name,
               YEAR(CURDATE()) - YEAR(s.birth_date) as age
        FROM supporters s
        LEFT JOIN regions r ON s.region_id = r.id
        LEFT JOIN users u ON s.added_by = u.id
        $where_clause
        ORDER BY s.created_at DESC";

$supporters = fetchAll($sql, $params);

// جلب المناطق للتصفية
$regions = fetchAll("SELECT * FROM regions ORDER BY name");

// إحصائيات
$total_supporters = fetchOne("SELECT COUNT(*) as count FROM supporters s $where_clause", $params)['count'];
$male_supporters = fetchOne("SELECT COUNT(*) as count FROM supporters s $where_clause AND s.gender = 'male'", array_merge($params, ['male']))['count'];
$female_supporters = fetchOne("SELECT COUNT(*) as count FROM supporters s $where_clause AND s.gender = 'female'", array_merge($params, ['female']))['count'];

function addSupporter() {
    // التحقق من صحة البيانات الأساسية
    if (empty($_POST['full_name']) || empty($_POST['phone']) || empty($_POST['region_id']) || empty($_POST['birth_date']) || empty($_POST['address'])) {
        showMessage('يرجى ملء جميع الحقول المطلوبة (الاسم، الهاتف، المنطقة، العنوان، تاريخ الميلاد)', 'error');
        return;
    }

    // تحضير البيانات
    $full_name = sanitize($_POST['full_name']);
    $gender = sanitize($_POST['gender']);
    $marital_status = sanitize($_POST['marital_status']);
    $birth_date = sanitize($_POST['birth_date']);
    $education = sanitize($_POST['education']);
    $profession = sanitize($_POST['profession']);
    $address = sanitize($_POST['address']);
    $phone = sanitize($_POST['phone']);
    $voter_number = sanitize($_POST['voter_number']);
    $voting_center = sanitize($_POST['voting_center']);
    $region_id = (int)$_POST['region_id'];
    $notes = sanitize($_POST['notes']);
    $added_by = $_SESSION['user_id'];
    $photo = null;

    // التحقق من عدم تكرار رقم الناخب
    if (!empty($voter_number)) {
        $existing = fetchOne("SELECT id FROM supporters WHERE voter_number = ?", [$voter_number]);
        if ($existing) {
            showMessage('رقم الناخب موجود مسبقاً', 'error');
            return;
        }
    }

    // رفع الصورة إن وجدت
    if (isset($_FILES['photo']) && $_FILES['photo']['error'] == 0) {
        $photo_path = uploadPhoto($_FILES['photo']);
        if ($photo_path) {
            $photo = $photo_path;
        }
    }

    // إدراج البيانات
    $sql = "INSERT INTO supporters (full_name, gender, marital_status, birth_date, education, profession, address, phone, voter_number, voting_center, region_id, photo, notes, added_by, created_at)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, NOW())";

    $params = [
        $full_name, $gender, $marital_status, $birth_date, $education,
        $profession, $address, $phone, $voter_number, $voting_center,
        $region_id, $photo, $notes, $added_by
    ];

    $result = executeQuery($sql, $params);

    if ($result) {
        showMessage('تم إضافة المؤيد بنجاح', 'success');
        redirect('supporters.php');
    } else {
        showMessage('حدث خطأ أثناء إضافة المؤيد', 'error');
    }
}

function editSupporter() {
    $id = (int)$_POST['id'];
    $data = [
        'full_name' => sanitize($_POST['full_name']),
        'gender' => sanitize($_POST['gender']),
        'marital_status' => sanitize($_POST['marital_status']),
        'birth_date' => sanitize($_POST['birth_date']),
        'education' => sanitize($_POST['education']),
        'profession' => sanitize($_POST['profession']),
        'address' => sanitize($_POST['address']),
        'phone' => sanitize($_POST['phone']),
        'voter_number' => sanitize($_POST['voter_number']),
        'voting_center' => sanitize($_POST['voting_center']),
        'region_id' => (int)$_POST['region_id'],
        'notes' => sanitize($_POST['notes'])
    ];

    // التحقق من صحة البيانات
    if (empty($data['full_name']) || empty($data['phone']) || empty($data['region_id'])) {
        showMessage('يرجى ملء جميع الحقول المطلوبة', 'error');
        return;
    }

    // التحقق من عدم تكرار رقم الناخب
    if (!empty($data['voter_number'])) {
        $existing = fetchOne("SELECT id FROM supporters WHERE voter_number = ? AND id != ?", [$data['voter_number'], $id]);
        if ($existing) {
            showMessage('رقم الناخب موجود مسبقاً', 'error');
            return;
        }
    }

    // رفع الصورة الجديدة إن وجدت
    if (isset($_FILES['photo']) && $_FILES['photo']['error'] == 0) {
        $photo_path = uploadPhoto($_FILES['photo']);
        if ($photo_path) {
            $data['photo'] = $photo_path;
        }
    }

    $set_clause = implode(', ', array_map(function($key) { return "$key = ?"; }, array_keys($data)));
    $sql = "UPDATE supporters SET $set_clause WHERE id = ?";

    $params = array_values($data);
    $params[] = $id;

    $result = executeQuery($sql, $params);

    if ($result) {
        showMessage('تم تحديث بيانات المؤيد بنجاح', 'success');
        redirect('supporters.php');
    } else {
        showMessage('حدث خطأ أثناء تحديث بيانات المؤيد', 'error');
    }
}

function deleteSupporter() {
    $id = (int)$_POST['id'];

    // حذف الصورة إن وجدت
    $supporter = fetchOne("SELECT photo FROM supporters WHERE id = ?", [$id]);
    if ($supporter && $supporter['photo'] && file_exists('../../' . $supporter['photo'])) {
        unlink('../../' . $supporter['photo']);
    }

    $result = executeQuery("DELETE FROM supporters WHERE id = ?", [$id]);

    if ($result) {
        showMessage('تم حذف المؤيد بنجاح', 'success');
    } else {
        showMessage('حدث خطأ أثناء حذف المؤيد', 'error');
    }
}

function uploadPhoto($file) {
    $allowed_types = ['image/jpeg', 'image/png', 'image/gif'];
    $max_size = 5 * 1024 * 1024; // 5MB

    if (!in_array($file['type'], $allowed_types)) {
        showMessage('نوع الملف غير مدعوم. يرجى رفع صورة بصيغة JPG, PNG أو GIF', 'error');
        return false;
    }

    if ($file['size'] > $max_size) {
        showMessage('حجم الملف كبير جداً. الحد الأقصى 5 ميجابايت', 'error');
        return false;
    }

    $upload_dir = '../../uploads/supporters/';
    if (!file_exists($upload_dir)) {
        mkdir($upload_dir, 0755, true);
    }

    $file_extension = pathinfo($file['name'], PATHINFO_EXTENSION);
    $new_filename = uniqid() . '.' . $file_extension;
    $upload_path = $upload_dir . $new_filename;

    if (move_uploaded_file($file['tmp_name'], $upload_path)) {
        return 'uploads/supporters/' . $new_filename;
    }

    return false;
}
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إدارة المؤيدين - <?php echo SITE_NAME; ?></title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="https://cdn.datatables.net/1.13.6/css/dataTables.bootstrap5.min.css" rel="stylesheet">
    <link href="../../assets/css/dashboard.css" rel="stylesheet">
    <link href="../../assets/css/supporters.css" rel="stylesheet">
</head>
<body>
    <!-- شريط التنقل العلوي -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary fixed-top">
        <div class="container-fluid">
            <a class="navbar-brand" href="../../dashboard.php">
                <i class="fas fa-vote-yea me-2"></i>
                نظام إدارة الحملة الانتخابية
            </a>

            <div class="navbar-nav ms-auto">
                <div class="nav-item dropdown">
                    <a class="nav-link dropdown-toggle" href="#" id="userDropdown" role="button" data-bs-toggle="dropdown">
                        <i class="fas fa-user-circle me-1"></i>
                        <?php echo htmlspecialchars($_SESSION['full_name']); ?>
                    </a>
                    <ul class="dropdown-menu dropdown-menu-end">
                        <li><a class="dropdown-item" href="../../logout.php"><i class="fas fa-sign-out-alt me-2"></i>تسجيل الخروج</a></li>
                    </ul>
                </div>
            </div>
        </div>
    </nav>

    <!-- المحتوى الرئيسي -->
    <div class="container-fluid main-content">
        <div class="row">
            <!-- الشريط الجانبي -->
            <nav class="col-md-3 col-lg-2 d-md-block sidebar">
                <div class="position-sticky pt-3">
                    <ul class="nav flex-column">
                        <li class="nav-item">
                            <a class="nav-link" href="../../dashboard.php">
                                <i class="fas fa-tachometer-alt me-2"></i>
                                لوحة التحكم
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link active" href="supporters.php">
                                <i class="fas fa-users me-2"></i>
                                المؤيدين
                            </a>
                        </li>
                        <?php if (isCandidate()): ?>
                        <li class="nav-item">
                            <a class="nav-link" href="../regions/regions.php">
                                <i class="fas fa-map-marked-alt me-2"></i>
                                المناطق
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="../admins/admins.php">
                                <i class="fas fa-user-tie me-2"></i>
                                الإداريين
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="../expenses/expenses.php">
                                <i class="fas fa-money-bill-wave me-2"></i>
                                المصروفات
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="../events/events.php">
                                <i class="fas fa-calendar-alt me-2"></i>
                                الفعاليات
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="../competitors/competitors.php">
                                <i class="fas fa-chess me-2"></i>
                                المنافسين
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="../reports/reports.php">
                                <i class="fas fa-chart-bar me-2"></i>
                                التقارير
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="../settings/settings.php">
                                <i class="fas fa-cog me-2"></i>
                                الإعدادات
                            </a>
                        </li>
                        <?php endif; ?>
                    </ul>
                </div>
            </nav>

            <!-- المحتوى الرئيسي -->
            <main class="col-md-9 ms-sm-auto col-lg-10 px-md-4">
                <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                    <h1 class="h2">
                        <i class="fas fa-users me-2"></i>
                        إدارة المؤيدين
                    </h1>
                    <div class="btn-toolbar mb-2 mb-md-0">
                        <div class="btn-group me-2">
                            <?php if ($can_add): ?>
                            <button type="button" class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#addSupporterModal">
                                <i class="fas fa-plus"></i> إضافة مؤيد
                            </button>
                            <?php endif; ?>
                            <button type="button" class="btn btn-success" onclick="exportToExcel()">
                                <i class="fas fa-file-excel"></i> تصدير Excel
                            </button>
                            <button type="button" class="btn btn-danger" onclick="exportToPDF()">
                                <i class="fas fa-file-pdf"></i> تصدير PDF
                            </button>
                            <?php if ($can_add): ?>
                            <button type="button" class="btn btn-info" data-bs-toggle="modal" data-bs-target="#importModal">
                                <i class="fas fa-upload"></i> استيراد
                            </button>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>

                <?php displayMessage(); ?>

                <!-- الإحصائيات -->
                <div class="row mb-4">
                    <div class="col-md-3">
                        <div class="card bg-primary text-white">
                            <div class="card-body">
                                <div class="d-flex justify-content-between">
                                    <div>
                                        <h4><?php echo number_format($total_supporters); ?></h4>
                                        <p class="mb-0">إجمالي المؤيدين</p>
                                    </div>
                                    <div class="align-self-center">
                                        <i class="fas fa-users fa-2x"></i>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="card bg-info text-white">
                            <div class="card-body">
                                <div class="d-flex justify-content-between">
                                    <div>
                                        <h4><?php echo number_format($male_supporters); ?></h4>
                                        <p class="mb-0">الذكور</p>
                                    </div>
                                    <div class="align-self-center">
                                        <i class="fas fa-male fa-2x"></i>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="card bg-warning text-white">
                            <div class="card-body">
                                <div class="d-flex justify-content-between">
                                    <div>
                                        <h4><?php echo number_format($female_supporters); ?></h4>
                                        <p class="mb-0">الإناث</p>
                                    </div>
                                    <div class="align-self-center">
                                        <i class="fas fa-female fa-2x"></i>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="card bg-success text-white">
                            <div class="card-body">
                                <div class="d-flex justify-content-between">
                                    <div>
                                        <h4><?php echo $total_supporters > 0 ? round(($male_supporters / $total_supporters) * 100, 1) : 0; ?>%</h4>
                                        <p class="mb-0">نسبة الذكور</p>
                                    </div>
                                    <div class="align-self-center">
                                        <i class="fas fa-chart-pie fa-2x"></i>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- فلاتر البحث -->
                <div class="card mb-4">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="fas fa-filter me-2"></i>
                            فلاتر البحث والتصفية
                        </h5>
                    </div>
                    <div class="card-body">
                        <form method="GET" action="">
                            <div class="row">
                                <div class="col-md-3 mb-3">
                                    <label for="search" class="form-label">البحث</label>
                                    <input type="text" class="form-control" id="search" name="search"
                                           value="<?php echo htmlspecialchars($_GET['search'] ?? ''); ?>"
                                           placeholder="الاسم، الهاتف، رقم الناخب...">
                                </div>
                                <div class="col-md-2 mb-3">
                                    <label for="region_id" class="form-label">المنطقة</label>
                                    <select class="form-select" id="region_id" name="region_id">
                                        <option value="">جميع المناطق</option>
                                        <?php foreach ($regions as $region): ?>
                                            <option value="<?php echo $region['id']; ?>"
                                                    <?php echo ($_GET['region_id'] ?? '') == $region['id'] ? 'selected' : ''; ?>>
                                                <?php echo htmlspecialchars($region['name']); ?>
                                            </option>
                                        <?php endforeach; ?>
                                    </select>
                                </div>
                                <div class="col-md-2 mb-3">
                                    <label for="gender" class="form-label">الجنس</label>
                                    <select class="form-select" id="gender" name="gender">
                                        <option value="">الكل</option>
                                        <option value="male" <?php echo ($_GET['gender'] ?? '') == 'male' ? 'selected' : ''; ?>>ذكر</option>
                                        <option value="female" <?php echo ($_GET['gender'] ?? '') == 'female' ? 'selected' : ''; ?>>أنثى</option>
                                    </select>
                                </div>
                                <div class="col-md-2 mb-3">
                                    <label for="age_from" class="form-label">العمر من</label>
                                    <input type="number" class="form-control" id="age_from" name="age_from"
                                           value="<?php echo htmlspecialchars($_GET['age_from'] ?? ''); ?>"
                                           min="18" max="100">
                                </div>
                                <div class="col-md-2 mb-3">
                                    <label for="age_to" class="form-label">العمر إلى</label>
                                    <input type="number" class="form-control" id="age_to" name="age_to"
                                           value="<?php echo htmlspecialchars($_GET['age_to'] ?? ''); ?>"
                                           min="18" max="100">
                                </div>
                                <div class="col-md-1 mb-3 d-flex align-items-end">
                                    <button type="submit" class="btn btn-primary w-100">
                                        <i class="fas fa-search"></i>
                                    </button>
                                </div>
                            </div>
                        </form>
                    </div>
                </div>

                <!-- جدول المؤيدين -->
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="fas fa-list me-2"></i>
                            قائمة المؤيدين (<?php echo number_format($total_supporters); ?>)
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="table-responsive">
                            <table class="table table-striped table-hover" id="supportersTable">
                                <thead class="table-dark">
                                    <tr>
                                        <th>الصورة</th>
                                        <th>الاسم الكامل</th>
                                        <th>الجنس</th>
                                        <th>العمر</th>
                                        <th>رقم الهاتف</th>
                                        <th>رقم الناخب</th>
                                        <th>المنطقة</th>
                                        <th>تاريخ الإضافة</th>
                                        <th>الإجراءات</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php foreach ($supporters as $supporter): ?>
                                    <tr>
                                        <td>
                                            <?php if ($supporter['photo']): ?>
                                                <img src="../../<?php echo htmlspecialchars($supporter['photo']); ?>"
                                                     alt="صورة المؤيد" class="supporter-photo"
                                                     onclick="showPhotoModal('../../<?php echo htmlspecialchars($supporter['photo']); ?>')">
                                            <?php else: ?>
                                                <div class="no-photo">
                                                    <i class="fas fa-user"></i>
                                                </div>
                                            <?php endif; ?>
                                        </td>
                                        <td><?php echo htmlspecialchars($supporter['full_name']); ?></td>
                                        <td>
                                            <span class="badge bg-<?php echo $supporter['gender'] == 'male' ? 'primary' : 'pink'; ?>">
                                                <?php echo $supporter['gender'] == 'male' ? 'ذكر' : 'أنثى'; ?>
                                            </span>
                                        </td>
                                        <td><?php echo $supporter['age']; ?> سنة</td>
                                        <td>
                                            <a href="tel:<?php echo $supporter['phone']; ?>" class="text-decoration-none">
                                                <?php echo htmlspecialchars($supporter['phone']); ?>
                                            </a>
                                        </td>
                                        <td><?php echo htmlspecialchars($supporter['voter_number']); ?></td>
                                        <td><?php echo htmlspecialchars($supporter['region_name']); ?></td>
                                        <td><?php echo formatArabicDate($supporter['created_at']); ?></td>
                                        <td>
                                            <div class="btn-group" role="group">
                                                <button type="button" class="btn btn-sm btn-info"
                                                        onclick="viewSupporter(<?php echo $supporter['id']; ?>)"
                                                        title="عرض">
                                                    <i class="fas fa-eye"></i>
                                                </button>
                                                <?php if ($can_edit): ?>
                                                <button type="button" class="btn btn-sm btn-warning"
                                                        onclick="editSupporter(<?php echo $supporter['id']; ?>)"
                                                        title="تعديل">
                                                    <i class="fas fa-edit"></i>
                                                </button>
                                                <?php endif; ?>
                                                <button type="button" class="btn btn-sm btn-success"
                                                        onclick="printSupporterCard(<?php echo $supporter['id']; ?>)"
                                                        title="طباعة بطاقة">
                                                    <i class="fas fa-print"></i>
                                                </button>
                                                <?php if ($can_delete): ?>
                                                <button type="button" class="btn btn-sm btn-danger"
                                                        onclick="deleteSupporter(<?php echo $supporter['id']; ?>)"
                                                        title="حذف">
                                                    <i class="fas fa-trash"></i>
                                                </button>
                                                <?php endif; ?>
                                            </div>
                                        </td>
                                    </tr>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </main>
        </div>
    </div>

    <!-- نوافذ منبثقة -->
    <?php include 'modals.php'; ?>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://code.jquery.com/jquery-3.7.0.min.js"></script>
    <script src="https://cdn.datatables.net/1.13.6/js/jquery.dataTables.min.js"></script>
    <script src="https://cdn.datatables.net/1.13.6/js/dataTables.bootstrap5.min.js"></script>
    <script src="../../assets/js/supporters.js"></script>
</body>
</html>
