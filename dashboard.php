<?php
require_once 'config/config.php';
require_once 'config/database.php';

// التحقق من تسجيل الدخول
if (!isLoggedIn() || !isCandidate()) {
    redirect('login.php');
}

// دالة تنسيق التاريخ العربي
function formatArabicDate($date, $format = 'short') {
    if (!$date) return '';

    $timestamp = is_string($date) ? strtotime($date) : $date;

    $arabic_months = [
        1 => 'يناير', 2 => 'فبراير', 3 => 'مارس', 4 => 'أبريل',
        5 => 'مايو', 6 => 'يونيو', 7 => 'يوليو', 8 => 'أغسطس',
        9 => 'سبتمبر', 10 => 'أكتوبر', 11 => 'نوفمبر', 12 => 'ديسمبر'
    ];

    $day = date('j', $timestamp);
    $month = $arabic_months[date('n', $timestamp)];
    $year = date('Y', $timestamp);

    if ($format === 'short') {
        return "$day $month $year";
    } else {
        $time = date('H:i', $timestamp);
        return "$day $month $year - $time";
    }
}

// جلب الإحصائيات
$stats = [];

// عدد المؤيدين
$stats['supporters'] = fetchOne("SELECT COUNT(*) as count FROM supporters")['count'] ?? 0;

// عدد المناطق
$stats['regions'] = fetchOne("SELECT COUNT(*) as count FROM regions")['count'] ?? 0;

// عدد الإداريين
$stats['admins'] = fetchOne("SELECT COUNT(*) as count FROM admins WHERE status = 'active'")['count'] ?? 0;

// عدد المطالب
$stats['requests'] = fetchOne("SELECT COUNT(*) as count FROM supporter_requests")['count'] ?? 0;

// عدد المطالب المعلقة
$stats['pending_requests'] = fetchOne("SELECT COUNT(*) as count FROM supporter_requests WHERE status = 'pending'")['count'] ?? 0;

// عدد الرسائل
$stats['messages'] = fetchOne("SELECT COUNT(*) as count FROM messages")['count'] ?? 0;

// عدد التقارير
$stats['reports'] = fetchOne("SELECT COUNT(*) as count FROM weekly_reports")['count'] ?? 0;

// آخر التقارير
$recent_reports = fetchAll("SELECT wr.*, a.full_name as admin_name, r.name as region_name
                           FROM weekly_reports wr
                           LEFT JOIN admins a ON wr.admin_id = a.id
                           LEFT JOIN regions r ON a.region_id = r.id
                           ORDER BY wr.created_at DESC LIMIT 5");

// آخر المطالب
$recent_requests = fetchAll("SELECT sr.*, a.full_name as admin_name
                            FROM supporter_requests sr
                            LEFT JOIN admins a ON sr.admin_id = a.id
                            ORDER BY sr.submitted_at DESC LIMIT 5");

// آخر الرسائل
$recent_messages = fetchAll("SELECT m.*,
                            CASE
                                WHEN m.sender_type = 'admin' THEN a.full_name
                                WHEN m.sender_type = 'management' THEN u.full_name
                                ELSE 'النظام'
                            END as sender_name
                            FROM messages m
                            LEFT JOIN admins a ON m.sender_id = a.id AND m.sender_type = 'admin'
                            LEFT JOIN users u ON m.sender_id = u.id AND m.sender_type = 'management'
                            ORDER BY m.created_at DESC LIMIT 5");

// الإشعارات غير المقروءة
$notifications = fetchAll("SELECT * FROM notifications WHERE (user_type = 'candidate' AND user_id = ?) OR user_type = 'all' ORDER BY created_at DESC LIMIT 10", [$_SESSION['user_id']]);

// آخر المؤيدين المضافين
$recent_supporters = fetchAll("SELECT s.*, a.full_name as admin_name, r.name as region_name
                              FROM supporters s
                              LEFT JOIN admins a ON s.added_by = a.id
                              LEFT JOIN regions r ON s.region_id = r.id
                              ORDER BY s.created_at DESC LIMIT 5");

// إحصائيات المرفقات
$stats['attachments'] = fetchOne("SELECT COUNT(*) as count FROM supporter_attachments")['count'] ?? 0;
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>لوحة التحكم - <?php echo SITE_NAME; ?></title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/fullcalendar@6.1.8/index.global.min.css" rel="stylesheet">
    <link href="assets/css/dashboard.css" rel="stylesheet">
</head>
<body>
    <!-- شريط التنقل العلوي -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary fixed-top">
        <div class="container-fluid">
            <a class="navbar-brand" href="#">
                <i class="fas fa-vote-yea me-2"></i>
                نظام إدارة الحملة الانتخابية
            </a>

            <div class="navbar-nav ms-auto">
                <!-- الإشعارات -->
                <div class="nav-item dropdown">
                    <a class="nav-link dropdown-toggle position-relative" href="#" id="notificationsDropdown" role="button" data-bs-toggle="dropdown">
                        <i class="fas fa-bell"></i>
                        <?php if (count($notifications) > 0): ?>
                            <span class="position-absolute top-0 start-100 translate-middle badge rounded-pill bg-danger">
                                <?php echo count($notifications); ?>
                            </span>
                        <?php endif; ?>
                    </a>
                    <ul class="dropdown-menu dropdown-menu-end notifications-dropdown">
                        <li><h6 class="dropdown-header">الإشعارات</h6></li>
                        <?php if (empty($notifications)): ?>
                            <li><span class="dropdown-item-text">لا توجد إشعارات جديدة</span></li>
                        <?php else: ?>
                            <?php foreach ($notifications as $notification): ?>
                                <li>
                                    <a class="dropdown-item" href="#">
                                        <div class="notification-item">
                                            <strong><?php echo htmlspecialchars($notification['title']); ?></strong>
                                            <p class="mb-0 text-muted small"><?php echo htmlspecialchars($notification['message']); ?></p>
                                            <small class="text-muted"><?php echo formatArabicDate($notification['created_at']); ?></small>
                                        </div>
                                    </a>
                                </li>
                            <?php endforeach; ?>
                        <?php endif; ?>
                    </ul>
                </div>

                <!-- قائمة المستخدم -->
                <div class="nav-item dropdown">
                    <a class="nav-link dropdown-toggle" href="#" id="userDropdown" role="button" data-bs-toggle="dropdown">
                        <i class="fas fa-user-circle me-1"></i>
                        <?php echo htmlspecialchars($_SESSION['full_name']); ?>
                    </a>
                    <ul class="dropdown-menu dropdown-menu-end">
                        <li><a class="dropdown-item" href="modules/settings/settings.php"><i class="fas fa-cog me-2"></i>الإعدادات</a></li>
                        <li><hr class="dropdown-divider"></li>
                        <li><a class="dropdown-item" href="logout.php"><i class="fas fa-sign-out-alt me-2"></i>تسجيل الخروج</a></li>
                    </ul>
                </div>
            </div>
        </div>
    </nav>

    <!-- المحتوى الرئيسي -->
    <div class="container-fluid main-content">
        <div class="row">
            <!-- الشريط الجانبي -->
            <nav class="col-md-3 col-lg-2 d-md-block sidebar">
                <div class="position-sticky pt-3">
                    <ul class="nav flex-column">
                        <li class="nav-item">
                            <a class="nav-link active" href="dashboard.php">
                                <i class="fas fa-tachometer-alt me-2"></i>
                                لوحة التحكم
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="modules/supporters/supporters.php">
                                <i class="fas fa-users me-2"></i>
                                المؤيدين
                                <span class="badge bg-primary ms-auto"><?php echo $stats['supporters']; ?></span>
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="modules/regions/regions.php">
                                <i class="fas fa-map-marked-alt me-2"></i>
                                المناطق
                                <span class="badge bg-success ms-auto"><?php echo $stats['regions']; ?></span>
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="modules/admins/manage_admins.php">
                                <i class="fas fa-user-tie me-2"></i>
                                الإداريين
                                <span class="badge bg-info ms-auto"><?php echo $stats['admins']; ?></span>
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="modules/requests/requests.php">
                                <i class="fas fa-hand-holding-heart me-2"></i>
                                المطالب
                                <span class="badge bg-warning ms-auto"><?php echo $stats['requests']; ?></span>
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="modules/messages/messages.php">
                                <i class="fas fa-envelope me-2"></i>
                                الرسائل
                                <span class="badge bg-secondary ms-auto"><?php echo $stats['messages']; ?></span>
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="modules/reports/reports.php">
                                <i class="fas fa-file-alt me-2"></i>
                                التقارير
                                <span class="badge bg-info ms-auto"><?php echo $stats['reports']; ?></span>
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="modules/notifications/notifications.php">
                                <i class="fas fa-bell me-2"></i>
                                الإشعارات
                                <span class="badge bg-danger ms-auto"><?php echo count($notifications); ?></span>
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="modules/settings/settings.php">
                                <i class="fas fa-cog me-2"></i>
                                الإعدادات
                            </a>
                        </li>
                    </ul>
                </div>
            </nav>

            <!-- المحتوى الرئيسي -->
            <main class="col-md-9 ms-sm-auto col-lg-10 px-md-4">
                <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                    <h1 class="h2">لوحة التحكم</h1>
                    <div class="btn-toolbar mb-2 mb-md-0">
                        <div class="btn-group me-2">
                            <button type="button" class="btn btn-sm btn-outline-secondary" onclick="refreshDashboard()">
                                <i class="fas fa-sync-alt"></i> تحديث
                            </button>
                        </div>
                    </div>
                </div>

                <?php displayMessage(); ?>

                <!-- البحث الشامل -->
                <div class="row mb-4">
                    <div class="col-12">
                        <div class="card">
                            <div class="card-body">
                                <div class="input-group">
                                    <span class="input-group-text">
                                        <i class="fas fa-search"></i>
                                    </span>
                                    <input type="text" class="form-control" id="globalSearch" placeholder="البحث في جميع البيانات...">
                                    <button class="btn btn-primary" type="button" onclick="performGlobalSearch()">
                                        بحث
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- بطاقات الإحصائيات -->
                <div class="row mb-4">
                    <div class="col-xl-3 col-md-6 mb-4">
                        <div class="card border-left-primary shadow h-100 py-2 stats-card" data-module="supporters">
                            <div class="card-body">
                                <div class="row no-gutters align-items-center">
                                    <div class="col mr-2">
                                        <div class="text-xs font-weight-bold text-primary text-uppercase mb-1">المؤيدين</div>
                                        <div class="h5 mb-0 font-weight-bold text-gray-800"><?php echo number_format($stats['supporters']); ?></div>
                                    </div>
                                    <div class="col-auto">
                                        <i class="fas fa-users fa-2x text-gray-300"></i>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="col-xl-3 col-md-6 mb-4">
                        <div class="card border-left-success shadow h-100 py-2 stats-card" data-module="regions">
                            <div class="card-body">
                                <div class="row no-gutters align-items-center">
                                    <div class="col mr-2">
                                        <div class="text-xs font-weight-bold text-success text-uppercase mb-1">المناطق</div>
                                        <div class="h5 mb-0 font-weight-bold text-gray-800"><?php echo number_format($stats['regions']); ?></div>
                                    </div>
                                    <div class="col-auto">
                                        <i class="fas fa-map-marked-alt fa-2x text-gray-300"></i>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="col-xl-3 col-md-6 mb-4">
                        <div class="card border-left-info shadow h-100 py-2 stats-card" data-module="admins">
                            <div class="card-body">
                                <div class="row no-gutters align-items-center">
                                    <div class="col mr-2">
                                        <div class="text-xs font-weight-bold text-info text-uppercase mb-1">الإداريين</div>
                                        <div class="h5 mb-0 font-weight-bold text-gray-800"><?php echo number_format($stats['admins']); ?></div>
                                    </div>
                                    <div class="col-auto">
                                        <i class="fas fa-user-tie fa-2x text-gray-300"></i>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="col-xl-3 col-md-6 mb-4">
                        <div class="card border-left-warning shadow h-100 py-2 stats-card" data-module="requests">
                            <div class="card-body">
                                <div class="row no-gutters align-items-center">
                                    <div class="col mr-2">
                                        <div class="text-xs font-weight-bold text-warning text-uppercase mb-1">المطالب</div>
                                        <div class="h5 mb-0 font-weight-bold text-gray-800"><?php echo number_format($stats['requests']); ?></div>
                                    </div>
                                    <div class="col-auto">
                                        <i class="fas fa-hand-holding-heart fa-2x text-gray-300"></i>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- الصف الثاني من الإحصائيات -->
                <div class="row mb-4">
                    <div class="col-xl-4 col-md-6 mb-4">
                        <div class="card border-left-secondary shadow h-100 py-2 stats-card" data-module="messages">
                            <div class="card-body">
                                <div class="row no-gutters align-items-center">
                                    <div class="col mr-2">
                                        <div class="text-xs font-weight-bold text-secondary text-uppercase mb-1">الرسائل</div>
                                        <div class="h5 mb-0 font-weight-bold text-gray-800"><?php echo number_format($stats['messages']); ?></div>
                                    </div>
                                    <div class="col-auto">
                                        <i class="fas fa-envelope fa-2x text-gray-300"></i>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="col-xl-4 col-md-6 mb-4">
                        <div class="card border-left-danger shadow h-100 py-2 stats-card" data-module="reports">
                            <div class="card-body">
                                <div class="row no-gutters align-items-center">
                                    <div class="col mr-2">
                                        <div class="text-xs font-weight-bold text-danger text-uppercase mb-1">التقارير</div>
                                        <div class="h5 mb-0 font-weight-bold text-gray-800"><?php echo number_format($stats['reports']); ?></div>
                                    </div>
                                    <div class="col-auto">
                                        <i class="fas fa-file-alt fa-2x text-gray-300"></i>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="col-xl-4 col-md-6 mb-4">
                        <div class="card border-left-dark shadow h-100 py-2">
                            <div class="card-body">
                                <div class="row no-gutters align-items-center">
                                    <div class="col mr-2">
                                        <div class="text-xs font-weight-bold text-dark text-uppercase mb-1">المرفقات</div>
                                        <div class="h5 mb-0 font-weight-bold text-gray-800"><?php echo number_format($stats['attachments']); ?></div>
                                    </div>
                                    <div class="col-auto">
                                        <i class="fas fa-paperclip fa-2x text-gray-300"></i>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- الأزرار الرئيسية ثلاثية الأبعاد -->
                <div class="row mb-4">
                    <div class="col-12">
                        <h4 class="mb-3">الوحدات الرئيسية</h4>
                        <div class="row">
                            <div class="col-lg-3 col-md-4 col-sm-6 mb-3">
                                <a href="modules/supporters/supporters.php" class="btn-3d btn-3d-primary">
                                    <i class="fas fa-users"></i>
                                    <span>المؤيدين</span>
                                </a>
                            </div>
                            <div class="col-lg-3 col-md-4 col-sm-6 mb-3">
                                <a href="modules/regions/regions.php" class="btn-3d btn-3d-success">
                                    <i class="fas fa-map-marked-alt"></i>
                                    <span>المناطق</span>
                                </a>
                            </div>
                            <div class="col-lg-3 col-md-4 col-sm-6 mb-3">
                                <a href="modules/admins/manage_admins.php" class="btn-3d btn-3d-info">
                                    <i class="fas fa-user-tie"></i>
                                    <span>الإداريين</span>
                                </a>
                            </div>
                            <div class="col-lg-3 col-md-4 col-sm-6 mb-3">
                                <a href="modules/requests/requests.php" class="btn-3d btn-3d-warning">
                                    <i class="fas fa-hand-holding-heart"></i>
                                    <span>المطالب</span>
                                </a>
                            </div>
                            <div class="col-lg-3 col-md-4 col-sm-6 mb-3">
                                <a href="modules/messages/messages.php" class="btn-3d btn-3d-secondary">
                                    <i class="fas fa-envelope"></i>
                                    <span>الرسائل</span>
                                </a>
                            </div>
                            <div class="col-lg-3 col-md-4 col-sm-6 mb-3">
                                <a href="modules/reports/reports.php" class="btn-3d btn-3d-danger">
                                    <i class="fas fa-file-alt"></i>
                                    <span>التقارير</span>
                                </a>
                            </div>
                            <div class="col-lg-3 col-md-4 col-sm-6 mb-3">
                                <a href="modules/notifications/notifications.php" class="btn-3d btn-3d-dark">
                                    <i class="fas fa-bell"></i>
                                    <span>الإشعارات</span>
                                </a>
                            </div>
                            <div class="col-lg-3 col-md-4 col-sm-6 mb-3">
                                <a href="modules/settings/settings.php" class="btn-3d btn-3d-light">
                                    <i class="fas fa-cog"></i>
                                    <span>الإعدادات</span>
                                </a>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- آخر الأنشطة والإحصائيات -->
                <div class="row">
                    <!-- آخر المؤيدين -->
                    <div class="col-lg-4 mb-4">
                        <div class="card shadow">
                            <div class="card-header py-3">
                                <h6 class="m-0 font-weight-bold text-primary">آخر المؤيدين المضافين</h6>
                            </div>
                            <div class="card-body">
                                <?php if (empty($recent_supporters)): ?>
                                    <p class="text-muted text-center">لا توجد مؤيدين حديثين</p>
                                <?php else: ?>
                                    <?php foreach ($recent_supporters as $supporter): ?>
                                        <div class="supporter-item mb-3 p-2 border-bottom">
                                            <h6 class="mb-1"><?php echo htmlspecialchars($supporter['full_name']); ?></h6>
                                            <p class="mb-1 text-muted small">
                                                <?php echo htmlspecialchars($supporter['phone']); ?> |
                                                <?php echo htmlspecialchars($supporter['region_name'] ?? 'غير محدد'); ?>
                                            </p>
                                            <small class="text-muted">
                                                بواسطة: <?php echo htmlspecialchars($supporter['admin_name'] ?? 'غير محدد'); ?> |
                                                <?php echo formatArabicDate($supporter['created_at']); ?>
                                            </small>
                                        </div>
                                    <?php endforeach; ?>
                                    <div class="text-center">
                                        <a href="modules/supporters/supporters.php" class="btn btn-sm btn-primary">عرض جميع المؤيدين</a>
                                    </div>
                                <?php endif; ?>
                            </div>
                        </div>
                    </div>

                    <!-- آخر المطالب -->
                    <div class="col-lg-4 mb-4">
                        <div class="card shadow">
                            <div class="card-header py-3">
                                <h6 class="m-0 font-weight-bold text-warning">آخر المطالب</h6>
                            </div>
                            <div class="card-body">
                                <?php if (empty($recent_requests)): ?>
                                    <p class="text-muted text-center">لا توجد مطالب حديثة</p>
                                <?php else: ?>
                                    <?php foreach ($recent_requests as $request): ?>
                                        <div class="request-item mb-3 p-2 border-bottom">
                                            <h6 class="mb-1"><?php echo htmlspecialchars($request['title']); ?></h6>
                                            <p class="mb-1 text-muted small">
                                                <?php echo htmlspecialchars($request['supporter_name']); ?> |
                                                <span class="badge bg-<?php echo $request['status'] === 'pending' ? 'warning' : ($request['status'] === 'completed' ? 'success' : 'info'); ?>">
                                                    <?php echo $request['status']; ?>
                                                </span>
                                            </p>
                                            <small class="text-muted">
                                                بواسطة: <?php echo htmlspecialchars($request['admin_name'] ?? 'غير محدد'); ?> |
                                                <?php echo formatArabicDate($request['submitted_at']); ?>
                                            </small>
                                        </div>
                                    <?php endforeach; ?>
                                    <div class="text-center">
                                        <a href="modules/requests/requests.php" class="btn btn-sm btn-warning">عرض جميع المطالب</a>
                                    </div>
                                <?php endif; ?>
                            </div>
                        </div>
                    </div>

                    <!-- آخر التقارير -->
                    <div class="col-lg-4 mb-4">
                        <div class="card shadow">
                            <div class="card-header py-3">
                                <h6 class="m-0 font-weight-bold text-success">آخر التقارير</h6>
                            </div>
                            <div class="card-body">
                                <?php if (empty($recent_reports)): ?>
                                    <p class="text-muted text-center">لا توجد تقارير حديثة</p>
                                <?php else: ?>
                                    <?php foreach ($recent_reports as $report): ?>
                                        <div class="report-item mb-3 p-2 border-bottom">
                                            <h6 class="mb-1"><?php echo htmlspecialchars($report['report_period']); ?></h6>
                                            <p class="mb-1 text-muted small">
                                                مؤيدين: <?php echo $report['supporters_added']; ?> |
                                                مطالب: <?php echo $report['requests_submitted']; ?>
                                            </p>
                                            <small class="text-muted">
                                                بواسطة: <?php echo htmlspecialchars($report['admin_name'] ?? 'غير محدد'); ?> |
                                                <?php echo formatArabicDate($report['created_at']); ?>
                                            </small>
                                        </div>
                                    <?php endforeach; ?>
                                    <div class="text-center">
                                        <a href="modules/reports/reports.php" class="btn btn-sm btn-success">عرض جميع التقارير</a>
                                    </div>
                                <?php endif; ?>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- آخر الرسائل -->
                <div class="row">
                    <div class="col-12 mb-4">
                        <div class="card shadow">
                            <div class="card-header py-3">
                                <h6 class="m-0 font-weight-bold text-info">آخر الرسائل</h6>
                            </div>
                            <div class="card-body">
                                <?php if (empty($recent_messages)): ?>
                                    <p class="text-muted text-center">لا توجد رسائل حديثة</p>
                                <?php else: ?>
                                    <div class="row">
                                        <?php foreach ($recent_messages as $message): ?>
                                            <div class="col-md-6 col-lg-4 mb-3">
                                                <div class="message-item p-3 border rounded">
                                                    <h6 class="mb-1"><?php echo htmlspecialchars($message['subject']); ?></h6>
                                                    <p class="mb-1 text-muted small"><?php echo htmlspecialchars(substr($message['message'], 0, 80)) . '...'; ?></p>
                                                    <small class="text-muted">
                                                        من: <?php echo htmlspecialchars($message['sender_name'] ?? 'غير محدد'); ?> |
                                                        <?php echo formatArabicDate($message['created_at']); ?>
                                                    </small>
                                                </div>
                                            </div>
                                        <?php endforeach; ?>
                                    </div>
                                    <div class="text-center">
                                        <a href="modules/messages/messages.php" class="btn btn-sm btn-info">عرض جميع الرسائل</a>
                                    </div>
                                <?php endif; ?>
                            </div>
                        </div>
                    </div>
                </div>
            </main>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/fullcalendar@6.1.8/index.global.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <script src="assets/js/dashboard.js"></script>
</body>
</html>
