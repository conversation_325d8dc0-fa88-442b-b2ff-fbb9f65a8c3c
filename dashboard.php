<?php
require_once 'config/config.php';
require_once 'config/database.php';

// التحقق من تسجيل الدخول
if (!isLoggedIn() || !isCandidate()) {
    redirect('login.php');
}

// جلب الإحصائيات
$stats = [];

// عدد المؤيدين
$stats['supporters'] = fetchOne("SELECT COUNT(*) as count FROM supporters")['count'] ?? 0;

// عدد المناطق
$stats['regions'] = fetchOne("SELECT COUNT(*) as count FROM regions")['count'] ?? 0;

// عدد الإداريين
$stats['admins'] = fetchOne("SELECT COUNT(*) as count FROM users WHERE user_type = 'admin'")['count'] ?? 0;

// عدد المصروفات
$stats['expenses'] = fetchOne("SELECT COUNT(*) as count FROM expenses")['count'] ?? 0;

// إجمالي المصروفات
$stats['total_expenses'] = fetchOne("SELECT SUM(amount) as total FROM expenses")['total'] ?? 0;

// عدد الفعاليات
$stats['events'] = fetchOne("SELECT COUNT(*) as count FROM events")['count'] ?? 0;

// عدد المنافسين
$stats['competitors'] = fetchOne("SELECT COUNT(*) as count FROM competitors")['count'] ?? 0;

// الفعاليات القادمة
$upcoming_events = fetchAll("SELECT * FROM events WHERE event_date >= CURDATE() ORDER BY event_date ASC LIMIT 5");

// آخر التقارير
$recent_reports = fetchAll("SELECT r.*, u.full_name as admin_name, reg.name as region_name 
                           FROM reports r 
                           LEFT JOIN users u ON r.submitted_by = u.id 
                           LEFT JOIN regions reg ON r.region_id = reg.id 
                           ORDER BY r.created_at DESC LIMIT 5");

// الإشعارات غير المقروءة
$notifications = fetchAll("SELECT * FROM notifications WHERE user_id IS NULL OR user_id = ? ORDER BY created_at DESC LIMIT 10", [$_SESSION['user_id']]);
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>لوحة التحكم - <?php echo SITE_NAME; ?></title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/fullcalendar@6.1.8/index.global.min.css" rel="stylesheet">
    <link href="assets/css/dashboard.css" rel="stylesheet">
</head>
<body>
    <!-- شريط التنقل العلوي -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary fixed-top">
        <div class="container-fluid">
            <a class="navbar-brand" href="#">
                <i class="fas fa-vote-yea me-2"></i>
                نظام إدارة الحملة الانتخابية
            </a>
            
            <div class="navbar-nav ms-auto">
                <!-- الإشعارات -->
                <div class="nav-item dropdown">
                    <a class="nav-link dropdown-toggle position-relative" href="#" id="notificationsDropdown" role="button" data-bs-toggle="dropdown">
                        <i class="fas fa-bell"></i>
                        <?php if (count($notifications) > 0): ?>
                            <span class="position-absolute top-0 start-100 translate-middle badge rounded-pill bg-danger">
                                <?php echo count($notifications); ?>
                            </span>
                        <?php endif; ?>
                    </a>
                    <ul class="dropdown-menu dropdown-menu-end notifications-dropdown">
                        <li><h6 class="dropdown-header">الإشعارات</h6></li>
                        <?php if (empty($notifications)): ?>
                            <li><span class="dropdown-item-text">لا توجد إشعارات جديدة</span></li>
                        <?php else: ?>
                            <?php foreach ($notifications as $notification): ?>
                                <li>
                                    <a class="dropdown-item" href="#">
                                        <div class="notification-item">
                                            <strong><?php echo htmlspecialchars($notification['title']); ?></strong>
                                            <p class="mb-0 text-muted small"><?php echo htmlspecialchars($notification['message']); ?></p>
                                            <small class="text-muted"><?php echo formatArabicDate($notification['created_at']); ?></small>
                                        </div>
                                    </a>
                                </li>
                            <?php endforeach; ?>
                        <?php endif; ?>
                    </ul>
                </div>
                
                <!-- قائمة المستخدم -->
                <div class="nav-item dropdown">
                    <a class="nav-link dropdown-toggle" href="#" id="userDropdown" role="button" data-bs-toggle="dropdown">
                        <i class="fas fa-user-circle me-1"></i>
                        <?php echo htmlspecialchars($_SESSION['full_name']); ?>
                    </a>
                    <ul class="dropdown-menu dropdown-menu-end">
                        <li><a class="dropdown-item" href="modules/settings/settings.php"><i class="fas fa-cog me-2"></i>الإعدادات</a></li>
                        <li><hr class="dropdown-divider"></li>
                        <li><a class="dropdown-item" href="logout.php"><i class="fas fa-sign-out-alt me-2"></i>تسجيل الخروج</a></li>
                    </ul>
                </div>
            </div>
        </div>
    </nav>

    <!-- المحتوى الرئيسي -->
    <div class="container-fluid main-content">
        <div class="row">
            <!-- الشريط الجانبي -->
            <nav class="col-md-3 col-lg-2 d-md-block sidebar">
                <div class="position-sticky pt-3">
                    <ul class="nav flex-column">
                        <li class="nav-item">
                            <a class="nav-link active" href="dashboard.php">
                                <i class="fas fa-tachometer-alt me-2"></i>
                                لوحة التحكم
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="modules/supporters/supporters.php">
                                <i class="fas fa-users me-2"></i>
                                المؤيدين
                                <span class="badge bg-primary ms-auto"><?php echo $stats['supporters']; ?></span>
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="modules/regions/regions.php">
                                <i class="fas fa-map-marked-alt me-2"></i>
                                المناطق
                                <span class="badge bg-success ms-auto"><?php echo $stats['regions']; ?></span>
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="modules/admins/admins.php">
                                <i class="fas fa-user-tie me-2"></i>
                                الإداريين
                                <span class="badge bg-info ms-auto"><?php echo $stats['admins']; ?></span>
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="modules/expenses/expenses.php">
                                <i class="fas fa-money-bill-wave me-2"></i>
                                المصروفات
                                <span class="badge bg-warning ms-auto"><?php echo $stats['expenses']; ?></span>
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="modules/events/events.php">
                                <i class="fas fa-calendar-alt me-2"></i>
                                الفعاليات
                                <span class="badge bg-secondary ms-auto"><?php echo $stats['events']; ?></span>
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="modules/competitors/competitors.php">
                                <i class="fas fa-chess me-2"></i>
                                المنافسين
                                <span class="badge bg-danger ms-auto"><?php echo $stats['competitors']; ?></span>
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="modules/reports/reports.php">
                                <i class="fas fa-chart-bar me-2"></i>
                                التقارير
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="modules/settings/settings.php">
                                <i class="fas fa-cog me-2"></i>
                                الإعدادات
                            </a>
                        </li>
                    </ul>
                </div>
            </nav>

            <!-- المحتوى الرئيسي -->
            <main class="col-md-9 ms-sm-auto col-lg-10 px-md-4">
                <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                    <h1 class="h2">لوحة التحكم</h1>
                    <div class="btn-toolbar mb-2 mb-md-0">
                        <div class="btn-group me-2">
                            <button type="button" class="btn btn-sm btn-outline-secondary" onclick="refreshDashboard()">
                                <i class="fas fa-sync-alt"></i> تحديث
                            </button>
                        </div>
                    </div>
                </div>

                <?php displayMessage(); ?>

                <!-- البحث الشامل -->
                <div class="row mb-4">
                    <div class="col-12">
                        <div class="card">
                            <div class="card-body">
                                <div class="input-group">
                                    <span class="input-group-text">
                                        <i class="fas fa-search"></i>
                                    </span>
                                    <input type="text" class="form-control" id="globalSearch" placeholder="البحث في جميع البيانات...">
                                    <button class="btn btn-primary" type="button" onclick="performGlobalSearch()">
                                        بحث
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- بطاقات الإحصائيات -->
                <div class="row mb-4">
                    <div class="col-xl-3 col-md-6 mb-4">
                        <div class="card border-left-primary shadow h-100 py-2 stats-card" data-module="supporters">
                            <div class="card-body">
                                <div class="row no-gutters align-items-center">
                                    <div class="col mr-2">
                                        <div class="text-xs font-weight-bold text-primary text-uppercase mb-1">المؤيدين</div>
                                        <div class="h5 mb-0 font-weight-bold text-gray-800"><?php echo number_format($stats['supporters']); ?></div>
                                    </div>
                                    <div class="col-auto">
                                        <i class="fas fa-users fa-2x text-gray-300"></i>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="col-xl-3 col-md-6 mb-4">
                        <div class="card border-left-success shadow h-100 py-2 stats-card" data-module="regions">
                            <div class="card-body">
                                <div class="row no-gutters align-items-center">
                                    <div class="col mr-2">
                                        <div class="text-xs font-weight-bold text-success text-uppercase mb-1">المناطق</div>
                                        <div class="h5 mb-0 font-weight-bold text-gray-800"><?php echo number_format($stats['regions']); ?></div>
                                    </div>
                                    <div class="col-auto">
                                        <i class="fas fa-map-marked-alt fa-2x text-gray-300"></i>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="col-xl-3 col-md-6 mb-4">
                        <div class="card border-left-info shadow h-100 py-2 stats-card" data-module="admins">
                            <div class="card-body">
                                <div class="row no-gutters align-items-center">
                                    <div class="col mr-2">
                                        <div class="text-xs font-weight-bold text-info text-uppercase mb-1">الإداريين</div>
                                        <div class="h5 mb-0 font-weight-bold text-gray-800"><?php echo number_format($stats['admins']); ?></div>
                                    </div>
                                    <div class="col-auto">
                                        <i class="fas fa-user-tie fa-2x text-gray-300"></i>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="col-xl-3 col-md-6 mb-4">
                        <div class="card border-left-warning shadow h-100 py-2 stats-card" data-module="expenses">
                            <div class="card-body">
                                <div class="row no-gutters align-items-center">
                                    <div class="col mr-2">
                                        <div class="text-xs font-weight-bold text-warning text-uppercase mb-1">إجمالي المصروفات</div>
                                        <div class="h5 mb-0 font-weight-bold text-gray-800"><?php echo number_format($stats['total_expenses']); ?> د.ع</div>
                                    </div>
                                    <div class="col-auto">
                                        <i class="fas fa-money-bill-wave fa-2x text-gray-300"></i>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- الصف الثاني من الإحصائيات -->
                <div class="row mb-4">
                    <div class="col-xl-4 col-md-6 mb-4">
                        <div class="card border-left-secondary shadow h-100 py-2 stats-card" data-module="events">
                            <div class="card-body">
                                <div class="row no-gutters align-items-center">
                                    <div class="col mr-2">
                                        <div class="text-xs font-weight-bold text-secondary text-uppercase mb-1">الفعاليات</div>
                                        <div class="h5 mb-0 font-weight-bold text-gray-800"><?php echo number_format($stats['events']); ?></div>
                                    </div>
                                    <div class="col-auto">
                                        <i class="fas fa-calendar-alt fa-2x text-gray-300"></i>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="col-xl-4 col-md-6 mb-4">
                        <div class="card border-left-danger shadow h-100 py-2 stats-card" data-module="competitors">
                            <div class="card-body">
                                <div class="row no-gutters align-items-center">
                                    <div class="col mr-2">
                                        <div class="text-xs font-weight-bold text-danger text-uppercase mb-1">المنافسين</div>
                                        <div class="h5 mb-0 font-weight-bold text-gray-800"><?php echo number_format($stats['competitors']); ?></div>
                                    </div>
                                    <div class="col-auto">
                                        <i class="fas fa-chess fa-2x text-gray-300"></i>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="col-xl-4 col-md-6 mb-4">
                        <div class="card border-left-dark shadow h-100 py-2">
                            <div class="card-body">
                                <div class="row no-gutters align-items-center">
                                    <div class="col mr-2">
                                        <div class="text-xs font-weight-bold text-dark text-uppercase mb-1">عدد المصروفات</div>
                                        <div class="h5 mb-0 font-weight-bold text-gray-800"><?php echo number_format($stats['expenses']); ?></div>
                                    </div>
                                    <div class="col-auto">
                                        <i class="fas fa-receipt fa-2x text-gray-300"></i>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- الأزرار الرئيسية ثلاثية الأبعاد -->
                <div class="row mb-4">
                    <div class="col-12">
                        <h4 class="mb-3">الوحدات الرئيسية</h4>
                        <div class="row">
                            <div class="col-lg-3 col-md-4 col-sm-6 mb-3">
                                <a href="modules/supporters/supporters.php" class="btn-3d btn-3d-primary">
                                    <i class="fas fa-users"></i>
                                    <span>المؤيدين</span>
                                </a>
                            </div>
                            <div class="col-lg-3 col-md-4 col-sm-6 mb-3">
                                <a href="modules/regions/regions.php" class="btn-3d btn-3d-success">
                                    <i class="fas fa-map-marked-alt"></i>
                                    <span>المناطق</span>
                                </a>
                            </div>
                            <div class="col-lg-3 col-md-4 col-sm-6 mb-3">
                                <a href="modules/admins/admins.php" class="btn-3d btn-3d-info">
                                    <i class="fas fa-user-tie"></i>
                                    <span>الإداريين</span>
                                </a>
                            </div>
                            <div class="col-lg-3 col-md-4 col-sm-6 mb-3">
                                <a href="modules/expenses/expenses.php" class="btn-3d btn-3d-warning">
                                    <i class="fas fa-money-bill-wave"></i>
                                    <span>المصروفات</span>
                                </a>
                            </div>
                            <div class="col-lg-3 col-md-4 col-sm-6 mb-3">
                                <a href="modules/events/events.php" class="btn-3d btn-3d-secondary">
                                    <i class="fas fa-calendar-alt"></i>
                                    <span>الفعاليات</span>
                                </a>
                            </div>
                            <div class="col-lg-3 col-md-4 col-sm-6 mb-3">
                                <a href="modules/competitors/competitors.php" class="btn-3d btn-3d-danger">
                                    <i class="fas fa-chess"></i>
                                    <span>المنافسين</span>
                                </a>
                            </div>
                            <div class="col-lg-3 col-md-4 col-sm-6 mb-3">
                                <a href="modules/reports/reports.php" class="btn-3d btn-3d-dark">
                                    <i class="fas fa-chart-bar"></i>
                                    <span>التقارير</span>
                                </a>
                            </div>
                            <div class="col-lg-3 col-md-4 col-sm-6 mb-3">
                                <a href="modules/settings/settings.php" class="btn-3d btn-3d-light">
                                    <i class="fas fa-cog"></i>
                                    <span>الإعدادات</span>
                                </a>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- تقويم الفعاليات وآخر التقارير -->
                <div class="row">
                    <div class="col-lg-8 mb-4">
                        <div class="card shadow">
                            <div class="card-header py-3">
                                <h6 class="m-0 font-weight-bold text-primary">تقويم الفعاليات</h6>
                            </div>
                            <div class="card-body">
                                <div id="calendar"></div>
                            </div>
                        </div>
                    </div>

                    <div class="col-lg-4 mb-4">
                        <div class="card shadow">
                            <div class="card-header py-3">
                                <h6 class="m-0 font-weight-bold text-primary">آخر التقارير</h6>
                            </div>
                            <div class="card-body">
                                <?php if (empty($recent_reports)): ?>
                                    <p class="text-muted text-center">لا توجد تقارير حديثة</p>
                                <?php else: ?>
                                    <?php foreach ($recent_reports as $report): ?>
                                        <div class="report-item mb-3 p-2 border-bottom">
                                            <h6 class="mb-1"><?php echo htmlspecialchars($report['title']); ?></h6>
                                            <p class="mb-1 text-muted small"><?php echo htmlspecialchars(substr($report['content'], 0, 100)) . '...'; ?></p>
                                            <small class="text-muted">
                                                بواسطة: <?php echo htmlspecialchars($report['admin_name']); ?> | 
                                                <?php echo formatArabicDate($report['created_at']); ?>
                                            </small>
                                        </div>
                                    <?php endforeach; ?>
                                    <div class="text-center">
                                        <a href="modules/reports/reports.php" class="btn btn-sm btn-primary">عرض جميع التقارير</a>
                                    </div>
                                <?php endif; ?>
                            </div>
                        </div>
                    </div>
                </div>
            </main>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/fullcalendar@6.1.8/index.global.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <script src="assets/js/dashboard.js"></script>
</body>
</html>
