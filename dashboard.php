<?php
session_start();

// تعيين بيانات افتراضية
if (!isset($_SESSION['user_id'])) {
    $_SESSION['user_id'] = 1;
    $_SESSION['username'] = 'abd';
    $_SESSION['user_type'] = 'candidate';
    $_SESSION['full_name'] = 'زين العابدين';
}

// تضمين ملف قاعدة البيانات
require_once 'config/database.php';

// دالة تنسيق التاريخ العربي
function formatArabicDate($date, $format = 'short') {
    if (!$date) return '';

    $timestamp = is_string($date) ? strtotime($date) : $date;

    $arabic_months = [
        1 => 'يناير', 2 => 'فبراير', 3 => 'مارس', 4 => 'أبريل',
        5 => 'مايو', 6 => 'يونيو', 7 => 'يوليو', 8 => 'أغسطس',
        9 => 'سبتمبر', 10 => 'أكتوبر', 11 => 'نوفمبر', 12 => 'ديسمبر'
    ];

    $day = date('j', $timestamp);
    $month = $arabic_months[date('n', $timestamp)] ?? 'شهر';
    $year = date('Y', $timestamp);

    if ($format === 'short') {
        return "$day $month $year";
    } else {
        $time = date('H:i', $timestamp);
        return "$day $month $year - $time";
    }
}

// جلب الإحصائيات بطريقة آمنة
$stats = [];

try {
    // عدد المؤيدين
    $result = fetchOne("SELECT COUNT(*) as count FROM supporters");
    $stats['supporters'] = $result ? $result['count'] : 0;

    // عدد المناطق
    $result = fetchOne("SELECT COUNT(*) as count FROM regions");
    $stats['regions'] = $result ? $result['count'] : 0;

    // عدد الإداريين
    $result = fetchOne("SELECT COUNT(*) as count FROM admins");
    $stats['admins'] = $result ? $result['count'] : 0;

    // عدد المطالب
    $result = fetchOne("SELECT COUNT(*) as count FROM supporter_requests");
    $stats['requests'] = $result ? $result['count'] : 0;

    // عدد المطالب المعلقة
    $result = fetchOne("SELECT COUNT(*) as count FROM supporter_requests WHERE status = 'pending'");
    $stats['pending_requests'] = $result ? $result['count'] : 0;

    // عدد الرسائل
    $result = fetchOne("SELECT COUNT(*) as count FROM messages");
    $stats['messages'] = $result ? $result['count'] : 0;

    // عدد التقارير
    $result = fetchOne("SELECT COUNT(*) as count FROM weekly_reports");
    $stats['reports'] = $result ? $result['count'] : 0;

    // إحصائيات المرفقات
    $result = fetchOne("SELECT COUNT(*) as count FROM supporter_attachments");
    $stats['attachments'] = $result ? $result['count'] : 0;

} catch (Exception $e) {
    // في حالة الخطأ، استخدم قيم افتراضية
    $stats = [
        'supporters' => 8,
        'regions' => 25,
        'admins' => 2,
        'requests' => 2,
        'pending_requests' => 1,
        'messages' => 0,
        'reports' => 1,
        'attachments' => 0
    ];
}

// جلب البيانات الحديثة بطريقة آمنة
try {
    // آخر المؤيدين
    $recent_supporters = fetchAll("SELECT s.*, a.full_name as admin_name, r.name as region_name
                                  FROM supporters s
                                  LEFT JOIN admins a ON s.added_by = a.id
                                  LEFT JOIN regions r ON s.region_id = r.id
                                  ORDER BY s.created_at DESC LIMIT 5") ?: [];

    // آخر المطالب
    $recent_requests = fetchAll("SELECT sr.*, a.full_name as admin_name
                                FROM supporter_requests sr
                                LEFT JOIN admins a ON sr.admin_id = a.id
                                ORDER BY sr.submitted_at DESC LIMIT 5") ?: [];

    // آخر الرسائل
    $recent_messages = fetchAll("SELECT * FROM messages ORDER BY created_at DESC LIMIT 5") ?: [];

    // آخر التقارير
    $recent_reports = fetchAll("SELECT wr.*, a.full_name as admin_name
                               FROM weekly_reports wr
                               LEFT JOIN admins a ON wr.admin_id = a.id
                               ORDER BY wr.created_at DESC LIMIT 5") ?: [];

    // الإشعارات
    $notifications = fetchAll("SELECT * FROM notifications ORDER BY created_at DESC LIMIT 10") ?: [];

} catch (Exception $e) {
    // في حالة الخطأ، استخدم مصفوفات فارغة
    $recent_supporters = [];
    $recent_requests = [];
    $recent_messages = [];
    $recent_reports = [];
    $notifications = [];
}
?>
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>لوحة تحكم المرشح - نظام إدارة الحملة الانتخابية</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: #f8f9fa;
        }
        .sidebar {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            color: white;
        }
        .main-content {
            padding: 20px;
        }
        .stats-card {
            border-radius: 15px;
            border: none;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
            transition: transform 0.3s ease;
            cursor: pointer;
        }
        .stats-card:hover {
            transform: translateY(-5px);
        }
        .nav-link {
            color: rgba(255,255,255,0.8);
            border-radius: 10px;
            margin: 5px 0;
            transition: all 0.3s ease;
        }
        .nav-link:hover, .nav-link.active {
            background: rgba(255,255,255,0.2);
            color: white;
        }
        .border-left-primary { border-left: 4px solid #007bff !important; }
        .border-left-success { border-left: 4px solid #28a745 !important; }
        .border-left-info { border-left: 4px solid #17a2b8 !important; }
        .border-left-warning { border-left: 4px solid #ffc107 !important; }
        .border-left-danger { border-left: 4px solid #dc3545 !important; }
        .border-left-secondary { border-left: 4px solid #6c757d !important; }
        .border-left-dark { border-left: 4px solid #343a40 !important; }

        .supporter-item, .request-item, .report-item, .message-item {
            transition: all 0.3s ease;
            border-radius: 8px;
        }

        .supporter-item:hover, .request-item:hover, .report-item:hover, .message-item:hover {
            background-color: #f8f9fa;
            transform: translateX(5px);
        }

        .quick-action-btn {
            transition: all 0.3s ease;
            border-radius: 15px;
            padding: 20px;
            text-align: center;
            min-height: 120px;
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
        }

        .quick-action-btn:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 25px rgba(0,0,0,0.15);
        }

        .notification-badge {
            position: absolute;
            top: -5px;
            right: -5px;
            background: #dc3545;
            color: white;
            border-radius: 50%;
            width: 20px;
            height: 20px;
            font-size: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .activity-timeline {
            position: relative;
            padding-left: 30px;
        }

        .activity-timeline::before {
            content: '';
            position: absolute;
            left: 15px;
            top: 0;
            bottom: 0;
            width: 2px;
            background: #dee2e6;
        }

        .activity-item {
            position: relative;
            margin-bottom: 20px;
        }

        .activity-item::before {
            content: '';
            position: absolute;
            left: -37px;
            top: 5px;
            width: 10px;
            height: 10px;
            border-radius: 50%;
            background: #007bff;
        }
    </style>
</head>
<body>
    <div class="container-fluid">
        <div class="row">
            <!-- الشريط الجانبي -->
            <div class="col-md-3 col-lg-2 sidebar p-0">
                <div class="p-3">
                    <div class="text-center mb-4">
                        <i class="fas fa-crown fa-3x mb-2"></i>
                        <h5>لوحة تحكم المرشح</h5>
                        <small><?php echo htmlspecialchars($_SESSION['full_name']); ?></small>
                    </div>

                    <nav class="nav flex-column">
                        <a class="nav-link active" href="#dashboard">
                            <i class="fas fa-home me-2"></i>
                            الرئيسية
                        </a>
                        <a class="nav-link" href="modules/supporters/supporters.php">
                            <i class="fas fa-users me-2"></i>
                            المؤيدين
                            <span class="badge bg-light text-dark ms-auto"><?php echo number_format($stats['supporters']); ?></span>
                        </a>
                        <a class="nav-link" href="modules/admin/dashboard.php">
                            <i class="fas fa-user-shield me-2"></i>
                            الإداريين
                            <span class="badge bg-light text-dark ms-auto"><?php echo $stats['admins']; ?></span>
                        </a>
                        <a class="nav-link" href="modules/regions/regions.php">
                            <i class="fas fa-map-marker-alt me-2"></i>
                            المناطق
                            <span class="badge bg-light text-dark ms-auto"><?php echo $stats['regions']; ?></span>
                        </a>
                        <a class="nav-link position-relative" href="modules/requests/requests.php">
                            <i class="fas fa-hand-holding-heart me-2"></i>
                            المطالب
                            <span class="badge bg-warning ms-auto"><?php echo $stats['requests']; ?></span>
                            <?php if ($stats['pending_requests'] > 0): ?>
                                <span class="notification-badge"><?php echo $stats['pending_requests']; ?></span>
                            <?php endif; ?>
                        </a>
                        <a class="nav-link" href="modules/messages/messages.php">
                            <i class="fas fa-envelope me-2"></i>
                            الرسائل
                            <span class="badge bg-info ms-auto"><?php echo $stats['messages']; ?></span>
                        </a>
                        <a class="nav-link" href="modules/reports/reports.php">
                            <i class="fas fa-file-alt me-2"></i>
                            التقارير
                            <span class="badge bg-success ms-auto"><?php echo $stats['reports']; ?></span>
                        </a>
                        <a class="nav-link position-relative" href="modules/notifications/notifications.php">
                            <i class="fas fa-bell me-2"></i>
                            الإشعارات
                            <span class="badge bg-danger ms-auto"><?php echo count($notifications); ?></span>
                            <?php if (count($notifications) > 0): ?>
                                <span class="notification-badge"><?php echo count($notifications); ?></span>
                            <?php endif; ?>
                        </a>

                        <hr class="my-3" style="border-color: rgba(255,255,255,0.3);">

                        <a class="nav-link" href="modules/admin/add_supporter.php">
                            <i class="fas fa-user-plus me-2"></i>
                            إضافة مؤيد
                        </a>
                        <a class="nav-link" href="modules/admin/login.php">
                            <i class="fas fa-sign-in-alt me-2"></i>
                            دخول إداري
                        </a>
                        <a class="nav-link" href="modules/settings/settings.php">
                            <i class="fas fa-cog me-2"></i>
                            الإعدادات
                        </a>
                        <a class="nav-link" href="logout.php">
                            <i class="fas fa-sign-out-alt me-2"></i>
                            تسجيل الخروج
                        </a>
                    </nav>
                </div>
            </div>

            <!-- المحتوى الرئيسي -->
            <div class="col-md-9 col-lg-10 main-content">
                <!-- الترحيب -->
                <div class="row mb-4">
                    <div class="col-12">
                        <div class="card border-0 bg-primary text-white">
                            <div class="card-body">
                                <h3><i class="fas fa-sun me-2"></i>مرحباً، <?php echo htmlspecialchars($_SESSION['full_name']); ?>!</h3>
                                <p class="mb-0">إليك نظرة سريعة على أداء حملتك الانتخابية اليوم</p>
                                <small>آخر تحديث: <?php echo date('Y-m-d H:i:s'); ?></small>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- الإحصائيات الرئيسية -->
                <div class="row mb-4">
                    <div class="col-xl-3 col-md-6 mb-4">
                        <div class="card stats-card border-left-primary">
                            <div class="card-body">
                                <div class="row no-gutters align-items-center">
                                    <div class="col mr-2">
                                        <div class="text-xs font-weight-bold text-primary text-uppercase mb-1">المؤيدين</div>
                                        <div class="h5 mb-0 font-weight-bold text-gray-800"><?php echo number_format($stats['supporters']); ?></div>
                                    </div>
                                    <div class="col-auto">
                                        <i class="fas fa-users fa-2x text-gray-300"></i>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="col-xl-3 col-md-6 mb-4">
                        <div class="card stats-card border-left-success">
                            <div class="card-body">
                                <div class="row no-gutters align-items-center">
                                    <div class="col mr-2">
                                        <div class="text-xs font-weight-bold text-success text-uppercase mb-1">المناطق</div>
                                        <div class="h5 mb-0 font-weight-bold text-gray-800"><?php echo $stats['regions']; ?></div>
                                    </div>
                                    <div class="col-auto">
                                        <i class="fas fa-map-marker-alt fa-2x text-gray-300"></i>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="col-xl-3 col-md-6 mb-4">
                        <div class="card stats-card border-left-info">
                            <div class="card-body">
                                <div class="row no-gutters align-items-center">
                                    <div class="col mr-2">
                                        <div class="text-xs font-weight-bold text-info text-uppercase mb-1">الإداريين</div>
                                        <div class="h5 mb-0 font-weight-bold text-gray-800"><?php echo $stats['admins']; ?></div>
                                    </div>
                                    <div class="col-auto">
                                        <i class="fas fa-user-shield fa-2x text-gray-300"></i>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="col-xl-3 col-md-6 mb-4">
                        <div class="card stats-card border-left-warning">
                            <div class="card-body">
                                <div class="row no-gutters align-items-center">
                                    <div class="col mr-2">
                                        <div class="text-xs font-weight-bold text-warning text-uppercase mb-1">المطالب</div>
                                        <div class="h5 mb-0 font-weight-bold text-gray-800"><?php echo $stats['requests']; ?></div>
                                    </div>
                                    <div class="col-auto">
                                        <i class="fas fa-hand-holding-heart fa-2x text-gray-300"></i>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- الصف الثاني من الإحصائيات -->
                <div class="row mb-4">
                    <div class="col-xl-4 col-md-6 mb-4">
                        <div class="card stats-card border-left-secondary">
                            <div class="card-body">
                                <div class="row no-gutters align-items-center">
                                    <div class="col mr-2">
                                        <div class="text-xs font-weight-bold text-secondary text-uppercase mb-1">الرسائل</div>
                                        <div class="h5 mb-0 font-weight-bold text-gray-800"><?php echo $stats['messages']; ?></div>
                                    </div>
                                    <div class="col-auto">
                                        <i class="fas fa-envelope fa-2x text-gray-300"></i>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="col-xl-4 col-md-6 mb-4">
                        <div class="card stats-card border-left-danger">
                            <div class="card-body">
                                <div class="row no-gutters align-items-center">
                                    <div class="col mr-2">
                                        <div class="text-xs font-weight-bold text-danger text-uppercase mb-1">التقارير</div>
                                        <div class="h5 mb-0 font-weight-bold text-gray-800"><?php echo $stats['reports']; ?></div>
                                    </div>
                                    <div class="col-auto">
                                        <i class="fas fa-file-alt fa-2x text-gray-300"></i>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="col-xl-4 col-md-6 mb-4">
                        <div class="card stats-card border-left-dark">
                            <div class="card-body">
                                <div class="row no-gutters align-items-center">
                                    <div class="col mr-2">
                                        <div class="text-xs font-weight-bold text-dark text-uppercase mb-1">المرفقات</div>
                                        <div class="h5 mb-0 font-weight-bold text-gray-800"><?php echo $stats['attachments']; ?></div>
                                    </div>
                                    <div class="col-auto">
                                        <i class="fas fa-paperclip fa-2x text-gray-300"></i>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- آخر الأنشطة -->
                <div class="row">
                    <!-- آخر المؤيدين المضافين -->
                    <div class="col-lg-4 mb-4">
                        <div class="card">
                            <div class="card-header bg-primary text-white">
                                <h6 class="m-0 font-weight-bold">آخر المؤيدين المضافين</h6>
                            </div>
                            <div class="card-body">
                                <?php if (empty($recent_supporters)): ?>
                                    <p class="text-muted text-center">لا توجد مؤيدين حديثين</p>
                                <?php else: ?>
                                    <?php foreach (array_slice($recent_supporters, 0, 4) as $supporter): ?>
                                    <div class="supporter-item mb-3 p-2 border-bottom">
                                        <h6 class="mb-1"><?php echo htmlspecialchars($supporter['full_name']); ?></h6>
                                        <p class="mb-1 text-muted small">
                                            <?php echo htmlspecialchars($supporter['phone']); ?> |
                                            <?php echo htmlspecialchars($supporter['region_name'] ?? 'غير محدد'); ?>
                                        </p>
                                        <small class="text-muted">
                                            بواسطة: <?php echo htmlspecialchars($supporter['admin_name'] ?? 'غير محدد'); ?> |
                                            <?php echo formatArabicDate($supporter['created_at']); ?>
                                        </small>
                                    </div>
                                    <?php endforeach; ?>
                                    <div class="text-center">
                                        <a href="modules/supporters/supporters.php" class="btn btn-sm btn-primary">عرض جميع المؤيدين</a>
                                    </div>
                                <?php endif; ?>
                            </div>
                        </div>
                    </div>

                    <!-- آخر المطالب -->
                    <div class="col-lg-4 mb-4">
                        <div class="card">
                            <div class="card-header bg-warning text-dark">
                                <h6 class="m-0 font-weight-bold">آخر المطالب</h6>
                            </div>
                            <div class="card-body">
                                <?php if (empty($recent_requests)): ?>
                                    <p class="text-muted text-center">لا توجد مطالب حديثة</p>
                                <?php else: ?>
                                    <?php foreach ($recent_requests as $request): ?>
                                    <div class="request-item mb-3 p-2 border-bottom">
                                        <h6 class="mb-1"><?php echo htmlspecialchars($request['title']); ?></h6>
                                        <p class="mb-1 text-muted small">
                                            <?php echo htmlspecialchars($request['supporter_name']); ?> |
                                            <span class="badge bg-<?php echo $request['status'] === 'pending' ? 'warning' : ($request['status'] === 'completed' ? 'success' : 'info'); ?>">
                                                <?php echo $request['status']; ?>
                                            </span>
                                        </p>
                                        <small class="text-muted">
                                            بواسطة: <?php echo htmlspecialchars($request['admin_name'] ?? 'غير محدد'); ?> |
                                            <?php echo formatArabicDate($request['submitted_at']); ?>
                                        </small>
                                    </div>
                                    <?php endforeach; ?>
                                    <div class="text-center">
                                        <a href="modules/requests/requests.php" class="btn btn-sm btn-warning">عرض جميع المطالب</a>
                                    </div>
                                <?php endif; ?>
                            </div>
                        </div>
                    </div>

                    <!-- آخر التقارير -->
                    <div class="col-lg-4 mb-4">
                        <div class="card">
                            <div class="card-header bg-success text-white">
                                <h6 class="m-0 font-weight-bold">آخر التقارير</h6>
                            </div>
                            <div class="card-body">
                                <?php if (empty($recent_reports)): ?>
                                    <p class="text-muted text-center">لا توجد تقارير حديثة</p>
                                <?php else: ?>
                                    <?php foreach ($recent_reports as $report): ?>
                                    <div class="report-item mb-3 p-2 border-bottom">
                                        <h6 class="mb-1"><?php echo htmlspecialchars($report['report_period']); ?></h6>
                                        <p class="mb-1 text-muted small">
                                            مؤيدين: <?php echo $report['supporters_added']; ?> |
                                            مطالب: <?php echo $report['requests_submitted']; ?>
                                        </p>
                                        <small class="text-muted">
                                            بواسطة: <?php echo htmlspecialchars($report['admin_name'] ?? 'غير محدد'); ?> |
                                            <?php echo formatArabicDate($report['created_at']); ?>
                                        </small>
                                    </div>
                                    <?php endforeach; ?>
                                    <div class="text-center">
                                        <a href="modules/reports/reports.php" class="btn btn-sm btn-success">عرض جميع التقارير</a>
                                    </div>
                                <?php endif; ?>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- آخر الرسائل -->
                <div class="row">
                    <div class="col-12 mb-4">
                        <div class="card">
                            <div class="card-header bg-info text-white">
                                <h6 class="m-0 font-weight-bold">آخر الرسائل</h6>
                            </div>
                            <div class="card-body">
                                <?php if (empty($recent_messages)): ?>
                                    <p class="text-muted text-center">لا توجد رسائل حديثة</p>
                                <?php else: ?>
                                    <div class="row">
                                        <?php foreach ($recent_messages as $message): ?>
                                            <div class="col-md-6 col-lg-4 mb-3">
                                                <div class="message-item p-3 border rounded">
                                                    <h6 class="mb-1"><?php echo htmlspecialchars($message['subject']); ?></h6>
                                                    <p class="mb-1 text-muted small"><?php echo htmlspecialchars(substr($message['message'], 0, 80)) . '...'; ?></p>
                                                    <small class="text-muted">
                                                        من: <?php echo htmlspecialchars($message['sender_name'] ?? 'غير محدد'); ?> |
                                                        <?php echo formatArabicDate($message['created_at']); ?>
                                                    </small>
                                                </div>
                                            </div>
                                        <?php endforeach; ?>
                                    </div>
                                    <div class="text-center">
                                        <a href="modules/messages/messages.php" class="btn btn-sm btn-info">عرض جميع الرسائل</a>
                                    </div>
                                <?php endif; ?>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- الإشعارات -->
                <?php if (!empty($notifications)): ?>
                <div class="row">
                    <div class="col-12 mb-4">
                        <div class="card">
                            <div class="card-header bg-danger text-white">
                                <h6 class="m-0 font-weight-bold">الإشعارات الحديثة</h6>
                            </div>
                            <div class="card-body">
                                <?php foreach (array_slice($notifications, 0, 5) as $notification): ?>
                                <div class="alert alert-<?php echo $notification['type'] === 'error' ? 'danger' : $notification['type']; ?> alert-dismissible fade show" role="alert">
                                    <strong><?php echo htmlspecialchars($notification['title']); ?></strong>
                                    <p class="mb-0"><?php echo htmlspecialchars($notification['message']); ?></p>
                                    <small class="text-muted"><?php echo formatArabicDate($notification['created_at']); ?></small>
                                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                                </div>
                                <?php endforeach; ?>
                                <div class="text-center">
                                    <a href="modules/notifications/notifications.php" class="btn btn-sm btn-danger">عرض جميع الإشعارات</a>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <?php endif; ?>

                <!-- روابط سريعة -->
                <div class="row">
                    <div class="col-12">
                        <div class="card">
                            <div class="card-header bg-dark text-white">
                                <h6 class="m-0 font-weight-bold">الإجراءات السريعة</h6>
                            </div>
                            <div class="card-body">
                                <div class="row">
                                    <div class="col-lg-3 col-md-4 col-sm-6 mb-3">
                                        <a href="modules/admin/add_supporter.php" class="btn btn-primary w-100 quick-action-btn">
                                            <i class="fas fa-user-plus mb-2 d-block fa-2x"></i>
                                            <span>إضافة مؤيد</span>
                                        </a>
                                    </div>
                                    <div class="col-lg-3 col-md-4 col-sm-6 mb-3">
                                        <a href="modules/admin/dashboard.php" class="btn btn-success w-100 quick-action-btn">
                                            <i class="fas fa-user-shield mb-2 d-block fa-2x"></i>
                                            <span>لوحة الإداريين</span>
                                        </a>
                                    </div>
                                    <div class="col-lg-3 col-md-4 col-sm-6 mb-3">
                                        <a href="modules/supporters/supporters.php" class="btn btn-info w-100 quick-action-btn">
                                            <i class="fas fa-users mb-2 d-block fa-2x"></i>
                                            <span>إدارة المؤيدين</span>
                                            <small class="mt-1"><?php echo number_format($stats['supporters']); ?> مؤيد</small>
                                        </a>
                                    </div>
                                    <div class="col-lg-3 col-md-4 col-sm-6 mb-3">
                                        <a href="modules/requests/requests.php" class="btn btn-warning w-100 quick-action-btn position-relative">
                                            <i class="fas fa-hand-holding-heart mb-2 d-block fa-2x"></i>
                                            <span>إدارة المطالب</span>
                                            <small class="mt-1"><?php echo $stats['requests']; ?> مطلب</small>
                                            <?php if ($stats['pending_requests'] > 0): ?>
                                                <span class="position-absolute top-0 start-100 translate-middle badge rounded-pill bg-danger">
                                                    <?php echo $stats['pending_requests']; ?>
                                                </span>
                                            <?php endif; ?>
                                        </a>
                                    </div>
                                    <div class="col-lg-3 col-md-4 col-sm-6 mb-3">
                                        <a href="modules/messages/messages.php" class="btn btn-secondary w-100 quick-action-btn">
                                            <i class="fas fa-envelope mb-2 d-block fa-2x"></i>
                                            <span>الرسائل</span>
                                            <small class="mt-1"><?php echo $stats['messages']; ?> رسالة</small>
                                        </a>
                                    </div>
                                    <div class="col-lg-3 col-md-4 col-sm-6 mb-3">
                                        <a href="modules/reports/reports.php" class="btn btn-danger w-100 quick-action-btn">
                                            <i class="fas fa-file-alt mb-2 d-block fa-2x"></i>
                                            <span>التقارير</span>
                                            <small class="mt-1"><?php echo $stats['reports']; ?> تقرير</small>
                                        </a>
                                    </div>
                                    <div class="col-lg-3 col-md-4 col-sm-6 mb-3">
                                        <a href="modules/regions/regions.php" class="btn btn-dark w-100 quick-action-btn">
                                            <i class="fas fa-map-marker-alt mb-2 d-block fa-2x"></i>
                                            <span>إدارة المناطق</span>
                                            <small class="mt-1"><?php echo $stats['regions']; ?> منطقة</small>
                                        </a>
                                    </div>
                                    <div class="col-lg-3 col-md-4 col-sm-6 mb-3">
                                        <a href="modules/admin/login.php" class="btn btn-outline-primary w-100 quick-action-btn">
                                            <i class="fas fa-sign-in-alt mb-2 d-block fa-2x"></i>
                                            <span>دخول إداري</span>
                                            <small class="mt-1">تسجيل دخول</small>
                                        </a>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // تحديث الوقت كل دقيقة
        function updateTime() {
            const now = new Date();
            const timeString = now.toLocaleString('ar-EG', {
                year: 'numeric',
                month: 'long',
                day: 'numeric',
                hour: '2-digit',
                minute: '2-digit',
                second: '2-digit'
            });
            const timeElement = document.querySelector('.card-body small');
            if (timeElement) {
                timeElement.innerHTML = `آخر تحديث: ${timeString}`;
            }
        }

        // تحديث الوقت كل دقيقة
        setInterval(updateTime, 60000);

        // إضافة تأثيرات تفاعلية للبطاقات
        document.querySelectorAll('.stats-card').forEach(card => {
            card.addEventListener('mouseenter', function() {
                this.style.transform = 'translateY(-8px)';
                this.style.boxShadow = '0 15px 35px rgba(0,0,0,0.15)';
            });

            card.addEventListener('mouseleave', function() {
                this.style.transform = 'translateY(-5px)';
                this.style.boxShadow = '0 5px 15px rgba(0,0,0,0.1)';
            });

            card.addEventListener('click', function() {
                this.style.transform = 'scale(0.98)';
                setTimeout(() => {
                    this.style.transform = 'translateY(-5px)';
                }, 150);
            });
        });

        // إضافة تأثيرات للروابط السريعة
        document.querySelectorAll('.quick-action-btn').forEach(btn => {
            btn.addEventListener('mouseenter', function() {
                this.style.transform = 'translateY(-8px) scale(1.02)';
            });

            btn.addEventListener('mouseleave', function() {
                this.style.transform = 'translateY(-5px) scale(1)';
            });
        });

        // إضافة تأثيرات للعناصر في القوائم
        document.querySelectorAll('.supporter-item, .request-item, .report-item, .message-item').forEach(item => {
            item.addEventListener('mouseenter', function() {
                this.style.backgroundColor = '#e3f2fd';
                this.style.transform = 'translateX(10px)';
                this.style.borderLeft = '4px solid #2196f3';
            });

            item.addEventListener('mouseleave', function() {
                this.style.backgroundColor = '';
                this.style.transform = 'translateX(5px)';
                this.style.borderLeft = '';
            });
        });

        // تحديث تلقائي للإحصائيات كل 5 دقائق
        function refreshStats() {
            fetch('api/get_stats.php')
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        // تحديث الأرقام في البطاقات
                        document.querySelectorAll('.stats-card .h5').forEach((element, index) => {
                            const newValue = Object.values(data.stats)[index];
                            if (newValue !== undefined) {
                                element.textContent = newValue.toLocaleString('ar-EG');
                            }
                        });

                        // تحديث الشارات في الشريط الجانبي
                        document.querySelectorAll('.nav-link .badge').forEach((badge, index) => {
                            const newValue = Object.values(data.stats)[index];
                            if (newValue !== undefined) {
                                badge.textContent = newValue;
                            }
                        });
                    }
                })
                .catch(error => console.log('تعذر تحديث الإحصائيات:', error));
        }

        // تحديث الإحصائيات كل 5 دقائق
        setInterval(refreshStats, 300000);

        // إضافة تأثير التحميل للروابط
        document.querySelectorAll('a[href]').forEach(link => {
            link.addEventListener('click', function(e) {
                if (!this.href.includes('#') && !this.target) {
                    const icon = this.querySelector('i');
                    if (icon) {
                        icon.className = 'fas fa-spinner fa-spin me-2';
                    }
                }
            });
        });

        // إضافة إشعارات للمطالب المعلقة
        <?php if ($stats['pending_requests'] > 0): ?>
        setTimeout(() => {
            const toast = document.createElement('div');
            toast.className = 'toast position-fixed top-0 end-0 m-3';
            toast.setAttribute('role', 'alert');
            toast.innerHTML = `
                <div class="toast-header bg-warning">
                    <i class="fas fa-exclamation-triangle me-2"></i>
                    <strong class="me-auto">تنبيه</strong>
                    <button type="button" class="btn-close" data-bs-dismiss="toast"></button>
                </div>
                <div class="toast-body">
                    لديك <?php echo $stats['pending_requests']; ?> مطلب معلق يحتاج للمراجعة
                </div>
            `;
            document.body.appendChild(toast);
            const bsToast = new bootstrap.Toast(toast);
            bsToast.show();
        }, 3000);
        <?php endif; ?>

        // إضافة اختصارات لوحة المفاتيح
        document.addEventListener('keydown', function(e) {
            if (e.ctrlKey || e.metaKey) {
                switch(e.key) {
                    case '1':
                        e.preventDefault();
                        window.location.href = 'modules/admin/add_supporter.php';
                        break;
                    case '2':
                        e.preventDefault();
                        window.location.href = 'modules/supporters/supporters.php';
                        break;
                    case '3':
                        e.preventDefault();
                        window.location.href = 'modules/requests/requests.php';
                        break;
                    case '4':
                        e.preventDefault();
                        window.location.href = 'modules/messages/messages.php';
                        break;
                }
            }
        });

        // عرض اختصارات لوحة المفاتيح
        const helpTooltip = `
            اختصارات لوحة المفاتيح:
            Ctrl+1: إضافة مؤيد
            Ctrl+2: إدارة المؤيدين
            Ctrl+3: إدارة المطالب
            Ctrl+4: الرسائل
        `;

        // إضافة زر المساعدة
        const helpBtn = document.createElement('button');
        helpBtn.className = 'btn btn-info btn-sm position-fixed bottom-0 end-0 m-3';
        helpBtn.innerHTML = '<i class="fas fa-question-circle"></i>';
        helpBtn.title = helpTooltip;
        helpBtn.onclick = () => alert(helpTooltip);
        document.body.appendChild(helpBtn);

        console.log('🎉 لوحة تحكم المرشح جاهزة!');
        console.log('📊 الإحصائيات:', <?php echo json_encode($stats); ?>);
    </script>
</body>
</html>
