<?php
// صفحة الرسائل للإداريين
session_start();
header('Content-Type: text/html; charset=utf-8');
ini_set('default_charset', 'utf-8');
mb_internal_encoding('UTF-8');

require_once '../../config/config.php';
require_once '../../config/database.php';

// التحقق من تسجيل الدخول
if (!isset($_SESSION['admin_id'])) {
    redirect('login.php');
}

$admin_id = $_SESSION['admin_id'];
$admin_name = $_SESSION['admin_name'];

// معالجة إرسال رسالة جديدة
if ($_SERVER['REQUEST_METHOD'] == 'POST' && isset($_POST['action']) && $_POST['action'] == 'send') {
    $subject = sanitize($_POST['subject']);
    $message = sanitize($_POST['message']);
    $priority = sanitize($_POST['priority']);

    if (!empty($subject) && !empty($message)) {
        $sql = "INSERT INTO messages (sender_type, sender_id, receiver_type, receiver_id, subject, message, priority, created_at)
                VALUES ('admin', ?, 'management', 1, ?, ?, ?, NOW())";

        $result = executeQuery($sql, [$admin_id, $subject, $message, $priority]);

        if ($result) {
            $message_id = $pdo->lastInsertId();

            // معالجة رفع المرفق إذا وجد
            if (isset($_FILES['attachment']) && $_FILES['attachment']['error'] == 0) {
                $file = $_FILES['attachment'];

                // التحقق من نوع الملف
                $allowed_types = ['image/jpeg', 'image/jpg', 'image/png', 'image/gif', 'application/pdf', 'application/msword', 'application/vnd.openxmlformats-officedocument.wordprocessingml.document', 'application/vnd.ms-excel', 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'];

                if (in_array($file['type'], $allowed_types) && $file['size'] <= 10 * 1024 * 1024) { // 10MB
                    $upload_dir = "../../uploads/messages/";

                    // إنشاء المجلد إذا لم يكن موجوداً
                    if (!file_exists($upload_dir)) {
                        mkdir($upload_dir, 0755, true);
                    }

                    // إنشاء اسم ملف فريد
                    $file_extension = pathinfo($file['name'], PATHINFO_EXTENSION);
                    $file_name = $message_id . "_" . time() . "." . $file_extension;
                    $file_path = $upload_dir . $file_name;

                    // رفع الملف
                    if (move_uploaded_file($file['tmp_name'], $file_path)) {
                        // حفظ في قاعدة البيانات
                        $attachment_sql = "INSERT INTO message_attachments (message_id, file_name, file_path, file_size, file_type)
                                         VALUES (?, ?, ?, ?, ?)";

                        executeQuery($attachment_sql, [
                            $message_id, $file['name'], $file_path,
                            $file['size'], $file['type']
                        ]);
                    }
                }
            }

            showMessage('تم إرسال الرسالة بنجاح', 'success');
        } else {
            showMessage('حدث خطأ أثناء إرسال الرسالة', 'error');
        }
    } else {
        showMessage('يرجى ملء جميع الحقول المطلوبة', 'error');
    }
}

// معالجة تحديد الرسالة كمقروءة
if (isset($_GET['read']) && is_numeric($_GET['read'])) {
    $message_id = (int)$_GET['read'];
    executeQuery("UPDATE messages SET status = 'read', read_at = NOW() WHERE id = ? AND receiver_type = 'admin' AND receiver_id = ?",
                [$message_id, $admin_id]);
}

// جلب الرسائل مع المرفقات
$messages = fetchAll("SELECT m.*,
                      (SELECT COUNT(*) FROM message_attachments WHERE message_id = m.id) as attachment_count
                      FROM messages m
                      WHERE receiver_type = 'admin' AND receiver_id = ?
                      ORDER BY created_at DESC", [$admin_id]);

$sent_messages = fetchAll("SELECT m.*,
                          (SELECT COUNT(*) FROM message_attachments WHERE message_id = m.id) as attachment_count
                          FROM messages m
                          WHERE sender_type = 'admin' AND sender_id = ?
                          ORDER BY created_at DESC", [$admin_id]);
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>الرسائل - لوحة تحكم الإداريين</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: #f8f9fa;
        }
        .main-content {
            margin-top: 80px;
        }
        .message-card {
            border-radius: 10px;
            border: none;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 15px;
            transition: transform 0.2s;
        }
        .message-card:hover {
            transform: translateY(-2px);
        }
        .unread {
            border-left: 4px solid #007bff;
            background: #f8f9ff;
        }
        .priority-high {
            border-left: 4px solid #dc3545;
        }
        .priority-urgent {
            border-left: 4px solid #fd7e14;
        }
    </style>
</head>
<body>
    <!-- شريط التنقل العلوي -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-dark fixed-top">
        <div class="container-fluid">
            <a class="navbar-brand" href="dashboard.php">
                <i class="fas fa-user-shield me-2"></i>
                لوحة تحكم الإداريين
            </a>

            <div class="navbar-nav ms-auto">
                <div class="nav-item dropdown">
                    <a class="nav-link dropdown-toggle" href="#" id="userDropdown" role="button" data-bs-toggle="dropdown">
                        <i class="fas fa-user-circle me-1"></i>
                        <?php echo htmlspecialchars($admin_name); ?>
                    </a>
                    <ul class="dropdown-menu dropdown-menu-end">
                        <li><a class="dropdown-item" href="dashboard.php"><i class="fas fa-tachometer-alt me-2"></i>لوحة التحكم</a></li>
                        <li><a class="dropdown-item" href="logout.php"><i class="fas fa-sign-out-alt me-2"></i>تسجيل الخروج</a></li>
                    </ul>
                </div>
            </div>
        </div>
    </nav>

    <!-- المحتوى الرئيسي -->
    <div class="container-fluid main-content">
        <div class="row">
            <div class="col-md-12">
                <div class="d-flex justify-content-between align-items-center mb-4">
                    <h2><i class="fas fa-envelope me-2"></i>الرسائل</h2>
                    <div>
                        <button type="button" class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#composeModal">
                            <i class="fas fa-pen me-2"></i>رسالة جديدة
                        </button>
                        <a href="dashboard.php" class="btn btn-secondary">
                            <i class="fas fa-arrow-left me-2"></i>العودة
                        </a>
                    </div>
                </div>

                <?php displayMessage(); ?>

                <!-- تبويبات -->
                <ul class="nav nav-tabs" id="messagesTabs" role="tablist">
                    <li class="nav-item" role="presentation">
                        <button class="nav-link active" id="inbox-tab" data-bs-toggle="tab" data-bs-target="#inbox" type="button" role="tab">
                            <i class="fas fa-inbox me-2"></i>الواردة (<?php echo count($messages); ?>)
                        </button>
                    </li>
                    <li class="nav-item" role="presentation">
                        <button class="nav-link" id="sent-tab" data-bs-toggle="tab" data-bs-target="#sent" type="button" role="tab">
                            <i class="fas fa-paper-plane me-2"></i>المرسلة (<?php echo count($sent_messages); ?>)
                        </button>
                    </li>
                </ul>

                <div class="tab-content" id="messagesTabContent">
                    <!-- الرسائل الواردة -->
                    <div class="tab-pane fade show active" id="inbox" role="tabpanel">
                        <div class="mt-3">
                            <?php if (empty($messages)): ?>
                                <div class="alert alert-info text-center">
                                    <i class="fas fa-inbox fa-3x mb-3"></i>
                                    <h5>لا توجد رسائل واردة</h5>
                                    <p>لم تتلق أي رسائل من الإدارة بعد</p>
                                </div>
                            <?php else: ?>
                                <?php foreach ($messages as $message): ?>
                                <div class="card message-card <?php echo $message['status'] == 'unread' ? 'unread' : ''; ?> <?php echo $message['priority'] == 'high' ? 'priority-high' : ($message['priority'] == 'urgent' ? 'priority-urgent' : ''); ?>">
                                    <div class="card-body">
                                        <div class="d-flex justify-content-between align-items-start">
                                            <div class="flex-grow-1">
                                                <h6 class="card-title">
                                                    <?php if ($message['status'] == 'unread'): ?>
                                                    <span class="badge bg-primary me-2">جديد</span>
                                                    <?php endif; ?>
                                                    <?php echo htmlspecialchars($message['subject']); ?>
                                                    <?php if ($message['priority'] == 'high'): ?>
                                                    <span class="badge bg-danger ms-2">عالي</span>
                                                    <?php elseif ($message['priority'] == 'urgent'): ?>
                                                    <span class="badge bg-warning ms-2">عاجل</span>
                                                    <?php endif; ?>
                                                </h6>
                                                <p class="card-text"><?php echo nl2br(htmlspecialchars($message['message'])); ?></p>

                                                <?php if ($message['attachment_count'] > 0): ?>
                                                <div class="mt-2">
                                                    <small class="text-primary">
                                                        <i class="fas fa-paperclip me-1"></i>
                                                        <?php echo $message['attachment_count']; ?> مرفق
                                                    </small>
                                                </div>
                                                <?php endif; ?>

                                                <small class="text-muted">
                                                    <i class="fas fa-clock me-1"></i>
                                                    <?php echo date('Y-m-d H:i', strtotime($message['created_at'])); ?>
                                                </small>
                                            </div>
                                            <?php if ($message['status'] == 'unread'): ?>
                                            <a href="?read=<?php echo $message['id']; ?>" class="btn btn-outline-primary btn-sm">
                                                <i class="fas fa-eye"></i> تحديد كمقروء
                                            </a>
                                            <?php endif; ?>
                                        </div>
                                    </div>
                                </div>
                                <?php endforeach; ?>
                            <?php endif; ?>
                        </div>
                    </div>

                    <!-- الرسائل المرسلة -->
                    <div class="tab-pane fade" id="sent" role="tabpanel">
                        <div class="mt-3">
                            <?php if (empty($sent_messages)): ?>
                                <div class="alert alert-info text-center">
                                    <i class="fas fa-paper-plane fa-3x mb-3"></i>
                                    <h5>لا توجد رسائل مرسلة</h5>
                                    <p>لم ترسل أي رسائل للإدارة بعد</p>
                                </div>
                            <?php else: ?>
                                <?php foreach ($sent_messages as $message): ?>
                                <div class="card message-card">
                                    <div class="card-body">
                                        <h6 class="card-title">
                                            <?php echo htmlspecialchars($message['subject']); ?>
                                            <?php if ($message['priority'] == 'high'): ?>
                                            <span class="badge bg-danger ms-2">عالي</span>
                                            <?php elseif ($message['priority'] == 'urgent'): ?>
                                            <span class="badge bg-warning ms-2">عاجل</span>
                                            <?php endif; ?>
                                        </h6>
                                        <p class="card-text"><?php echo nl2br(htmlspecialchars($message['message'])); ?></p>

                                        <?php if ($message['attachment_count'] > 0): ?>
                                        <div class="mt-2">
                                            <small class="text-primary">
                                                <i class="fas fa-paperclip me-1"></i>
                                                <?php echo $message['attachment_count']; ?> مرفق
                                            </small>
                                        </div>
                                        <?php endif; ?>

                                        <small class="text-muted">
                                            <i class="fas fa-clock me-1"></i>
                                            <?php echo date('Y-m-d H:i', strtotime($message['created_at'])); ?>
                                        </small>
                                    </div>
                                </div>
                                <?php endforeach; ?>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- نافذة إنشاء رسالة -->
    <div class="modal fade" id="composeModal" tabindex="-1">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">إرسال رسالة للإدارة</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <form method="POST" action="" enctype="multipart/form-data">
                    <input type="hidden" name="action" value="send">
                    <div class="modal-body">
                        <div class="mb-3">
                            <label for="subject" class="form-label">الموضوع *</label>
                            <input type="text" class="form-control" id="subject" name="subject" required>
                        </div>

                        <div class="mb-3">
                            <label for="priority" class="form-label">الأولوية</label>
                            <select class="form-select" id="priority" name="priority">
                                <option value="normal">عادي</option>
                                <option value="high">عالي</option>
                                <option value="urgent">عاجل</option>
                            </select>
                        </div>

                        <div class="mb-3">
                            <label for="message" class="form-label">الرسالة *</label>
                            <textarea class="form-control" id="message" name="message" rows="6" required></textarea>
                        </div>

                        <div class="mb-3">
                            <label for="attachment" class="form-label">مرفق (اختياري)</label>
                            <input type="file" class="form-control" id="attachment" name="attachment"
                                   accept="image/*,application/pdf,.doc,.docx,.xls,.xlsx">
                            <div class="form-text">
                                الملفات المدعومة: الصور، PDF، Word، Excel - الحد الأقصى 10MB
                            </div>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-paper-plane me-2"></i>إرسال
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
