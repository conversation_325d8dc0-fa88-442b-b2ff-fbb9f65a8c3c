/* تصميم صفحة المناطق */

/* بطاقات المناطق */
.region-card {
    transition: all 0.3s ease;
    border: none;
    border-radius: 15px;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    overflow: hidden;
    cursor: pointer;
}

.region-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

.region-card .card-header {
    background: linear-gradient(135deg, #007bff, #0056b3);
    border: none;
    padding: 1rem;
}

.region-card .card-title {
    font-size: 1.1rem;
    font-weight: 600;
    margin: 0;
}

.region-card .card-body {
    padding: 1.5rem;
}

.region-card .card-footer {
    border: none;
    padding: 1rem;
}

/* إحصائيات المناطق */
.stat-item {
    padding: 0.5rem;
    border-radius: 8px;
    transition: all 0.3s ease;
}

.stat-item:hover {
    background-color: #f8f9fa;
    transform: scale(1.05);
}

.stat-item h4 {
    font-size: 1.5rem;
    font-weight: bold;
    margin-bottom: 0.25rem;
}

.stat-item small {
    font-size: 0.75rem;
    font-weight: 500;
}

/* شريط التقدم للنسب */
.progress {
    height: 8px;
    border-radius: 10px;
    background-color: #e9ecef;
}

.progress-bar {
    border-radius: 10px;
    transition: width 0.6s ease;
}

.bg-pink {
    background-color: #e91e63 !important;
}

.text-pink {
    color: #e91e63 !important;
}

/* تدرج الألوان للرؤوس */
.bg-gradient-primary {
    background: linear-gradient(135deg, #007bff 0%, #0056b3 100%);
}

.bg-gradient-success {
    background: linear-gradient(135deg, #28a745 0%, #1e7e34 100%);
}

.bg-gradient-info {
    background: linear-gradient(135deg, #17a2b8 0%, #117a8b 100%);
}

.bg-gradient-warning {
    background: linear-gradient(135deg, #ffc107 0%, #e0a800 100%);
}

/* جدول المناطق */
#regionsTable {
    font-size: 0.9rem;
}

#regionsTable th {
    background-color: #343a40;
    color: white;
    font-weight: 600;
    border: none;
    padding: 12px 8px;
    text-align: center;
    vertical-align: middle;
}

#regionsTable td {
    padding: 10px 8px;
    vertical-align: middle;
    text-align: center;
    border-bottom: 1px solid #dee2e6;
}

#regionsTable tbody tr {
    transition: all 0.3s ease;
}

#regionsTable tbody tr:hover {
    background-color: #f8f9fa;
    transform: scale(1.01);
}

/* أزرار الإجراءات */
.btn-group .btn {
    margin: 0 1px;
    border-radius: 4px;
}

.btn-sm {
    padding: 0.25rem 0.5rem;
    font-size: 0.875rem;
}

/* بطاقات الإحصائيات */
.card {
    transition: all 0.3s ease;
    border: none;
    border-radius: 10px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
}

.card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 15px rgba(0,0,0,0.15);
}

/* النوافذ المنبثقة */
.modal-content {
    border-radius: 15px;
    border: none;
    box-shadow: 0 10px 30px rgba(0,0,0,0.3);
}

.modal-header {
    background: linear-gradient(135deg, #007bff, #0056b3);
    color: white;
    border-radius: 15px 15px 0 0;
    border: none;
}

.modal-footer {
    border: none;
    border-radius: 0 0 15px 15px;
}

/* نموذج إضافة/تعديل المنطقة */
.region-form .form-label {
    font-weight: 600;
    color: #495057;
    margin-bottom: 0.5rem;
}

.region-form .form-control,
.region-form .form-select {
    margin-bottom: 1rem;
    border-radius: 8px;
    border: 2px solid #e9ecef;
    transition: all 0.3s ease;
}

.region-form .form-control:focus,
.region-form .form-select:focus {
    border-color: #007bff;
    box-shadow: 0 0 0 0.2rem rgba(0,123,255,0.25);
    transform: translateY(-1px);
}

.region-form .required {
    color: #dc3545;
}

/* تأثيرات التحميل */
.loading {
    position: relative;
    overflow: hidden;
}

.loading::after {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.4), transparent);
    animation: loading 1.5s infinite;
}

@keyframes loading {
    0% {
        left: -100%;
    }
    100% {
        left: 100%;
    }
}

/* تحسينات للجوال */
@media (max-width: 768px) {
    .region-card {
        margin-bottom: 1rem;
    }
    
    .stat-item h4 {
        font-size: 1.2rem;
    }
    
    .stat-item small {
        font-size: 0.7rem;
    }
    
    #regionsTable {
        font-size: 0.8rem;
    }
    
    #regionsTable th,
    #regionsTable td {
        padding: 6px 4px;
    }
    
    .btn-group .btn {
        padding: 0.2rem 0.4rem;
        font-size: 0.75rem;
    }
    
    .card-body {
        padding: 1rem;
    }
}

/* تحسينات إضافية */
.table-responsive {
    border-radius: 10px;
    overflow: hidden;
}

.dataTables_wrapper .dataTables_length,
.dataTables_wrapper .dataTables_filter,
.dataTables_wrapper .dataTables_info,
.dataTables_wrapper .dataTables_paginate {
    margin-bottom: 1rem;
}

.dataTables_wrapper .dataTables_filter input {
    border-radius: 20px;
    border: 2px solid #e9ecef;
    padding: 8px 15px;
    margin-left: 10px;
}

.dataTables_wrapper .dataTables_length select {
    border-radius: 8px;
    border: 2px solid #e9ecef;
    padding: 5px 10px;
    margin: 0 10px;
}

/* أزرار التصدير */
.btn-toolbar .btn-group .btn {
    margin-left: 5px;
    border-radius: 8px;
    font-weight: 600;
    transition: all 0.3s ease;
}

.btn-toolbar .btn-group .btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0,0,0,0.2);
}

/* مؤشر التحميل */
.loading-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(255,255,255,0.8);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 9999;
}

.loading-spinner {
    width: 50px;
    height: 50px;
    border: 5px solid #f3f3f3;
    border-top: 5px solid #007bff;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* تحسين الألوان */
.badge.bg-pink {
    background-color: #e91e63 !important;
}

.text-pink {
    color: #e91e63 !important;
}

/* تحسين الحدود */
.border-primary {
    border-color: #007bff !important;
}

.border-success {
    border-color: #28a745 !important;
}

.border-warning {
    border-color: #ffc107 !important;
}

.border-danger {
    border-color: #dc3545 !important;
}

/* تحسين الظلال */
.shadow-sm {
    box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075) !important;
}

.shadow {
    box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15) !important;
}

.shadow-lg {
    box-shadow: 0 1rem 3rem rgba(0, 0, 0, 0.175) !important;
}

/* تحسين الانتقالات */
.fade-in {
    animation: fadeIn 0.5s ease-in;
}

@keyframes fadeIn {
    from { opacity: 0; transform: translateY(20px); }
    to { opacity: 1; transform: translateY(0); }
}

.slide-in {
    animation: slideIn 0.5s ease-out;
}

@keyframes slideIn {
    from { transform: translateX(-100%); }
    to { transform: translateX(0); }
}

/* تحسين التمرير */
.smooth-scroll {
    scroll-behavior: smooth;
}

/* تحسين التركيز */
.form-control:focus,
.form-select:focus,
.btn:focus {
    outline: none;
    box-shadow: 0 0 0 0.2rem rgba(0,123,255,0.25);
}

/* تحسين النصوص */
.text-truncate {
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

.text-wrap {
    word-wrap: break-word;
    word-break: break-word;
}

/* تأثيرات خاصة للبطاقات */
.region-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg, #007bff, #28a745, #ffc107, #dc3545);
    background-size: 400% 100%;
    animation: gradient 3s ease infinite;
    z-index: 1;
}

@keyframes gradient {
    0% { background-position: 0% 50%; }
    50% { background-position: 100% 50%; }
    100% { background-position: 0% 50%; }
}
