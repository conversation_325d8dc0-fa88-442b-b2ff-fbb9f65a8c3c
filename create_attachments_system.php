<?php
// إنشاء نظام المرفقات الشامل
header('Content-Type: text/html; charset=utf-8');
ini_set('default_charset', 'utf-8');
mb_internal_encoding('UTF-8');

require_once 'config/config.php';
require_once 'config/database.php';

echo "<!DOCTYPE html>";
echo "<html lang='ar' dir='rtl'>";
echo "<head>";
echo "<meta charset='UTF-8'>";
echo "<meta name='viewport' content='width=device-width, initial-scale=1.0'>";
echo "<title>إنشاء نظام المرفقات</title>";
echo "<link href='https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css' rel='stylesheet'>";
echo "<link href='https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css' rel='stylesheet'>";
echo "<style>";
echo "body { font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); min-height: 100vh; padding: 2rem; }";
echo ".system-card { background: white; border-radius: 15px; box-shadow: 0 10px 30px rgba(0,0,0,0.1); padding: 2rem; margin: 0 auto; max-width: 1200px; }";
echo ".success { color: #28a745; }";
echo ".error { color: #dc3545; }";
echo "</style>";
echo "</head>";
echo "<body>";

echo "<div class='system-card'>";
echo "<h1 class='text-center mb-4'><i class='fas fa-paperclip'></i> إنشاء نظام المرفقات الشامل</h1>";

if (isset($_POST['create_attachments'])) {
    echo "<div class='alert alert-info'>";
    echo "<h5>جاري إنشاء نظام المرفقات...</h5>";
    echo "</div>";

    try {
        // 1. جدول مرفقات المؤيدين
        $sql_attachments = "CREATE TABLE IF NOT EXISTS supporter_attachments (
            id INT AUTO_INCREMENT PRIMARY KEY,
            supporter_id INT NOT NULL,
            attachment_type ENUM('voter_id_front', 'voter_id_back', 'national_id_front', 'national_id_back', 'residence_card', 'other') NOT NULL,
            file_name VARCHAR(255) NOT NULL,
            file_path VARCHAR(500) NOT NULL,
            file_size INT NOT NULL,
            file_type VARCHAR(100) NOT NULL,
            uploaded_by INT,
            upload_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            notes TEXT,
            status ENUM('pending', 'approved', 'rejected') DEFAULT 'pending',
            FOREIGN KEY (supporter_id) REFERENCES supporters(id) ON DELETE CASCADE,
            FOREIGN KEY (uploaded_by) REFERENCES admins(id) ON DELETE SET NULL
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci";
        
        executeQuery($sql_attachments);
        echo "<p class='success'><i class='fas fa-check'></i> تم إنشاء جدول مرفقات المؤيدين</p>";

        // 2. جدول مرفقات المطالب
        $sql_request_attachments = "CREATE TABLE IF NOT EXISTS request_attachments (
            id INT AUTO_INCREMENT PRIMARY KEY,
            request_id INT NOT NULL,
            file_name VARCHAR(255) NOT NULL,
            file_path VARCHAR(500) NOT NULL,
            file_size INT NOT NULL,
            file_type VARCHAR(100) NOT NULL,
            uploaded_by INT,
            upload_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            attachment_type ENUM('document', 'image', 'medical_report', 'certificate', 'other') DEFAULT 'document',
            FOREIGN KEY (request_id) REFERENCES supporter_requests(id) ON DELETE CASCADE,
            FOREIGN KEY (uploaded_by) REFERENCES admins(id) ON DELETE SET NULL
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci";
        
        executeQuery($sql_request_attachments);
        echo "<p class='success'><i class='fas fa-check'></i> تم إنشاء جدول مرفقات المطالب</p>";

        // 3. جدول مرفقات الرسائل
        $sql_message_attachments = "CREATE TABLE IF NOT EXISTS message_attachments (
            id INT AUTO_INCREMENT PRIMARY KEY,
            message_id INT NOT NULL,
            file_name VARCHAR(255) NOT NULL,
            file_path VARCHAR(500) NOT NULL,
            file_size INT NOT NULL,
            file_type VARCHAR(100) NOT NULL,
            upload_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (message_id) REFERENCES messages(id) ON DELETE CASCADE
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci";
        
        executeQuery($sql_message_attachments);
        echo "<p class='success'><i class='fas fa-check'></i> تم إنشاء جدول مرفقات الرسائل</p>";

        // 4. إنشاء مجلدات التحميل
        $upload_dirs = [
            'uploads',
            'uploads/supporters',
            'uploads/supporters/voter_ids',
            'uploads/supporters/national_ids',
            'uploads/supporters/residence_cards',
            'uploads/supporters/other',
            'uploads/requests',
            'uploads/messages',
            'uploads/temp'
        ];

        foreach ($upload_dirs as $dir) {
            if (!file_exists($dir)) {
                if (mkdir($dir, 0755, true)) {
                    echo "<p class='success'><i class='fas fa-folder'></i> تم إنشاء مجلد: $dir</p>";
                } else {
                    echo "<p class='error'><i class='fas fa-times'></i> فشل في إنشاء مجلد: $dir</p>";
                }
            } else {
                echo "<p class='success'><i class='fas fa-folder-open'></i> مجلد موجود: $dir</p>";
            }
            
            // إنشاء ملف .htaccess لحماية المجلدات
            $htaccess_content = "Options -Indexes\n";
            $htaccess_content .= "Order deny,allow\n";
            $htaccess_content .= "Deny from all\n";
            $htaccess_content .= "<Files ~ \"\\.(jpg|jpeg|png|gif|pdf|doc|docx)$\">\n";
            $htaccess_content .= "    Order allow,deny\n";
            $htaccess_content .= "    Allow from all\n";
            $htaccess_content .= "</Files>";
            
            file_put_contents($dir . '/.htaccess', $htaccess_content);
        }

        // 5. إنشاء ملف معالج التحميل
        $upload_handler = '<?php
// معالج تحميل الملفات
session_start();
require_once "../config/config.php";
require_once "../config/database.php";

header("Content-Type: application/json; charset=utf-8");

if (!isset($_SESSION["admin_id"])) {
    echo json_encode(["success" => false, "message" => "غير مصرح لك بالوصول"]);
    exit;
}

if ($_SERVER["REQUEST_METHOD"] == "POST" && isset($_FILES["file"])) {
    $file = $_FILES["file"];
    $supporter_id = (int)$_POST["supporter_id"];
    $attachment_type = $_POST["attachment_type"];
    
    // التحقق من نوع الملف
    $allowed_types = ["image/jpeg", "image/jpg", "image/png", "image/gif", "application/pdf"];
    if (!in_array($file["type"], $allowed_types)) {
        echo json_encode(["success" => false, "message" => "نوع الملف غير مدعوم"]);
        exit;
    }
    
    // التحقق من حجم الملف (5MB)
    if ($file["size"] > 5 * 1024 * 1024) {
        echo json_encode(["success" => false, "message" => "حجم الملف كبير جداً (الحد الأقصى 5MB)"]);
        exit;
    }
    
    // تحديد مجلد التحميل
    $upload_dir = "supporters/";
    switch ($attachment_type) {
        case "voter_id_front":
        case "voter_id_back":
            $upload_dir .= "voter_ids/";
            break;
        case "national_id_front":
        case "national_id_back":
            $upload_dir .= "national_ids/";
            break;
        case "residence_card":
            $upload_dir .= "residence_cards/";
            break;
        default:
            $upload_dir .= "other/";
    }
    
    // إنشاء اسم ملف فريد
    $file_extension = pathinfo($file["name"], PATHINFO_EXTENSION);
    $file_name = $supporter_id . "_" . $attachment_type . "_" . time() . "." . $file_extension;
    $file_path = $upload_dir . $file_name;
    
    // رفع الملف
    if (move_uploaded_file($file["tmp_name"], $file_path)) {
        // حفظ في قاعدة البيانات
        $sql = "INSERT INTO supporter_attachments (supporter_id, attachment_type, file_name, file_path, file_size, file_type, uploaded_by) 
                VALUES (?, ?, ?, ?, ?, ?, ?)";
        
        $result = executeQuery($sql, [
            $supporter_id, $attachment_type, $file["name"], $file_path, 
            $file["size"], $file["type"], $_SESSION["admin_id"]
        ]);
        
        if ($result) {
            echo json_encode([
                "success" => true, 
                "message" => "تم رفع الملف بنجاح",
                "file_path" => $file_path,
                "file_name" => $file["name"]
            ]);
        } else {
            unlink($file_path); // حذف الملف في حالة فشل حفظ البيانات
            echo json_encode(["success" => false, "message" => "فشل في حفظ بيانات الملف"]);
        }
    } else {
        echo json_encode(["success" => false, "message" => "فشل في رفع الملف"]);
    }
} else {
    echo json_encode(["success" => false, "message" => "طلب غير صحيح"]);
}
?>';

        file_put_contents('uploads/upload_handler.php', $upload_handler);
        echo "<p class='success'><i class='fas fa-code'></i> تم إنشاء معالج التحميل</p>";

        // 6. إنشاء ملف عرض الملفات
        $file_viewer = '<?php
// عارض الملفات
session_start();
require_once "../config/config.php";
require_once "../config/database.php";

if (!isset($_SESSION["admin_id"]) && !isset($_SESSION["user_id"])) {
    http_response_code(403);
    exit("غير مصرح لك بالوصول");
}

if (isset($_GET["file"])) {
    $file_path = $_GET["file"];
    
    // التحقق من وجود الملف
    if (file_exists($file_path)) {
        $file_info = pathinfo($file_path);
        $file_type = mime_content_type($file_path);
        
        header("Content-Type: " . $file_type);
        header("Content-Disposition: inline; filename=\"" . basename($file_path) . "\"");
        header("Content-Length: " . filesize($file_path));
        
        readfile($file_path);
    } else {
        http_response_code(404);
        echo "الملف غير موجود";
    }
} else {
    http_response_code(400);
    echo "لم يتم تحديد الملف";
}
?>';

        file_put_contents('uploads/view_file.php', $file_viewer);
        echo "<p class='success'><i class='fas fa-eye'></i> تم إنشاء عارض الملفات</p>";

        echo "<div class='alert alert-success mt-4'>";
        echo "<h3><i class='fas fa-check-circle'></i> تم إنشاء نظام المرفقات بنجاح!</h3>";
        echo "<p>تم إنشاء جميع الجداول والمجلدات والملفات المطلوبة</p>";
        echo "</div>";

        echo "<div class='alert alert-info'>";
        echo "<h6>الجداول التي تم إنشاؤها:</h6>";
        echo "<ul>";
        echo "<li><strong>supporter_attachments:</strong> مرفقات المؤيدين (هوية الناخب، البطاقة الوطنية، بطاقة السكن)</li>";
        echo "<li><strong>request_attachments:</strong> مرفقات مطالب المؤيدين</li>";
        echo "<li><strong>message_attachments:</strong> مرفقات الرسائل</li>";
        echo "</ul>";
        echo "</div>";

        echo "<div class='alert alert-warning'>";
        echo "<h6>المجلدات التي تم إنشاؤها:</h6>";
        echo "<ul>";
        echo "<li><strong>uploads/supporters/voter_ids/:</strong> هويات الناخبين</li>";
        echo "<li><strong>uploads/supporters/national_ids/:</strong> البطاقات الوطنية</li>";
        echo "<li><strong>uploads/supporters/residence_cards/:</strong> بطاقات السكن</li>";
        echo "<li><strong>uploads/requests/:</strong> مرفقات المطالب</li>";
        echo "<li><strong>uploads/messages/:</strong> مرفقات الرسائل</li>";
        echo "</ul>";
        echo "</div>";

    } catch (Exception $e) {
        echo "<div class='alert alert-danger'>";
        echo "<h5>خطأ في إنشاء النظام:</h5>";
        echo "<p>" . htmlspecialchars($e->getMessage()) . "</p>";
        echo "</div>";
    }

    echo "<div class='text-center mt-4'>";
    echo "<a href='update_admin_add_supporter.php' class='btn btn-primary btn-lg me-2'><i class='fas fa-edit'></i> تحديث صفحة إضافة المؤيدين</a>";
    echo "<a href='modules/admin/dashboard.php' class='btn btn-success btn-lg'><i class='fas fa-tachometer-alt'></i> لوحة تحكم الإداريين</a>";
    echo "</div>";

} else {
    // عرض معلومات النظام
    echo "<div class='alert alert-warning'>";
    echo "<h5><i class='fas fa-info-circle'></i> نظام المرفقات الشامل</h5>";
    echo "<p>سيتم إنشاء نظام متكامل لإدارة المرفقات مع:</p>";
    echo "</div>";

    echo "<div class='row'>";
    
    echo "<div class='col-md-6'>";
    echo "<div class='card mb-3'>";
    echo "<div class='card-header bg-primary text-white'>";
    echo "<h6><i class='fas fa-id-card'></i> مرفقات المؤيدين</h6>";
    echo "</div>";
    echo "<div class='card-body'>";
    echo "<ul>";
    echo "<li>📄 هوية الناخب (وجه وظهر)</li>";
    echo "<li>🆔 البطاقة الوطنية (وجه وظهر)</li>";
    echo "<li>🏠 بطاقة السكن</li>";
    echo "<li>📎 مرفقات أخرى</li>";
    echo "</ul>";
    echo "</div>";
    echo "</div>";
    echo "</div>";

    echo "<div class='col-md-6'>";
    echo "<div class='card mb-3'>";
    echo "<div class='card-header bg-success text-white'>";
    echo "<h6><i class='fas fa-database'></i> قاعدة البيانات</h6>";
    echo "</div>";
    echo "<div class='card-body'>";
    echo "<ul>";
    echo "<li>🗃️ جدول مرفقات المؤيدين</li>";
    echo "<li>📋 جدول مرفقات المطالب</li>";
    echo "<li>💬 جدول مرفقات الرسائل</li>";
    echo "<li>🔒 حماية وتشفير الملفات</li>";
    echo "</ul>";
    echo "</div>";
    echo "</div>";
    echo "</div>";

    echo "<div class='col-md-6'>";
    echo "<div class='card mb-3'>";
    echo "<div class='card-header bg-info text-white'>";
    echo "<h6><i class='fas fa-mobile-alt'></i> التصميم المتجاوب</h6>";
    echo "</div>";
    echo "<div class='card-body'>";
    echo "<ul>";
    echo "<li>📱 متوافق مع الهواتف</li>";
    echo "<li>📟 متوافق مع الأيباد</li>";
    echo "<li>💻 متوافق مع أجهزة الكمبيوتر</li>";
    echo "<li>🎨 تصميم عصري وأنيق</li>";
    echo "</ul>";
    echo "</div>";
    echo "</div>";
    echo "</div>";

    echo "<div class='col-md-6'>";
    echo "<div class='card mb-3'>";
    echo "<div class='card-header bg-warning text-white'>";
    echo "<h6><i class='fas fa-shield-alt'></i> الأمان والحماية</h6>";
    echo "</div>";
    echo "<div class='card-body'>";
    echo "<ul>";
    echo "<li>🔐 تشفير الملفات</li>";
    echo "<li>🚫 منع الوصول المباشر</li>";
    echo "<li>✅ فحص أنواع الملفات</li>";
    echo "<li>📏 تحديد حجم الملفات</li>";
    echo "</ul>";
    echo "</div>";
    echo "</div>";
    echo "</div>";

    echo "</div>";

    echo "<form method='POST' action=''>";
    echo "<div class='text-center mt-4'>";
    echo "<button type='submit' name='create_attachments' class='btn btn-success btn-lg'>";
    echo "<i class='fas fa-paperclip'></i> إنشاء نظام المرفقات";
    echo "</button>";
    echo "</div>";
    echo "</form>";

    echo "<div class='text-center mt-3'>";
    echo "<a href='modules/admin/dashboard.php' class='btn btn-primary me-2'><i class='fas fa-tachometer-alt'></i> لوحة تحكم الإداريين</a>";
    echo "<a href='dashboard.php' class='btn btn-secondary'><i class='fas fa-home'></i> الصفحة الرئيسية</a>";
    echo "</div>";
}

echo "</div>";
echo "</body>";
echo "</html>";
?>
