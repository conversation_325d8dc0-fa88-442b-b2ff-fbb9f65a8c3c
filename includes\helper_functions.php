<?php
// دوال مساعدة عامة

/**
 * تنظيف وتعقيم البيانات
 */
function sanitize($data) {
    if (is_array($data)) {
        return array_map('sanitize', $data);
    }
    return htmlspecialchars(trim($data), ENT_QUOTES, 'UTF-8');
}

/**
 * التحقق من صحة البريد الإلكتروني
 */
function validateEmail($email) {
    return filter_var($email, FILTER_VALIDATE_EMAIL) !== false;
}

/**
 * التحقق من صحة رقم الهاتف العراقي
 */
function validateIraqiPhone($phone) {
    // إزالة المسافات والرموز
    $phone = preg_replace('/[^0-9]/', '', $phone);
    
    // التحقق من الأنماط المختلفة للأرقام العراقية
    $patterns = [
        '/^07[3-9][0-9]{8}$/',  // أرقام الموبايل
        '/^01[0-9]{8}$/',       // أرقام بغداد
        '/^02[1-9][0-9]{7}$/',  // أرقام المحافظات
    ];
    
    foreach ($patterns as $pattern) {
        if (preg_match($pattern, $phone)) {
            return true;
        }
    }
    
    return false;
}

/**
 * تنسيق رقم الهاتف
 */
function formatPhone($phone) {
    $phone = preg_replace('/[^0-9]/', '', $phone);
    
    if (strlen($phone) === 11 && substr($phone, 0, 2) === '07') {
        return substr($phone, 0, 4) . '-' . substr($phone, 4, 3) . '-' . substr($phone, 7);
    }
    
    return $phone;
}

/**
 * تحويل التاريخ إلى تنسيق عربي
 */
function formatArabicDate($date, $format = 'full') {
    if (!$date) return '';
    
    $timestamp = is_string($date) ? strtotime($date) : $date;
    
    $arabic_months = [
        1 => 'يناير', 2 => 'فبراير', 3 => 'مارس', 4 => 'أبريل',
        5 => 'مايو', 6 => 'يونيو', 7 => 'يوليو', 8 => 'أغسطس',
        9 => 'سبتمبر', 10 => 'أكتوبر', 11 => 'نوفمبر', 12 => 'ديسمبر'
    ];
    
    $arabic_days = [
        'Sunday' => 'الأحد', 'Monday' => 'الاثنين', 'Tuesday' => 'الثلاثاء',
        'Wednesday' => 'الأربعاء', 'Thursday' => 'الخميس', 'Friday' => 'الجمعة',
        'Saturday' => 'السبت'
    ];
    
    $day = date('j', $timestamp);
    $month = $arabic_months[date('n', $timestamp)];
    $year = date('Y', $timestamp);
    $day_name = $arabic_days[date('l', $timestamp)];
    
    switch ($format) {
        case 'short':
            return "$day $month $year";
        case 'long':
            return "$day_name، $day $month $year";
        case 'full':
            $time = date('H:i', $timestamp);
            return "$day_name، $day $month $year - $time";
        default:
            return "$day $month $year";
    }
}

/**
 * حساب العمر من تاريخ الميلاد
 */
function calculateAge($birth_date) {
    if (!$birth_date) return null;
    
    $birth = new DateTime($birth_date);
    $today = new DateTime();
    $age = $today->diff($birth);
    
    return $age->y;
}

/**
 * تحويل الأرقام إلى أرقام عربية
 */
function toArabicNumbers($string) {
    $arabic_numbers = ['٠', '١', '٢', '٣', '٤', '٥', '٦', '٧', '٨', '٩'];
    $english_numbers = ['0', '1', '2', '3', '4', '5', '6', '7', '8', '9'];
    
    return str_replace($english_numbers, $arabic_numbers, $string);
}

/**
 * تحويل الأرقام العربية إلى إنجليزية
 */
function toEnglishNumbers($string) {
    $arabic_numbers = ['٠', '١', '٢', '٣', '٤', '٥', '٦', '٧', '٨', '٩'];
    $english_numbers = ['0', '1', '2', '3', '4', '5', '6', '7', '8', '9'];
    
    return str_replace($arabic_numbers, $english_numbers, $string);
}

/**
 * تنسيق الأرقام بالفواصل
 */
function formatNumber($number, $decimals = 0) {
    return number_format($number, $decimals, '.', ',');
}

/**
 * تحويل حجم الملف إلى تنسيق قابل للقراءة
 */
function formatFileSize($bytes) {
    $units = ['بايت', 'كيلوبايت', 'ميجابايت', 'جيجابايت'];
    
    for ($i = 0; $bytes > 1024 && $i < count($units) - 1; $i++) {
        $bytes /= 1024;
    }
    
    return round($bytes, 2) . ' ' . $units[$i];
}

/**
 * إنشاء كلمة مرور عشوائية
 */
function generateRandomPassword($length = 8) {
    $characters = 'abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789!@#$%^&*';
    $password = '';
    
    for ($i = 0; $i < $length; $i++) {
        $password .= $characters[rand(0, strlen($characters) - 1)];
    }
    
    return $password;
}

/**
 * التحقق من قوة كلمة المرور
 */
function checkPasswordStrength($password) {
    $score = 0;
    $feedback = [];
    
    // الطول
    if (strlen($password) >= 8) {
        $score += 2;
    } else {
        $feedback[] = 'يجب أن تكون كلمة المرور 8 أحرف على الأقل';
    }
    
    // الأحرف الصغيرة
    if (preg_match('/[a-z]/', $password)) {
        $score += 1;
    } else {
        $feedback[] = 'يجب أن تحتوي على أحرف صغيرة';
    }
    
    // الأحرف الكبيرة
    if (preg_match('/[A-Z]/', $password)) {
        $score += 1;
    } else {
        $feedback[] = 'يجب أن تحتوي على أحرف كبيرة';
    }
    
    // الأرقام
    if (preg_match('/[0-9]/', $password)) {
        $score += 1;
    } else {
        $feedback[] = 'يجب أن تحتوي على أرقام';
    }
    
    // الرموز الخاصة
    if (preg_match('/[^a-zA-Z0-9]/', $password)) {
        $score += 1;
    } else {
        $feedback[] = 'يجب أن تحتوي على رموز خاصة';
    }
    
    $strength = match(true) {
        $score >= 5 => 'قوية جداً',
        $score >= 4 => 'قوية',
        $score >= 3 => 'متوسطة',
        $score >= 2 => 'ضعيفة',
        default => 'ضعيفة جداً'
    };
    
    return [
        'score' => $score,
        'strength' => $strength,
        'feedback' => $feedback
    ];
}

/**
 * إنشاء رمز تحقق عشوائي
 */
function generateVerificationCode($length = 6) {
    return str_pad(rand(0, pow(10, $length) - 1), $length, '0', STR_PAD_LEFT);
}

/**
 * تشفير البيانات الحساسة
 */
function encryptData($data, $key = null) {
    if (!$key) {
        $key = $_ENV['ENCRYPTION_KEY'] ?? 'default_key_change_this';
    }
    
    $cipher = 'AES-256-CBC';
    $iv = openssl_random_pseudo_bytes(openssl_cipher_iv_length($cipher));
    $encrypted = openssl_encrypt($data, $cipher, $key, 0, $iv);
    
    return base64_encode($iv . $encrypted);
}

/**
 * فك تشفير البيانات
 */
function decryptData($encrypted_data, $key = null) {
    if (!$key) {
        $key = $_ENV['ENCRYPTION_KEY'] ?? 'default_key_change_this';
    }
    
    $cipher = 'AES-256-CBC';
    $data = base64_decode($encrypted_data);
    $iv_length = openssl_cipher_iv_length($cipher);
    $iv = substr($data, 0, $iv_length);
    $encrypted = substr($data, $iv_length);
    
    return openssl_decrypt($encrypted, $cipher, $key, 0, $iv);
}

/**
 * إنشاء رابط آمن مع token
 */
function generateSecureLink($base_url, $params = [], $expiry_hours = 24) {
    $params['expires'] = time() + ($expiry_hours * 3600);
    $params['token'] = hash_hmac('sha256', serialize($params), $_ENV['APP_KEY'] ?? 'default_key');
    
    return $base_url . '?' . http_build_query($params);
}

/**
 * التحقق من صحة الرابط الآمن
 */
function validateSecureLink($params) {
    if (!isset($params['token']) || !isset($params['expires'])) {
        return false;
    }
    
    // التحقق من انتهاء الصلاحية
    if (time() > $params['expires']) {
        return false;
    }
    
    // التحقق من صحة التوقيع
    $token = $params['token'];
    unset($params['token']);
    
    $expected_token = hash_hmac('sha256', serialize($params), $_ENV['APP_KEY'] ?? 'default_key');
    
    return hash_equals($expected_token, $token);
}

/**
 * تحويل النص إلى slug صالح للروابط
 */
function createSlug($text) {
    // تحويل النص العربي إلى transliteration
    $arabic_to_latin = [
        'ا' => 'a', 'ب' => 'b', 'ت' => 't', 'ث' => 'th', 'ج' => 'j',
        'ح' => 'h', 'خ' => 'kh', 'د' => 'd', 'ذ' => 'th', 'ر' => 'r',
        'ز' => 'z', 'س' => 's', 'ش' => 'sh', 'ص' => 's', 'ض' => 'd',
        'ط' => 't', 'ظ' => 'z', 'ع' => 'a', 'غ' => 'gh', 'ف' => 'f',
        'ق' => 'q', 'ك' => 'k', 'ل' => 'l', 'م' => 'm', 'ن' => 'n',
        'ه' => 'h', 'و' => 'w', 'ي' => 'y', 'ة' => 'h', 'ى' => 'a'
    ];
    
    $text = str_replace(array_keys($arabic_to_latin), array_values($arabic_to_latin), $text);
    $text = strtolower($text);
    $text = preg_replace('/[^a-z0-9]+/', '-', $text);
    $text = trim($text, '-');
    
    return $text;
}

/**
 * إنشاء QR Code
 */
function generateQRCode($data, $size = 200) {
    $api_url = "https://api.qrserver.com/v1/create-qr-code/";
    $params = [
        'size' => $size . 'x' . $size,
        'data' => $data,
        'format' => 'png'
    ];
    
    return $api_url . '?' . http_build_query($params);
}

/**
 * إرسال إشعار بريد إلكتروني بسيط
 */
function sendEmail($to, $subject, $message, $from = null) {
    if (!$from) {
        $from = $_ENV['MAIL_FROM'] ?? '<EMAIL>';
    }
    
    $headers = [
        'From: ' . $from,
        'Reply-To: ' . $from,
        'Content-Type: text/html; charset=UTF-8',
        'MIME-Version: 1.0'
    ];
    
    return mail($to, $subject, $message, implode("\r\n", $headers));
}

/**
 * تسجيل الأخطاء
 */
function logError($message, $context = []) {
    $log_entry = [
        'timestamp' => date('Y-m-d H:i:s'),
        'message' => $message,
        'context' => $context,
        'ip' => $_SERVER['REMOTE_ADDR'] ?? 'unknown',
        'user_agent' => $_SERVER['HTTP_USER_AGENT'] ?? 'unknown'
    ];
    
    $log_file = __DIR__ . '/../logs/error_' . date('Y-m-d') . '.log';
    
    // إنشاء مجلد اللوجات إذا لم يكن موجوداً
    $log_dir = dirname($log_file);
    if (!file_exists($log_dir)) {
        mkdir($log_dir, 0755, true);
    }
    
    file_put_contents($log_file, json_encode($log_entry, JSON_UNESCAPED_UNICODE) . "\n", FILE_APPEND | LOCK_EX);
}

/**
 * تحديد نوع الملف من الامتداد
 */
function getFileTypeFromExtension($filename) {
    $extension = strtolower(pathinfo($filename, PATHINFO_EXTENSION));
    
    $types = [
        // صور
        'jpg' => 'image', 'jpeg' => 'image', 'png' => 'image', 'gif' => 'image',
        'bmp' => 'image', 'webp' => 'image', 'svg' => 'image',
        
        // مستندات
        'pdf' => 'document', 'doc' => 'document', 'docx' => 'document',
        'xls' => 'document', 'xlsx' => 'document', 'ppt' => 'document',
        'pptx' => 'document', 'txt' => 'document', 'rtf' => 'document',
        
        // أرشيف
        'zip' => 'archive', 'rar' => 'archive', '7z' => 'archive',
        'tar' => 'archive', 'gz' => 'archive',
        
        // فيديو
        'mp4' => 'video', 'avi' => 'video', 'mov' => 'video',
        'wmv' => 'video', 'flv' => 'video', 'webm' => 'video',
        
        // صوت
        'mp3' => 'audio', 'wav' => 'audio', 'ogg' => 'audio',
        'aac' => 'audio', 'flac' => 'audio'
    ];
    
    return $types[$extension] ?? 'other';
}

/**
 * التحقق من صحة رقم الهوية العراقية
 */
function validateIraqiNationalId($id) {
    // إزالة المسافات والرموز
    $id = preg_replace('/[^0-9]/', '', $id);
    
    // التحقق من الطول (12 رقم)
    if (strlen($id) !== 12) {
        return false;
    }
    
    // التحقق من السنة (أول رقمين)
    $year = substr($id, 0, 2);
    if ($year < 20 || $year > 99) { // من 1920 إلى 1999
        return false;
    }
    
    // التحقق من المحافظة (الرقمين 3-4)
    $province = substr($id, 2, 2);
    if ($province < 1 || $province > 18) {
        return false;
    }
    
    return true;
}
?>
