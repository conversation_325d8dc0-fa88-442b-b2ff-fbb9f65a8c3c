<?php
// إعداد قاعدة البيانات يدوياً

echo "<!DOCTYPE html>";
echo "<html lang='ar' dir='rtl'>";
echo "<head>";
echo "<meta charset='UTF-8'>";
echo "<meta name='viewport' content='width=device-width, initial-scale=1.0'>";
echo "<title>إعداد قاعدة البيانات</title>";
echo "<link href='https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css' rel='stylesheet'>";
echo "<link href='https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css' rel='stylesheet'>";
echo "<style>";
echo "body { font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); min-height: 100vh; padding: 2rem; }";
echo ".setup-card { background: white; border-radius: 15px; box-shadow: 0 10px 30px rgba(0,0,0,0.1); padding: 2rem; margin: 0 auto; max-width: 600px; }";
echo ".success { color: #28a745; }";
echo ".error { color: #dc3545; }";
echo "</style>";
echo "</head>";
echo "<body>";

echo "<div class='setup-card'>";
echo "<h1 class='text-center mb-4'><i class='fas fa-database'></i> إعداد قاعدة البيانات</h1>";

if ($_SERVER['REQUEST_METHOD'] == 'POST') {
    $host = $_POST['host'];
    $dbname = $_POST['dbname'];
    $username = $_POST['username'];
    $password = $_POST['password'];
    
    try {
        // اختبار الاتصال
        $dsn = "mysql:host=$host;charset=utf8mb4";
        $pdo = new PDO($dsn, $username, $password, [
            PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION
        ]);
        
        echo "<div class='alert alert-success'>";
        echo "<h5><i class='fas fa-check'></i> نجح الاتصال بالخادم!</h5>";
        echo "</div>";
        
        // إنشاء قاعدة البيانات إذا لم تكن موجودة
        try {
            $pdo->exec("CREATE DATABASE IF NOT EXISTS `$dbname` CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci");
            echo "<div class='alert alert-info'>";
            echo "<p><i class='fas fa-check'></i> تم إنشاء/التحقق من قاعدة البيانات: $dbname</p>";
            echo "</div>";
        } catch (Exception $e) {
            echo "<div class='alert alert-warning'>";
            echo "<p>تحذير: " . $e->getMessage() . "</p>";
            echo "</div>";
        }
        
        // الاتصال بقاعدة البيانات المحددة
        $dsn = "mysql:host=$host;dbname=$dbname;charset=utf8mb4";
        $pdo = new PDO($dsn, $username, $password, [
            PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
            PDO::MYSQL_ATTR_INIT_COMMAND => "SET NAMES utf8mb4 COLLATE utf8mb4_unicode_ci"
        ]);
        
        echo "<div class='alert alert-success'>";
        echo "<h5><i class='fas fa-check'></i> نجح الاتصال بقاعدة البيانات!</h5>";
        echo "</div>";
        
        // تحديث ملف الإعدادات
        $config_content = "<?php\n";
        $config_content .= "// إعدادات قاعدة البيانات\n";
        $config_content .= "define('DB_HOST', '$host');\n";
        $config_content .= "define('DB_NAME', '$dbname');\n";
        $config_content .= "define('DB_USER', '$username');\n";
        $config_content .= "define('DB_PASS', '$password');\n";
        $config_content .= "define('DB_CHARSET', 'utf8mb4');\n\n";
        
        $config_content .= "class Database {\n";
        $config_content .= "    private \$host = DB_HOST;\n";
        $config_content .= "    private \$db_name = DB_NAME;\n";
        $config_content .= "    private \$username = DB_USER;\n";
        $config_content .= "    private \$password = DB_PASS;\n";
        $config_content .= "    private \$charset = DB_CHARSET;\n";
        $config_content .= "    public \$conn;\n\n";
        
        $config_content .= "    public function getConnection() {\n";
        $config_content .= "        \$this->conn = null;\n";
        $config_content .= "        try {\n";
        $config_content .= "            \$dsn = \"mysql:host=\" . \$this->host . \";dbname=\" . \$this->db_name . \";charset=\" . \$this->charset;\n";
        $config_content .= "            \$this->conn = new PDO(\$dsn, \$this->username, \$this->password, array(\n";
        $config_content .= "                PDO::MYSQL_ATTR_INIT_COMMAND => \"SET NAMES utf8mb4 COLLATE utf8mb4_unicode_ci\",\n";
        $config_content .= "                PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,\n";
        $config_content .= "                PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,\n";
        $config_content .= "                PDO::ATTR_EMULATE_PREPARES => false\n";
        $config_content .= "            ));\n";
        $config_content .= "        } catch(PDOException \$exception) {\n";
        $config_content .= "            echo \"خطأ في الاتصال: \" . \$exception->getMessage();\n";
        $config_content .= "        }\n";
        $config_content .= "        return \$this->conn;\n";
        $config_content .= "    }\n";
        $config_content .= "}\n\n";
        
        $config_content .= "// إنشاء اتصال عام\n";
        $config_content .= "\$database = new Database();\n";
        $config_content .= "\$db = \$database->getConnection();\n\n";
        
        $config_content .= "// دوال مساعدة\n";
        $config_content .= "function executeQuery(\$sql, \$params = []) {\n";
        $config_content .= "    global \$db;\n";
        $config_content .= "    try {\n";
        $config_content .= "        \$stmt = \$db->prepare(\$sql);\n";
        $config_content .= "        return \$stmt->execute(\$params);\n";
        $config_content .= "    } catch(PDOException \$e) {\n";
        $config_content .= "        error_log(\"Database Error: \" . \$e->getMessage());\n";
        $config_content .= "        return false;\n";
        $config_content .= "    }\n";
        $config_content .= "}\n\n";
        
        $config_content .= "function fetchAll(\$sql, \$params = []) {\n";
        $config_content .= "    global \$db;\n";
        $config_content .= "    try {\n";
        $config_content .= "        \$stmt = \$db->prepare(\$sql);\n";
        $config_content .= "        \$stmt->execute(\$params);\n";
        $config_content .= "        return \$stmt->fetchAll();\n";
        $config_content .= "    } catch(PDOException \$e) {\n";
        $config_content .= "        error_log(\"Database Error: \" . \$e->getMessage());\n";
        $config_content .= "        return [];\n";
        $config_content .= "    }\n";
        $config_content .= "}\n\n";
        
        $config_content .= "function fetchOne(\$sql, \$params = []) {\n";
        $config_content .= "    global \$db;\n";
        $config_content .= "    try {\n";
        $config_content .= "        \$stmt = \$db->prepare(\$sql);\n";
        $config_content .= "        \$stmt->execute(\$params);\n";
        $config_content .= "        return \$stmt->fetch();\n";
        $config_content .= "    } catch(PDOException \$e) {\n";
        $config_content .= "        error_log(\"Database Error: \" . \$e->getMessage());\n";
        $config_content .= "        return false;\n";
        $config_content .= "    }\n";
        $config_content .= "}\n";
        $config_content .= "?>";
        
        file_put_contents('config/database.php', $config_content);
        
        echo "<div class='alert alert-success'>";
        echo "<h5><i class='fas fa-check'></i> تم تحديث ملف الإعدادات بنجاح!</h5>";
        echo "</div>";
        
        echo "<div class='text-center mt-4'>";
        echo "<a href='fix_all_issues.php' class='btn btn-primary btn-lg me-2'><i class='fas fa-tools'></i> إصلاح النظام</a>";
        echo "<a href='test.php' class='btn btn-info btn-lg'><i class='fas fa-cog'></i> اختبار النظام</a>";
        echo "</div>";
        
    } catch (PDOException $e) {
        echo "<div class='alert alert-danger'>";
        echo "<h5><i class='fas fa-times'></i> فشل الاتصال!</h5>";
        echo "<p><strong>الخطأ:</strong> " . $e->getMessage() . "</p>";
        echo "</div>";
    }
    
} else {
    // عرض نموذج الإعداد
    echo "<form method='POST' action=''>";
    
    echo "<div class='mb-3'>";
    echo "<label for='host' class='form-label'>مضيف قاعدة البيانات</label>";
    echo "<input type='text' class='form-control' id='host' name='host' value='localhost' required>";
    echo "</div>";
    
    echo "<div class='mb-3'>";
    echo "<label for='dbname' class='form-label'>اسم قاعدة البيانات</label>";
    echo "<input type='text' class='form-control' id='dbname' name='dbname' value='irjnpfzw_mr' required>";
    echo "</div>";
    
    echo "<div class='mb-3'>";
    echo "<label for='username' class='form-label'>اسم المستخدم</label>";
    echo "<input type='text' class='form-control' id='username' name='username' value='irjnpfzw_mr' required>";
    echo "</div>";
    
    echo "<div class='mb-3'>";
    echo "<label for='password' class='form-label'>كلمة المرور</label>";
    echo "<input type='password' class='form-control' id='password' name='password' value='irjnpfzw_mr'>";
    echo "<small class='form-text text-muted'>اتركها فارغة إذا لم تكن هناك كلمة مرور</small>";
    echo "</div>";
    
    echo "<div class='alert alert-info'>";
    echo "<h6>إعدادات شائعة:</h6>";
    echo "<ul>";
    echo "<li><strong>cPanel:</strong> عادة اسم المستخدم وكلمة المرور نفس اسم قاعدة البيانات</li>";
    echo "<li><strong>XAMPP:</strong> المستخدم: root، كلمة المرور: فارغة</li>";
    echo "<li><strong>WAMP:</strong> المستخدم: root، كلمة المرور: فارغة أو root</li>";
    echo "</ul>";
    echo "</div>";
    
    echo "<div class='text-center'>";
    echo "<button type='submit' class='btn btn-primary btn-lg'>";
    echo "<i class='fas fa-database'></i> اختبار الاتصال وحفظ الإعدادات";
    echo "</button>";
    echo "</div>";
    
    echo "</form>";
    
    echo "<div class='text-center mt-4'>";
    echo "<a href='test_db_connection.php' class='btn btn-info me-2'><i class='fas fa-search'></i> اختبار إعدادات مختلفة</a>";
    echo "<a href='test.php' class='btn btn-secondary'><i class='fas fa-cog'></i> اختبار النظام</a>";
    echo "</div>";
}

echo "</div>";
echo "</body>";
echo "</html>";
?>
