<?php
// ملف إصلاح كلمة المرور للمستخدم الافتراضي

require_once 'config/config.php';
require_once 'config/database.php';

echo "<!DOCTYPE html>";
echo "<html lang='ar' dir='rtl'>";
echo "<head>";
echo "<meta charset='UTF-8'>";
echo "<meta name='viewport' content='width=device-width, initial-scale=1.0'>";
echo "<title>إصلاح كلمة المرور</title>";
echo "<link href='https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css' rel='stylesheet'>";
echo "<link href='https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css' rel='stylesheet'>";
echo "<style>";
echo "body { font-family: '<PERSON><PERSON><PERSON>I', Tahoma, Geneva, Verdana, sans-serif; background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); min-height: 100vh; padding: 2rem; }";
echo ".fix-card { background: white; border-radius: 15px; box-shadow: 0 10px 30px rgba(0,0,0,0.1); padding: 2rem; margin: 0 auto; max-width: 600px; }";
echo ".success { color: #28a745; }";
echo ".error { color: #dc3545; }";
echo "</style>";
echo "</head>";
echo "<body>";

echo "<div class='fix-card'>";
echo "<h1 class='text-center mb-4'><i class='fas fa-key'></i> إصلاح كلمة المرور</h1>";

try {
    // التحقق من وجود المستخدم
    $user = fetchOne("SELECT * FROM users WHERE username = 'abd'", []);
    
    if (!$user) {
        echo "<div class='alert alert-warning'>";
        echo "<h5>إنشاء المستخدم الافتراضي</h5>";
        
        // إنشاء المستخدم الافتراضي
        $password_hash = password_hash('abdabd', PASSWORD_DEFAULT);
        $sql = "INSERT INTO users (username, password, phone, full_name, user_type, status) 
                VALUES (?, ?, ?, ?, ?, ?)";
        
        $result = executeQuery($sql, [
            'abd',
            $password_hash,
            '07719992716',
            'المرشح الرئيسي',
            'candidate',
            'active'
        ]);
        
        if ($result) {
            echo "<p class='success'><i class='fas fa-check'></i> تم إنشاء المستخدم الافتراضي بنجاح</p>";
        } else {
            echo "<p class='error'><i class='fas fa-times'></i> فشل في إنشاء المستخدم</p>";
        }
        
        echo "</div>";
    } else {
        echo "<div class='alert alert-info'>";
        echo "<h5>تحديث كلمة المرور</h5>";
        
        // تحديث كلمة المرور
        $new_password_hash = password_hash('abdabd', PASSWORD_DEFAULT);
        $sql = "UPDATE users SET password = ?, status = 'active' WHERE username = 'abd'";
        
        $result = executeQuery($sql, [$new_password_hash]);
        
        if ($result) {
            echo "<p class='success'><i class='fas fa-check'></i> تم تحديث كلمة المرور بنجاح</p>";
        } else {
            echo "<p class='error'><i class='fas fa-times'></i> فشل في تحديث كلمة المرور</p>";
        }
        
        echo "</div>";
    }
    
    // عرض بيانات المستخدم الحالية
    $updated_user = fetchOne("SELECT * FROM users WHERE username = 'abd'", []);
    
    if ($updated_user) {
        echo "<div class='alert alert-success'>";
        echo "<h5>بيانات المستخدم الحالية:</h5>";
        echo "<p><strong>اسم المستخدم:</strong> " . htmlspecialchars($updated_user['username']) . "</p>";
        echo "<p><strong>رقم الهاتف:</strong> " . htmlspecialchars($updated_user['phone']) . "</p>";
        echo "<p><strong>الاسم الكامل:</strong> " . htmlspecialchars($updated_user['full_name']) . "</p>";
        echo "<p><strong>نوع المستخدم:</strong> " . htmlspecialchars($updated_user['user_type']) . "</p>";
        echo "<p><strong>الحالة:</strong> " . htmlspecialchars($updated_user['status']) . "</p>";
        echo "<p><strong>كلمة المرور مشفرة:</strong> " . substr($updated_user['password'], 0, 20) . "...</p>";
        echo "</div>";
        
        // اختبار كلمة المرور
        echo "<div class='alert alert-warning'>";
        echo "<h5>اختبار كلمة المرور:</h5>";
        
        if (password_verify('abdabd', $updated_user['password'])) {
            echo "<p class='success'><i class='fas fa-check'></i> كلمة المرور 'abdabd' صحيحة</p>";
        } else {
            echo "<p class='error'><i class='fas fa-times'></i> كلمة المرور 'abdabd' غير صحيحة</p>";
        }
        echo "</div>";
    }
    
    // إنشاء كلمات مرور بديلة
    echo "<div class='alert alert-info'>";
    echo "<h5>كلمات مرور بديلة (للاختبار):</h5>";
    
    $test_passwords = ['123456', 'admin', 'password', 'abd123'];
    
    foreach ($test_passwords as $test_pass) {
        $test_hash = password_hash($test_pass, PASSWORD_DEFAULT);
        echo "<p><strong>$test_pass:</strong> " . substr($test_hash, 0, 30) . "...</p>";
        
        // تحديث بكلمة مرور بديلة
        if ($test_pass == '123456') {
            $sql = "UPDATE users SET password = ? WHERE username = 'abd'";
            executeQuery($sql, [$test_hash]);
            echo "<p class='success'>تم تحديث كلمة المرور إلى: $test_pass</p>";
        }
    }
    echo "</div>";
    
} catch (Exception $e) {
    echo "<div class='alert alert-danger'>";
    echo "<h5>خطأ:</h5>";
    echo "<p>" . htmlspecialchars($e->getMessage()) . "</p>";
    echo "</div>";
}

echo "<div class='text-center mt-4'>";
echo "<h5>بيانات تسجيل الدخول المحدثة:</h5>";
echo "<div class='alert alert-success'>";
echo "<p><strong>اسم المستخدم:</strong> abd</p>";
echo "<p><strong>كلمة المرور:</strong> 123456</p>";
echo "<p><strong>رقم الهاتف:</strong> 07719992716</p>";
echo "</div>";

echo "<a href='login.php' class='btn btn-primary me-2'><i class='fas fa-sign-in-alt'></i> تسجيل الدخول</a>";
echo "<a href='test.php' class='btn btn-info'><i class='fas fa-cog'></i> اختبار النظام</a>";
echo "</div>";

echo "</div>";
echo "</body>";
echo "</html>";
?>
