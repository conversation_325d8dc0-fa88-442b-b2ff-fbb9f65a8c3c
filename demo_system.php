<?php
// نظام تجريبي يعمل بدون قاعدة بيانات
session_start();
header('Content-Type: text/html; charset=utf-8');

// تعيين بيانات تجريبية
if (!isset($_SESSION['user_id'])) {
    $_SESSION['user_id'] = 1;
    $_SESSION['username'] = 'المرشح';
    $_SESSION['user_type'] = 'candidate';
    $_SESSION['full_name'] = 'زين العابدين';
}

// بيانات تجريبية
$demo_data = [
    'supporters' => 1250,
    'regions' => 15,
    'admins' => 8,
    'requests' => 42,
    'messages' => 18,
    'reports' => 12,
    'pending_requests' => 15,
    'completed_requests' => 27
];

$recent_supporters = [
    ['name' => 'أحمد محمد علي', 'phone' => '07701234567', 'region' => 'بغداد - الكرخ', 'time' => 'منذ ساعة'],
    ['name' => 'فاطمة حسن محمود', 'phone' => '07701234568', 'region' => 'النجف', 'time' => 'منذ ساعتين'],
    ['name' => 'محمد عبدالله أحمد', 'phone' => '07701234569', 'region' => 'البصرة', 'time' => 'منذ 3 ساعات'],
    ['name' => 'زينب علي حسن', 'phone' => '07701234570', 'region' => 'كربلاء', 'time' => 'منذ 4 ساعات'],
    ['name' => 'علي حسين محمد', 'phone' => '07701234571', 'region' => 'الأنبار', 'time' => 'منذ 5 ساعات']
];

$recent_requests = [
    ['title' => 'مساعدة طبية عاجلة', 'supporter' => 'أحمد محمد', 'status' => 'pending', 'time' => 'منذ 30 دقيقة'],
    ['title' => 'مساعدة مالية للدراسة', 'supporter' => 'فاطمة حسن', 'status' => 'in_progress', 'time' => 'منذ ساعة'],
    ['title' => 'طلب وظيفة', 'supporter' => 'محمد عبدالله', 'status' => 'completed', 'time' => 'منذ ساعتين'],
    ['title' => 'مساعدة في السكن', 'supporter' => 'زينب علي', 'status' => 'pending', 'time' => 'منذ 3 ساعات']
];

$recent_messages = [
    ['subject' => 'ترحيب بالفريق الجديد', 'sender' => 'إدارة الحملة', 'time' => 'منذ ساعة'],
    ['subject' => 'تحديث مهم حول الفعاليات', 'sender' => 'مدير المنطقة', 'time' => 'منذ ساعتين'],
    ['subject' => 'تذكير بالاجتماع الأسبوعي', 'sender' => 'المنسق العام', 'time' => 'منذ 3 ساعات']
];
?>
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>النظام التجريبي - لوحة تحكم المرشح</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: #f8f9fa;
        }
        .sidebar {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            color: white;
        }
        .main-content {
            padding: 20px;
        }
        .stats-card {
            border-radius: 15px;
            border: none;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
            transition: transform 0.3s ease;
            cursor: pointer;
        }
        .stats-card:hover {
            transform: translateY(-5px);
        }
        .nav-link {
            color: rgba(255,255,255,0.8);
            border-radius: 10px;
            margin: 5px 0;
            transition: all 0.3s ease;
        }
        .nav-link:hover, .nav-link.active {
            background: rgba(255,255,255,0.2);
            color: white;
        }
        .demo-badge {
            position: fixed;
            top: 10px;
            right: 10px;
            z-index: 1000;
        }
        .border-left-primary { border-left: 4px solid #007bff !important; }
        .border-left-success { border-left: 4px solid #28a745 !important; }
        .border-left-info { border-left: 4px solid #17a2b8 !important; }
        .border-left-warning { border-left: 4px solid #ffc107 !important; }
        .border-left-danger { border-left: 4px solid #dc3545 !important; }
        .border-left-secondary { border-left: 4px solid #6c757d !important; }
    </style>
</head>
<body>
    <!-- شارة النظام التجريبي -->
    <div class="demo-badge">
        <span class="badge bg-warning text-dark">
            <i class="fas fa-flask"></i> نظام تجريبي
        </span>
    </div>

    <div class="container-fluid">
        <div class="row">
            <!-- الشريط الجانبي -->
            <div class="col-md-3 col-lg-2 sidebar p-0">
                <div class="p-3">
                    <div class="text-center mb-4">
                        <i class="fas fa-crown fa-3x mb-2"></i>
                        <h5>لوحة تحكم المرشح</h5>
                        <small><?php echo htmlspecialchars($_SESSION['full_name']); ?></small>
                    </div>
                    
                    <nav class="nav flex-column">
                        <a class="nav-link active" href="#dashboard">
                            <i class="fas fa-home me-2"></i>
                            الرئيسية
                        </a>
                        <a class="nav-link" href="#supporters">
                            <i class="fas fa-users me-2"></i>
                            المؤيدين
                            <span class="badge bg-light text-dark ms-auto"><?php echo $demo_data['supporters']; ?></span>
                        </a>
                        <a class="nav-link" href="#admins">
                            <i class="fas fa-user-shield me-2"></i>
                            الإداريين
                            <span class="badge bg-light text-dark ms-auto"><?php echo $demo_data['admins']; ?></span>
                        </a>
                        <a class="nav-link" href="#regions">
                            <i class="fas fa-map-marker-alt me-2"></i>
                            المناطق
                            <span class="badge bg-light text-dark ms-auto"><?php echo $demo_data['regions']; ?></span>
                        </a>
                        <a class="nav-link" href="#requests">
                            <i class="fas fa-hand-holding-heart me-2"></i>
                            المطالب
                            <span class="badge bg-warning ms-auto"><?php echo $demo_data['requests']; ?></span>
                        </a>
                        <a class="nav-link" href="#messages">
                            <i class="fas fa-envelope me-2"></i>
                            الرسائل
                            <span class="badge bg-info ms-auto"><?php echo $demo_data['messages']; ?></span>
                        </a>
                        <a class="nav-link" href="#reports">
                            <i class="fas fa-file-alt me-2"></i>
                            التقارير
                            <span class="badge bg-success ms-auto"><?php echo $demo_data['reports']; ?></span>
                        </a>
                        <a class="nav-link" href="#settings">
                            <i class="fas fa-cog me-2"></i>
                            الإعدادات
                        </a>
                    </nav>
                </div>
            </div>

            <!-- المحتوى الرئيسي -->
            <div class="col-md-9 col-lg-10 main-content">
                <!-- الترحيب -->
                <div class="row mb-4">
                    <div class="col-12">
                        <div class="card border-0 bg-primary text-white">
                            <div class="card-body">
                                <h3><i class="fas fa-sun me-2"></i>مرحباً، <?php echo htmlspecialchars($_SESSION['full_name']); ?>!</h3>
                                <p class="mb-0">إليك نظرة سريعة على أداء حملتك الانتخابية اليوم</p>
                                <small>آخر تحديث: <?php echo date('Y-m-d H:i:s'); ?> | <span class="badge bg-warning text-dark">نظام تجريبي</span></small>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- الإحصائيات الرئيسية -->
                <div class="row mb-4">
                    <div class="col-xl-3 col-md-6 mb-4">
                        <div class="card stats-card border-left-primary">
                            <div class="card-body">
                                <div class="row no-gutters align-items-center">
                                    <div class="col mr-2">
                                        <div class="text-xs font-weight-bold text-primary text-uppercase mb-1">المؤيدين</div>
                                        <div class="h5 mb-0 font-weight-bold text-gray-800"><?php echo number_format($demo_data['supporters']); ?></div>
                                    </div>
                                    <div class="col-auto">
                                        <i class="fas fa-users fa-2x text-gray-300"></i>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="col-xl-3 col-md-6 mb-4">
                        <div class="card stats-card border-left-success">
                            <div class="card-body">
                                <div class="row no-gutters align-items-center">
                                    <div class="col mr-2">
                                        <div class="text-xs font-weight-bold text-success text-uppercase mb-1">المناطق</div>
                                        <div class="h5 mb-0 font-weight-bold text-gray-800"><?php echo $demo_data['regions']; ?></div>
                                    </div>
                                    <div class="col-auto">
                                        <i class="fas fa-map-marker-alt fa-2x text-gray-300"></i>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="col-xl-3 col-md-6 mb-4">
                        <div class="card stats-card border-left-info">
                            <div class="card-body">
                                <div class="row no-gutters align-items-center">
                                    <div class="col mr-2">
                                        <div class="text-xs font-weight-bold text-info text-uppercase mb-1">الإداريين</div>
                                        <div class="h5 mb-0 font-weight-bold text-gray-800"><?php echo $demo_data['admins']; ?></div>
                                    </div>
                                    <div class="col-auto">
                                        <i class="fas fa-user-shield fa-2x text-gray-300"></i>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="col-xl-3 col-md-6 mb-4">
                        <div class="card stats-card border-left-warning">
                            <div class="card-body">
                                <div class="row no-gutters align-items-center">
                                    <div class="col mr-2">
                                        <div class="text-xs font-weight-bold text-warning text-uppercase mb-1">المطالب</div>
                                        <div class="h5 mb-0 font-weight-bold text-gray-800"><?php echo $demo_data['requests']; ?></div>
                                    </div>
                                    <div class="col-auto">
                                        <i class="fas fa-hand-holding-heart fa-2x text-gray-300"></i>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- الصف الثاني من الإحصائيات -->
                <div class="row mb-4">
                    <div class="col-xl-4 col-md-6 mb-4">
                        <div class="card stats-card border-left-secondary">
                            <div class="card-body">
                                <div class="row no-gutters align-items-center">
                                    <div class="col mr-2">
                                        <div class="text-xs font-weight-bold text-secondary text-uppercase mb-1">الرسائل</div>
                                        <div class="h5 mb-0 font-weight-bold text-gray-800"><?php echo $demo_data['messages']; ?></div>
                                    </div>
                                    <div class="col-auto">
                                        <i class="fas fa-envelope fa-2x text-gray-300"></i>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="col-xl-4 col-md-6 mb-4">
                        <div class="card stats-card border-left-danger">
                            <div class="card-body">
                                <div class="row no-gutters align-items-center">
                                    <div class="col mr-2">
                                        <div class="text-xs font-weight-bold text-danger text-uppercase mb-1">التقارير</div>
                                        <div class="h5 mb-0 font-weight-bold text-gray-800"><?php echo $demo_data['reports']; ?></div>
                                    </div>
                                    <div class="col-auto">
                                        <i class="fas fa-file-alt fa-2x text-gray-300"></i>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="col-xl-4 col-md-6 mb-4">
                        <div class="card stats-card border-left-dark">
                            <div class="card-body">
                                <div class="row no-gutters align-items-center">
                                    <div class="col mr-2">
                                        <div class="text-xs font-weight-bold text-dark text-uppercase mb-1">المطالب المعلقة</div>
                                        <div class="h5 mb-0 font-weight-bold text-gray-800"><?php echo $demo_data['pending_requests']; ?></div>
                                    </div>
                                    <div class="col-auto">
                                        <i class="fas fa-clock fa-2x text-gray-300"></i>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- آخر الأنشطة -->
                <div class="row">
                    <!-- آخر المؤيدين -->
                    <div class="col-lg-4 mb-4">
                        <div class="card">
                            <div class="card-header bg-primary text-white">
                                <h6 class="m-0 font-weight-bold">آخر المؤيدين المضافين</h6>
                            </div>
                            <div class="card-body">
                                <?php foreach (array_slice($recent_supporters, 0, 4) as $supporter): ?>
                                <div class="d-flex justify-content-between align-items-center mb-3 pb-2 border-bottom">
                                    <div>
                                        <h6 class="mb-1"><?php echo $supporter['name']; ?></h6>
                                        <small class="text-muted"><?php echo $supporter['phone']; ?> - <?php echo $supporter['region']; ?></small>
                                    </div>
                                    <small class="text-muted"><?php echo $supporter['time']; ?></small>
                                </div>
                                <?php endforeach; ?>
                                <div class="text-center">
                                    <button class="btn btn-primary btn-sm">عرض جميع المؤيدين</button>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- آخر المطالب -->
                    <div class="col-lg-4 mb-4">
                        <div class="card">
                            <div class="card-header bg-warning text-dark">
                                <h6 class="m-0 font-weight-bold">آخر المطالب</h6>
                            </div>
                            <div class="card-body">
                                <?php foreach ($recent_requests as $request): ?>
                                <div class="d-flex justify-content-between align-items-center mb-3 pb-2 border-bottom">
                                    <div>
                                        <h6 class="mb-1"><?php echo $request['title']; ?></h6>
                                        <small class="text-muted"><?php echo $request['supporter']; ?></small>
                                    </div>
                                    <div class="text-end">
                                        <?php
                                        $badge_class = [
                                            'pending' => 'bg-warning',
                                            'in_progress' => 'bg-info',
                                            'completed' => 'bg-success'
                                        ][$request['status']] ?? 'bg-secondary';
                                        ?>
                                        <span class="badge <?php echo $badge_class; ?> mb-1">
                                            <?php echo $request['status']; ?>
                                        </span>
                                        <br>
                                        <small class="text-muted"><?php echo $request['time']; ?></small>
                                    </div>
                                </div>
                                <?php endforeach; ?>
                                <div class="text-center">
                                    <button class="btn btn-warning btn-sm">عرض جميع المطالب</button>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- آخر الرسائل -->
                    <div class="col-lg-4 mb-4">
                        <div class="card">
                            <div class="card-header bg-info text-white">
                                <h6 class="m-0 font-weight-bold">آخر الرسائل</h6>
                            </div>
                            <div class="card-body">
                                <?php foreach ($recent_messages as $message): ?>
                                <div class="d-flex justify-content-between align-items-center mb-3 pb-2 border-bottom">
                                    <div>
                                        <h6 class="mb-1"><?php echo $message['subject']; ?></h6>
                                        <small class="text-muted">من: <?php echo $message['sender']; ?></small>
                                    </div>
                                    <small class="text-muted"><?php echo $message['time']; ?></small>
                                </div>
                                <?php endforeach; ?>
                                <div class="text-center">
                                    <button class="btn btn-info btn-sm">عرض جميع الرسائل</button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- روابط سريعة -->
                <div class="row">
                    <div class="col-12">
                        <div class="card">
                            <div class="card-header bg-success text-white">
                                <h6 class="m-0 font-weight-bold">روابط سريعة</h6>
                            </div>
                            <div class="card-body">
                                <div class="row">
                                    <div class="col-md-3 mb-2">
                                        <button class="btn btn-primary w-100">
                                            <i class="fas fa-user-plus me-2"></i>إضافة مؤيد
                                        </button>
                                    </div>
                                    <div class="col-md-3 mb-2">
                                        <button class="btn btn-success w-100">
                                            <i class="fas fa-user-shield me-2"></i>لوحة الإداريين
                                        </button>
                                    </div>
                                    <div class="col-md-3 mb-2">
                                        <a href="quick_fix.php" class="btn btn-warning w-100">
                                            <i class="fas fa-wrench me-2"></i>إصلاح قاعدة البيانات
                                        </a>
                                    </div>
                                    <div class="col-md-3 mb-2">
                                        <a href="dashboard.php" class="btn btn-info w-100">
                                            <i class="fas fa-database me-2"></i>النظام الأصلي
                                        </a>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- تنبيه النظام التجريبي -->
                <div class="row mt-4">
                    <div class="col-12">
                        <div class="alert alert-warning">
                            <h5><i class="fas fa-flask me-2"></i>هذا نظام تجريبي</h5>
                            <p class="mb-0">
                                هذا النظام يعمل بدون قاعدة بيانات ويعرض بيانات تجريبية فقط. 
                                لتشغيل النظام الحقيقي، يجب إصلاح مشكلة قاعدة البيانات أولاً.
                            </p>
                            <hr>
                            <div class="text-center">
                                <a href="quick_fix.php" class="btn btn-warning me-2">
                                    <i class="fas fa-tools me-2"></i>إصلاح قاعدة البيانات
                                </a>
                                <a href="emergency_fix.php" class="btn btn-danger">
                                    <i class="fas fa-ambulance me-2"></i>إصلاح طارئ
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // إضافة تأثيرات تفاعلية
        document.querySelectorAll('.stats-card').forEach(card => {
            card.addEventListener('click', function() {
                this.style.transform = 'scale(0.95)';
                setTimeout(() => {
                    this.style.transform = 'translateY(-5px)';
                }, 100);
            });
        });

        // تحديث الوقت كل دقيقة
        setInterval(() => {
            const now = new Date();
            const timeString = now.toLocaleString('ar-EG');
            document.querySelector('small').innerHTML = `آخر تحديث: ${timeString} | <span class="badge bg-warning text-dark">نظام تجريبي</span>`;
        }, 60000);
    </script>
</body>
</html>
