<?php
// دوال التقارير
require_once __DIR__ . '/../config/database.php';
require_once __DIR__ . '/notification_functions.php';

/**
 * إنشاء تقرير أسبوعي جديد
 */
function createWeeklyReport($data, $admin_id) {
    // توليد رقم التقرير
    $report_number = generateReportNumber($admin_id);
    
    $sql = "INSERT INTO weekly_reports (
        report_number, admin_id, week_start, week_end, report_period,
        supporters_added, supporters_contacted, supporters_visited, new_families_reached,
        events_attended, meetings_held, calls_made, visits_conducted,
        requests_submitted, requests_followed_up, requests_completed,
        messages_sent, messages_received, achievements, challenges,
        solutions_implemented, next_week_plans, recommendations, notes,
        working_hours, overtime_hours, travel_distance_km, fuel_cost, other_expenses,
        priority
    ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)";
    
    $params = [
        $report_number,
        $admin_id,
        $data['week_start'],
        $data['week_end'],
        $data['report_period'],
        $data['supporters_added'] ?? 0,
        $data['supporters_contacted'] ?? 0,
        $data['supporters_visited'] ?? 0,
        $data['new_families_reached'] ?? 0,
        $data['events_attended'] ?? 0,
        $data['meetings_held'] ?? 0,
        $data['calls_made'] ?? 0,
        $data['visits_conducted'] ?? 0,
        $data['requests_submitted'] ?? 0,
        $data['requests_followed_up'] ?? 0,
        $data['requests_completed'] ?? 0,
        $data['messages_sent'] ?? 0,
        $data['messages_received'] ?? 0,
        $data['achievements'] ?? null,
        $data['challenges'] ?? null,
        $data['solutions_implemented'] ?? null,
        $data['next_week_plans'] ?? null,
        $data['recommendations'] ?? null,
        $data['notes'] ?? null,
        $data['working_hours'] ?? 0,
        $data['overtime_hours'] ?? 0,
        $data['travel_distance_km'] ?? 0,
        $data['fuel_cost'] ?? 0,
        $data['other_expenses'] ?? 0,
        $data['priority'] ?? 'normal'
    ];
    
    try {
        $result = executeQuery($sql, $params);
        $report_id = $GLOBALS['pdo']->lastInsertId();
        
        // تسجيل النشاط
        logActivity([
            'user_type' => 'admin',
            'user_id' => $admin_id,
            'action_type' => 'create',
            'target_type' => 'report',
            'target_id' => $report_id,
            'target_name' => $data['report_period'],
            'description' => 'تم إنشاء تقرير أسبوعي: ' . $data['report_period']
        ]);
        
        // تحديث إحصائيات الإداري
        updateAdminDailyStats($admin_id, 'reports_submitted', 1);
        
        return $report_id;
    } catch (Exception $e) {
        error_log("خطأ في إنشاء التقرير: " . $e->getMessage());
        return false;
    }
}

/**
 * توليد رقم تقرير فريد
 */
function generateReportNumber($admin_id) {
    $year = date('Y');
    $week = date('W');
    
    // جلب آخر رقم تقرير لهذا الإداري في هذا الأسبوع
    $last_report = fetchOne("
        SELECT report_number 
        FROM weekly_reports 
        WHERE admin_id = ? AND report_number LIKE ? 
        ORDER BY id DESC 
        LIMIT 1
    ", [$admin_id, "RPT-$year-W$week-$admin_id-%"]);
    
    if ($last_report) {
        $last_number = intval(substr($last_report['report_number'], -2));
        $new_number = $last_number + 1;
    } else {
        $new_number = 1;
    }
    
    return sprintf("RPT-%s-W%s-%s-%02d", $year, $week, $admin_id, $new_number);
}

/**
 * تحديث تقرير
 */
function updateWeeklyReport($report_id, $data, $admin_id) {
    // التحقق من ملكية التقرير
    $report = fetchOne("SELECT * FROM weekly_reports WHERE id = ? AND admin_id = ?", [$report_id, $admin_id]);
    
    if (!$report || $report['status'] === 'approved') {
        return false; // لا يمكن تعديل التقرير المعتمد
    }
    
    $sql = "UPDATE weekly_reports SET 
        supporters_added = ?, supporters_contacted = ?, supporters_visited = ?, new_families_reached = ?,
        events_attended = ?, meetings_held = ?, calls_made = ?, visits_conducted = ?,
        requests_submitted = ?, requests_followed_up = ?, requests_completed = ?,
        messages_sent = ?, messages_received = ?, achievements = ?, challenges = ?,
        solutions_implemented = ?, next_week_plans = ?, recommendations = ?, notes = ?,
        working_hours = ?, overtime_hours = ?, travel_distance_km = ?, fuel_cost = ?, other_expenses = ?,
        priority = ?, updated_at = NOW()
        WHERE id = ? AND admin_id = ?";
    
    $params = [
        $data['supporters_added'] ?? 0,
        $data['supporters_contacted'] ?? 0,
        $data['supporters_visited'] ?? 0,
        $data['new_families_reached'] ?? 0,
        $data['events_attended'] ?? 0,
        $data['meetings_held'] ?? 0,
        $data['calls_made'] ?? 0,
        $data['visits_conducted'] ?? 0,
        $data['requests_submitted'] ?? 0,
        $data['requests_followed_up'] ?? 0,
        $data['requests_completed'] ?? 0,
        $data['messages_sent'] ?? 0,
        $data['messages_received'] ?? 0,
        $data['achievements'] ?? null,
        $data['challenges'] ?? null,
        $data['solutions_implemented'] ?? null,
        $data['next_week_plans'] ?? null,
        $data['recommendations'] ?? null,
        $data['notes'] ?? null,
        $data['working_hours'] ?? 0,
        $data['overtime_hours'] ?? 0,
        $data['travel_distance_km'] ?? 0,
        $data['fuel_cost'] ?? 0,
        $data['other_expenses'] ?? 0,
        $data['priority'] ?? 'normal',
        $report_id,
        $admin_id
    ];
    
    try {
        $result = executeQuery($sql, $params);
        
        // تسجيل النشاط
        logActivity([
            'user_type' => 'admin',
            'user_id' => $admin_id,
            'action_type' => 'update',
            'target_type' => 'report',
            'target_id' => $report_id,
            'target_name' => $report['report_period'],
            'description' => 'تم تحديث التقرير الأسبوعي: ' . $report['report_period']
        ]);
        
        return true;
    } catch (Exception $e) {
        error_log("خطأ في تحديث التقرير: " . $e->getMessage());
        return false;
    }
}

/**
 * تقديم التقرير للمراجعة
 */
function submitReport($report_id, $admin_id) {
    $report = fetchOne("SELECT * FROM weekly_reports WHERE id = ? AND admin_id = ?", [$report_id, $admin_id]);
    
    if (!$report || $report['status'] !== 'draft') {
        return false;
    }
    
    $result = executeQuery("
        UPDATE weekly_reports 
        SET status = 'submitted', submitted_at = NOW() 
        WHERE id = ? AND admin_id = ?
    ", [$report_id, $admin_id]);
    
    if ($result) {
        // إنشاء إشعار للإدارة
        notifyNewReport($report_id);
        
        // تسجيل النشاط
        logActivity([
            'user_type' => 'admin',
            'user_id' => $admin_id,
            'action_type' => 'update',
            'target_type' => 'report',
            'target_id' => $report_id,
            'target_name' => $report['report_period'],
            'description' => 'تم تقديم التقرير للمراجعة: ' . $report['report_period']
        ]);
    }
    
    return $result;
}

/**
 * مراجعة التقرير
 */
function reviewReport($report_id, $status, $reviewer_id, $review_notes = null) {
    $valid_statuses = ['approved', 'rejected', 'revision_required'];
    
    if (!in_array($status, $valid_statuses)) {
        return false;
    }
    
    $sql = "UPDATE weekly_reports SET 
        status = ?, reviewed_by = ?, reviewed_at = NOW(), review_notes = ?";
    
    $params = [$status, $reviewer_id, $review_notes];
    
    if ($status === 'approved') {
        $sql .= ", approved_by = ?, approved_at = NOW()";
        $params[] = $reviewer_id;
    }
    
    $sql .= " WHERE id = ?";
    $params[] = $report_id;
    
    try {
        $result = executeQuery($sql, $params);
        
        if ($result) {
            // جلب بيانات التقرير للإشعار
            $report = fetchOne("SELECT admin_id, report_period FROM weekly_reports WHERE id = ?", [$report_id]);
            
            // إنشاء إشعار للإداري
            $status_text = [
                'approved' => 'تم اعتماد',
                'rejected' => 'تم رفض',
                'revision_required' => 'يحتاج مراجعة'
            ];
            
            createNotification([
                'user_type' => 'admin',
                'user_id' => $report['admin_id'],
                'title' => 'تحديث حالة التقرير',
                'message' => $status_text[$status] . ' التقرير الأسبوعي: ' . $report['report_period'],
                'type' => $status === 'approved' ? 'success' : ($status === 'rejected' ? 'error' : 'warning'),
                'category' => 'report',
                'related_type' => 'report',
                'related_id' => $report_id,
                'action_url' => "modules/admin/reports.php?view=$report_id"
            ]);
            
            // تسجيل النشاط
            logActivity([
                'user_type' => 'management',
                'user_id' => $reviewer_id,
                'action_type' => 'update',
                'target_type' => 'report',
                'target_id' => $report_id,
                'target_name' => $report['report_period'],
                'description' => "تم $status التقرير: " . $report['report_period']
            ]);
        }
        
        return $result;
    } catch (Exception $e) {
        error_log("خطأ في مراجعة التقرير: " . $e->getMessage());
        return false;
    }
}

/**
 * البحث في التقارير
 */
function searchReports($search_term = '', $filters = [], $limit = 50, $offset = 0) {
    $sql = "SELECT wr.*, a.full_name as admin_name, r.name as region_name,
            (SELECT COUNT(*) FROM report_attachments WHERE report_id = wr.id) as attachment_count
            FROM weekly_reports wr 
            JOIN admins a ON wr.admin_id = a.id 
            LEFT JOIN regions r ON a.region_id = r.id 
            WHERE 1=1";
    $params = [];
    
    // البحث النصي
    if (!empty($search_term)) {
        $sql .= " AND (wr.report_number LIKE ? OR wr.report_period LIKE ? OR a.full_name LIKE ?)";
        $params[] = "%$search_term%";
        $params[] = "%$search_term%";
        $params[] = "%$search_term%";
    }
    
    // فلاتر
    if (!empty($filters['status'])) {
        $sql .= " AND wr.status = ?";
        $params[] = $filters['status'];
    }
    
    if (!empty($filters['admin_id'])) {
        $sql .= " AND wr.admin_id = ?";
        $params[] = $filters['admin_id'];
    }
    
    if (!empty($filters['priority'])) {
        $sql .= " AND wr.priority = ?";
        $params[] = $filters['priority'];
    }
    
    if (!empty($filters['week_start'])) {
        $sql .= " AND wr.week_start >= ?";
        $params[] = $filters['week_start'];
    }
    
    if (!empty($filters['week_end'])) {
        $sql .= " AND wr.week_end <= ?";
        $params[] = $filters['week_end'];
    }
    
    $sql .= " ORDER BY wr.week_start DESC, wr.priority DESC LIMIT ? OFFSET ?";
    $params[] = $limit;
    $params[] = $offset;
    
    return fetchAll($sql, $params);
}

/**
 * عدد التقارير حسب الفلاتر
 */
function countReports($search_term = '', $filters = []) {
    $sql = "SELECT COUNT(*) as count FROM weekly_reports wr 
            JOIN admins a ON wr.admin_id = a.id 
            WHERE 1=1";
    $params = [];
    
    // البحث النصي
    if (!empty($search_term)) {
        $sql .= " AND (wr.report_number LIKE ? OR wr.report_period LIKE ? OR a.full_name LIKE ?)";
        $params[] = "%$search_term%";
        $params[] = "%$search_term%";
        $params[] = "%$search_term%";
    }
    
    // فلاتر
    if (!empty($filters['status'])) {
        $sql .= " AND wr.status = ?";
        $params[] = $filters['status'];
    }
    
    if (!empty($filters['admin_id'])) {
        $sql .= " AND wr.admin_id = ?";
        $params[] = $filters['admin_id'];
    }
    
    if (!empty($filters['priority'])) {
        $sql .= " AND wr.priority = ?";
        $params[] = $filters['priority'];
    }
    
    if (!empty($filters['week_start'])) {
        $sql .= " AND wr.week_start >= ?";
        $params[] = $filters['week_start'];
    }
    
    if (!empty($filters['week_end'])) {
        $sql .= " AND wr.week_end <= ?";
        $params[] = $filters['week_end'];
    }
    
    $result = fetchOne($sql, $params);
    return $result['count'] ?? 0;
}

/**
 * إضافة مرفق للتقرير
 */
function addReportAttachment($report_id, $file_data, $admin_id) {
    $sql = "INSERT INTO report_attachments (
        report_id, file_name, original_name, file_path, file_size,
        file_type, mime_type, attachment_type, description, uploaded_by
    ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)";
    
    $params = [
        $report_id,
        $file_data['file_name'],
        $file_data['original_name'],
        $file_data['file_path'],
        $file_data['file_size'],
        $file_data['file_type'],
        $file_data['mime_type'],
        $file_data['attachment_type'] ?? 'document',
        $file_data['description'] ?? null,
        $admin_id
    ];
    
    try {
        $result = executeQuery($sql, $params);
        return $GLOBALS['pdo']->lastInsertId();
    } catch (Exception $e) {
        error_log("خطأ في إضافة مرفق التقرير: " . $e->getMessage());
        return false;
    }
}

/**
 * جلب مرفقات التقرير
 */
function getReportAttachments($report_id) {
    return fetchAll("
        SELECT ra.*, a.full_name as uploaded_by_name 
        FROM report_attachments ra 
        LEFT JOIN admins a ON ra.uploaded_by = a.id 
        WHERE ra.report_id = ? 
        ORDER BY ra.upload_date DESC
    ", [$report_id]);
}

/**
 * إضافة تعليق على التقرير
 */
function addReportComment($report_id, $comment_data, $user_id, $user_type) {
    $sql = "INSERT INTO report_comments (
        report_id, commenter_type, commenter_id, comment, comment_type,
        is_internal, parent_comment_id
    ) VALUES (?, ?, ?, ?, ?, ?, ?)";
    
    $params = [
        $report_id,
        $user_type,
        $user_id,
        $comment_data['comment'],
        $comment_data['comment_type'] ?? 'note',
        $comment_data['is_internal'] ?? false,
        $comment_data['parent_comment_id'] ?? null
    ];
    
    try {
        $result = executeQuery($sql, $params);
        return $GLOBALS['pdo']->lastInsertId();
    } catch (Exception $e) {
        error_log("خطأ في إضافة التعليق: " . $e->getMessage());
        return false;
    }
}

/**
 * جلب تعليقات التقرير
 */
function getReportComments($report_id) {
    return fetchAll("
        SELECT rc.*, 
               CASE 
                   WHEN rc.commenter_type = 'admin' THEN a.full_name
                   WHEN rc.commenter_type = 'management' THEN u.full_name
                   ELSE 'النظام'
               END as commenter_name
        FROM report_comments rc 
        LEFT JOIN admins a ON rc.commenter_id = a.id AND rc.commenter_type = 'admin'
        LEFT JOIN users u ON rc.commenter_id = u.id AND rc.commenter_type = 'management'
        WHERE rc.report_id = ? 
        ORDER BY rc.created_at ASC
    ", [$report_id]);
}

/**
 * إحصائيات التقارير
 */
function getReportsStatistics($filters = []) {
    $sql = "SELECT 
        COUNT(*) as total,
        SUM(CASE WHEN status = 'draft' THEN 1 ELSE 0 END) as draft_count,
        SUM(CASE WHEN status = 'submitted' THEN 1 ELSE 0 END) as submitted_count,
        SUM(CASE WHEN status = 'approved' THEN 1 ELSE 0 END) as approved_count,
        SUM(CASE WHEN status = 'rejected' THEN 1 ELSE 0 END) as rejected_count,
        SUM(supporters_added) as total_supporters_added,
        SUM(requests_submitted) as total_requests_submitted,
        SUM(working_hours) as total_working_hours,
        SUM(fuel_cost + other_expenses) as total_expenses,
        AVG(supporters_added) as avg_supporters_per_report,
        COUNT(DISTINCT admin_id) as active_admins
    FROM weekly_reports WHERE 1=1";
    
    $params = [];
    
    if (!empty($filters['admin_id'])) {
        $sql .= " AND admin_id = ?";
        $params[] = $filters['admin_id'];
    }
    
    if (!empty($filters['week_start'])) {
        $sql .= " AND week_start >= ?";
        $params[] = $filters['week_start'];
    }
    
    if (!empty($filters['week_end'])) {
        $sql .= " AND week_end <= ?";
        $params[] = $filters['week_end'];
    }
    
    return fetchOne($sql, $params);
}

/**
 * التقارير المتأخرة
 */
function getOverdueReports($days = 7) {
    return fetchAll("
        SELECT wr.*, a.full_name as admin_name 
        FROM weekly_reports wr 
        JOIN admins a ON wr.admin_id = a.id 
        WHERE wr.status = 'draft' 
        AND wr.created_at < DATE_SUB(NOW(), INTERVAL ? DAY)
        ORDER BY wr.created_at ASC
    ", [$days]);
}

/**
 * التقارير المطلوبة (الإداريين الذين لم يقدموا تقارير هذا الأسبوع)
 */
function getMissingReports() {
    $week_start = date('Y-m-d', strtotime('monday this week'));
    $week_end = date('Y-m-d', strtotime('sunday this week'));
    
    return fetchAll("
        SELECT a.id, a.full_name, a.phone, r.name as region_name
        FROM admins a 
        LEFT JOIN regions r ON a.region_id = r.id
        WHERE a.status = 'active' 
        AND a.id NOT IN (
            SELECT admin_id 
            FROM weekly_reports 
            WHERE week_start = ? AND week_end = ?
        )
        ORDER BY a.full_name
    ", [$week_start, $week_end]);
}
?>
