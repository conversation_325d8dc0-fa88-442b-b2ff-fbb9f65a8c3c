<?php
// تعيين الترميز العربي
header('Content-Type: text/html; charset=utf-8');
ini_set('default_charset', 'utf-8');
mb_internal_encoding('UTF-8');

require_once '../../config/config.php';
require_once '../../config/database.php';

// التحقق من تسجيل الدخول
if (!isLoggedIn()) {
    redirect('../../login.php');
}

// التحقق من الصلاحيات - التقارير للمرشح فقط
if (!isCandidate()) {
    showMessage('ليس لديك صلاحية للوصول إلى هذه الصفحة', 'error');
    redirect('../../dashboard.php');
}

// جلب الإحصائيات
$database = new Database();
$db = $database->getConnection();

// إحصائيات عامة
$total_supporters = $db->query("SELECT COUNT(*) as count FROM supporters")->fetch()['count'];
$total_regions = $db->query("SELECT COUNT(*) as count FROM regions")->fetch()['count'];
$total_admins = $db->query("SELECT COUNT(*) as count FROM users WHERE user_type = 'admin'")->fetch()['count'];
$total_expenses = $db->query("SELECT COALESCE(SUM(amount), 0) as total FROM expenses")->fetch()['total'];

// إحصائيات المؤيدين حسب المنطقة
$supporters_by_region = $db->query("
    SELECT r.name as region_name, COUNT(s.id) as supporters_count 
    FROM regions r 
    LEFT JOIN supporters s ON r.id = s.region_id 
    GROUP BY r.id, r.name 
    ORDER BY supporters_count DESC
")->fetchAll();

// إحصائيات المؤيدين حسب الجنس
$supporters_by_gender = $db->query("
    SELECT gender, COUNT(*) as count 
    FROM supporters 
    GROUP BY gender
")->fetchAll();

// إحصائيات المؤيدين حسب الحالة الاجتماعية
$supporters_by_marital = $db->query("
    SELECT marital_status, COUNT(*) as count 
    FROM supporters 
    GROUP BY marital_status
")->fetchAll();

// إحصائيات المصروفات حسب الفئة
$expenses_by_category = $db->query("
    SELECT category, COUNT(*) as count, SUM(amount) as total_amount 
    FROM expenses 
    GROUP BY category 
    ORDER BY total_amount DESC
")->fetchAll();

// إحصائيات الفعاليات حسب الحالة
$events_by_status = $db->query("
    SELECT status, COUNT(*) as count 
    FROM events 
    GROUP BY status
")->fetchAll();
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>التقارير والإحصائيات - <?php echo SITE_NAME; ?></title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/chart.js@3.9.1/dist/chart.min.css" rel="stylesheet">
    <link href="../../assets/css/dashboard.css" rel="stylesheet">
    <style>
        .chart-container {
            position: relative;
            height: 400px;
            margin-bottom: 2rem;
        }
        .stats-card {
            transition: all 0.3s ease;
            border: none;
            border-radius: 15px;
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
        }
        .stats-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 8px 25px rgba(0,0,0,0.15);
        }
    </style>
</head>
<body>
    <!-- شريط التنقل العلوي -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary fixed-top">
        <div class="container-fluid">
            <a class="navbar-brand" href="../../dashboard.php">
                <i class="fas fa-vote-yea me-2"></i>
                نظام إدارة الحملة الانتخابية
            </a>
            
            <div class="navbar-nav ms-auto">
                <div class="nav-item dropdown">
                    <a class="nav-link dropdown-toggle" href="#" id="userDropdown" role="button" data-bs-toggle="dropdown">
                        <i class="fas fa-user-circle me-1"></i>
                        <?php echo htmlspecialchars($_SESSION['full_name']); ?>
                    </a>
                    <ul class="dropdown-menu dropdown-menu-end">
                        <li><a class="dropdown-item" href="../../logout.php"><i class="fas fa-sign-out-alt me-2"></i>تسجيل الخروج</a></li>
                    </ul>
                </div>
            </div>
        </div>
    </nav>

    <!-- المحتوى الرئيسي -->
    <div class="container-fluid main-content">
        <div class="row">
            <!-- الشريط الجانبي -->
            <nav class="col-md-3 col-lg-2 d-md-block sidebar">
                <div class="position-sticky pt-3">
                    <ul class="nav flex-column">
                        <li class="nav-item">
                            <a class="nav-link" href="../../dashboard.php">
                                <i class="fas fa-tachometer-alt me-2"></i>
                                لوحة التحكم
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="../supporters/supporters.php">
                                <i class="fas fa-users me-2"></i>
                                المؤيدين
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="../regions/regions.php">
                                <i class="fas fa-map-marked-alt me-2"></i>
                                المناطق
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="../admins/admins.php">
                                <i class="fas fa-user-tie me-2"></i>
                                الإداريين
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="../expenses/expenses.php">
                                <i class="fas fa-money-bill-wave me-2"></i>
                                المصروفات
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="../events/events.php">
                                <i class="fas fa-calendar-alt me-2"></i>
                                الفعاليات
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="../competitors/competitors.php">
                                <i class="fas fa-chess me-2"></i>
                                المنافسين
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link active" href="reports.php">
                                <i class="fas fa-chart-bar me-2"></i>
                                التقارير
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="../settings/settings.php">
                                <i class="fas fa-cog me-2"></i>
                                الإعدادات
                            </a>
                        </li>
                    </ul>
                </div>
            </nav>

            <!-- المحتوى الرئيسي -->
            <main class="col-md-9 ms-sm-auto col-lg-10 px-md-4">
                <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                    <h1 class="h2">
                        <i class="fas fa-chart-bar me-2"></i>
                        التقارير والإحصائيات
                    </h1>
                    <div class="btn-toolbar mb-2 mb-md-0">
                        <div class="btn-group me-2">
                            <button type="button" class="btn btn-success" onclick="exportReport()">
                                <i class="fas fa-file-excel"></i> تصدير التقرير
                            </button>
                            <button type="button" class="btn btn-danger" onclick="printReport()">
                                <i class="fas fa-print"></i> طباعة التقرير
                            </button>
                        </div>
                    </div>
                </div>

                <!-- الإحصائيات العامة -->
                <div class="row mb-4">
                    <div class="col-md-3">
                        <div class="card stats-card bg-primary text-white">
                            <div class="card-body">
                                <div class="d-flex justify-content-between">
                                    <div>
                                        <h4><?php echo number_format($total_supporters); ?></h4>
                                        <p class="mb-0">إجمالي المؤيدين</p>
                                    </div>
                                    <div class="align-self-center">
                                        <i class="fas fa-users fa-2x"></i>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="card stats-card bg-success text-white">
                            <div class="card-body">
                                <div class="d-flex justify-content-between">
                                    <div>
                                        <h4><?php echo number_format($total_regions); ?></h4>
                                        <p class="mb-0">المناطق</p>
                                    </div>
                                    <div class="align-self-center">
                                        <i class="fas fa-map-marked-alt fa-2x"></i>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="card stats-card bg-info text-white">
                            <div class="card-body">
                                <div class="d-flex justify-content-between">
                                    <div>
                                        <h4><?php echo number_format($total_admins); ?></h4>
                                        <p class="mb-0">الإداريين</p>
                                    </div>
                                    <div class="align-self-center">
                                        <i class="fas fa-user-tie fa-2x"></i>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="card stats-card bg-warning text-white">
                            <div class="card-body">
                                <div class="d-flex justify-content-between">
                                    <div>
                                        <h4><?php echo number_format($total_expenses); ?> د.ع</h4>
                                        <p class="mb-0">إجمالي المصروفات</p>
                                    </div>
                                    <div class="align-self-center">
                                        <i class="fas fa-money-bill-wave fa-2x"></i>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- الرسوم البيانية -->
                <div class="row">
                    <!-- المؤيدين حسب المنطقة -->
                    <div class="col-md-6">
                        <div class="card">
                            <div class="card-header">
                                <h5 class="mb-0">المؤيدين حسب المنطقة</h5>
                            </div>
                            <div class="card-body">
                                <div class="chart-container">
                                    <canvas id="supportersByRegionChart"></canvas>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- المؤيدين حسب الجنس -->
                    <div class="col-md-6">
                        <div class="card">
                            <div class="card-header">
                                <h5 class="mb-0">المؤيدين حسب الجنس</h5>
                            </div>
                            <div class="card-body">
                                <div class="chart-container">
                                    <canvas id="supportersByGenderChart"></canvas>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="row mt-4">
                    <!-- المصروفات حسب الفئة -->
                    <div class="col-md-6">
                        <div class="card">
                            <div class="card-header">
                                <h5 class="mb-0">المصروفات حسب الفئة</h5>
                            </div>
                            <div class="card-body">
                                <div class="chart-container">
                                    <canvas id="expensesByCategoryChart"></canvas>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- الفعاليات حسب الحالة -->
                    <div class="col-md-6">
                        <div class="card">
                            <div class="card-header">
                                <h5 class="mb-0">الفعاليات حسب الحالة</h5>
                            </div>
                            <div class="card-body">
                                <div class="chart-container">
                                    <canvas id="eventsByStatusChart"></canvas>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- جداول تفصيلية -->
                <div class="row mt-4">
                    <div class="col-12">
                        <div class="card">
                            <div class="card-header">
                                <h5 class="mb-0">تفاصيل المؤيدين حسب المنطقة</h5>
                            </div>
                            <div class="card-body">
                                <div class="table-responsive">
                                    <table class="table table-striped">
                                        <thead>
                                            <tr>
                                                <th>المنطقة</th>
                                                <th>عدد المؤيدين</th>
                                                <th>النسبة المئوية</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <?php foreach ($supporters_by_region as $region): ?>
                                            <tr>
                                                <td><?php echo htmlspecialchars($region['region_name']); ?></td>
                                                <td><?php echo number_format($region['supporters_count']); ?></td>
                                                <td>
                                                    <?php 
                                                    $percentage = $total_supporters > 0 ? ($region['supporters_count'] / $total_supporters) * 100 : 0;
                                                    echo number_format($percentage, 1) . '%';
                                                    ?>
                                                </td>
                                            </tr>
                                            <?php endforeach; ?>
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </main>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/chart.js@3.9.1/dist/chart.min.js"></script>
    <script>
        // بيانات الرسوم البيانية
        const supportersRegionData = <?php echo json_encode($supporters_by_region); ?>;
        const supportersGenderData = <?php echo json_encode($supporters_by_gender); ?>;
        const expensesCategoryData = <?php echo json_encode($expenses_by_category); ?>;
        const eventsStatusData = <?php echo json_encode($events_by_status); ?>;

        // رسم بياني للمؤيدين حسب المنطقة
        const ctx1 = document.getElementById('supportersByRegionChart').getContext('2d');
        new Chart(ctx1, {
            type: 'bar',
            data: {
                labels: supportersRegionData.map(item => item.region_name),
                datasets: [{
                    label: 'عدد المؤيدين',
                    data: supportersRegionData.map(item => item.supporters_count),
                    backgroundColor: 'rgba(54, 162, 235, 0.8)',
                    borderColor: 'rgba(54, 162, 235, 1)',
                    borderWidth: 1
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        display: false
                    }
                },
                scales: {
                    y: {
                        beginAtZero: true
                    }
                }
            }
        });

        // رسم بياني للمؤيدين حسب الجنس
        const ctx2 = document.getElementById('supportersByGenderChart').getContext('2d');
        new Chart(ctx2, {
            type: 'doughnut',
            data: {
                labels: supportersGenderData.map(item => item.gender === 'male' ? 'ذكر' : 'أنثى'),
                datasets: [{
                    data: supportersGenderData.map(item => item.count),
                    backgroundColor: ['#36A2EB', '#FF6384'],
                    borderWidth: 2
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false
            }
        });

        // رسم بياني للمصروفات حسب الفئة
        const ctx3 = document.getElementById('expensesByCategoryChart').getContext('2d');
        new Chart(ctx3, {
            type: 'pie',
            data: {
                labels: expensesCategoryData.map(item => item.category),
                datasets: [{
                    data: expensesCategoryData.map(item => item.total_amount),
                    backgroundColor: [
                        '#FF6384',
                        '#36A2EB',
                        '#FFCE56',
                        '#4BC0C0',
                        '#9966FF',
                        '#FF9F40'
                    ],
                    borderWidth: 2
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false
            }
        });

        // رسم بياني للفعاليات حسب الحالة
        const ctx4 = document.getElementById('eventsByStatusChart').getContext('2d');
        const statusLabels = {
            'planned': 'مخططة',
            'ongoing': 'جارية',
            'completed': 'مكتملة',
            'cancelled': 'ملغية'
        };
        
        new Chart(ctx4, {
            type: 'bar',
            data: {
                labels: eventsStatusData.map(item => statusLabels[item.status] || item.status),
                datasets: [{
                    label: 'عدد الفعاليات',
                    data: eventsStatusData.map(item => item.count),
                    backgroundColor: [
                        '#FFCE56',
                        '#36A2EB',
                        '#4BC0C0',
                        '#FF6384'
                    ],
                    borderWidth: 1
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        display: false
                    }
                },
                scales: {
                    y: {
                        beginAtZero: true
                    }
                }
            }
        });

        // دوال التصدير والطباعة
        function exportReport() {
            window.location.href = 'export_report.php';
        }

        function printReport() {
            window.print();
        }
    </script>
</body>
</html>
