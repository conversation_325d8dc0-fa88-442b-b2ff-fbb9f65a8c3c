<?php
// تعيين الترميز العربي
header('Content-Type: text/html; charset=utf-8');
ini_set('default_charset', 'utf-8');
mb_internal_encoding('UTF-8');

require_once '../../config/config.php';
require_once '../../config/database.php';

// التحقق من تسجيل الدخول
if (!isLoggedIn()) {
    redirect('../../login.php');
}

// التحقق من الصلاحيات - الإداريين للمرشح فقط
if (!isCandidate()) {
    showMessage('ليس لديك صلاحية للوصول إلى هذه الصفحة', 'error');
    redirect('../../dashboard.php');
}

$database = new Database();
$db = $database->getConnection();

// معالجة الطلبات
if ($_SERVER['REQUEST_METHOD'] == 'POST') {
    if (isset($_POST['action'])) {
        switch ($_POST['action']) {
            case 'add_admin':
                addAdmin();
                break;
            case 'edit_admin':
                editAdmin();
                break;
            case 'delete_admin':
                deleteAdmin();
                break;
            case 'toggle_status':
                toggleAdminStatus();
                break;
            case 'send_message':
                sendMessage();
                break;
        }
    }
}

function addAdmin() {
    global $db;

    $username = sanitize($_POST['username']);
    $password = $_POST['password'];
    $phone = sanitize($_POST['phone']);
    $full_name = sanitize($_POST['full_name']);
    $region_id = (int)$_POST['region_id'];

    // التحقق من صحة البيانات
    if (empty($username) || empty($password) || empty($phone) || empty($full_name)) {
        showMessage('يرجى ملء جميع الحقول المطلوبة', 'error');
        return;
    }

    // التحقق من عدم تكرار اسم المستخدم
    $stmt = $db->prepare("SELECT id FROM users WHERE username = ?");
    $stmt->execute([$username]);
    if ($stmt->fetch()) {
        showMessage('اسم المستخدم موجود مسبقاً', 'error');
        return;
    }

    // إضافة الإداري
    $password_hash = password_hash($password, PASSWORD_DEFAULT);
    $stmt = $db->prepare("INSERT INTO users (username, password, phone, full_name, user_type, region_id, status) VALUES (?, ?, ?, ?, 'admin', ?, 'active')");
    $result = $stmt->execute([$username, $password_hash, $phone, $full_name, $region_id]);

    if ($result) {
        showMessage('تم إضافة الإداري بنجاح', 'success');
        redirect('admins_new.php');
    } else {
        showMessage('حدث خطأ أثناء إضافة الإداري', 'error');
    }
}

function editAdmin() {
    global $db;

    $admin_id = (int)$_POST['admin_id'];
    $username = sanitize($_POST['username']);
    $phone = sanitize($_POST['phone']);
    $full_name = sanitize($_POST['full_name']);
    $region_id = (int)$_POST['region_id'];

    // التحقق من صحة البيانات
    if (empty($username) || empty($phone) || empty($full_name)) {
        showMessage('يرجى ملء جميع الحقول المطلوبة', 'error');
        return;
    }

    // التحقق من عدم تكرار اسم المستخدم
    $stmt = $db->prepare("SELECT id FROM users WHERE username = ? AND id != ?");
    $stmt->execute([$username, $admin_id]);
    if ($stmt->fetch()) {
        showMessage('اسم المستخدم موجود مسبقاً', 'error');
        return;
    }

    // تحديث البيانات
    $stmt = $db->prepare("UPDATE users SET username = ?, phone = ?, full_name = ?, region_id = ? WHERE id = ? AND user_type = 'admin'");
    $result = $stmt->execute([$username, $phone, $full_name, $region_id, $admin_id]);

    if ($result) {
        // تحديث كلمة المرور إذا تم إدخالها
        if (!empty($_POST['password'])) {
            $password_hash = password_hash($_POST['password'], PASSWORD_DEFAULT);
            $stmt = $db->prepare("UPDATE users SET password = ? WHERE id = ?");
            $stmt->execute([$password_hash, $admin_id]);
        }

        showMessage('تم تحديث بيانات الإداري بنجاح', 'success');
        redirect('admins_new.php');
    } else {
        showMessage('حدث خطأ أثناء تحديث البيانات', 'error');
    }
}

function deleteAdmin() {
    global $db;

    $admin_id = (int)$_POST['admin_id'];

    $stmt = $db->prepare("DELETE FROM users WHERE id = ? AND user_type = 'admin'");
    $result = $stmt->execute([$admin_id]);

    if ($result) {
        showMessage('تم حذف الإداري بنجاح', 'success');
    } else {
        showMessage('حدث خطأ أثناء حذف الإداري', 'error');
    }

    redirect('admins_new.php');
}

function toggleAdminStatus() {
    global $db;

    $admin_id = (int)$_POST['admin_id'];
    $new_status = $_POST['new_status'] === 'active' ? 'active' : 'inactive';

    $stmt = $db->prepare("UPDATE users SET status = ? WHERE id = ? AND user_type = 'admin'");
    $result = $stmt->execute([$new_status, $admin_id]);

    if ($result) {
        $status_text = $new_status === 'active' ? 'تفعيل' : 'إلغاء تفعيل';
        showMessage("تم $status_text الإداري بنجاح", 'success');
    } else {
        showMessage('حدث خطأ أثناء تحديث حالة الإداري', 'error');
    }

    redirect('admins_new.php');
}

function sendMessage() {
    global $db;

    $recipient_id = (int)$_POST['recipient_id'];
    $subject = sanitize($_POST['subject']);
    $content = sanitize($_POST['content']);
    $sender_id = $_SESSION['user_id'];

    // التحقق من صحة البيانات
    if (empty($subject) || empty($content)) {
        showMessage('يرجى ملء جميع الحقول المطلوبة', 'error');
        return;
    }

    // معالجة المرفق إذا وجد
    $attachment = null;
    if (isset($_FILES['attachment']) && $_FILES['attachment']['error'] == 0) {
        $upload_dir = '../../uploads/messages/';
        if (!file_exists($upload_dir)) {
            mkdir($upload_dir, 0755, true);
        }

        $file_extension = pathinfo($_FILES['attachment']['name'], PATHINFO_EXTENSION);
        $attachment = 'message_' . time() . '.' . $file_extension;

        if (move_uploaded_file($_FILES['attachment']['tmp_name'], $upload_dir . $attachment)) {
            // تم رفع الملف بنجاح
        } else {
            $attachment = null;
        }
    }

    // إرسال الرسالة
    $stmt = $db->prepare("INSERT INTO messages (sender_id, recipient_id, subject, content, attachment) VALUES (?, ?, ?, ?, ?)");
    $result = $stmt->execute([$sender_id, $recipient_id, $subject, $content, $attachment]);

    if ($result) {
        // إضافة إشعار للمستلم
        $stmt = $db->prepare("INSERT INTO notifications (user_id, title, message, type) VALUES (?, ?, ?, 'info')");
        $stmt->execute([$recipient_id, 'رسالة جديدة', "لديك رسالة جديدة من المرشح بعنوان: $subject"]);

        showMessage('تم إرسال الرسالة بنجاح', 'success');
    } else {
        showMessage('حدث خطأ أثناء إرسال الرسالة', 'error');
    }

    redirect('admins_new.php');
}

// جلب قائمة الإداريين
$search = isset($_GET['search']) ? sanitize($_GET['search']) : '';
$region_filter = isset($_GET['region']) ? (int)$_GET['region'] : 0;

$sql = "SELECT u.*, r.name as region_name FROM users u
        LEFT JOIN regions r ON u.region_id = r.id
        WHERE u.user_type = 'admin'";

$params = [];

if (!empty($search)) {
    $sql .= " AND (u.full_name LIKE ? OR u.username LIKE ? OR u.phone LIKE ?)";
    $search_term = '%' . $search . '%';
    $params = array_merge($params, [$search_term, $search_term, $search_term]);
}

if ($region_filter > 0) {
    $sql .= " AND u.region_id = ?";
    $params[] = $region_filter;
}

$sql .= " ORDER BY u.created_at DESC";

$stmt = $db->prepare($sql);
$stmt->execute($params);
$admins = $stmt->fetchAll();

// جلب قائمة المناطق
$regions = $db->query("SELECT * FROM regions ORDER BY name")->fetchAll();
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إدارة الإداريين - <?php echo SITE_NAME; ?></title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="../../assets/css/dashboard.css" rel="stylesheet">
    <style>
        .admin-card {
            transition: all 0.3s ease;
            border: none;
            border-radius: 15px;
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
        }
        .admin-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 8px 25px rgba(0,0,0,0.15);
        }
        .status-badge {
            font-size: 0.8rem;
            padding: 0.4rem 0.8rem;
            border-radius: 20px;
        }
        .actions-dropdown {
            min-width: 200px;
        }
    </style>
</head>
<body>
    <!-- شريط التنقل العلوي -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary fixed-top">
        <div class="container-fluid">
            <a class="navbar-brand" href="../../dashboard.php">
                <i class="fas fa-vote-yea me-2"></i>
                نظام إدارة الحملة الانتخابية
            </a>

            <div class="navbar-nav ms-auto">
                <div class="nav-item dropdown">
                    <a class="nav-link dropdown-toggle" href="#" id="userDropdown" role="button" data-bs-toggle="dropdown">
                        <i class="fas fa-user-circle me-1"></i>
                        <?php echo htmlspecialchars($_SESSION['full_name']); ?>
                    </a>
                    <ul class="dropdown-menu dropdown-menu-end">
                        <li><a class="dropdown-item" href="../../logout.php"><i class="fas fa-sign-out-alt me-2"></i>تسجيل الخروج</a></li>
                    </ul>
                </div>
            </div>
        </div>
    </nav>

    <!-- المحتوى الرئيسي -->
    <div class="container-fluid main-content">
        <div class="row">
            <!-- الشريط الجانبي -->
            <nav class="col-md-3 col-lg-2 d-md-block sidebar">
                <div class="position-sticky pt-3">
                    <ul class="nav flex-column">
                        <li class="nav-item">
                            <a class="nav-link" href="../../dashboard.php">
                                <i class="fas fa-tachometer-alt me-2"></i>
                                لوحة التحكم
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="../supporters/supporters.php">
                                <i class="fas fa-users me-2"></i>
                                المؤيدين
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="../regions/regions.php">
                                <i class="fas fa-map-marked-alt me-2"></i>
                                المناطق
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link active" href="admins_new.php">
                                <i class="fas fa-user-tie me-2"></i>
                                الإداريين
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="../expenses/expenses.php">
                                <i class="fas fa-money-bill-wave me-2"></i>
                                المصروفات
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="../events/events.php">
                                <i class="fas fa-calendar-alt me-2"></i>
                                الفعاليات
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="../competitors/competitors.php">
                                <i class="fas fa-chess me-2"></i>
                                المنافسين
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="../reports/reports.php">
                                <i class="fas fa-chart-bar me-2"></i>
                                التقارير
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="../settings/settings.php">
                                <i class="fas fa-cog me-2"></i>
                                الإعدادات
                            </a>
                        </li>
                    </ul>
                </div>
            </nav>

            <!-- المحتوى الرئيسي -->
            <main class="col-md-9 ms-sm-auto col-lg-10 px-md-4">
                <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                    <h1 class="h2">
                        <i class="fas fa-user-tie me-2"></i>
                        إدارة الإداريين
                    </h1>
                    <div class="btn-toolbar mb-2 mb-md-0">
                        <div class="btn-group me-2">
                            <button type="button" class="btn btn-success" data-bs-toggle="modal" data-bs-target="#addAdminModal">
                                <i class="fas fa-plus"></i> إضافة إداري جديد
                            </button>
                            <button type="button" class="btn btn-info" onclick="exportAdmins()">
                                <i class="fas fa-file-excel"></i> تصدير Excel
                            </button>
                        </div>
                    </div>
                </div>

                <?php displayMessage(); ?>

                <!-- فلاتر البحث -->
                <div class="card mb-4">
                    <div class="card-body">
                        <form method="GET" action="" class="row g-3">
                            <div class="col-md-4">
                                <label for="search" class="form-label">البحث</label>
                                <input type="text" class="form-control" id="search" name="search"
                                       value="<?php echo htmlspecialchars($search); ?>"
                                       placeholder="ابحث بالاسم أو اسم المستخدم أو الهاتف">
                            </div>
                            <div class="col-md-3">
                                <label for="region" class="form-label">المنطقة</label>
                                <select class="form-select" id="region" name="region">
                                    <option value="0">جميع المناطق</option>
                                    <?php foreach ($regions as $region): ?>
                                    <option value="<?php echo $region['id']; ?>"
                                            <?php echo $region_filter == $region['id'] ? 'selected' : ''; ?>>
                                        <?php echo htmlspecialchars($region['name']); ?>
                                    </option>
                                    <?php endforeach; ?>
                                </select>
                            </div>
                            <div class="col-md-2">
                                <label class="form-label">&nbsp;</label>
                                <div class="d-grid">
                                    <button type="submit" class="btn btn-primary">
                                        <i class="fas fa-search"></i> بحث
                                    </button>
                                </div>
                            </div>
                            <div class="col-md-2">
                                <label class="form-label">&nbsp;</label>
                                <div class="d-grid">
                                    <a href="admins_new.php" class="btn btn-secondary">
                                        <i class="fas fa-times"></i> مسح
                                    </a>
                                </div>
                            </div>
                        </form>
                    </div>
                </div>

                <!-- قائمة الإداريين -->
                <div class="row">
                    <?php if (empty($admins)): ?>
                        <div class="col-12">
                            <div class="alert alert-info text-center">
                                <i class="fas fa-info-circle fa-2x mb-3"></i>
                                <h5>لا يوجد إداريين</h5>
                                <p>لم يتم العثور على أي إداريين. يمكنك إضافة إداري جديد من الزر أعلاه.</p>
                            </div>
                        </div>
                    <?php else: ?>
                        <?php foreach ($admins as $admin): ?>
                        <div class="col-md-6 col-lg-4 mb-4">
                            <div class="card admin-card h-100">
                                <div class="card-body">
                                    <div class="d-flex justify-content-between align-items-start mb-3">
                                        <div>
                                            <h5 class="card-title mb-1"><?php echo htmlspecialchars($admin['full_name']); ?></h5>
                                            <small class="text-muted">@<?php echo htmlspecialchars($admin['username']); ?></small>
                                        </div>
                                        <span class="status-badge <?php echo $admin['status'] === 'active' ? 'bg-success' : 'bg-danger'; ?> text-white">
                                            <?php echo $admin['status'] === 'active' ? 'نشط' : 'غير نشط'; ?>
                                        </span>
                                    </div>

                                    <div class="mb-3">
                                        <p class="mb-1"><i class="fas fa-phone text-primary me-2"></i><?php echo htmlspecialchars($admin['phone']); ?></p>
                                        <p class="mb-1"><i class="fas fa-map-marker-alt text-success me-2"></i><?php echo htmlspecialchars($admin['region_name'] ?: 'غير محدد'); ?></p>
                                        <p class="mb-0"><i class="fas fa-calendar text-info me-2"></i><?php echo date('Y-m-d', strtotime($admin['created_at'])); ?></p>
                                    </div>

                                    <div class="dropdown">
                                        <button class="btn btn-outline-primary btn-sm dropdown-toggle w-100" type="button" data-bs-toggle="dropdown">
                                            <i class="fas fa-cogs me-2"></i>الإجراءات
                                        </button>
                                        <ul class="dropdown-menu actions-dropdown">
                                            <li>
                                                <a class="dropdown-item" href="#" onclick="editAdmin(<?php echo $admin['id']; ?>)">
                                                    <i class="fas fa-edit text-warning me-2"></i>تعديل
                                                </a>
                                            </li>
                                            <li>
                                                <a class="dropdown-item" href="#" onclick="sendMessage(<?php echo $admin['id']; ?>, '<?php echo htmlspecialchars($admin['full_name']); ?>')">
                                                    <i class="fas fa-envelope text-info me-2"></i>إرسال رسالة
                                                </a>
                                            </li>
                                            <li>
                                                <a class="dropdown-item" href="#" onclick="toggleStatus(<?php echo $admin['id']; ?>, '<?php echo $admin['status'] === 'active' ? 'inactive' : 'active'; ?>')">
                                                    <i class="fas fa-toggle-<?php echo $admin['status'] === 'active' ? 'off' : 'on'; ?> text-secondary me-2"></i>
                                                    <?php echo $admin['status'] === 'active' ? 'إلغاء التفعيل' : 'تفعيل'; ?>
                                                </a>
                                            </li>
                                            <li><hr class="dropdown-divider"></li>
                                            <li>
                                                <a class="dropdown-item text-danger" href="#" onclick="deleteAdmin(<?php echo $admin['id']; ?>, '<?php echo htmlspecialchars($admin['full_name']); ?>')">
                                                    <i class="fas fa-trash me-2"></i>حذف
                                                </a>
                                            </li>
                                        </ul>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <?php endforeach; ?>
                    <?php endif; ?>
                </div>
            </main>
        </div>
    </div>

    <!-- نافذة إضافة إداري -->
    <div class="modal fade" id="addAdminModal" tabindex="-1">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">إضافة إداري جديد</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <form method="POST" action="">
                    <input type="hidden" name="action" value="add_admin">
                    <div class="modal-body">
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="full_name" class="form-label">الاسم الكامل *</label>
                                    <input type="text" class="form-control" id="full_name" name="full_name" required>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="username" class="form-label">اسم المستخدم *</label>
                                    <input type="text" class="form-control" id="username" name="username" required>
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="password" class="form-label">كلمة المرور *</label>
                                    <input type="password" class="form-control" id="password" name="password" required>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="phone" class="form-label">رقم الهاتف *</label>
                                    <input type="tel" class="form-control" id="phone" name="phone" required>
                                </div>
                            </div>
                        </div>
                        <div class="mb-3">
                            <label for="region_id" class="form-label">المنطقة</label>
                            <select class="form-select" id="region_id" name="region_id">
                                <option value="0">بدون منطقة محددة</option>
                                <?php foreach ($regions as $region): ?>
                                <option value="<?php echo $region['id']; ?>"><?php echo htmlspecialchars($region['name']); ?></option>
                                <?php endforeach; ?>
                            </select>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                        <button type="submit" class="btn btn-success">إضافة الإداري</button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- نافذة تعديل إداري -->
    <div class="modal fade" id="editAdminModal" tabindex="-1">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">تعديل بيانات الإداري</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <form method="POST" action="">
                    <input type="hidden" name="action" value="edit_admin">
                    <input type="hidden" name="admin_id" id="edit_admin_id">
                    <div class="modal-body">
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="edit_full_name" class="form-label">الاسم الكامل *</label>
                                    <input type="text" class="form-control" id="edit_full_name" name="full_name" required>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="edit_username" class="form-label">اسم المستخدم *</label>
                                    <input type="text" class="form-control" id="edit_username" name="username" required>
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="edit_password" class="form-label">كلمة المرور الجديدة</label>
                                    <input type="password" class="form-control" id="edit_password" name="password">
                                    <small class="form-text text-muted">اتركها فارغة إذا لم ترد تغييرها</small>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="edit_phone" class="form-label">رقم الهاتف *</label>
                                    <input type="tel" class="form-control" id="edit_phone" name="phone" required>
                                </div>
                            </div>
                        </div>
                        <div class="mb-3">
                            <label for="edit_region_id" class="form-label">المنطقة</label>
                            <select class="form-select" id="edit_region_id" name="region_id">
                                <option value="0">بدون منطقة محددة</option>
                                <?php foreach ($regions as $region): ?>
                                <option value="<?php echo $region['id']; ?>"><?php echo htmlspecialchars($region['name']); ?></option>
                                <?php endforeach; ?>
                            </select>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                        <button type="submit" class="btn btn-warning">تحديث البيانات</button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- نافذة إرسال رسالة -->
    <div class="modal fade" id="sendMessageModal" tabindex="-1">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">إرسال رسالة إلى <span id="message_recipient_name"></span></h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <form method="POST" action="" enctype="multipart/form-data">
                    <input type="hidden" name="action" value="send_message">
                    <input type="hidden" name="recipient_id" id="message_recipient_id">
                    <div class="modal-body">
                        <div class="mb-3">
                            <label for="subject" class="form-label">موضوع الرسالة *</label>
                            <input type="text" class="form-control" id="subject" name="subject" required>
                        </div>
                        <div class="mb-3">
                            <label for="content" class="form-label">محتوى الرسالة *</label>
                            <textarea class="form-control" id="content" name="content" rows="5" required></textarea>
                        </div>
                        <div class="mb-3">
                            <label for="attachment" class="form-label">مرفق (اختياري)</label>
                            <input type="file" class="form-control" id="attachment" name="attachment">
                            <small class="form-text text-muted">يمكنك إرفاق ملف (صورة، مستند، إلخ)</small>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                        <button type="submit" class="btn btn-info">إرسال الرسالة</button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // تعديل إداري
        function editAdmin(adminId) {
            // جلب بيانات الإداري من الصفحة
            const adminCards = document.querySelectorAll('.admin-card');
            let adminData = null;

            adminCards.forEach(card => {
                const dropdown = card.querySelector('.dropdown-toggle');
                if (dropdown && dropdown.getAttribute('onclick') && dropdown.getAttribute('onclick').includes(adminId)) {
                    const fullName = card.querySelector('.card-title').textContent;
                    const username = card.querySelector('.text-muted').textContent.replace('@', '');
                    const phone = card.querySelector('.fa-phone').parentElement.textContent.trim();

                    adminData = {
                        id: adminId,
                        full_name: fullName,
                        username: username,
                        phone: phone
                    };
                }
            });

            if (adminData) {
                document.getElementById('edit_admin_id').value = adminData.id;
                document.getElementById('edit_full_name').value = adminData.full_name;
                document.getElementById('edit_username').value = adminData.username;
                document.getElementById('edit_phone').value = adminData.phone;

                const editModal = new bootstrap.Modal(document.getElementById('editAdminModal'));
                editModal.show();
            }
        }

        // إرسال رسالة
        function sendMessage(adminId, adminName) {
            document.getElementById('message_recipient_id').value = adminId;
            document.getElementById('message_recipient_name').textContent = adminName;

            const messageModal = new bootstrap.Modal(document.getElementById('sendMessageModal'));
            messageModal.show();
        }

        // تغيير حالة الإداري
        function toggleStatus(adminId, newStatus) {
            const statusText = newStatus === 'active' ? 'تفعيل' : 'إلغاء تفعيل';

            if (confirm(`هل أنت متأكد من ${statusText} هذا الإداري؟`)) {
                const form = document.createElement('form');
                form.method = 'POST';
                form.innerHTML = `
                    <input type="hidden" name="action" value="toggle_status">
                    <input type="hidden" name="admin_id" value="${adminId}">
                    <input type="hidden" name="new_status" value="${newStatus}">
                `;
                document.body.appendChild(form);
                form.submit();
            }
        }

        // حذف إداري
        function deleteAdmin(adminId, adminName) {
            if (confirm(`هل أنت متأكد من حذف الإداري "${adminName}"؟\nهذا الإجراء لا يمكن التراجع عنه.`)) {
                const form = document.createElement('form');
                form.method = 'POST';
                form.innerHTML = `
                    <input type="hidden" name="action" value="delete_admin">
                    <input type="hidden" name="admin_id" value="${adminId}">
                `;
                document.body.appendChild(form);
                form.submit();
            }
        }

        // تصدير Excel
        function exportAdmins() {
            window.location.href = 'export_admins.php';
        }
    </script>
</body>
</html>
