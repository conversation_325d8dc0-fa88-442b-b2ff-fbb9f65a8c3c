<?php
// إنشاء جداول المؤيدين والمرفقات
header('Content-Type: text/html; charset=utf-8');
ini_set('default_charset', 'utf-8');
mb_internal_encoding('UTF-8');

require_once '../config/config.php';
require_once '../config/database.php';

function createSupportersTables() {
    $tables = [];
    
    // جدول المؤيدين
    $tables['supporters'] = "CREATE TABLE IF NOT EXISTS supporters (
        id INT AUTO_INCREMENT PRIMARY KEY,
        full_name VARCHAR(100) NOT NULL,
        gender ENUM('male', 'female') NOT NULL,
        marital_status ENUM('single', 'married', 'divorced', 'widowed') DEFAULT 'single',
        birth_date DATE NOT NULL,
        age INT GENERATED ALWAYS AS (YEAR(CURDATE()) - YEAR(birth_date)) STORED,
        education ENUM('primary', 'secondary', 'diploma', 'bachelor', 'master', 'phd', 'other') DEFAULT 'secondary',
        profession VARCHAR(100),
        monthly_income DECIMAL(10,2),
        address TEXT NOT NULL,
        phone VARCHAR(15) NOT NULL,
        alternative_phone VARCHAR(15),
        email VARCHAR(100),
        voter_number VARCHAR(50),
        voting_center VARCHAR(100),
        region_id INT,
        district VARCHAR(100),
        neighborhood VARCHAR(100),
        family_members INT DEFAULT 1,
        support_level ENUM('strong', 'moderate', 'weak', 'undecided') DEFAULT 'moderate',
        contact_method ENUM('phone', 'visit', 'social_media', 'event') DEFAULT 'phone',
        last_contact_date DATE,
        next_contact_date DATE,
        notes TEXT,
        tags VARCHAR(255),
        added_by INT,
        status ENUM('active', 'inactive', 'moved', 'deceased') DEFAULT 'active',
        verification_status ENUM('pending', 'verified', 'rejected') DEFAULT 'pending',
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        FOREIGN KEY (region_id) REFERENCES regions(id) ON DELETE SET NULL,
        FOREIGN KEY (added_by) REFERENCES admins(id) ON DELETE SET NULL,
        INDEX idx_phone (phone),
        INDEX idx_voter_number (voter_number),
        INDEX idx_region_id (region_id),
        INDEX idx_added_by (added_by),
        INDEX idx_status (status),
        INDEX idx_support_level (support_level),
        INDEX idx_created_at (created_at),
        FULLTEXT idx_fulltext_search (full_name, address, profession, notes)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci";

    // جدول مرفقات المؤيدين
    $tables['supporter_attachments'] = "CREATE TABLE IF NOT EXISTS supporter_attachments (
        id INT AUTO_INCREMENT PRIMARY KEY,
        supporter_id INT NOT NULL,
        attachment_type ENUM('voter_id_front', 'voter_id_back', 'national_id_front', 'national_id_back', 'residence_card', 'passport', 'birth_certificate', 'other') NOT NULL,
        file_name VARCHAR(255) NOT NULL,
        original_name VARCHAR(255) NOT NULL,
        file_path VARCHAR(500) NOT NULL,
        file_size INT NOT NULL,
        file_type VARCHAR(100) NOT NULL,
        mime_type VARCHAR(100),
        uploaded_by INT,
        upload_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        notes TEXT,
        status ENUM('pending', 'approved', 'rejected') DEFAULT 'pending',
        reviewed_by INT,
        reviewed_at DATETIME,
        review_notes TEXT,
        FOREIGN KEY (supporter_id) REFERENCES supporters(id) ON DELETE CASCADE,
        FOREIGN KEY (uploaded_by) REFERENCES admins(id) ON DELETE SET NULL,
        FOREIGN KEY (reviewed_by) REFERENCES users(id) ON DELETE SET NULL,
        INDEX idx_supporter_id (supporter_id),
        INDEX idx_attachment_type (attachment_type),
        INDEX idx_status (status),
        INDEX idx_upload_date (upload_date)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci";

    // جدول تاريخ الاتصالات مع المؤيدين
    $tables['supporter_contacts'] = "CREATE TABLE IF NOT EXISTS supporter_contacts (
        id INT AUTO_INCREMENT PRIMARY KEY,
        supporter_id INT NOT NULL,
        admin_id INT NOT NULL,
        contact_type ENUM('phone_call', 'visit', 'message', 'event', 'social_media') NOT NULL,
        contact_date DATETIME NOT NULL,
        duration_minutes INT DEFAULT 0,
        result ENUM('successful', 'no_answer', 'busy', 'refused', 'wrong_number') DEFAULT 'successful',
        notes TEXT,
        follow_up_required BOOLEAN DEFAULT FALSE,
        follow_up_date DATE,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (supporter_id) REFERENCES supporters(id) ON DELETE CASCADE,
        FOREIGN KEY (admin_id) REFERENCES admins(id) ON DELETE CASCADE,
        INDEX idx_supporter_id (supporter_id),
        INDEX idx_admin_id (admin_id),
        INDEX idx_contact_date (contact_date),
        INDEX idx_contact_type (contact_type)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci";

    // جدول عائلات المؤيدين
    $tables['supporter_families'] = "CREATE TABLE IF NOT EXISTS supporter_families (
        id INT AUTO_INCREMENT PRIMARY KEY,
        supporter_id INT NOT NULL,
        family_member_name VARCHAR(100) NOT NULL,
        relationship ENUM('spouse', 'son', 'daughter', 'father', 'mother', 'brother', 'sister', 'other') NOT NULL,
        age INT,
        gender ENUM('male', 'female'),
        is_voter BOOLEAN DEFAULT FALSE,
        voter_number VARCHAR(50),
        support_level ENUM('strong', 'moderate', 'weak', 'opposed', 'undecided') DEFAULT 'undecided',
        notes TEXT,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (supporter_id) REFERENCES supporters(id) ON DELETE CASCADE,
        INDEX idx_supporter_id (supporter_id),
        INDEX idx_is_voter (is_voter),
        INDEX idx_support_level (support_level)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci";

    // جدول مجموعات المؤيدين
    $tables['supporter_groups'] = "CREATE TABLE IF NOT EXISTS supporter_groups (
        id INT AUTO_INCREMENT PRIMARY KEY,
        name VARCHAR(100) NOT NULL,
        description TEXT,
        group_type ENUM('region', 'profession', 'age', 'interest', 'custom') DEFAULT 'custom',
        created_by INT,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (created_by) REFERENCES admins(id) ON DELETE SET NULL,
        INDEX idx_group_type (group_type),
        INDEX idx_created_by (created_by)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci";

    // جدول ربط المؤيدين بالمجموعات
    $tables['supporter_group_members'] = "CREATE TABLE IF NOT EXISTS supporter_group_members (
        id INT AUTO_INCREMENT PRIMARY KEY,
        group_id INT NOT NULL,
        supporter_id INT NOT NULL,
        added_by INT,
        added_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (group_id) REFERENCES supporter_groups(id) ON DELETE CASCADE,
        FOREIGN KEY (supporter_id) REFERENCES supporters(id) ON DELETE CASCADE,
        FOREIGN KEY (added_by) REFERENCES admins(id) ON DELETE SET NULL,
        UNIQUE KEY unique_group_supporter (group_id, supporter_id),
        INDEX idx_group_id (group_id),
        INDEX idx_supporter_id (supporter_id)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci";

    return $tables;
}

function insertDefaultSupporterData() {
    $queries = [];
    
    // إدراج مجموعات افتراضية
    $queries[] = "INSERT IGNORE INTO supporter_groups (name, description, group_type) VALUES 
        ('المؤيدين الأقوياء', 'المؤيدين ذوي الدعم القوي', 'interest'),
        ('الشباب', 'المؤيدين من الفئة العمرية الشابة', 'age'),
        ('المهنيين', 'المؤيدين من أصحاب المهن المختلفة', 'profession'),
        ('النساء', 'المؤيدات من النساء', 'interest'),
        ('كبار السن', 'المؤيدين من كبار السن', 'age')";

    return $queries;
}

// تشغيل الدوال إذا تم استدعاء الملف مباشرة
if (basename($_SERVER['PHP_SELF']) == 'create_supporters_tables.php') {
    echo "<!DOCTYPE html>";
    echo "<html lang='ar' dir='rtl'>";
    echo "<head>";
    echo "<meta charset='UTF-8'>";
    echo "<meta name='viewport' content='width=device-width, initial-scale=1.0'>";
    echo "<title>إنشاء جداول المؤيدين</title>";
    echo "<link href='https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css' rel='stylesheet'>";
    echo "<link href='https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css' rel='stylesheet'>";
    echo "<style>";
    echo "body { font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); min-height: 100vh; padding: 2rem; }";
    echo ".system-card { background: white; border-radius: 15px; box-shadow: 0 10px 30px rgba(0,0,0,0.1); padding: 2rem; margin: 0 auto; max-width: 1000px; }";
    echo ".test-result { padding: 10px; margin: 5px 0; border-radius: 5px; }";
    echo ".test-success { background: #d4edda; border: 1px solid #c3e6cb; }";
    echo ".test-error { background: #f8d7da; border: 1px solid #f5c6cb; }";
    echo "</style>";
    echo "</head>";
    echo "<body>";

    echo "<div class='system-card'>";
    echo "<h1 class='text-center mb-4'><i class='fas fa-user-friends'></i> إنشاء جداول المؤيدين</h1>";

    if (isset($_POST['create_supporters_tables'])) {
        echo "<div class='alert alert-info'>";
        echo "<h5>جاري إنشاء جداول المؤيدين...</h5>";
        echo "</div>";

        try {
            // إنشاء الجداول
            $tables = createSupportersTables();
            foreach ($tables as $table_name => $sql) {
                try {
                    executeQuery($sql);
                    echo "<div class='test-result test-success'><i class='fas fa-check'></i> جدول $table_name: تم إنشاؤه بنجاح</div>";
                } catch (Exception $e) {
                    echo "<div class='test-result test-success'><i class='fas fa-info'></i> جدول $table_name: موجود مسبقاً</div>";
                }
            }

            // إدراج البيانات الافتراضية
            echo "<h6 class='mt-4'>إدراج البيانات الافتراضية:</h6>";
            $queries = insertDefaultSupporterData();
            foreach ($queries as $query) {
                try {
                    executeQuery($query);
                    echo "<div class='test-result test-success'><i class='fas fa-check'></i> تم إدراج البيانات الافتراضية</div>";
                } catch (Exception $e) {
                    echo "<div class='test-result test-success'><i class='fas fa-info'></i> البيانات موجودة مسبقاً</div>";
                }
            }

            echo "<div class='alert alert-success mt-4'>";
            echo "<h3><i class='fas fa-check-circle'></i> تم إنشاء جداول المؤيدين بنجاح!</h3>";
            echo "</div>";

        } catch (Exception $e) {
            echo "<div class='alert alert-danger'>";
            echo "<h5>خطأ في الإنشاء:</h5>";
            echo "<p>" . htmlspecialchars($e->getMessage()) . "</p>";
            echo "</div>";
        }

        echo "<div class='text-center mt-4'>";
        echo "<a href='create_messages_tables.php' class='btn btn-primary btn-lg me-2'><i class='fas fa-envelope'></i> إنشاء جداول الرسائل</a>";
        echo "<a href='../dashboard.php' class='btn btn-success btn-lg'><i class='fas fa-home'></i> الصفحة الرئيسية</a>";
        echo "</div>";

    } else {
        echo "<div class='alert alert-warning'>";
        echo "<h5><i class='fas fa-info-circle'></i> إنشاء جداول المؤيدين</h5>";
        echo "<p>سيتم إنشاء جميع الجداول المتعلقة بالمؤيدين والمرفقات</p>";
        echo "</div>";

        echo "<div class='card mb-4'>";
        echo "<div class='card-header bg-success text-white'>";
        echo "<h6><i class='fas fa-table'></i> الجداول التي سيتم إنشاؤها</h6>";
        echo "</div>";
        echo "<div class='card-body'>";
        echo "<div class='row'>";
        echo "<div class='col-md-6'>";
        echo "<ul>";
        echo "<li><strong>supporters:</strong> بيانات المؤيدين الكاملة</li>";
        echo "<li><strong>supporter_attachments:</strong> مرفقات المؤيدين</li>";
        echo "<li><strong>supporter_contacts:</strong> تاريخ الاتصالات</li>";
        echo "</ul>";
        echo "</div>";
        echo "<div class='col-md-6'>";
        echo "<ul>";
        echo "<li><strong>supporter_families:</strong> عائلات المؤيدين</li>";
        echo "<li><strong>supporter_groups:</strong> مجموعات المؤيدين</li>";
        echo "<li><strong>supporter_group_members:</strong> أعضاء المجموعات</li>";
        echo "</ul>";
        echo "</div>";
        echo "</div>";
        echo "</div>";
        echo "</div>";

        echo "<form method='POST' action=''>";
        echo "<div class='text-center'>";
        echo "<button type='submit' name='create_supporters_tables' class='btn btn-success btn-lg'>";
        echo "<i class='fas fa-database'></i> إنشاء جداول المؤيدين";
        echo "</button>";
        echo "</div>";
        echo "</form>";

        echo "<div class='text-center mt-3'>";
        echo "<a href='create_users_tables.php' class='btn btn-secondary me-2'><i class='fas fa-arrow-right'></i> جداول المستخدمين</a>";
        echo "<a href='../dashboard.php' class='btn btn-secondary'><i class='fas fa-home'></i> الصفحة الرئيسية</a>";
        echo "</div>";
    }

    echo "</div>";
    echo "</body>";
    echo "</html>";
}
?>
