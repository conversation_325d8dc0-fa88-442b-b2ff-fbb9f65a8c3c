<!-- نافذة إضافة مؤيد جديد -->
<?php if ($can_add): ?>
<div class="modal fade" id="addSupporterModal" tabindex="-1" aria-labelledby="addSupporterModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="addSupporterModalLabel">
                    <i class="fas fa-user-plus me-2"></i>
                    إضافة مؤيد جديد
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="إغلاق"></button>
            </div>
            <form method="POST" action="" enctype="multipart/form-data" class="supporter-form needs-validation" novalidate>
                <input type="hidden" name="action" value="add">
                <div class="modal-body">
                    <div class="row">
                        <!-- الاسم الكامل -->
                        <div class="col-md-6 mb-3">
                            <label for="full_name" class="form-label">الاسم الرباعي <span class="required">*</span></label>
                            <input type="text" class="form-control" id="full_name" name="full_name" required>
                            <div class="invalid-feedback">يرجى إدخال الاسم الرباعي</div>
                        </div>

                        <!-- الجنس -->
                        <div class="col-md-6 mb-3">
                            <label for="gender" class="form-label">الجنس <span class="required">*</span></label>
                            <select class="form-select" id="gender" name="gender" required>
                                <option value="">اختر الجنس</option>
                                <option value="male">ذكر</option>
                                <option value="female">أنثى</option>
                            </select>
                            <div class="invalid-feedback">يرجى اختيار الجنس</div>
                        </div>

                        <!-- الحالة الاجتماعية -->
                        <div class="col-md-6 mb-3">
                            <label for="marital_status" class="form-label">الحالة الاجتماعية <span class="required">*</span></label>
                            <select class="form-select" id="marital_status" name="marital_status" required>
                                <option value="">اختر الحالة الاجتماعية</option>
                                <option value="single">أعزب</option>
                                <option value="married">متزوج</option>
                                <option value="divorced">مطلق</option>
                                <option value="widowed">أرمل</option>
                            </select>
                            <div class="invalid-feedback">يرجى اختيار الحالة الاجتماعية</div>
                        </div>

                        <!-- تاريخ الميلاد -->
                        <div class="col-md-6 mb-3">
                            <label for="birth_date" class="form-label">تاريخ الميلاد <span class="required">*</span></label>
                            <input type="date" class="form-control" id="birth_date" name="birth_date" required>
                            <div class="invalid-feedback">يرجى إدخال تاريخ الميلاد</div>
                        </div>

                        <!-- التحصيل الدراسي -->
                        <div class="col-md-6 mb-3">
                            <label for="education" class="form-label">التحصيل الدراسي</label>
                            <select class="form-select" id="education" name="education">
                                <option value="">اختر التحصيل الدراسي</option>
                                <option value="ابتدائية">ابتدائية</option>
                                <option value="متوسطة">متوسطة</option>
                                <option value="إعدادية">إعدادية</option>
                                <option value="دبلوم">دبلوم</option>
                                <option value="بكالوريوس">بكالوريوس</option>
                                <option value="ماجستير">ماجستير</option>
                                <option value="دكتوراه">دكتوراه</option>
                                <option value="أخرى">أخرى</option>
                            </select>
                        </div>

                        <!-- المهنة -->
                        <div class="col-md-6 mb-3">
                            <label for="profession" class="form-label">المهنة</label>
                            <input type="text" class="form-control" id="profession" name="profession">
                        </div>

                        <!-- العنوان -->
                        <div class="col-12 mb-3">
                            <label for="address" class="form-label">العنوان <span class="required">*</span></label>
                            <textarea class="form-control" id="address" name="address" rows="2" required></textarea>
                            <div class="invalid-feedback">يرجى إدخال العنوان</div>
                        </div>

                        <!-- رقم الهاتف -->
                        <div class="col-md-6 mb-3">
                            <label for="phone" class="form-label">رقم الهاتف <span class="required">*</span></label>
                            <input type="tel" class="form-control" id="phone" name="phone" placeholder="07XXXXXXXXX" required>
                            <div class="invalid-feedback">يرجى إدخال رقم هاتف صحيح</div>
                        </div>

                        <!-- رقم الناخب -->
                        <div class="col-md-6 mb-3">
                            <label for="voter_number" class="form-label">رقم الناخب</label>
                            <input type="text" class="form-control" id="voter_number" name="voter_number">
                            <div class="invalid-feedback">رقم الناخب موجود مسبقاً</div>
                        </div>

                        <!-- المركز الانتخابي -->
                        <div class="col-md-6 mb-3">
                            <label for="voting_center" class="form-label">المركز الانتخابي</label>
                            <input type="text" class="form-control" id="voting_center" name="voting_center">
                        </div>

                        <!-- المنطقة -->
                        <div class="col-md-6 mb-3">
                            <label for="region_id" class="form-label">المنطقة <span class="required">*</span></label>
                            <select class="form-select" id="region_id" name="region_id" required>
                                <option value="">اختر المنطقة</option>
                                <?php foreach ($regions as $region): ?>
                                    <option value="<?php echo $region['id']; ?>">
                                        <?php echo htmlspecialchars($region['name']); ?>
                                    </option>
                                <?php endforeach; ?>
                            </select>
                            <div class="invalid-feedback">يرجى اختيار المنطقة</div>
                        </div>

                        <!-- الصورة -->
                        <div class="col-12 mb-3">
                            <label for="photo" class="form-label">الصورة الشخصية</label>
                            <div class="photo-upload-area">
                                <i class="fas fa-cloud-upload-alt fa-2x mb-2"></i>
                                <p>اسحب الصورة هنا أو انقر للاختيار</p>
                                <small class="text-muted">الحد الأقصى: 5 ميجابايت (JPG, PNG, GIF)</small>
                            </div>
                            <input type="file" class="form-control d-none" id="photo" name="photo" accept="image/*">
                            <img id="photoPreview" class="photo-preview mt-2" style="display: none;">
                        </div>

                        <!-- الملاحظات -->
                        <div class="col-12 mb-3">
                            <label for="notes" class="form-label">ملاحظات</label>
                            <textarea class="form-control" id="notes" name="notes" rows="3"></textarea>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                        <i class="fas fa-times me-2"></i>إلغاء
                    </button>
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-save me-2"></i>حفظ المؤيد
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>
<?php endif; ?>

<!-- نافذة تعديل المؤيد -->
<?php if ($can_edit): ?>
<div class="modal fade" id="editSupporterModal" tabindex="-1" aria-labelledby="editSupporterModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="editSupporterModalLabel">
                    <i class="fas fa-user-edit me-2"></i>
                    تعديل بيانات المؤيد
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="إغلاق"></button>
            </div>
            <form method="POST" action="" enctype="multipart/form-data" class="supporter-form needs-validation" novalidate>
                <input type="hidden" name="action" value="edit">
                <input type="hidden" id="editId" name="id">
                <div class="modal-body">
                    <div class="row">
                        <!-- الصورة الحالية -->
                        <div class="col-12 mb-3">
                            <label class="form-label">الصورة الحالية</label>
                            <div class="text-center">
                                <img id="editCurrentPhoto" class="supporter-photo" style="width: 100px; height: 100px; display: none;">
                            </div>
                        </div>

                        <!-- نفس الحقول كما في نموذج الإضافة مع معرفات مختلفة -->
                        <div class="col-md-6 mb-3">
                            <label for="editFullName" class="form-label">الاسم الرباعي <span class="required">*</span></label>
                            <input type="text" class="form-control" id="editFullName" name="full_name" required>
                        </div>

                        <div class="col-md-6 mb-3">
                            <label for="editGender" class="form-label">الجنس <span class="required">*</span></label>
                            <select class="form-select" id="editGender" name="gender" required>
                                <option value="male">ذكر</option>
                                <option value="female">أنثى</option>
                            </select>
                        </div>

                        <div class="col-md-6 mb-3">
                            <label for="editMaritalStatus" class="form-label">الحالة الاجتماعية <span class="required">*</span></label>
                            <select class="form-select" id="editMaritalStatus" name="marital_status" required>
                                <option value="single">أعزب</option>
                                <option value="married">متزوج</option>
                                <option value="divorced">مطلق</option>
                                <option value="widowed">أرمل</option>
                            </select>
                        </div>

                        <div class="col-md-6 mb-3">
                            <label for="editBirthDate" class="form-label">تاريخ الميلاد <span class="required">*</span></label>
                            <input type="date" class="form-control" id="editBirthDate" name="birth_date" required>
                        </div>

                        <div class="col-md-6 mb-3">
                            <label for="editEducation" class="form-label">التحصيل الدراسي</label>
                            <select class="form-select" id="editEducation" name="education">
                                <option value="">اختر التحصيل الدراسي</option>
                                <option value="ابتدائية">ابتدائية</option>
                                <option value="متوسطة">متوسطة</option>
                                <option value="إعدادية">إعدادية</option>
                                <option value="دبلوم">دبلوم</option>
                                <option value="بكالوريوس">بكالوريوس</option>
                                <option value="ماجستير">ماجستير</option>
                                <option value="دكتوراه">دكتوراه</option>
                                <option value="أخرى">أخرى</option>
                            </select>
                        </div>

                        <div class="col-md-6 mb-3">
                            <label for="editProfession" class="form-label">المهنة</label>
                            <input type="text" class="form-control" id="editProfession" name="profession">
                        </div>

                        <div class="col-12 mb-3">
                            <label for="editAddress" class="form-label">العنوان <span class="required">*</span></label>
                            <textarea class="form-control" id="editAddress" name="address" rows="2" required></textarea>
                        </div>

                        <div class="col-md-6 mb-3">
                            <label for="editPhone" class="form-label">رقم الهاتف <span class="required">*</span></label>
                            <input type="tel" class="form-control" id="editPhone" name="phone" required>
                        </div>

                        <div class="col-md-6 mb-3">
                            <label for="editVoterNumber" class="form-label">رقم الناخب</label>
                            <input type="text" class="form-control" id="editVoterNumber" name="voter_number">
                        </div>

                        <div class="col-md-6 mb-3">
                            <label for="editVotingCenter" class="form-label">المركز الانتخابي</label>
                            <input type="text" class="form-control" id="editVotingCenter" name="voting_center">
                        </div>

                        <div class="col-md-6 mb-3">
                            <label for="editRegionId" class="form-label">المنطقة <span class="required">*</span></label>
                            <select class="form-select" id="editRegionId" name="region_id" required>
                                <?php foreach ($regions as $region): ?>
                                    <option value="<?php echo $region['id']; ?>">
                                        <?php echo htmlspecialchars($region['name']); ?>
                                    </option>
                                <?php endforeach; ?>
                            </select>
                        </div>

                        <div class="col-12 mb-3">
                            <label for="editPhoto" class="form-label">تغيير الصورة</label>
                            <input type="file" class="form-control" id="editPhoto" name="photo" accept="image/*">
                            <small class="text-muted">اتركه فارغاً للاحتفاظ بالصورة الحالية</small>
                        </div>

                        <div class="col-12 mb-3">
                            <label for="editNotes" class="form-label">ملاحظات</label>
                            <textarea class="form-control" id="editNotes" name="notes" rows="3"></textarea>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                        <i class="fas fa-times me-2"></i>إلغاء
                    </button>
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-save me-2"></i>حفظ التغييرات
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>
<?php endif; ?>

<!-- نافذة عرض تفاصيل المؤيد -->
<div class="modal fade" id="viewSupporterModal" tabindex="-1" aria-labelledby="viewSupporterModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="viewSupporterModalLabel">
                    <i class="fas fa-user me-2"></i>
                    تفاصيل المؤيد
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="إغلاق"></button>
            </div>
            <div class="modal-body">
                <div class="supporter-details">
                    <div class="text-center mb-4">
                        <img id="viewPhoto" class="supporter-photo" style="width: 120px; height: 120px;">
                    </div>

                    <div class="row">
                        <div class="col-md-6">
                            <div class="detail-item">
                                <span class="detail-label">الاسم الكامل:</span>
                                <span class="detail-value" id="viewFullName"></span>
                            </div>
                            <div class="detail-item">
                                <span class="detail-label">الجنس:</span>
                                <span class="detail-value" id="viewGender"></span>
                            </div>
                            <div class="detail-item">
                                <span class="detail-label">الحالة الاجتماعية:</span>
                                <span class="detail-value" id="viewMaritalStatus"></span>
                            </div>
                            <div class="detail-item">
                                <span class="detail-label">تاريخ الميلاد:</span>
                                <span class="detail-value" id="viewBirthDate"></span>
                            </div>
                            <div class="detail-item">
                                <span class="detail-label">العمر:</span>
                                <span class="detail-value" id="viewAge"></span>
                            </div>
                            <div class="detail-item">
                                <span class="detail-label">التحصيل الدراسي:</span>
                                <span class="detail-value" id="viewEducation"></span>
                            </div>
                            <div class="detail-item">
                                <span class="detail-label">المهنة:</span>
                                <span class="detail-value" id="viewProfession"></span>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="detail-item">
                                <span class="detail-label">رقم الهاتف:</span>
                                <span class="detail-value" id="viewPhone"></span>
                            </div>
                            <div class="detail-item">
                                <span class="detail-label">رقم الناخب:</span>
                                <span class="detail-value" id="viewVoterNumber"></span>
                            </div>
                            <div class="detail-item">
                                <span class="detail-label">المركز الانتخابي:</span>
                                <span class="detail-value" id="viewVotingCenter"></span>
                            </div>
                            <div class="detail-item">
                                <span class="detail-label">المنطقة:</span>
                                <span class="detail-value" id="viewRegion"></span>
                            </div>
                            <div class="detail-item">
                                <span class="detail-label">أضيف بواسطة:</span>
                                <span class="detail-value" id="viewAddedBy"></span>
                            </div>
                            <div class="detail-item">
                                <span class="detail-label">تاريخ الإضافة:</span>
                                <span class="detail-value" id="viewCreatedAt"></span>
                            </div>
                        </div>
                    </div>

                    <div class="detail-item">
                        <span class="detail-label">العنوان:</span>
                        <span class="detail-value" id="viewAddress"></span>
                    </div>

                    <div class="detail-item">
                        <span class="detail-label">ملاحظات:</span>
                        <span class="detail-value" id="viewNotes"></span>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                    <i class="fas fa-times me-2"></i>إغلاق
                </button>
                <?php if ($can_edit): ?>
                <button type="button" class="btn btn-warning" onclick="editSupporter(document.getElementById('editId').value)">
                    <i class="fas fa-edit me-2"></i>تعديل
                </button>
                <?php endif; ?>
                <button type="button" class="btn btn-success" onclick="printSupporterCard(document.getElementById('editId').value)">
                    <i class="fas fa-print me-2"></i>طباعة بطاقة
                </button>
            </div>
        </div>
    </div>
</div>

<!-- نافذة استيراد البيانات -->
<?php if ($can_add): ?>
<div class="modal fade" id="importModal" tabindex="-1" aria-labelledby="importModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="importModalLabel">
                    <i class="fas fa-upload me-2"></i>
                    استيراد بيانات المؤيدين
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="إغلاق"></button>
            </div>
            <form method="POST" action="" enctype="multipart/form-data">
                <div class="modal-body">
                    <div class="mb-3">
                        <label for="importFile" class="form-label">ملف CSV</label>
                        <input type="file" class="form-control" id="importFile" name="import_file" accept=".csv,.txt" required>
                        <div class="form-text">يجب أن يكون الملف بصيغة CSV أو TXT</div>
                    </div>

                    <div class="alert alert-info">
                        <h6><i class="fas fa-info-circle me-2"></i>تعليمات الاستيراد:</h6>
                        <ul class="mb-0">
                            <li><strong>ترتيب الأعمدة:</strong> الاسم الكامل، الجنس، الحالة الاجتماعية، تاريخ الميلاد، التحصيل الدراسي، المهنة، العنوان، رقم الهاتف، رقم الناخب، المركز الانتخابي، المنطقة، ملاحظات</li>
                            <li><strong>الحقول المطلوبة:</strong> الاسم الكامل، الجنس، العنوان، رقم الهاتف</li>
                            <li><strong>الجنس:</strong> ذكر أو أنثى (أو male/female)</li>
                            <li><strong>رقم الهاتف:</strong> يجب أن يبدأ بـ 07 ويتكون من 11 رقم</li>
                            <li>الصف الأول يجب أن يحتوي على عناوين الأعمدة</li>
                        </ul>
                    </div>

                    <div class="mb-3">
                        <a href="template_csv.php" class="btn btn-outline-primary btn-sm">
                            <i class="fas fa-download me-2"></i>تحميل نموذج CSV
                        </a>
                    </div>

                    <div class="alert alert-secondary">
                        <h6><i class="fas fa-file-csv me-2"></i>مثال على ملف CSV:</h6>
                        <code style="font-size: 12px;">
                            أحمد محمد علي,ذكر,متزوج,1990-01-01,بكالوريوس,مهندس,بغداد - الكرادة,07701234567,123456789,مدرسة الكرادة,الكرادة,مؤيد نشط
                        </code>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                        <i class="fas fa-times me-2"></i>إلغاء
                    </button>
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-upload me-2"></i>استيراد البيانات
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>
<?php endif; ?>
