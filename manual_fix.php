<?php
// إصلاح يدوي سريع
if ($_POST) {
    $host = $_POST['host'] ?? 'localhost';
    $dbname = $_POST['dbname'] ?? '';
    $username = $_POST['username'] ?? '';
    $password = $_POST['password'] ?? '';
    
    try {
        $pdo = new PDO("mysql:host=$host;dbname=$dbname;charset=utf8mb4", $username, $password);
        $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
        
        // تحديث ملف database.php
        $content = "<?php
define('DB_HOST', '$host');
define('DB_NAME', '$dbname');
define('DB_USER', '$username');
define('DB_PASS', '$password');
define('DB_CHARSET', 'utf8mb4');

class Database {
    private \$host = DB_HOST;
    private \$db_name = DB_NAME;
    private \$username = DB_USER;
    private \$password = DB_PASS;
    public \$conn;

    public function getConnection() {
        \$this->conn = null;
        try {
            \$dsn = \"mysql:host=\" . \$this->host . \";dbname=\" . \$this->db_name . \";charset=utf8mb4\";
            \$this->conn = new PDO(\$dsn, \$this->username, \$this->password, array(
                PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
                PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC
            ));
        } catch(PDOException \$exception) {
            echo \"خطأ في الاتصال: \" . \$exception->getMessage();
        }
        return \$this->conn;
    }
}

function getDBConnection() {
    \$database = new Database();
    return \$database->getConnection();
}

function executeQuery(\$sql, \$params = []) {
    try {
        \$conn = getDBConnection();
        \$stmt = \$conn->prepare(\$sql);
        return \$stmt->execute(\$params);
    } catch(PDOException \$e) {
        return false;
    }
}

function fetchOne(\$sql, \$params = []) {
    try {
        \$conn = getDBConnection();
        \$stmt = \$conn->prepare(\$sql);
        \$stmt->execute(\$params);
        return \$stmt->fetch(PDO::FETCH_ASSOC);
    } catch(PDOException \$e) {
        return false;
    }
}

function fetchAll(\$sql, \$params = []) {
    try {
        \$conn = getDBConnection();
        \$stmt = \$conn->prepare(\$sql);
        \$stmt->execute(\$params);
        return \$stmt->fetchAll(PDO::FETCH_ASSOC);
    } catch(PDOException \$e) {
        return [];
    }
}

function getLastInsertId() {
    \$conn = getDBConnection();
    return \$conn->lastInsertId();
}

function showMessage(\$message, \$type = 'info') {
    if (\$type === 'error') {
        error_log(\$message);
    }
}
?>";
        
        file_put_contents('config/database.php', $content);
        
        echo "<!DOCTYPE html><html><head><meta charset='UTF-8'><title>نجح الإصلاح</title>";
        echo "<link href='https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css' rel='stylesheet'></head>";
        echo "<body style='padding:20px;'><div class='container'>";
        echo "<div class='alert alert-success'><h4>✅ تم الإصلاح بنجاح!</h4>";
        echo "<p>تم تحديث ملف قاعدة البيانات. جرب النظام الآن:</p>";
        echo "<a href='dashboard.php' class='btn btn-primary me-2'>لوحة تحكم المرشح</a>";
        echo "<a href='modules/admin/dashboard.php' class='btn btn-success'>لوحة تحكم الإداريين</a>";
        echo "</div></div></body></html>";
        exit;
        
    } catch (Exception $e) {
        echo "<!DOCTYPE html><html><head><meta charset='UTF-8'><title>فشل الإصلاح</title>";
        echo "<link href='https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css' rel='stylesheet'></head>";
        echo "<body style='padding:20px;'><div class='container'>";
        echo "<div class='alert alert-danger'><h4>❌ فشل الاتصال</h4>";
        echo "<p>الخطأ: " . $e->getMessage() . "</p>";
        echo "<a href='manual_fix.php' class='btn btn-warning'>حاول مرة أخرى</a>";
        echo "</div></div></body></html>";
        exit;
    }
}
?>
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <title>إصلاح يدوي سريع</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
</head>
<body style="padding:20px;">
<div class="container">
    <div class="card">
        <div class="card-header bg-warning text-dark">
            <h3>⚡ إصلاح يدوي سريع</h3>
        </div>
        <div class="card-body">
            <form method="POST">
                <div class="row">
                    <div class="col-md-6">
                        <label>Host:</label>
                        <input type="text" class="form-control" name="host" value="localhost" required>
                    </div>
                    <div class="col-md-6">
                        <label>Database:</label>
                        <input type="text" class="form-control" name="dbname" value="irjnpfzw_mr" required>
                    </div>
                </div>
                <div class="row mt-3">
                    <div class="col-md-6">
                        <label>Username:</label>
                        <input type="text" class="form-control" name="username" placeholder="اسم المستخدم الصحيح" required>
                    </div>
                    <div class="col-md-6">
                        <label>Password:</label>
                        <input type="password" class="form-control" name="password" placeholder="كلمة المرور الصحيحة" required>
                    </div>
                </div>
                <div class="text-center mt-4">
                    <button type="submit" class="btn btn-warning btn-lg">إصلاح الآن</button>
                </div>
            </form>
        </div>
    </div>
</div>
</body>
</html>
