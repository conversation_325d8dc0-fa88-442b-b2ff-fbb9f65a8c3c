<?php
// نظام إدارة الحملة الانتخابية - الصفحة الرئيسية

// التحقق من وجود ملف الإعدادات
if (!file_exists('config/config.php')) {
    // عرض صفحة التثبيت
    ?>
    <!DOCTYPE html>
    <html lang="ar" dir="rtl">
    <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>نظام إدارة الحملة الانتخابية</title>
        <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
        <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
        <style>
            body {
                background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                min-height: 100vh;
                font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
                display: flex;
                align-items: center;
                justify-content: center;
            }
            .welcome-card {
                background: rgba(255, 255, 255, 0.95);
                border-radius: 20px;
                box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
                backdrop-filter: blur(10px);
                border: 1px solid rgba(255, 255, 255, 0.2);
                overflow: hidden;
                max-width: 600px;
                width: 100%;
                margin: 20px;
            }
            .welcome-header {
                background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                color: white;
                padding: 2rem;
                text-align: center;
            }
            .welcome-body {
                padding: 2rem;
            }
            .btn-custom {
                background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                border: none;
                color: white;
                padding: 12px 30px;
                border-radius: 25px;
                font-weight: 600;
                transition: all 0.3s ease;
                text-decoration: none;
                display: inline-block;
                margin: 10px;
            }
            .btn-custom:hover {
                transform: translateY(-2px);
                box-shadow: 0 5px 15px rgba(0,0,0,0.2);
                color: white;
            }
        </style>
    </head>
    <body>
        <div class="welcome-card">
            <div class="welcome-header">
                <i class="fas fa-vote-yea fa-3x mb-3"></i>
                <h1>نظام إدارة الحملة الانتخابية</h1>
                <p class="mb-0">مرحباً بك في النظام الشامل لإدارة الحملات الانتخابية</p>
            </div>
            <div class="welcome-body">
                <div class="alert alert-info">
                    <i class="fas fa-info-circle me-2"></i>
                    <strong>مرحباً!</strong> يبدو أن النظام لم يتم تثبيته بعد.
                </div>

                <div class="text-center">
                    <h5>اختر أحد الخيارات التالية:</h5>

                    <a href="setup.php" class="btn-custom">
                        <i class="fas fa-cog me-2"></i>
                        معلومات الإعداد
                    </a>

                    <a href="install.php" class="btn-custom">
                        <i class="fas fa-download me-2"></i>
                        بدء التثبيت
                    </a>

                    <a href="login.php" class="btn-custom">
                        <i class="fas fa-sign-in-alt me-2"></i>
                        تسجيل الدخول
                    </a>
                </div>

                <div class="mt-4">
                    <h6>ميزات النظام:</h6>
                    <ul class="list-unstyled">
                        <li><i class="fas fa-check text-success me-2"></i> إدارة شاملة للمؤيدين</li>
                        <li><i class="fas fa-check text-success me-2"></i> إدارة المناطق الانتخابية</li>
                        <li><i class="fas fa-check text-success me-2"></i> تتبع المصروفات والفعاليات</li>
                        <li><i class="fas fa-check text-success me-2"></i> تقارير وإحصائيات متقدمة</li>
                        <li><i class="fas fa-check text-success me-2"></i> واجهة متجاوبة مع الجوال</li>
                        <li><i class="fas fa-check text-success me-2"></i> دعم كامل للغة العربية</li>
                    </ul>
                </div>
            </div>
        </div>
    </body>
    </html>
    <?php
    exit;
}

// تضمين ملف الإعدادات
require_once 'config/config.php';

// التحقق من تسجيل الدخول
if (isLoggedIn()) {
    // إعادة توجيه حسب نوع المستخدم
    if (isCandidate()) {
        header('Location: dashboard.php');
        exit;
    } else {
        header('Location: dashboard.php');
        exit;
    }
}

// عرض صفحة تسجيل الدخول
header('Location: login.php');
exit;
?>
