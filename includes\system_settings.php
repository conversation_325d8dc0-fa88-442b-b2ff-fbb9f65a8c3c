<?php
// إعدادات النظام والثوابت

// إعدادات قاعدة البيانات
define('DB_HOST', 'localhost');
define('DB_NAME', 'campaign_db');
define('DB_USER', 'root');
define('DB_PASS', '');
define('DB_CHARSET', 'utf8mb4');

// إعدادات النظام العامة
define('SYSTEM_NAME', 'نظام إدارة الحملة الانتخابية');
define('SYSTEM_VERSION', '2.0.0');
define('SYSTEM_AUTHOR', 'فريق التطوير');
define('SYSTEM_EMAIL', '<EMAIL>');
define('SYSTEM_PHONE', '07701234567');

// إعدادات الأمان
define('SESSION_TIMEOUT', 3600); // ساعة واحدة
define('MAX_LOGIN_ATTEMPTS', 5);
define('PASSWORD_MIN_LENGTH', 8);
define('ENCRYPTION_KEY', 'your-secret-encryption-key-here');
define('JWT_SECRET', 'your-jwt-secret-key-here');

// إعدادات الملفات
define('UPLOAD_MAX_SIZE', 10 * 1024 * 1024); // 10MB
define('ALLOWED_IMAGE_TYPES', ['jpg', 'jpeg', 'png', 'gif', 'webp']);
define('ALLOWED_DOCUMENT_TYPES', ['pdf', 'doc', 'docx', 'xls', 'xlsx', 'txt']);
define('ALLOWED_ARCHIVE_TYPES', ['zip', 'rar', '7z']);

// مسارات الملفات
define('UPLOAD_PATH', __DIR__ . '/../uploads/');
define('SUPPORTERS_UPLOAD_PATH', UPLOAD_PATH . 'supporters/');
define('REQUESTS_UPLOAD_PATH', UPLOAD_PATH . 'requests/');
define('MESSAGES_UPLOAD_PATH', UPLOAD_PATH . 'messages/');
define('REPORTS_UPLOAD_PATH', UPLOAD_PATH . 'reports/');
define('TEMP_UPLOAD_PATH', UPLOAD_PATH . 'temp/');

// إعدادات الإشعارات
define('NOTIFICATION_RETENTION_DAYS', 30);
define('ACTIVITY_LOG_RETENTION_DAYS', 90);
define('MAX_NOTIFICATIONS_PER_USER', 100);

// إعدادات التقارير
define('REPORT_DEADLINE_DAYS', 7); // أسبوع لتقديم التقرير
define('REPORT_REMINDER_DAYS', 2); // تذكير قبل يومين من الموعد النهائي

// إعدادات المطالب
define('REQUEST_AUTO_EXPIRE_DAYS', 90);
define('REQUEST_FOLLOW_UP_DAYS', 7);
define('MAX_REQUEST_ATTACHMENTS', 5);

// إعدادات الرسائل
define('MESSAGE_AUTO_DELETE_DAYS', 365);
define('MAX_MESSAGE_ATTACHMENTS', 3);
define('MESSAGE_MAX_LENGTH', 5000);

// إعدادات المؤيدين
define('SUPPORTER_DUPLICATE_CHECK_FIELDS', ['phone', 'voter_number']);
define('SUPPORTER_CONTACT_REMINDER_DAYS', 30);

// أنواع المطالب
define('REQUEST_TYPES', [
    'financial' => 'مطالب مالية',
    'medical' => 'مطالب طبية',
    'educational' => 'مطالب تعليمية',
    'employment' => 'مطالب وظيفية',
    'housing' => 'مطالب سكنية',
    'legal' => 'مطالب قانونية',
    'social' => 'مطالب اجتماعية',
    'infrastructure' => 'مطالب البنية التحتية',
    'documentation' => 'مطالب الوثائق',
    'other' => 'مطالب أخرى'
]);

// حالات المطالب
define('REQUEST_STATUSES', [
    'pending' => 'في الانتظار',
    'received' => 'تم الاستلام',
    'under_review' => 'قيد المراجعة',
    'in_progress' => 'قيد التنفيذ',
    'completed' => 'مكتمل',
    'rejected' => 'مرفوض',
    'cancelled' => 'ملغي'
]);

// أولويات المطالب
define('REQUEST_PRIORITIES', [
    'low' => 'منخفضة',
    'normal' => 'عادية',
    'high' => 'عالية',
    'urgent' => 'عاجلة'
]);

// مستويات الدعم
define('SUPPORT_LEVELS', [
    'strong' => 'دعم قوي',
    'moderate' => 'دعم متوسط',
    'weak' => 'دعم ضعيف',
    'undecided' => 'غير محدد'
]);

// أدوار الإداريين
define('ADMIN_ROLES', [
    'admin' => 'إداري',
    'supervisor' => 'مشرف',
    'coordinator' => 'منسق'
]);

// حالات الإداريين
define('ADMIN_STATUSES', [
    'active' => 'نشط',
    'inactive' => 'غير نشط',
    'suspended' => 'معلق'
]);

// صلاحيات النظام
define('SYSTEM_PERMISSIONS', [
    'add_supporters' => 'إضافة مؤيدين',
    'edit_supporters' => 'تعديل المؤيدين',
    'delete_supporters' => 'حذف المؤيدين',
    'view_supporters' => 'عرض المؤيدين',
    'export_supporters' => 'تصدير المؤيدين',
    'import_supporters' => 'استيراد المؤيدين',
    'send_messages' => 'إرسال الرسائل',
    'receive_messages' => 'استقبال الرسائل',
    'submit_requests' => 'تقديم المطالب',
    'manage_requests' => 'إدارة المطالب',
    'create_reports' => 'إنشاء التقارير',
    'view_reports' => 'عرض التقارير',
    'approve_reports' => 'اعتماد التقارير',
    'view_statistics' => 'عرض الإحصائيات',
    'manage_admins' => 'إدارة الإداريين',
    'system_settings' => 'إعدادات النظام'
]);

// أنواع الإشعارات
define('NOTIFICATION_TYPES', [
    'info' => 'معلومات',
    'success' => 'نجح',
    'warning' => 'تحذير',
    'error' => 'خطأ',
    'new_supporter' => 'مؤيد جديد',
    'new_request' => 'مطلب جديد',
    'new_message' => 'رسالة جديدة',
    'new_report' => 'تقرير جديد',
    'system' => 'نظام',
    'reminder' => 'تذكير',
    'urgent' => 'عاجل'
]);

// فئات الإشعارات
define('NOTIFICATION_CATEGORIES', [
    'system' => 'النظام',
    'supporter' => 'المؤيدين',
    'request' => 'المطالب',
    'message' => 'الرسائل',
    'report' => 'التقارير',
    'admin' => 'الإداريين',
    'general' => 'عام'
]);

// أولويات الإشعارات
define('NOTIFICATION_PRIORITIES', [
    'low' => 'منخفضة',
    'normal' => 'عادية',
    'high' => 'عالية',
    'urgent' => 'عاجلة'
]);

// أنواع الأنشطة
define('ACTIVITY_TYPES', [
    'create' => 'إنشاء',
    'update' => 'تحديث',
    'delete' => 'حذف',
    'login' => 'تسجيل دخول',
    'logout' => 'تسجيل خروج',
    'view' => 'عرض',
    'export' => 'تصدير',
    'import' => 'استيراد',
    'approve' => 'اعتماد',
    'reject' => 'رفض',
    'send' => 'إرسال',
    'receive' => 'استقبال'
]);

// مستويات خطورة الأنشطة
define('ACTIVITY_SEVERITY_LEVELS', [
    'low' => 'منخفضة',
    'medium' => 'متوسطة',
    'high' => 'عالية',
    'critical' => 'حرجة'
]);

// فئات الأنشطة
define('ACTIVITY_CATEGORIES', [
    'authentication' => 'المصادقة',
    'data_modification' => 'تعديل البيانات',
    'file_operation' => 'عمليات الملفات',
    'communication' => 'التواصل',
    'system' => 'النظام',
    'security' => 'الأمان'
]);

// إعدادات التصدير
define('EXPORT_FORMATS', [
    'excel' => 'Excel',
    'csv' => 'CSV',
    'pdf' => 'PDF'
]);

// إعدادات الاستيراد
define('IMPORT_FORMATS', [
    'csv' => 'CSV',
    'excel' => 'Excel'
]);

// إعدادات التاريخ والوقت
define('DEFAULT_TIMEZONE', 'Asia/Baghdad');
define('DATE_FORMAT', 'Y-m-d');
define('DATETIME_FORMAT', 'Y-m-d H:i:s');
define('DISPLAY_DATE_FORMAT', 'd/m/Y');
define('DISPLAY_DATETIME_FORMAT', 'd/m/Y H:i');

// إعدادات اللغة
define('DEFAULT_LANGUAGE', 'ar');
define('SUPPORTED_LANGUAGES', ['ar', 'en']);

// إعدادات الألوان
define('THEME_COLORS', [
    'primary' => '#007bff',
    'secondary' => '#6c757d',
    'success' => '#28a745',
    'danger' => '#dc3545',
    'warning' => '#ffc107',
    'info' => '#17a2b8',
    'light' => '#f8f9fa',
    'dark' => '#343a40'
]);

// إعدادات الرسوم البيانية
define('CHART_COLORS', [
    '#FF6384', '#36A2EB', '#FFCE56', '#4BC0C0',
    '#9966FF', '#FF9F40', '#FF6384', '#C9CBCF'
]);

// إعدادات التصفح
define('DEFAULT_PAGE_SIZE', 20);
define('MAX_PAGE_SIZE', 100);
define('SEARCH_MIN_LENGTH', 2);

// إعدادات الكاش
define('CACHE_ENABLED', true);
define('CACHE_DURATION', 3600); // ساعة واحدة
define('CACHE_PATH', __DIR__ . '/../cache/');

// إعدادات اللوجات
define('LOG_ENABLED', true);
define('LOG_LEVEL', 'INFO'); // DEBUG, INFO, WARNING, ERROR
define('LOG_PATH', __DIR__ . '/../logs/');
define('LOG_MAX_SIZE', 10 * 1024 * 1024); // 10MB

// إعدادات النسخ الاحتياطي
define('BACKUP_ENABLED', true);
define('BACKUP_PATH', __DIR__ . '/../backups/');
define('BACKUP_RETENTION_DAYS', 30);

// إعدادات الصيانة
define('MAINTENANCE_MODE', false);
define('MAINTENANCE_MESSAGE', 'النظام قيد الصيانة، يرجى المحاولة لاحقاً');

// إعدادات الأداء
define('ENABLE_COMPRESSION', true);
define('ENABLE_CACHING', true);
define('ENABLE_MINIFICATION', false);

// معلومات الاتصال
define('CONTACT_INFO', [
    'address' => 'بغداد، العراق',
    'phone' => '07701234567',
    'email' => '<EMAIL>',
    'website' => 'https://campaign.com',
    'facebook' => 'https://facebook.com/campaign',
    'twitter' => 'https://twitter.com/campaign',
    'instagram' => 'https://instagram.com/campaign'
]);

// إعدادات البريد الإلكتروني
define('MAIL_SETTINGS', [
    'host' => 'smtp.gmail.com',
    'port' => 587,
    'username' => '<EMAIL>',
    'password' => 'your-app-password',
    'encryption' => 'tls',
    'from_name' => 'نظام إدارة الحملة',
    'from_email' => '<EMAIL>'
]);

// إعدادات الرسائل النصية
define('SMS_SETTINGS', [
    'provider' => 'local', // local, twilio, nexmo
    'api_key' => '********-api-key',
    'sender_name' => 'Campaign'
]);

// دالة للحصول على إعداد معين
function getSetting($key, $default = null) {
    static $settings = null;
    
    if ($settings === null) {
        // تحميل الإعدادات من قاعدة البيانات أو ملف التكوين
        $settings = [];
        
        // يمكن إضافة منطق لتحميل الإعدادات من قاعدة البيانات
        try {
            if (function_exists('fetchAll')) {
                $db_settings = fetchAll("SELECT setting_key, setting_value FROM system_settings");
                foreach ($db_settings as $setting) {
                    $settings[$setting['setting_key']] = $setting['setting_value'];
                }
            }
        } catch (Exception $e) {
            // تجاهل الأخطاء إذا لم تكن قاعدة البيانات متاحة
        }
    }
    
    return $settings[$key] ?? $default;
}

// دالة لحفظ إعداد
function saveSetting($key, $value) {
    try {
        if (function_exists('executeQuery')) {
            executeQuery(
                "INSERT INTO system_settings (setting_key, setting_value) VALUES (?, ?) 
                 ON DUPLICATE KEY UPDATE setting_value = VALUES(setting_value)",
                [$key, $value]
            );
            return true;
        }
    } catch (Exception $e) {
        error_log("خطأ في حفظ الإعداد: " . $e->getMessage());
    }
    
    return false;
}

// تعيين المنطقة الزمنية
date_default_timezone_set(DEFAULT_TIMEZONE);

// تعيين ترميز الأحرف
mb_internal_encoding('UTF-8');
mb_http_output('UTF-8');
mb_regex_encoding('UTF-8');
?>
