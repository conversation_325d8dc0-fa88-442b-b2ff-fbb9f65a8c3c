<?php
// إنشاء جداول مطالب المؤيدين والمرفقات
header('Content-Type: text/html; charset=utf-8');
ini_set('default_charset', 'utf-8');
mb_internal_encoding('UTF-8');

require_once '../config/config.php';
require_once '../config/database.php';

function createRequestsTables() {
    $tables = [];
    
    // جدول مطالب المؤيدين
    $tables['supporter_requests'] = "CREATE TABLE IF NOT EXISTS supporter_requests (
        id INT AUTO_INCREMENT PRIMARY KEY,
        request_number VARCHAR(20) UNIQUE NOT NULL,
        admin_id INT NOT NULL,
        supporter_id INT,
        supporter_name VARCHAR(100) NOT NULL,
        supporter_phone VARCHAR(15) NOT NULL,
        supporter_address TEXT,
        request_type ENUM('financial', 'medical', 'educational', 'employment', 'housing', 'legal', 'social', 'infrastructure', 'documentation', 'other') NOT NULL,
        category ENUM('urgent', 'important', 'normal', 'low') DEFAULT 'normal',
        title VARCHAR(200) NOT NULL,
        description TEXT NOT NULL,
        amount_requested DECIMAL(10,2),
        currency ENUM('IQD', 'USD') DEFAULT 'IQD',
        priority ENUM('low', 'normal', 'high', 'urgent') DEFAULT 'normal',
        status ENUM('pending', 'received', 'under_review', 'in_progress', 'completed', 'rejected', 'cancelled') DEFAULT 'pending',
        urgency_level ENUM('low', 'medium', 'high', 'critical') DEFAULT 'medium',
        expected_completion_date DATE,
        actual_completion_date DATE,
        management_response TEXT,
        rejection_reason TEXT,
        management_notes TEXT,
        follow_up_required BOOLEAN DEFAULT FALSE,
        follow_up_date DATE,
        beneficiary_count INT DEFAULT 1,
        location_details TEXT,
        contact_person VARCHAR(100),
        contact_phone VARCHAR(15),
        submitted_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        reviewed_at DATETIME,
        completed_at DATETIME,
        reviewed_by INT,
        approved_by INT,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        FOREIGN KEY (admin_id) REFERENCES admins(id) ON DELETE CASCADE,
        FOREIGN KEY (supporter_id) REFERENCES supporters(id) ON DELETE SET NULL,
        FOREIGN KEY (reviewed_by) REFERENCES users(id) ON DELETE SET NULL,
        FOREIGN KEY (approved_by) REFERENCES users(id) ON DELETE SET NULL,
        INDEX idx_request_number (request_number),
        INDEX idx_admin_id (admin_id),
        INDEX idx_supporter_id (supporter_id),
        INDEX idx_status (status),
        INDEX idx_request_type (request_type),
        INDEX idx_priority (priority),
        INDEX idx_submitted_at (submitted_at),
        FULLTEXT idx_fulltext_search (title, description, supporter_name)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci";

    // جدول مرفقات المطالب
    $tables['request_attachments'] = "CREATE TABLE IF NOT EXISTS request_attachments (
        id INT AUTO_INCREMENT PRIMARY KEY,
        request_id INT NOT NULL,
        file_name VARCHAR(255) NOT NULL,
        original_name VARCHAR(255) NOT NULL,
        file_path VARCHAR(500) NOT NULL,
        file_size INT NOT NULL,
        file_type VARCHAR(100) NOT NULL,
        mime_type VARCHAR(100),
        uploaded_by INT,
        upload_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        attachment_type ENUM('document', 'image', 'medical_report', 'certificate', 'invoice', 'id_copy', 'proof', 'other') DEFAULT 'document',
        description TEXT,
        is_required BOOLEAN DEFAULT FALSE,
        status ENUM('pending', 'approved', 'rejected') DEFAULT 'pending',
        reviewed_by INT,
        reviewed_at DATETIME,
        review_notes TEXT,
        FOREIGN KEY (request_id) REFERENCES supporter_requests(id) ON DELETE CASCADE,
        FOREIGN KEY (uploaded_by) REFERENCES admins(id) ON DELETE SET NULL,
        FOREIGN KEY (reviewed_by) REFERENCES users(id) ON DELETE SET NULL,
        INDEX idx_request_id (request_id),
        INDEX idx_attachment_type (attachment_type),
        INDEX idx_status (status),
        INDEX idx_upload_date (upload_date)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci";

    // جدول تاريخ حالات المطالب
    $tables['request_status_history'] = "CREATE TABLE IF NOT EXISTS request_status_history (
        id INT AUTO_INCREMENT PRIMARY KEY,
        request_id INT NOT NULL,
        old_status ENUM('pending', 'received', 'under_review', 'in_progress', 'completed', 'rejected', 'cancelled'),
        new_status ENUM('pending', 'received', 'under_review', 'in_progress', 'completed', 'rejected', 'cancelled') NOT NULL,
        changed_by INT NOT NULL,
        change_reason TEXT,
        notes TEXT,
        changed_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (request_id) REFERENCES supporter_requests(id) ON DELETE CASCADE,
        FOREIGN KEY (changed_by) REFERENCES users(id) ON DELETE CASCADE,
        INDEX idx_request_id (request_id),
        INDEX idx_new_status (new_status),
        INDEX idx_changed_at (changed_at)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci";

    // جدول تعليقات المطالب
    $tables['request_comments'] = "CREATE TABLE IF NOT EXISTS request_comments (
        id INT AUTO_INCREMENT PRIMARY KEY,
        request_id INT NOT NULL,
        commenter_type ENUM('admin', 'management', 'candidate') NOT NULL,
        commenter_id INT NOT NULL,
        comment TEXT NOT NULL,
        comment_type ENUM('note', 'question', 'update', 'approval', 'rejection') DEFAULT 'note',
        is_internal BOOLEAN DEFAULT FALSE,
        parent_comment_id INT NULL,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        FOREIGN KEY (request_id) REFERENCES supporter_requests(id) ON DELETE CASCADE,
        FOREIGN KEY (parent_comment_id) REFERENCES request_comments(id) ON DELETE CASCADE,
        INDEX idx_request_id (request_id),
        INDEX idx_commenter (commenter_type, commenter_id),
        INDEX idx_comment_type (comment_type),
        INDEX idx_created_at (created_at)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci";

    // جدول فئات المطالب
    $tables['request_categories'] = "CREATE TABLE IF NOT EXISTS request_categories (
        id INT AUTO_INCREMENT PRIMARY KEY,
        name VARCHAR(100) NOT NULL,
        description TEXT,
        parent_category_id INT NULL,
        icon VARCHAR(50) DEFAULT 'fas fa-folder',
        color VARCHAR(7) DEFAULT '#007bff',
        is_active BOOLEAN DEFAULT TRUE,
        sort_order INT DEFAULT 0,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (parent_category_id) REFERENCES request_categories(id) ON DELETE SET NULL,
        INDEX idx_parent_category_id (parent_category_id),
        INDEX idx_is_active (is_active),
        INDEX idx_sort_order (sort_order)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci";

    // جدول إحصائيات المطالب
    $tables['request_statistics'] = "CREATE TABLE IF NOT EXISTS request_statistics (
        id INT AUTO_INCREMENT PRIMARY KEY,
        date DATE NOT NULL,
        admin_id INT,
        region_id INT,
        total_requests INT DEFAULT 0,
        pending_requests INT DEFAULT 0,
        completed_requests INT DEFAULT 0,
        rejected_requests INT DEFAULT 0,
        total_amount DECIMAL(12,2) DEFAULT 0,
        approved_amount DECIMAL(12,2) DEFAULT 0,
        average_processing_days DECIMAL(5,2) DEFAULT 0,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        FOREIGN KEY (admin_id) REFERENCES admins(id) ON DELETE SET NULL,
        FOREIGN KEY (region_id) REFERENCES regions(id) ON DELETE SET NULL,
        UNIQUE KEY unique_date_admin_region (date, admin_id, region_id),
        INDEX idx_date (date),
        INDEX idx_admin_id (admin_id),
        INDEX idx_region_id (region_id)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci";

    return $tables;
}

function insertDefaultRequestData() {
    $queries = [];
    
    // إدراج فئات المطالب الافتراضية
    $queries[] = "INSERT IGNORE INTO request_categories (name, description, icon, color) VALUES 
        ('مطالب مالية', 'المساعدات المالية والقروض', 'fas fa-money-bill-wave', '#28a745'),
        ('مطالب طبية', 'العلاج والأدوية والمساعدات الطبية', 'fas fa-heartbeat', '#dc3545'),
        ('مطالب تعليمية', 'الرسوم الدراسية والكتب والمستلزمات', 'fas fa-graduation-cap', '#007bff'),
        ('مطالب وظيفية', 'فرص العمل والتوظيف', 'fas fa-briefcase', '#6f42c1'),
        ('مطالب سكنية', 'السكن والإيجار والصيانة', 'fas fa-home', '#fd7e14'),
        ('مطالب قانونية', 'الاستشارات والمساعدات القانونية', 'fas fa-balance-scale', '#20c997'),
        ('مطالب اجتماعية', 'المساعدات الاجتماعية والأسرية', 'fas fa-users', '#e83e8c'),
        ('مطالب البنية التحتية', 'الطرق والكهرباء والماء', 'fas fa-tools', '#6c757d'),
        ('مطالب الوثائق', 'استخراج الوثائق الرسمية', 'fas fa-file-alt', '#17a2b8'),
        ('مطالب أخرى', 'مطالب متنوعة أخرى', 'fas fa-ellipsis-h', '#ffc107')";

    return $queries;
}

// تشغيل الدوال إذا تم استدعاء الملف مباشرة
if (basename($_SERVER['PHP_SELF']) == 'create_requests_tables.php') {
    echo "<!DOCTYPE html>";
    echo "<html lang='ar' dir='rtl'>";
    echo "<head>";
    echo "<meta charset='UTF-8'>";
    echo "<meta name='viewport' content='width=device-width, initial-scale=1.0'>";
    echo "<title>إنشاء جداول المطالب</title>";
    echo "<link href='https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css' rel='stylesheet'>";
    echo "<link href='https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css' rel='stylesheet'>";
    echo "<style>";
    echo "body { font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); min-height: 100vh; padding: 2rem; }";
    echo ".system-card { background: white; border-radius: 15px; box-shadow: 0 10px 30px rgba(0,0,0,0.1); padding: 2rem; margin: 0 auto; max-width: 1000px; }";
    echo ".test-result { padding: 10px; margin: 5px 0; border-radius: 5px; }";
    echo ".test-success { background: #d4edda; border: 1px solid #c3e6cb; }";
    echo ".test-error { background: #f8d7da; border: 1px solid #f5c6cb; }";
    echo "</style>";
    echo "</head>";
    echo "<body>";

    echo "<div class='system-card'>";
    echo "<h1 class='text-center mb-4'><i class='fas fa-hand-holding-heart'></i> إنشاء جداول المطالب</h1>";

    if (isset($_POST['create_requests_tables'])) {
        echo "<div class='alert alert-info'>";
        echo "<h5>جاري إنشاء جداول المطالب...</h5>";
        echo "</div>";

        try {
            // إنشاء الجداول
            $tables = createRequestsTables();
            foreach ($tables as $table_name => $sql) {
                try {
                    executeQuery($sql);
                    echo "<div class='test-result test-success'><i class='fas fa-check'></i> جدول $table_name: تم إنشاؤه بنجاح</div>";
                } catch (Exception $e) {
                    echo "<div class='test-result test-success'><i class='fas fa-info'></i> جدول $table_name: موجود مسبقاً</div>";
                }
            }

            // إدراج البيانات الافتراضية
            echo "<h6 class='mt-4'>إدراج البيانات الافتراضية:</h6>";
            $queries = insertDefaultRequestData();
            foreach ($queries as $query) {
                try {
                    executeQuery($query);
                    echo "<div class='test-result test-success'><i class='fas fa-check'></i> تم إدراج فئات المطالب الافتراضية</div>";
                } catch (Exception $e) {
                    echo "<div class='test-result test-success'><i class='fas fa-info'></i> الفئات موجودة مسبقاً</div>";
                }
            }

            echo "<div class='alert alert-success mt-4'>";
            echo "<h3><i class='fas fa-check-circle'></i> تم إنشاء جداول المطالب بنجاح!</h3>";
            echo "</div>";

        } catch (Exception $e) {
            echo "<div class='alert alert-danger'>";
            echo "<h5>خطأ في الإنشاء:</h5>";
            echo "<p>" . htmlspecialchars($e->getMessage()) . "</p>";
            echo "</div>";
        }

        echo "<div class='text-center mt-4'>";
        echo "<a href='create_reports_tables.php' class='btn btn-primary btn-lg me-2'><i class='fas fa-file-alt'></i> إنشاء جداول التقارير</a>";
        echo "<a href='../dashboard.php' class='btn btn-success btn-lg'><i class='fas fa-home'></i> الصفحة الرئيسية</a>";
        echo "</div>";

    } else {
        echo "<div class='alert alert-warning'>";
        echo "<h5><i class='fas fa-info-circle'></i> إنشاء جداول المطالب</h5>";
        echo "<p>سيتم إنشاء جميع الجداول المتعلقة بمطالب المؤيدين والمرفقات</p>";
        echo "</div>";

        echo "<div class='card mb-4'>";
        echo "<div class='card-header bg-warning text-white'>";
        echo "<h6><i class='fas fa-table'></i> الجداول التي سيتم إنشاؤها</h6>";
        echo "</div>";
        echo "<div class='card-body'>";
        echo "<div class='row'>";
        echo "<div class='col-md-6'>";
        echo "<ul>";
        echo "<li><strong>supporter_requests:</strong> المطالب الأساسية</li>";
        echo "<li><strong>request_attachments:</strong> مرفقات المطالب</li>";
        echo "<li><strong>request_status_history:</strong> تاريخ الحالات</li>";
        echo "</ul>";
        echo "</div>";
        echo "<div class='col-md-6'>";
        echo "<ul>";
        echo "<li><strong>request_comments:</strong> تعليقات المطالب</li>";
        echo "<li><strong>request_categories:</strong> فئات المطالب</li>";
        echo "<li><strong>request_statistics:</strong> إحصائيات المطالب</li>";
        echo "</ul>";
        echo "</div>";
        echo "</div>";
        echo "</div>";
        echo "</div>";

        echo "<div class='alert alert-info'>";
        echo "<h6><i class='fas fa-star'></i> الميزات المتضمنة:</h6>";
        echo "<ul>";
        echo "<li>📋 <strong>مطالب شاملة:</strong> جميع أنواع المطالب</li>";
        echo "<li>📎 <strong>مرفقات متنوعة:</strong> وثائق ومستندات</li>";
        echo "<li>📊 <strong>تتبع الحالات:</strong> تاريخ كامل للتغييرات</li>";
        echo "<li>💬 <strong>نظام تعليقات:</strong> تفاعل ومتابعة</li>";
        echo "<li>📈 <strong>إحصائيات متقدمة:</strong> تقارير شاملة</li>";
        echo "<li>🏷️ <strong>فئات منظمة:</strong> تصنيف المطالب</li>";
        echo "</ul>";
        echo "</div>";

        echo "<form method='POST' action=''>";
        echo "<div class='text-center'>";
        echo "<button type='submit' name='create_requests_tables' class='btn btn-success btn-lg'>";
        echo "<i class='fas fa-database'></i> إنشاء جداول المطالب";
        echo "</button>";
        echo "</div>";
        echo "</form>";

        echo "<div class='text-center mt-3'>";
        echo "<a href='create_messages_tables.php' class='btn btn-secondary me-2'><i class='fas fa-arrow-right'></i> جداول الرسائل</a>";
        echo "<a href='../dashboard.php' class='btn btn-secondary'><i class='fas fa-home'></i> الصفحة الرئيسية</a>";
        echo "</div>";
    }

    echo "</div>";
    echo "</body>";
    echo "</html>";
}
?>
