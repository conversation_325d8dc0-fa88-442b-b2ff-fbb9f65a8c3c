<?php
// إنشاء جداول الرسائل والمرفقات
header('Content-Type: text/html; charset=utf-8');
ini_set('default_charset', 'utf-8');
mb_internal_encoding('UTF-8');

require_once '../config/config.php';
require_once '../config/database.php';

function createMessagesTables() {
    $tables = [];
    
    // جدول الرسائل
    $tables['messages'] = "CREATE TABLE IF NOT EXISTS messages (
        id INT AUTO_INCREMENT PRIMARY KEY,
        sender_type ENUM('admin', 'management', 'candidate') NOT NULL,
        sender_id INT NOT NULL,
        receiver_type ENUM('admin', 'management', 'candidate', 'all_admins') NOT NULL,
        receiver_id INT NULL,
        subject VARCHAR(200) NOT NULL,
        message TEXT NOT NULL,
        message_type ENUM('personal', 'announcement', 'urgent', 'system') DEFAULT 'personal',
        priority ENUM('low', 'normal', 'high', 'urgent') DEFAULT 'normal',
        status ENUM('unread', 'read', 'archived', 'deleted') DEFAULT 'unread',
        is_broadcast BOOLEAN DEFAULT FALSE,
        scheduled_at DATETIME NULL,
        expires_at DATETIME NULL,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        read_at DATETIME NULL,
        replied_at DATETIME NULL,
        parent_message_id INT NULL,
        thread_id VARCHAR(50),
        FOREIGN KEY (parent_message_id) REFERENCES messages(id) ON DELETE SET NULL,
        INDEX idx_sender (sender_type, sender_id),
        INDEX idx_receiver (receiver_type, receiver_id),
        INDEX idx_status (status),
        INDEX idx_priority (priority),
        INDEX idx_created_at (created_at),
        INDEX idx_thread_id (thread_id),
        FULLTEXT idx_fulltext_search (subject, message)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci";

    // جدول مرفقات الرسائل
    $tables['message_attachments'] = "CREATE TABLE IF NOT EXISTS message_attachments (
        id INT AUTO_INCREMENT PRIMARY KEY,
        message_id INT NOT NULL,
        file_name VARCHAR(255) NOT NULL,
        original_name VARCHAR(255) NOT NULL,
        file_path VARCHAR(500) NOT NULL,
        file_size INT NOT NULL,
        file_type VARCHAR(100) NOT NULL,
        mime_type VARCHAR(100),
        attachment_type ENUM('document', 'image', 'video', 'audio', 'archive', 'other') DEFAULT 'document',
        is_inline BOOLEAN DEFAULT FALSE,
        download_count INT DEFAULT 0,
        upload_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (message_id) REFERENCES messages(id) ON DELETE CASCADE,
        INDEX idx_message_id (message_id),
        INDEX idx_file_type (file_type),
        INDEX idx_attachment_type (attachment_type)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci";

    // جدول قراءة الرسائل (للرسائل المرسلة لعدة أشخاص)
    $tables['message_reads'] = "CREATE TABLE IF NOT EXISTS message_reads (
        id INT AUTO_INCREMENT PRIMARY KEY,
        message_id INT NOT NULL,
        reader_type ENUM('admin', 'management', 'candidate') NOT NULL,
        reader_id INT NOT NULL,
        read_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        ip_address VARCHAR(45),
        user_agent TEXT,
        FOREIGN KEY (message_id) REFERENCES messages(id) ON DELETE CASCADE,
        UNIQUE KEY unique_message_reader (message_id, reader_type, reader_id),
        INDEX idx_message_id (message_id),
        INDEX idx_reader (reader_type, reader_id),
        INDEX idx_read_at (read_at)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci";

    // جدول قوالب الرسائل
    $tables['message_templates'] = "CREATE TABLE IF NOT EXISTS message_templates (
        id INT AUTO_INCREMENT PRIMARY KEY,
        name VARCHAR(100) NOT NULL,
        subject VARCHAR(200) NOT NULL,
        content TEXT NOT NULL,
        template_type ENUM('welcome', 'reminder', 'announcement', 'report_request', 'custom') DEFAULT 'custom',
        variables JSON,
        is_active BOOLEAN DEFAULT TRUE,
        created_by INT,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        FOREIGN KEY (created_by) REFERENCES users(id) ON DELETE SET NULL,
        INDEX idx_template_type (template_type),
        INDEX idx_is_active (is_active),
        INDEX idx_created_by (created_by)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci";

    // جدول مجلدات الرسائل
    $tables['message_folders'] = "CREATE TABLE IF NOT EXISTS message_folders (
        id INT AUTO_INCREMENT PRIMARY KEY,
        user_type ENUM('admin', 'management', 'candidate') NOT NULL,
        user_id INT NOT NULL,
        folder_name VARCHAR(100) NOT NULL,
        folder_type ENUM('inbox', 'sent', 'drafts', 'archive', 'trash', 'custom') DEFAULT 'custom',
        color VARCHAR(7) DEFAULT '#007bff',
        icon VARCHAR(50) DEFAULT 'fas fa-folder',
        message_count INT DEFAULT 0,
        unread_count INT DEFAULT 0,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        INDEX idx_user (user_type, user_id),
        INDEX idx_folder_type (folder_type)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci";

    // جدول ربط الرسائل بالمجلدات
    $tables['message_folder_items'] = "CREATE TABLE IF NOT EXISTS message_folder_items (
        id INT AUTO_INCREMENT PRIMARY KEY,
        folder_id INT NOT NULL,
        message_id INT NOT NULL,
        added_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (folder_id) REFERENCES message_folders(id) ON DELETE CASCADE,
        FOREIGN KEY (message_id) REFERENCES messages(id) ON DELETE CASCADE,
        UNIQUE KEY unique_folder_message (folder_id, message_id),
        INDEX idx_folder_id (folder_id),
        INDEX idx_message_id (message_id)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci";

    return $tables;
}

function insertDefaultMessageData() {
    $queries = [];
    
    // إدراج قوالب رسائل افتراضية
    $queries[] = "INSERT IGNORE INTO message_templates (name, subject, content, template_type, variables) VALUES 
        ('ترحيب بإداري جديد', 'مرحباً بك في فريق العمل', 'مرحباً {admin_name}،\n\nنرحب بك في فريق العمل. نتمنى لك التوفيق في مهامك الجديدة.\n\nتحياتنا،\nإدارة الحملة', 'welcome', '{\"admin_name\": \"اسم الإداري\"}'),
        ('تذكير بالتقرير الأسبوعي', 'تذكير: التقرير الأسبوعي', 'عزيزي {admin_name}،\n\nهذا تذكير بضرورة تقديم التقرير الأسبوعي قبل نهاية اليوم.\n\nشكراً لك', 'reminder', '{\"admin_name\": \"اسم الإداري\"}'),
        ('إعلان عام', 'إعلان مهم', 'إلى جميع أعضاء الفريق،\n\n{announcement_content}\n\nشكراً لكم', 'announcement', '{\"announcement_content\": \"محتوى الإعلان\"}'),
        ('طلب تقرير', 'مطلوب تقرير عن {report_topic}', 'عزيزي {admin_name}،\n\nيرجى تقديم تقرير مفصل عن {report_topic} في أقرب وقت ممكن.\n\nشكراً', 'report_request', '{\"admin_name\": \"اسم الإداري\", \"report_topic\": \"موضوع التقرير\"}')";

    return $queries;
}

// تشغيل الدوال إذا تم استدعاء الملف مباشرة
if (basename($_SERVER['PHP_SELF']) == 'create_messages_tables.php') {
    echo "<!DOCTYPE html>";
    echo "<html lang='ar' dir='rtl'>";
    echo "<head>";
    echo "<meta charset='UTF-8'>";
    echo "<meta name='viewport' content='width=device-width, initial-scale=1.0'>";
    echo "<title>إنشاء جداول الرسائل</title>";
    echo "<link href='https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css' rel='stylesheet'>";
    echo "<link href='https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css' rel='stylesheet'>";
    echo "<style>";
    echo "body { font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); min-height: 100vh; padding: 2rem; }";
    echo ".system-card { background: white; border-radius: 15px; box-shadow: 0 10px 30px rgba(0,0,0,0.1); padding: 2rem; margin: 0 auto; max-width: 1000px; }";
    echo ".test-result { padding: 10px; margin: 5px 0; border-radius: 5px; }";
    echo ".test-success { background: #d4edda; border: 1px solid #c3e6cb; }";
    echo ".test-error { background: #f8d7da; border: 1px solid #f5c6cb; }";
    echo "</style>";
    echo "</head>";
    echo "<body>";

    echo "<div class='system-card'>";
    echo "<h1 class='text-center mb-4'><i class='fas fa-envelope'></i> إنشاء جداول الرسائل</h1>";

    if (isset($_POST['create_messages_tables'])) {
        echo "<div class='alert alert-info'>";
        echo "<h5>جاري إنشاء جداول الرسائل...</h5>";
        echo "</div>";

        try {
            // إنشاء الجداول
            $tables = createMessagesTables();
            foreach ($tables as $table_name => $sql) {
                try {
                    executeQuery($sql);
                    echo "<div class='test-result test-success'><i class='fas fa-check'></i> جدول $table_name: تم إنشاؤه بنجاح</div>";
                } catch (Exception $e) {
                    echo "<div class='test-result test-success'><i class='fas fa-info'></i> جدول $table_name: موجود مسبقاً</div>";
                }
            }

            // إدراج البيانات الافتراضية
            echo "<h6 class='mt-4'>إدراج البيانات الافتراضية:</h6>";
            $queries = insertDefaultMessageData();
            foreach ($queries as $query) {
                try {
                    executeQuery($query);
                    echo "<div class='test-result test-success'><i class='fas fa-check'></i> تم إدراج قوالب الرسائل الافتراضية</div>";
                } catch (Exception $e) {
                    echo "<div class='test-result test-success'><i class='fas fa-info'></i> القوالب موجودة مسبقاً</div>";
                }
            }

            echo "<div class='alert alert-success mt-4'>";
            echo "<h3><i class='fas fa-check-circle'></i> تم إنشاء جداول الرسائل بنجاح!</h3>";
            echo "</div>";

        } catch (Exception $e) {
            echo "<div class='alert alert-danger'>";
            echo "<h5>خطأ في الإنشاء:</h5>";
            echo "<p>" . htmlspecialchars($e->getMessage()) . "</p>";
            echo "</div>";
        }

        echo "<div class='text-center mt-4'>";
        echo "<a href='create_requests_tables.php' class='btn btn-primary btn-lg me-2'><i class='fas fa-hand-holding-heart'></i> إنشاء جداول المطالب</a>";
        echo "<a href='../dashboard.php' class='btn btn-success btn-lg'><i class='fas fa-home'></i> الصفحة الرئيسية</a>";
        echo "</div>";

    } else {
        echo "<div class='alert alert-warning'>";
        echo "<h5><i class='fas fa-info-circle'></i> إنشاء جداول الرسائل</h5>";
        echo "<p>سيتم إنشاء جميع الجداول المتعلقة بالرسائل والمرفقات</p>";
        echo "</div>";

        echo "<div class='card mb-4'>";
        echo "<div class='card-header bg-info text-white'>";
        echo "<h6><i class='fas fa-table'></i> الجداول التي سيتم إنشاؤها</h6>";
        echo "</div>";
        echo "<div class='card-body'>";
        echo "<div class='row'>";
        echo "<div class='col-md-6'>";
        echo "<ul>";
        echo "<li><strong>messages:</strong> الرسائل الأساسية</li>";
        echo "<li><strong>message_attachments:</strong> مرفقات الرسائل</li>";
        echo "<li><strong>message_reads:</strong> قراءة الرسائل</li>";
        echo "</ul>";
        echo "</div>";
        echo "<div class='col-md-6'>";
        echo "<ul>";
        echo "<li><strong>message_templates:</strong> قوالب الرسائل</li>";
        echo "<li><strong>message_folders:</strong> مجلدات الرسائل</li>";
        echo "<li><strong>message_folder_items:</strong> محتويات المجلدات</li>";
        echo "</ul>";
        echo "</div>";
        echo "</div>";
        echo "</div>";
        echo "</div>";

        echo "<div class='alert alert-info'>";
        echo "<h6><i class='fas fa-star'></i> الميزات المتضمنة:</h6>";
        echo "<ul>";
        echo "<li>📧 <strong>رسائل متقدمة:</strong> دعم الردود والمحادثات</li>";
        echo "<li>📎 <strong>مرفقات متنوعة:</strong> صور، مستندات، فيديو</li>";
        echo "<li>📁 <strong>مجلدات منظمة:</strong> تنظيم الرسائل في مجلدات</li>";
        echo "<li>📋 <strong>قوالب جاهزة:</strong> قوالب رسائل معدة مسبقاً</li>";
        echo "<li>🔔 <strong>إشعارات القراءة:</strong> تتبع قراءة الرسائل</li>";
        echo "<li>⏰ <strong>رسائل مجدولة:</strong> إرسال في وقت محدد</li>";
        echo "</ul>";
        echo "</div>";

        echo "<form method='POST' action=''>";
        echo "<div class='text-center'>";
        echo "<button type='submit' name='create_messages_tables' class='btn btn-success btn-lg'>";
        echo "<i class='fas fa-database'></i> إنشاء جداول الرسائل";
        echo "</button>";
        echo "</div>";
        echo "</form>";

        echo "<div class='text-center mt-3'>";
        echo "<a href='create_supporters_tables.php' class='btn btn-secondary me-2'><i class='fas fa-arrow-right'></i> جداول المؤيدين</a>";
        echo "<a href='../dashboard.php' class='btn btn-secondary'><i class='fas fa-home'></i> الصفحة الرئيسية</a>";
        echo "</div>";
    }

    echo "</div>";
    echo "</body>";
    echo "</html>";
}
?>
