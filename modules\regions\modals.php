<!-- نافذة إضافة منطقة جديدة -->
<div class="modal fade" id="addRegionModal" tabindex="-1" aria-labelledby="addRegionModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="addRegionModalLabel">
                    <i class="fas fa-plus me-2"></i>
                    إضافة منطقة جديدة
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="إغلاق"></button>
            </div>
            <form method="POST" action="" class="region-form needs-validation" novalidate>
                <input type="hidden" name="action" value="add">
                <div class="modal-body">
                    <div class="mb-3">
                        <label for="name" class="form-label">اسم المنطقة <span class="required">*</span></label>
                        <input type="text" class="form-control" id="name" name="name" required>
                        <div class="invalid-feedback">يرجى إدخال اسم المنطقة</div>
                    </div>
                    
                    <div class="mb-3">
                        <label for="description" class="form-label">وصف المنطقة</label>
                        <textarea class="form-control" id="description" name="description" rows="3" 
                                  placeholder="وصف مختصر عن المنطقة (اختياري)"></textarea>
                    </div>
                    
                    <div class="alert alert-info">
                        <i class="fas fa-info-circle me-2"></i>
                        <strong>ملاحظة:</strong> بعد إضافة المنطقة يمكنك ربط المؤيدين بها من صفحة إدارة المؤيدين.
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                        <i class="fas fa-times me-2"></i>إلغاء
                    </button>
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-save me-2"></i>حفظ المنطقة
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- نافذة تعديل المنطقة -->
<div class="modal fade" id="editRegionModal" tabindex="-1" aria-labelledby="editRegionModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="editRegionModalLabel">
                    <i class="fas fa-edit me-2"></i>
                    تعديل المنطقة
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="إغلاق"></button>
            </div>
            <form method="POST" action="" class="region-form needs-validation" novalidate>
                <input type="hidden" name="action" value="edit">
                <input type="hidden" id="editId" name="id">
                <div class="modal-body">
                    <div class="mb-3">
                        <label for="editName" class="form-label">اسم المنطقة <span class="required">*</span></label>
                        <input type="text" class="form-control" id="editName" name="name" required>
                        <div class="invalid-feedback">يرجى إدخال اسم المنطقة</div>
                    </div>
                    
                    <div class="mb-3">
                        <label for="editDescription" class="form-label">وصف المنطقة</label>
                        <textarea class="form-control" id="editDescription" name="description" rows="3" 
                                  placeholder="وصف مختصر عن المنطقة (اختياري)"></textarea>
                    </div>
                    
                    <div class="alert alert-warning">
                        <i class="fas fa-exclamation-triangle me-2"></i>
                        <strong>تنبيه:</strong> تعديل اسم المنطقة سيؤثر على جميع المؤيدين المرتبطين بها.
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                        <i class="fas fa-times me-2"></i>إلغاء
                    </button>
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-save me-2"></i>حفظ التغييرات
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- نافذة تأكيد الحذف -->
<div class="modal fade" id="deleteRegionModal" tabindex="-1" aria-labelledby="deleteRegionModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header bg-danger text-white">
                <h5 class="modal-title" id="deleteRegionModalLabel">
                    <i class="fas fa-trash me-2"></i>
                    تأكيد حذف المنطقة
                </h5>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="إغلاق"></button>
            </div>
            <div class="modal-body">
                <div class="alert alert-danger">
                    <i class="fas fa-exclamation-triangle me-2"></i>
                    <strong>تحذير:</strong> هذا الإجراء لا يمكن التراجع عنه!
                </div>
                
                <p>هل أنت متأكد من حذف المنطقة <strong id="deleteRegionName"></strong>؟</p>
                
                <div id="deleteRegionWarning" class="alert alert-warning" style="display: none;">
                    <i class="fas fa-users me-2"></i>
                    هذه المنطقة تحتوي على <strong id="deleteRegionSupportersCount"></strong> مؤيد. 
                    لا يمكن حذفها إلا بعد نقل أو حذف جميع المؤيدين المرتبطين بها.
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                    <i class="fas fa-times me-2"></i>إلغاء
                </button>
                <button type="button" class="btn btn-danger" id="confirmDeleteBtn">
                    <i class="fas fa-trash me-2"></i>حذف المنطقة
                </button>
            </div>
        </div>
    </div>
</div>

<!-- نافذة إحصائيات المنطقة -->
<div class="modal fade" id="regionStatsModal" tabindex="-1" aria-labelledby="regionStatsModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="regionStatsModalLabel">
                    <i class="fas fa-chart-bar me-2"></i>
                    إحصائيات المنطقة
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="إغلاق"></button>
            </div>
            <div class="modal-body">
                <div id="regionStatsContent">
                    <!-- سيتم ملء المحتوى ديناميكياً -->
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                    <i class="fas fa-times me-2"></i>إغلاق
                </button>
                <button type="button" class="btn btn-primary" onclick="exportRegionStats()">
                    <i class="fas fa-download me-2"></i>تصدير الإحصائيات
                </button>
            </div>
        </div>
    </div>
</div>

<!-- نافذة استيراد المناطق -->
<div class="modal fade" id="importRegionsModal" tabindex="-1" aria-labelledby="importRegionsModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="importRegionsModalLabel">
                    <i class="fas fa-upload me-2"></i>
                    استيراد المناطق
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="إغلاق"></button>
            </div>
            <form method="POST" action="import.php" enctype="multipart/form-data">
                <div class="modal-body">
                    <div class="mb-3">
                        <label for="importFile" class="form-label">ملف Excel</label>
                        <input type="file" class="form-control" id="importFile" name="import_file" accept=".xlsx,.xls" required>
                        <div class="form-text">يجب أن يكون الملف بصيغة Excel (.xlsx أو .xls)</div>
                    </div>
                    
                    <div class="alert alert-info">
                        <h6><i class="fas fa-info-circle me-2"></i>تعليمات الاستيراد:</h6>
                        <ul class="mb-0">
                            <li>يجب أن يحتوي الملف على عمودين: اسم المنطقة، الوصف</li>
                            <li>الصف الأول يجب أن يحتوي على عناوين الأعمدة</li>
                            <li>تأكد من عدم تكرار أسماء المناطق</li>
                        </ul>
                    </div>
                    
                    <div class="mb-3">
                        <a href="template.xlsx" class="btn btn-outline-primary btn-sm">
                            <i class="fas fa-download me-2"></i>تحميل نموذج Excel
                        </a>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                        <i class="fas fa-times me-2"></i>إلغاء
                    </button>
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-upload me-2"></i>استيراد المناطق
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- نافذة عرض مؤيدي المنطقة -->
<div class="modal fade" id="regionSupportersModal" tabindex="-1" aria-labelledby="regionSupportersModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-xl">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="regionSupportersModalLabel">
                    <i class="fas fa-users me-2"></i>
                    مؤيدو المنطقة
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="إغلاق"></button>
            </div>
            <div class="modal-body">
                <div id="regionSupportersContent">
                    <!-- سيتم ملء المحتوى ديناميكياً -->
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                    <i class="fas fa-times me-2"></i>إغلاق
                </button>
                <button type="button" class="btn btn-primary" onclick="goToSupportersPage()">
                    <i class="fas fa-external-link-alt me-2"></i>انتقال لصفحة المؤيدين
                </button>
            </div>
        </div>
    </div>
</div>
