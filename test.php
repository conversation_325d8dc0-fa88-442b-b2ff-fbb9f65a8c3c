<?php
// ملف اختبار بسيط للتأكد من عمل النظام

echo "<!DOCTYPE html>";
echo "<html lang='ar' dir='rtl'>";
echo "<head>";
echo "<meta charset='UTF-8'>";
echo "<meta name='viewport' content='width=device-width, initial-scale=1.0'>";
echo "<title>اختبار النظام</title>";
echo "<link href='https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css' rel='stylesheet'>";
echo "<link href='https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css' rel='stylesheet'>";
echo "<style>";
echo "body { font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); min-height: 100vh; padding: 2rem; }";
echo ".test-card { background: white; border-radius: 15px; box-shadow: 0 10px 30px rgba(0,0,0,0.1); padding: 2rem; margin: 0 auto; max-width: 800px; }";
echo ".success { color: #28a745; }";
echo ".error { color: #dc3545; }";
echo ".info { color: #17a2b8; }";
echo "</style>";
echo "</head>";
echo "<body>";

echo "<div class='test-card'>";
echo "<h1 class='text-center mb-4'><i class='fas fa-cog'></i> اختبار نظام إدارة الحملة الانتخابية</h1>";

// معلومات PHP
echo "<div class='alert alert-info'>";
echo "<h5><i class='fas fa-info-circle'></i> معلومات الخادم</h5>";
echo "<p><strong>إصدار PHP:</strong> " . PHP_VERSION . "</p>";
echo "<p><strong>الخادم:</strong> " . $_SERVER['SERVER_SOFTWARE'] . "</p>";
echo "<p><strong>النظام:</strong> " . PHP_OS . "</p>";
echo "<p><strong>التوقيت:</strong> " . date('Y-m-d H:i:s') . "</p>";
echo "</div>";

// اختبار الملفات
echo "<div class='alert alert-primary'>";
echo "<h5><i class='fas fa-file-code'></i> اختبار الملفات</h5>";

$files_to_check = [
    'config/config.php' => 'ملف الإعدادات الرئيسي',
    'config/database.php' => 'ملف إعدادات قاعدة البيانات',
    'database/schema.sql' => 'ملف هيكل قاعدة البيانات',
    'login.php' => 'صفحة تسجيل الدخول',
    'dashboard.php' => 'لوحة التحكم',
    'setup.php' => 'صفحة الإعداد',
    'install.php' => 'معالج التثبيت'
];

foreach ($files_to_check as $file => $description) {
    if (file_exists($file)) {
        echo "<p class='success'><i class='fas fa-check'></i> $description: موجود</p>";
    } else {
        echo "<p class='error'><i class='fas fa-times'></i> $description: غير موجود</p>";
    }
}
echo "</div>";

// اختبار المجلدات
echo "<div class='alert alert-warning'>";
echo "<h5><i class='fas fa-folder'></i> اختبار المجلدات</h5>";

$folders_to_check = [
    'uploads' => 'مجلد الملفات المرفوعة',
    'exports' => 'مجلد ملفات التصدير',
    'assets/css' => 'مجلد ملفات CSS',
    'assets/js' => 'مجلد ملفات JavaScript',
    'modules/supporters' => 'وحدة المؤيدين',
    'modules/regions' => 'وحدة المناطق',
    'modules/admins' => 'وحدة الإداريين',
    'modules/expenses' => 'وحدة المصروفات'
];

foreach ($folders_to_check as $folder => $description) {
    if (is_dir($folder)) {
        $writable = is_writable($folder) ? 'قابل للكتابة' : 'غير قابل للكتابة';
        echo "<p class='success'><i class='fas fa-check'></i> $description: موجود ($writable)</p>";
    } else {
        echo "<p class='error'><i class='fas fa-times'></i> $description: غير موجود</p>";
    }
}
echo "</div>";

// اختبار الإضافات المطلوبة
echo "<div class='alert alert-success'>";
echo "<h5><i class='fas fa-puzzle-piece'></i> اختبار إضافات PHP</h5>";

$extensions = [
    'pdo' => 'PDO Database',
    'pdo_mysql' => 'PDO MySQL',
    'gd' => 'GD Library',
    'fileinfo' => 'File Info',
    'curl' => 'cURL',
    'openssl' => 'OpenSSL',
    'mbstring' => 'Multibyte String'
];

foreach ($extensions as $ext => $name) {
    if (extension_loaded($ext)) {
        echo "<p class='success'><i class='fas fa-check'></i> $name: متوفر</p>";
    } else {
        echo "<p class='error'><i class='fas fa-times'></i> $name: غير متوفر</p>";
    }
}
echo "</div>";

// روابط سريعة
echo "<div class='text-center mt-4'>";
echo "<h5>روابط سريعة:</h5>";
echo "<a href='index.php' class='btn btn-primary me-2'><i class='fas fa-home'></i> الصفحة الرئيسية</a>";
echo "<a href='setup.php' class='btn btn-info me-2'><i class='fas fa-cog'></i> معلومات الإعداد</a>";
echo "<a href='install.php' class='btn btn-success me-2'><i class='fas fa-download'></i> التثبيت</a>";
echo "<a href='login.php' class='btn btn-warning'><i class='fas fa-sign-in-alt'></i> تسجيل الدخول</a>";
echo "</div>";

echo "<div class='text-center mt-3'>";
echo "<small class='text-muted'>نظام إدارة الحملة الانتخابية - اختبار النظام</small>";
echo "</div>";

echo "</div>";
echo "</body>";
echo "</html>";
?>
