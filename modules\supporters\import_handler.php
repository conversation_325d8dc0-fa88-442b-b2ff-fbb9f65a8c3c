<?php
// معالج الاستيراد للصفحة الأصلية
require_once '../../config/config.php';
require_once '../../config/database.php';

// التحقق من تسجيل الدخول
if (!isLoggedIn()) {
    redirect('../../login.php');
}

if ($_SERVER['REQUEST_METHOD'] == 'POST' && isset($_FILES['import_file'])) {
    $file = $_FILES['import_file'];
    
    // التحقق من نوع الملف
    $file_extension = strtolower(pathinfo($file['name'], PATHINFO_EXTENSION));
    if (!in_array($file_extension, ['csv', 'txt'])) {
        showMessage('نوع الملف غير مدعوم. يرجى رفع ملف CSV', 'error');
        redirect('supporters.php');
    }
    
    // التحقق من حجم الملف (5MB)
    if ($file['size'] > 5 * 1024 * 1024) {
        showMessage('حجم الملف كبير جداً. الحد الأقصى 5 ميجابايت', 'error');
        redirect('supporters.php');
    }
    
    try {
        // قراءة الملف
        $file_content = file_get_contents($file['tmp_name']);
        
        // تحويل الترميز إلى UTF-8
        if (!mb_check_encoding($file_content, 'UTF-8')) {
            $file_content = mb_convert_encoding($file_content, 'UTF-8', 'auto');
        }
        
        // تقسيم الملف إلى أسطر
        $lines = explode("\n", $file_content);
        
        // إزالة السطر الأول (العناوين) إذا كان موجود
        if (count($lines) > 1) {
            array_shift($lines);
        }
        
        $success_count = 0;
        $error_count = 0;
        $errors = [];
        
        // جلب المناطق
        $regions = fetchAll("SELECT id, name FROM regions");
        $region_map = [];
        foreach ($regions as $region) {
            $region_map[strtolower(trim($region['name']))] = $region['id'];
        }
        
        foreach ($lines as $line_number => $line) {
            $line = trim($line);
            if (empty($line)) continue;
            
            // تقسيم السطر (CSV)
            $data = str_getcsv($line);
            
            // التأكد من وجود البيانات الأساسية
            if (count($data) < 4) {
                $errors[] = "السطر " . ($line_number + 2) . ": بيانات غير كافية";
                $error_count++;
                continue;
            }
            
            // استخراج البيانات
            $full_name = trim($data[0] ?? '');
            $gender = strtolower(trim($data[1] ?? ''));
            $marital_status = trim($data[2] ?? 'single');
            $birth_date = trim($data[3] ?? '1990-01-01');
            $education = trim($data[4] ?? '');
            $profession = trim($data[5] ?? '');
            $address = trim($data[6] ?? '');
            $phone = trim($data[7] ?? '');
            $voter_number = trim($data[8] ?? '');
            $voting_center = trim($data[9] ?? '');
            $region_name = strtolower(trim($data[10] ?? ''));
            $notes = trim($data[11] ?? '');
            
            // التحقق من البيانات المطلوبة
            if (empty($full_name) || empty($phone) || empty($address)) {
                $errors[] = "السطر " . ($line_number + 2) . ": الاسم والهاتف والعنوان مطلوبة";
                $error_count++;
                continue;
            }
            
            // تحويل الجنس
            $gender_map = [
                'ذكر' => 'male', 'male' => 'male', 'm' => 'male',
                'أنثى' => 'female', 'female' => 'female', 'f' => 'female'
            ];
            
            if (!isset($gender_map[$gender])) {
                $gender = 'male'; // افتراضي
            } else {
                $gender = $gender_map[$gender];
            }
            
            // تحويل الحالة الاجتماعية
            $marital_map = [
                'أعزب' => 'single', 'single' => 'single',
                'متزوج' => 'married', 'married' => 'married',
                'مطلق' => 'divorced', 'divorced' => 'divorced',
                'أرمل' => 'widowed', 'widowed' => 'widowed'
            ];
            
            if (!isset($marital_map[$marital_status])) {
                $marital_status = 'single';
            } else {
                $marital_status = $marital_map[$marital_status];
            }
            
            // التحقق من رقم الهاتف
            if (!preg_match('/^07[0-9]{9}$/', $phone)) {
                $errors[] = "السطر " . ($line_number + 2) . ": رقم هاتف غير صحيح ($phone)";
                $error_count++;
                continue;
            }
            
            // البحث عن المنطقة أو استخدام المنطقة الافتراضية
            $region_id = 1; // افتراضي
            if (!empty($region_name)) {
                if (isset($region_map[$region_name])) {
                    $region_id = $region_map[$region_name];
                } else {
                    // إنشاء منطقة جديدة
                    $sql = "INSERT INTO regions (name, created_at) VALUES (?, NOW())";
                    $result = executeQuery($sql, [ucfirst($region_name)]);
                    if ($result) {
                        $region_id = getLastInsertId();
                        $region_map[$region_name] = $region_id;
                    }
                }
            }
            
            // تحويل تاريخ الميلاد
            if (empty($birth_date)) {
                $birth_date = '1990-01-01';
            } else {
                $birth_date = date('Y-m-d', strtotime($birth_date));
                if ($birth_date === '1970-01-01') {
                    $birth_date = '1990-01-01';
                }
            }
            
            // التحقق من تكرار رقم الناخب
            if (!empty($voter_number)) {
                $existing = fetchOne("SELECT id FROM supporters WHERE voter_number = ?", [$voter_number]);
                if ($existing) {
                    $errors[] = "السطر " . ($line_number + 2) . ": رقم الناخب موجود مسبقاً ($voter_number)";
                    $error_count++;
                    continue;
                }
            }
            
            // إدراج البيانات
            $sql = "INSERT INTO supporters (full_name, gender, marital_status, birth_date, education, profession, address, phone, voter_number, voting_center, region_id, notes, added_by, created_at) 
                    VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, NOW())";
            
            $params = [
                $full_name, $gender, $marital_status, $birth_date, $education,
                $profession, $address, $phone, $voter_number, $voting_center,
                $region_id, $notes, $_SESSION['user_id']
            ];
            
            $result = executeQuery($sql, $params);
            
            if ($result) {
                $success_count++;
            } else {
                $errors[] = "السطر " . ($line_number + 2) . ": فشل في إدراج البيانات";
                $error_count++;
            }
        }
        
        // عرض النتائج
        if ($success_count > 0) {
            showMessage("تم استيراد $success_count مؤيد بنجاح", 'success');
        }
        
        if ($error_count > 0) {
            showMessage("حدث $error_count خطأ أثناء الاستيراد", 'warning');
            $_SESSION['import_errors'] = array_slice($errors, 0, 10); // أول 10 أخطاء فقط
        }
        
    } catch (Exception $e) {
        showMessage('حدث خطأ أثناء معالجة الملف: ' . $e->getMessage(), 'error');
    }
    
    redirect('supporters.php');
} else {
    redirect('supporters.php');
}
?>
