<?php
require_once '../../config/config.php';
require_once '../../config/database.php';

// التحقق من تسجيل الدخول
if (!isLoggedIn()) {
    http_response_code(401);
    echo json_encode(['success' => false, 'message' => 'غير مصرح']);
    exit;
}

// التحقق من الصلاحيات - المناطق للمرشح فقط
if (!isCandidate()) {
    http_response_code(403);
    echo json_encode(['success' => false, 'message' => 'ليس لديك صلاحية']);
    exit;
}

// التحقق من وجود معرف المنطقة
if (!isset($_GET['id']) || !is_numeric($_GET['id'])) {
    http_response_code(400);
    echo json_encode(['success' => false, 'message' => 'معرف المنطقة غير صحيح']);
    exit;
}

$region_id = (int)$_GET['id'];

// جلب بيانات المنطقة مع إحصائيات المؤيدين
$sql = "SELECT r.*, 
               COUNT(s.id) as supporters_count,
               COUNT(CASE WHEN s.gender = 'male' THEN 1 END) as male_count,
               COUNT(CASE WHEN s.gender = 'female' THEN 1 END) as female_count
        FROM regions r 
        LEFT JOIN supporters s ON r.id = s.region_id 
        WHERE r.id = ?
        GROUP BY r.id";

$region = fetchOne($sql, [$region_id]);

if (!$region) {
    http_response_code(404);
    echo json_encode(['success' => false, 'message' => 'المنطقة غير موجودة']);
    exit;
}

// إرجاع البيانات
header('Content-Type: application/json; charset=utf-8');
echo json_encode([
    'success' => true,
    'region' => $region
]);
?>
