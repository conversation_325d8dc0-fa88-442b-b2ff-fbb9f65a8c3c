<?php
require_once '../../config/config.php';
require_once '../../config/database.php';

// التحقق من تسجيل الدخول
if (!isLoggedIn()) {
    http_response_code(401);
    echo json_encode(['success' => false, 'message' => 'غير مصرح']);
    exit;
}

// قراءة البيانات المرسلة
$input = json_decode(file_get_contents('php://input'), true);

if (!isset($input['voter_number']) || empty($input['voter_number'])) {
    echo json_encode(['exists' => false]);
    exit;
}

$voter_number = sanitize($input['voter_number']);
$supporter_id = isset($input['supporter_id']) ? (int)$input['supporter_id'] : null;

// بناء الاستعلام
$sql = "SELECT id FROM supporters WHERE voter_number = ?";
$params = [$voter_number];

// استبعاد المؤيد الحالي في حالة التعديل
if ($supporter_id) {
    $sql .= " AND id != ?";
    $params[] = $supporter_id;
}

$existing = fetchOne($sql, $params);

header('Content-Type: application/json; charset=utf-8');
echo json_encode(['exists' => (bool)$existing]);
?>
