// JavaScript لصفحة تسجيل الدخول

document.addEventListener('DOMContentLoaded', function() {
    // التحقق من صحة رقم الهاتف العراقي
    const phoneInput = document.getElementById('phone');
    if (phoneInput) {
        phoneInput.addEventListener('input', function(e) {
            let value = e.target.value;
            
            // إزالة أي أحرف غير رقمية
            value = value.replace(/\D/g, '');
            
            // التأكد من أن الرقم يبدأ بـ 07
            if (value.length > 0 && !value.startsWith('07')) {
                value = '07' + value.replace(/^0*/, '');
            }
            
            // تحديد الطول الأقصى (11 رقم)
            if (value.length > 11) {
                value = value.substring(0, 11);
            }
            
            e.target.value = value;
            
            // التحقق من صحة الرقم
            validatePhoneNumber(value);
        });
        
        // التحقق عند فقدان التركيز
        phoneInput.addEventListener('blur', function(e) {
            validatePhoneNumber(e.target.value);
        });
    }
    
    // تحسين تجربة المستخدم للنموذج
    const form = document.querySelector('form');
    if (form) {
        form.addEventListener('submit', function(e) {
            const submitBtn = form.querySelector('.btn-login');
            if (submitBtn) {
                submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>جاري تسجيل الدخول...';
                submitBtn.disabled = true;
            }
        });
    }
    
    // تأثيرات بصرية للحقول
    const inputs = document.querySelectorAll('.form-control');
    inputs.forEach(input => {
        input.addEventListener('focus', function() {
            this.parentElement.classList.add('focused');
        });
        
        input.addEventListener('blur', function() {
            this.parentElement.classList.remove('focused');
            if (this.value.trim() !== '') {
                this.classList.add('has-value');
            } else {
                this.classList.remove('has-value');
            }
        });
    });
    
    // إخفاء رسائل الخطأ تلقائياً
    const alerts = document.querySelectorAll('.alert');
    alerts.forEach(alert => {
        setTimeout(() => {
            alert.style.opacity = '0';
            setTimeout(() => {
                alert.style.display = 'none';
            }, 300);
        }, 5000);
    });
    
    // تحسين الأمان - منع النسخ واللصق لكلمة المرور
    const passwordInput = document.getElementById('password');
    if (passwordInput) {
        passwordInput.addEventListener('paste', function(e) {
            e.preventDefault();
            showToast('لا يمكن لصق كلمة المرور لأسباب أمنية', 'warning');
        });
    }
    
    // كشف محاولات الاختراق
    let suspiciousActivity = 0;
    document.addEventListener('keydown', function(e) {
        // كشف محاولات استخدام أدوات المطور
        if (e.key === 'F12' || 
            (e.ctrlKey && e.shiftKey && e.key === 'I') ||
            (e.ctrlKey && e.shiftKey && e.key === 'C') ||
            (e.ctrlKey && e.key === 'U')) {
            suspiciousActivity++;
            if (suspiciousActivity > 3) {
                showToast('تم رصد نشاط مشبوه', 'error');
            }
        }
    });
});

// دالة للتحقق من صحة رقم الهاتف العراقي
function validatePhoneNumber(phone) {
    const phonePattern = /^07[3-9][0-9]{8}$/;
    const phoneInput = document.getElementById('phone');
    const isValid = phonePattern.test(phone);
    
    if (phone.length > 0) {
        if (isValid) {
            phoneInput.classList.remove('is-invalid');
            phoneInput.classList.add('is-valid');
            removePhoneError();
        } else {
            phoneInput.classList.remove('is-valid');
            phoneInput.classList.add('is-invalid');
            showPhoneError('رقم الهاتف غير صحيح. يجب أن يبدأ بـ 07 ويتكون من 11 رقم');
        }
    } else {
        phoneInput.classList.remove('is-valid', 'is-invalid');
        removePhoneError();
    }
    
    return isValid;
}

// دالة لإظهار خطأ رقم الهاتف
function showPhoneError(message) {
    removePhoneError();
    const phoneInput = document.getElementById('phone');
    const errorDiv = document.createElement('div');
    errorDiv.className = 'invalid-feedback';
    errorDiv.textContent = message;
    phoneInput.parentElement.appendChild(errorDiv);
}

// دالة لإزالة خطأ رقم الهاتف
function removePhoneError() {
    const phoneInput = document.getElementById('phone');
    const existingError = phoneInput.parentElement.querySelector('.invalid-feedback');
    if (existingError) {
        existingError.remove();
    }
}

// دالة لإظهار رسائل التنبيه
function showToast(message, type = 'info') {
    const toastContainer = getOrCreateToastContainer();
    const toast = document.createElement('div');
    toast.className = `toast align-items-center text-white bg-${getBootstrapColor(type)} border-0`;
    toast.setAttribute('role', 'alert');
    toast.innerHTML = `
        <div class="d-flex">
            <div class="toast-body">
                <i class="fas fa-${getIcon(type)} me-2"></i>
                ${message}
            </div>
            <button type="button" class="btn-close btn-close-white me-2 m-auto" data-bs-dismiss="toast"></button>
        </div>
    `;
    
    toastContainer.appendChild(toast);
    const bsToast = new bootstrap.Toast(toast);
    bsToast.show();
    
    // إزالة التوست بعد إخفائه
    toast.addEventListener('hidden.bs.toast', function() {
        toast.remove();
    });
}

// دالة للحصول على حاوية التوست أو إنشاؤها
function getOrCreateToastContainer() {
    let container = document.querySelector('.toast-container');
    if (!container) {
        container = document.createElement('div');
        container.className = 'toast-container position-fixed top-0 end-0 p-3';
        container.style.zIndex = '9999';
        document.body.appendChild(container);
    }
    return container;
}

// دالة للحصول على لون Bootstrap حسب النوع
function getBootstrapColor(type) {
    const colors = {
        'success': 'success',
        'error': 'danger',
        'warning': 'warning',
        'info': 'info'
    };
    return colors[type] || 'info';
}

// دالة للحصول على الأيقونة حسب النوع
function getIcon(type) {
    const icons = {
        'success': 'check-circle',
        'error': 'exclamation-triangle',
        'warning': 'exclamation-circle',
        'info': 'info-circle'
    };
    return icons[type] || 'info-circle';
}

// دالة لتحسين الأداء - تأخير التنفيذ
function debounce(func, wait) {
    let timeout;
    return function executedFunction(...args) {
        const later = () => {
            clearTimeout(timeout);
            func(...args);
        };
        clearTimeout(timeout);
        timeout = setTimeout(later, wait);
    };
}

// تحسين أداء التحقق من رقم الهاتف
const debouncedPhoneValidation = debounce(validatePhoneNumber, 300);

// إضافة مستمع للأحداث المحسن
document.addEventListener('DOMContentLoaded', function() {
    const phoneInput = document.getElementById('phone');
    if (phoneInput) {
        phoneInput.addEventListener('input', function(e) {
            debouncedPhoneValidation(e.target.value);
        });
    }
});

// حفظ حالة النموذج في التخزين المحلي (اختياري)
function saveFormState() {
    const username = document.getElementById('username')?.value || '';
    const phone = document.getElementById('phone')?.value || '';
    
    localStorage.setItem('loginForm', JSON.stringify({
        username: username,
        phone: phone,
        timestamp: Date.now()
    }));
}

// استرداد حالة النموذج من التخزين المحلي
function restoreFormState() {
    const saved = localStorage.getItem('loginForm');
    if (saved) {
        try {
            const data = JSON.parse(saved);
            // التحقق من أن البيانات ليست قديمة (أقل من ساعة)
            if (Date.now() - data.timestamp < 3600000) {
                const usernameInput = document.getElementById('username');
                const phoneInput = document.getElementById('phone');
                
                if (usernameInput && data.username) {
                    usernameInput.value = data.username;
                }
                if (phoneInput && data.phone) {
                    phoneInput.value = data.phone;
                }
            } else {
                localStorage.removeItem('loginForm');
            }
        } catch (e) {
            localStorage.removeItem('loginForm');
        }
    }
}

// حفظ البيانات عند التغيير
document.addEventListener('DOMContentLoaded', function() {
    restoreFormState();
    
    const inputs = document.querySelectorAll('#username, #phone');
    inputs.forEach(input => {
        input.addEventListener('input', debounce(saveFormState, 1000));
    });
});

// مسح البيانات المحفوظة عند تسجيل الدخول بنجاح
window.addEventListener('beforeunload', function() {
    // إذا كان النموذج يتم إرساله، امسح البيانات المحفوظة
    if (document.querySelector('form')?.classList.contains('submitting')) {
        localStorage.removeItem('loginForm');
    }
});
