<?php
// فهرس هيكل الملفات المقسمة
header('Content-Type: text/html; charset=utf-8');
ini_set('default_charset', 'utf-8');
mb_internal_encoding('UTF-8');

echo "<!DOCTYPE html>";
echo "<html lang='ar' dir='rtl'>";
echo "<head>";
echo "<meta charset='UTF-8'>";
echo "<meta name='viewport' content='width=device-width, initial-scale=1.0'>";
echo "<title>فهرس هيكل الملفات المقسمة</title>";
echo "<link href='https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css' rel='stylesheet'>";
echo "<link href='https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css' rel='stylesheet'>";
echo "<style>";
echo "body { font-family: 'Se<PERSON>e UI', Tahoma, Geneva, Verdana, sans-serif; background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); min-height: 100vh; padding: 2rem; }";
echo ".system-card { background: white; border-radius: 15px; box-shadow: 0 10px 30px rgba(0,0,0,0.1); padding: 2rem; margin: 0 auto; max-width: 1400px; }";
echo ".file-tree { font-family: 'Courier New', monospace; background: #f8f9fa; padding: 20px; border-radius: 10px; }";
echo ".folder { color: #007bff; font-weight: bold; }";
echo ".file { color: #28a745; }";
echo ".description { color: #6c757d; font-style: italic; }";
echo ".section-header { background: linear-gradient(45deg, #007bff, #0056b3); color: white; padding: 15px; border-radius: 10px; margin: 20px 0 10px 0; }";
echo "</style>";
echo "</head>";
echo "<body>";

echo "<div class='system-card'>";
echo "<h1 class='text-center mb-4'><i class='fas fa-folder-tree'></i> فهرس هيكل الملفات المقسمة</h1>";

echo "<div class='alert alert-info'>";
echo "<h5><i class='fas fa-info-circle'></i> نظرة عامة</h5>";
echo "<p>تم تقسيم النظام إلى ملفات منفصلة ومنظمة لسهولة الصيانة والتطوير. كل ملف يحتوي على وظائف محددة ومترابطة.</p>";
echo "</div>";

// هيكل الملفات
echo "<div class='section-header'>";
echo "<h4><i class='fas fa-sitemap'></i> هيكل الملفات الكامل</h4>";
echo "</div>";

echo "<div class='file-tree'>";
echo "<pre>";
echo "📁 <span class='folder'>campaign_system/</span>\n";
echo "├── 📁 <span class='folder'>config/</span>\n";
echo "│   ├── 📄 <span class='file'>config.php</span> <span class='description'>- الإعدادات الأساسية</span>\n";
echo "│   └── 📄 <span class='file'>database.php</span> <span class='description'>- اتصال قاعدة البيانات</span>\n";
echo "│\n";
echo "├── 📁 <span class='folder'>database/</span> <span class='description'>- ملفات إنشاء الجداول</span>\n";
echo "│   ├── 📄 <span class='file'>create_users_tables.php</span> <span class='description'>- جداول المستخدمين والإداريين</span>\n";
echo "│   ├── 📄 <span class='file'>create_supporters_tables.php</span> <span class='description'>- جداول المؤيدين</span>\n";
echo "│   ├── 📄 <span class='file'>create_messages_tables.php</span> <span class='description'>- جداول الرسائل</span>\n";
echo "│   ├── 📄 <span class='file'>create_requests_tables.php</span> <span class='description'>- جداول المطالب</span>\n";
echo "│   ├── 📄 <span class='file'>create_reports_tables.php</span> <span class='description'>- جداول التقارير</span>\n";
echo "│   └── 📄 <span class='file'>create_notifications_tables.php</span> <span class='description'>- جداول الإشعارات</span>\n";
echo "│\n";
echo "├── 📁 <span class='folder'>includes/</span> <span class='description'>- ملفات الدوال المساعدة</span>\n";
echo "│   ├── 📄 <span class='file'>notification_functions.php</span> <span class='description'>- دوال الإشعارات</span>\n";
echo "│   ├── 📄 <span class='file'>supporter_functions.php</span> <span class='description'>- دوال المؤيدين</span>\n";
echo "│   ├── 📄 <span class='file'>request_functions.php</span> <span class='description'>- دوال المطالب</span>\n";
echo "│   ├── 📄 <span class='file'>message_functions.php</span> <span class='description'>- دوال الرسائل</span>\n";
echo "│   ├── 📄 <span class='file'>report_functions.php</span> <span class='description'>- دوال التقارير</span>\n";
echo "│   ├── 📄 <span class='file'>admin_functions.php</span> <span class='description'>- دوال الإداريين</span>\n";
echo "│   ├── 📄 <span class='file'>helper_functions.php</span> <span class='description'>- دوال مساعدة عامة</span>\n";
echo "│   └── 📄 <span class='file'>system_settings.php</span> <span class='description'>- إعدادات النظام</span>\n";
echo "│\n";
echo "├── 📁 <span class='folder'>modules/</span> <span class='description'>- وحدات النظام</span>\n";
echo "│   ├── 📁 <span class='folder'>admin/</span> <span class='description'>- وحدة الإداريين</span>\n";
echo "│   ├── 📁 <span class='folder'>candidate/</span> <span class='description'>- وحدة المرشح</span>\n";
echo "│   ├── 📁 <span class='folder'>supporters/</span> <span class='description'>- وحدة المؤيدين</span>\n";
echo "│   ├── 📁 <span class='folder'>requests/</span> <span class='description'>- وحدة المطالب</span>\n";
echo "│   ├── 📁 <span class='folder'>messages/</span> <span class='description'>- وحدة الرسائل</span>\n";
echo "│   └── 📁 <span class='folder'>reports/</span> <span class='description'>- وحدة التقارير</span>\n";
echo "│\n";
echo "├── 📁 <span class='folder'>uploads/</span> <span class='description'>- مجلدات الملفات المرفوعة</span>\n";
echo "│   ├── 📁 <span class='folder'>supporters/</span>\n";
echo "│   ├── 📁 <span class='folder'>requests/</span>\n";
echo "│   ├── 📁 <span class='folder'>messages/</span>\n";
echo "│   ├── 📁 <span class='folder'>reports/</span>\n";
echo "│   └── 📁 <span class='folder'>temp/</span>\n";
echo "│\n";
echo "├── 📁 <span class='folder'>assets/</span> <span class='description'>- الملفات الثابتة</span>\n";
echo "│   ├── 📁 <span class='folder'>css/</span>\n";
echo "│   ├── 📁 <span class='folder'>js/</span>\n";
echo "│   └── 📁 <span class='folder'>images/</span>\n";
echo "│\n";
echo "├── 📁 <span class='folder'>logs/</span> <span class='description'>- ملفات السجلات</span>\n";
echo "├── 📁 <span class='folder'>cache/</span> <span class='description'>- ملفات التخزين المؤقت</span>\n";
echo "├── 📁 <span class='folder'>backups/</span> <span class='description'>- النسخ الاحتياطية</span>\n";
echo "│\n";
echo "├── 📄 <span class='file'>dashboard.php</span> <span class='description'>- الصفحة الرئيسية</span>\n";
echo "├── 📄 <span class='file'>create_all_database.php</span> <span class='description'>- إنشاء جميع الجداول</span>\n";
echo "├── 📄 <span class='file'>file_structure_index.php</span> <span class='description'>- فهرس الملفات (هذا الملف)</span>\n";
echo "└── 📄 <span class='file'>README.md</span> <span class='description'>- دليل النظام</span>\n";
echo "</pre>";
echo "</div>";

// وصف الملفات الرئيسية
echo "<div class='section-header'>";
echo "<h4><i class='fas fa-file-code'></i> وصف الملفات الرئيسية</h4>";
echo "</div>";

echo "<div class='row'>";

// ملفات قاعدة البيانات
echo "<div class='col-md-6'>";
echo "<div class='card mb-3'>";
echo "<div class='card-header bg-primary text-white'>";
echo "<h6><i class='fas fa-database'></i> ملفات قاعدة البيانات</h6>";
echo "</div>";
echo "<div class='card-body'>";
echo "<ul class='list-unstyled'>";
echo "<li><strong>create_users_tables.php:</strong> إنشاء جداول المستخدمين، الإداريين، المناطق، الصلاحيات، والجلسات</li>";
echo "<li><strong>create_supporters_tables.php:</strong> إنشاء جداول المؤيدين، المرفقات، الاتصالات، العائلات، والمجموعات</li>";
echo "<li><strong>create_messages_tables.php:</strong> إنشاء جداول الرسائل، المرفقات، القراءة، القوالب، والمجلدات</li>";
echo "<li><strong>create_requests_tables.php:</strong> إنشاء جداول المطالب، المرفقات، التاريخ، التعليقات، والفئات</li>";
echo "<li><strong>create_reports_tables.php:</strong> إنشاء جداول التقارير، المرفقات، القوالب، التعليقات، والإحصائيات</li>";
echo "<li><strong>create_notifications_tables.php:</strong> إنشاء جداول الإشعارات، سجل الأنشطة، الإعدادات، والقوالب</li>";
echo "</ul>";
echo "</div>";
echo "</div>";
echo "</div>";

// ملفات الدوال
echo "<div class='col-md-6'>";
echo "<div class='card mb-3'>";
echo "<div class='card-header bg-success text-white'>";
echo "<h6><i class='fas fa-code'></i> ملفات الدوال</h6>";
echo "</div>";
echo "<div class='card-body'>";
echo "<ul class='list-unstyled'>";
echo "<li><strong>notification_functions.php:</strong> إنشاء وإدارة الإشعارات، تسجيل الأنشطة</li>";
echo "<li><strong>supporter_functions.php:</strong> إضافة وإدارة المؤيدين، المرفقات، الاتصالات</li>";
echo "<li><strong>request_functions.php:</strong> إنشاء وإدارة المطالب، تغيير الحالات، التعليقات</li>";
echo "<li><strong>message_functions.php:</strong> إرسال واستقبال الرسائل، المرفقات، البحث</li>";
echo "<li><strong>report_functions.php:</strong> إنشاء ومراجعة التقارير، المرفقات، الإحصائيات</li>";
echo "<li><strong>admin_functions.php:</strong> إدارة الإداريين، الصلاحيات، الجلسات</li>";
echo "<li><strong>helper_functions.php:</strong> دوال مساعدة عامة للتنسيق والتحقق</li>";
echo "<li><strong>system_settings.php:</strong> إعدادات النظام والثوابت</li>";
echo "</ul>";
echo "</div>";
echo "</div>";
echo "</div>";

echo "</div>";

// الميزات الرئيسية
echo "<div class='section-header'>";
echo "<h4><i class='fas fa-star'></i> الميزات الرئيسية للنظام المقسم</h4>";
echo "</div>";

echo "<div class='row'>";

echo "<div class='col-md-4'>";
echo "<div class='card mb-3 border-primary'>";
echo "<div class='card-header bg-primary text-white'>";
echo "<h6><i class='fas fa-puzzle-piece'></i> التنظيم المودولي</h6>";
echo "</div>";
echo "<div class='card-body'>";
echo "<ul class='list-unstyled'>";
echo "<li>✅ فصل الوظائف حسب النوع</li>";
echo "<li>✅ سهولة الصيانة والتطوير</li>";
echo "<li>✅ إعادة استخدام الكود</li>";
echo "<li>✅ تقليل التعقيد</li>";
echo "</ul>";
echo "</div>";
echo "</div>";
echo "</div>";

echo "<div class='col-md-4'>";
echo "<div class='card mb-3 border-success'>";
echo "<div class='card-header bg-success text-white'>";
echo "<h6><i class='fas fa-database'></i> قاعدة البيانات المنظمة</h6>";
echo "</div>";
echo "<div class='card-body'>";
echo "<ul class='list-unstyled'>";
echo "<li>✅ جداول محسنة ومفهرسة</li>";
echo "<li>✅ علاقات واضحة ومترابطة</li>";
echo "<li>✅ دعم البحث النصي الكامل</li>";
echo "<li>✅ إحصائيات شاملة</li>";
echo "</ul>";
echo "</div>";
echo "</div>";
echo "</div>";

echo "<div class='col-md-4'>";
echo "<div class='card mb-3 border-info'>";
echo "<div class='card-header bg-info text-white'>";
echo "<h6><i class='fas fa-bell'></i> نظام الإشعارات المتقدم</h6>";
echo "</div>";
echo "<div class='card-body'>";
echo "<ul class='list-unstyled'>";
echo "<li>✅ إشعارات فورية ومجدولة</li>";
echo "<li>✅ قوالب قابلة للتخصيص</li>";
echo "<li>✅ تتبع شامل للأنشطة</li>";
echo "<li>✅ إعدادات مخصصة للمستخدمين</li>";
echo "</ul>";
echo "</div>";
echo "</div>";
echo "</div>";

echo "</div>";

// روابط سريعة
echo "<div class='section-header'>";
echo "<h4><i class='fas fa-link'></i> روابط سريعة للنظام</h4>";
echo "</div>";

echo "<div class='row'>";

echo "<div class='col-md-3'>";
echo "<div class='card mb-3'>";
echo "<div class='card-header bg-warning text-dark'>";
echo "<h6><i class='fas fa-database'></i> إنشاء قاعدة البيانات</h6>";
echo "</div>";
echo "<div class='card-body text-center'>";
echo "<a href='create_all_database.php' class='btn btn-warning btn-lg w-100'>";
echo "<i class='fas fa-database'></i><br>إنشاء جميع الجداول";
echo "</a>";
echo "</div>";
echo "</div>";
echo "</div>";

echo "<div class='col-md-3'>";
echo "<div class='card mb-3'>";
echo "<div class='card-header bg-primary text-white'>";
echo "<h6><i class='fas fa-users'></i> جداول المستخدمين</h6>";
echo "</div>";
echo "<div class='card-body text-center'>";
echo "<a href='database/create_users_tables.php' class='btn btn-primary btn-lg w-100'>";
echo "<i class='fas fa-users'></i><br>المستخدمين والإداريين";
echo "</a>";
echo "</div>";
echo "</div>";
echo "</div>";

echo "<div class='col-md-3'>";
echo "<div class='card mb-3'>";
echo "<div class='card-header bg-success text-white'>";
echo "<h6><i class='fas fa-user-friends'></i> جداول المؤيدين</h6>";
echo "</div>";
echo "<div class='card-body text-center'>";
echo "<a href='database/create_supporters_tables.php' class='btn btn-success btn-lg w-100'>";
echo "<i class='fas fa-user-friends'></i><br>المؤيدين والمرفقات";
echo "</a>";
echo "</div>";
echo "</div>";
echo "</div>";

echo "<div class='col-md-3'>";
echo "<div class='card mb-3'>";
echo "<div class='card-header bg-info text-white'>";
echo "<h6><i class='fas fa-envelope'></i> جداول الرسائل</h6>";
echo "</div>";
echo "<div class='card-body text-center'>";
echo "<a href='database/create_messages_tables.php' class='btn btn-info btn-lg w-100'>";
echo "<i class='fas fa-envelope'></i><br>الرسائل والمرفقات";
echo "</a>";
echo "</div>";
echo "</div>";
echo "</div>";

echo "<div class='col-md-3'>";
echo "<div class='card mb-3'>";
echo "<div class='card-header bg-secondary text-white'>";
echo "<h6><i class='fas fa-hand-holding-heart'></i> جداول المطالب</h6>";
echo "</div>";
echo "<div class='card-body text-center'>";
echo "<a href='database/create_requests_tables.php' class='btn btn-secondary btn-lg w-100'>";
echo "<i class='fas fa-hand-holding-heart'></i><br>المطالب والتعليقات";
echo "</a>";
echo "</div>";
echo "</div>";
echo "</div>";

echo "<div class='col-md-3'>";
echo "<div class='card mb-3'>";
echo "<div class='card-header bg-dark text-white'>";
echo "<h6><i class='fas fa-file-alt'></i> جداول التقارير</h6>";
echo "</div>";
echo "<div class='card-body text-center'>";
echo "<a href='database/create_reports_tables.php' class='btn btn-dark btn-lg w-100'>";
echo "<i class='fas fa-file-alt'></i><br>التقارير والإحصائيات";
echo "</a>";
echo "</div>";
echo "</div>";
echo "</div>";

echo "<div class='col-md-3'>";
echo "<div class='card mb-3'>";
echo "<div class='card-header bg-danger text-white'>";
echo "<h6><i class='fas fa-bell'></i> جداول الإشعارات</h6>";
echo "</div>";
echo "<div class='card-body text-center'>";
echo "<a href='database/create_notifications_tables.php' class='btn btn-danger btn-lg w-100'>";
echo "<i class='fas fa-bell'></i><br>الإشعارات والأنشطة";
echo "</a>";
echo "</div>";
echo "</div>";
echo "</div>";

echo "<div class='col-md-3'>";
echo "<div class='card mb-3'>";
echo "<div class='card-header' style='background: linear-gradient(45deg, #667eea, #764ba2); color: white;'>";
echo "<h6><i class='fas fa-home'></i> الصفحة الرئيسية</h6>";
echo "</div>";
echo "<div class='card-body text-center'>";
echo "<a href='dashboard.php' class='btn btn-lg w-100' style='background: linear-gradient(45deg, #667eea, #764ba2); color: white;'>";
echo "<i class='fas fa-home'></i><br>العودة للرئيسية";
echo "</a>";
echo "</div>";
echo "</div>";
echo "</div>";

echo "</div>";

// معلومات إضافية
echo "<div class='alert alert-success'>";
echo "<h5><i class='fas fa-check-circle'></i> النظام جاهز للاستخدام</h5>";
echo "<p>تم تقسيم النظام بنجاح إلى ملفات منفصلة ومنظمة. يمكنك الآن:</p>";
echo "<ul>";
echo "<li>إنشاء قاعدة البيانات الكاملة بنقرة واحدة</li>";
echo "<li>إنشاء جداول محددة حسب الحاجة</li>";
echo "<li>استخدام الدوال المقسمة في التطوير</li>";
echo "<li>صيانة وتطوير كل وحدة بشكل منفصل</li>";
echo "</ul>";
echo "</div>";

echo "</div>";
echo "</body>";
echo "</html>";
?>
