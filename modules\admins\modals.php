<!-- نافذة إضافة إداري جديد -->
<div class="modal fade" id="addAdminModal" tabindex="-1" aria-labelledby="addAdminModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="addAdminModalLabel">
                    <i class="fas fa-user-plus me-2"></i>
                    إضافة إداري جديد
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="إغلاق"></button>
            </div>
            <form method="POST" action="" class="admin-form needs-validation" novalidate>
                <input type="hidden" name="action" value="add">
                <div class="modal-body">
                    <div class="row">
                        <!-- الاسم الكامل -->
                        <div class="col-md-6 mb-3">
                            <label for="full_name" class="form-label">الاسم الكامل <span class="required">*</span></label>
                            <input type="text" class="form-control" id="full_name" name="full_name" required>
                            <div class="invalid-feedback">يرجى إدخال الاسم الكامل</div>
                        </div>
                        
                        <!-- اسم المستخدم -->
                        <div class="col-md-6 mb-3">
                            <label for="username" class="form-label">اسم المستخدم <span class="required">*</span></label>
                            <input type="text" class="form-control" id="username" name="username" required>
                            <div class="invalid-feedback">يرجى إدخال اسم المستخدم</div>
                            <small class="form-text text-muted">سيستخدم لتسجيل الدخول</small>
                        </div>
                        
                        <!-- كلمة المرور -->
                        <div class="col-md-6 mb-3">
                            <label for="password" class="form-label">كلمة المرور <span class="required">*</span></label>
                            <div class="input-group">
                                <input type="password" class="form-control" id="password" name="password" required minlength="6">
                                <button class="btn btn-outline-secondary" type="button" onclick="togglePassword('password')">
                                    <i class="fas fa-eye" id="passwordIcon"></i>
                                </button>
                            </div>
                            <div class="invalid-feedback">كلمة المرور يجب أن تكون 6 أحرف على الأقل</div>
                        </div>
                        
                        <!-- تأكيد كلمة المرور -->
                        <div class="col-md-6 mb-3">
                            <label for="confirm_password" class="form-label">تأكيد كلمة المرور <span class="required">*</span></label>
                            <input type="password" class="form-control" id="confirm_password" name="confirm_password" required>
                            <div class="invalid-feedback">كلمات المرور غير متطابقة</div>
                        </div>
                        
                        <!-- رقم الهاتف -->
                        <div class="col-md-6 mb-3">
                            <label for="phone" class="form-label">رقم الهاتف <span class="required">*</span></label>
                            <input type="tel" class="form-control" id="phone" name="phone" placeholder="07XXXXXXXXX" required>
                            <div class="invalid-feedback">يرجى إدخال رقم هاتف صحيح</div>
                        </div>
                        
                        <!-- المنطقة -->
                        <div class="col-md-6 mb-3">
                            <label for="region_id" class="form-label">المنطقة المسؤول عنها</label>
                            <select class="form-select" id="region_id" name="region_id">
                                <option value="">جميع المناطق</option>
                                <?php foreach ($regions as $region): ?>
                                    <option value="<?php echo $region['id']; ?>">
                                        <?php echo htmlspecialchars($region['name']); ?>
                                    </option>
                                <?php endforeach; ?>
                            </select>
                            <small class="form-text text-muted">اتركه فارغاً للوصول لجميع المناطق</small>
                        </div>
                    </div>
                    
                    <div class="alert alert-info">
                        <i class="fas fa-info-circle me-2"></i>
                        <strong>ملاحظة:</strong> سيتم إرسال بيانات تسجيل الدخول للإداري عبر رسالة نصية.
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                        <i class="fas fa-times me-2"></i>إلغاء
                    </button>
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-save me-2"></i>حفظ الإداري
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- نافذة تعديل الإداري -->
<div class="modal fade" id="editAdminModal" tabindex="-1" aria-labelledby="editAdminModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="editAdminModalLabel">
                    <i class="fas fa-user-edit me-2"></i>
                    تعديل بيانات الإداري
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="إغلاق"></button>
            </div>
            <form method="POST" action="" class="admin-form needs-validation" novalidate>
                <input type="hidden" name="action" value="edit">
                <input type="hidden" id="editId" name="id">
                <div class="modal-body">
                    <div class="row">
                        <!-- الاسم الكامل -->
                        <div class="col-md-6 mb-3">
                            <label for="editFullName" class="form-label">الاسم الكامل <span class="required">*</span></label>
                            <input type="text" class="form-control" id="editFullName" name="full_name" required>
                        </div>
                        
                        <!-- اسم المستخدم -->
                        <div class="col-md-6 mb-3">
                            <label for="editUsername" class="form-label">اسم المستخدم <span class="required">*</span></label>
                            <input type="text" class="form-control" id="editUsername" name="username" required>
                        </div>
                        
                        <!-- كلمة المرور الجديدة -->
                        <div class="col-md-6 mb-3">
                            <label for="editPassword" class="form-label">كلمة المرور الجديدة</label>
                            <div class="input-group">
                                <input type="password" class="form-control" id="editPassword" name="password" minlength="6">
                                <button class="btn btn-outline-secondary" type="button" onclick="togglePassword('editPassword')">
                                    <i class="fas fa-eye" id="editPasswordIcon"></i>
                                </button>
                            </div>
                            <small class="form-text text-muted">اتركه فارغاً للاحتفاظ بكلمة المرور الحالية</small>
                        </div>
                        
                        <!-- رقم الهاتف -->
                        <div class="col-md-6 mb-3">
                            <label for="editPhone" class="form-label">رقم الهاتف <span class="required">*</span></label>
                            <input type="tel" class="form-control" id="editPhone" name="phone" required>
                        </div>
                        
                        <!-- المنطقة -->
                        <div class="col-md-12 mb-3">
                            <label for="editRegionId" class="form-label">المنطقة المسؤول عنها</label>
                            <select class="form-select" id="editRegionId" name="region_id">
                                <option value="">جميع المناطق</option>
                                <?php foreach ($regions as $region): ?>
                                    <option value="<?php echo $region['id']; ?>">
                                        <?php echo htmlspecialchars($region['name']); ?>
                                    </option>
                                <?php endforeach; ?>
                            </select>
                        </div>
                    </div>
                    
                    <div class="alert alert-warning">
                        <i class="fas fa-exclamation-triangle me-2"></i>
                        <strong>تنبيه:</strong> تعديل بيانات الإداري سيؤثر على صلاحياته في النظام.
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                        <i class="fas fa-times me-2"></i>إلغاء
                    </button>
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-save me-2"></i>حفظ التغييرات
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- نافذة عرض تفاصيل الإداري -->
<div class="modal fade" id="viewAdminModal" tabindex="-1" aria-labelledby="viewAdminModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="viewAdminModalLabel">
                    <i class="fas fa-user me-2"></i>
                    تفاصيل الإداري
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="إغلاق"></button>
            </div>
            <div class="modal-body">
                <div class="admin-details">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="detail-item">
                                <span class="detail-label">الاسم الكامل:</span>
                                <span class="detail-value" id="viewFullName"></span>
                            </div>
                            <div class="detail-item">
                                <span class="detail-label">اسم المستخدم:</span>
                                <span class="detail-value" id="viewUsername"></span>
                            </div>
                            <div class="detail-item">
                                <span class="detail-label">رقم الهاتف:</span>
                                <span class="detail-value" id="viewPhone"></span>
                            </div>
                            <div class="detail-item">
                                <span class="detail-label">المنطقة:</span>
                                <span class="detail-value" id="viewRegion"></span>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="detail-item">
                                <span class="detail-label">عدد المؤيدين:</span>
                                <span class="detail-value" id="viewSupportersCount"></span>
                            </div>
                            <div class="detail-item">
                                <span class="detail-label">الحالة:</span>
                                <span class="detail-value" id="viewStatus"></span>
                            </div>
                            <div class="detail-item">
                                <span class="detail-label">تاريخ الإضافة:</span>
                                <span class="detail-value" id="viewCreatedAt"></span>
                            </div>
                            <div class="detail-item">
                                <span class="detail-label">آخر تسجيل دخول:</span>
                                <span class="detail-value" id="viewLastLogin"></span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                    <i class="fas fa-times me-2"></i>إغلاق
                </button>
                <button type="button" class="btn btn-warning" onclick="editAdmin(document.getElementById('editId').value)">
                    <i class="fas fa-edit me-2"></i>تعديل
                </button>
                <button type="button" class="btn btn-info" onclick="sendMessage()">
                    <i class="fas fa-envelope me-2"></i>إرسال رسالة
                </button>
            </div>
        </div>
    </div>
</div>

<script>
// تبديل إظهار/إخفاء كلمة المرور
function togglePassword(inputId) {
    const input = document.getElementById(inputId);
    const icon = document.getElementById(inputId + 'Icon');
    
    if (input.type === 'password') {
        input.type = 'text';
        icon.classList.remove('fa-eye');
        icon.classList.add('fa-eye-slash');
    } else {
        input.type = 'password';
        icon.classList.remove('fa-eye-slash');
        icon.classList.add('fa-eye');
    }
}

// التحقق من تطابق كلمات المرور
document.getElementById('confirm_password').addEventListener('input', function() {
    const password = document.getElementById('password').value;
    const confirmPassword = this.value;
    
    if (password !== confirmPassword) {
        this.setCustomValidity('كلمات المرور غير متطابقة');
        this.classList.add('is-invalid');
        this.classList.remove('is-valid');
    } else {
        this.setCustomValidity('');
        this.classList.remove('is-invalid');
        this.classList.add('is-valid');
    }
});

// تنسيق تفاصيل الإداري
document.addEventListener('DOMContentLoaded', function() {
    const detailItems = document.querySelectorAll('.detail-item');
    detailItems.forEach(item => {
        item.style.display = 'flex';
        item.style.justifyContent = 'space-between';
        item.style.alignItems = 'center';
        item.style.padding = '10px 0';
        item.style.borderBottom = '1px solid #eee';
    });
    
    const detailLabels = document.querySelectorAll('.detail-label');
    detailLabels.forEach(label => {
        label.style.fontWeight = '600';
        label.style.color = '#495057';
        label.style.minWidth = '120px';
    });
    
    const detailValues = document.querySelectorAll('.detail-value');
    detailValues.forEach(value => {
        value.style.color = '#6c757d';
        value.style.textAlign = 'left';
    });
});
</script>
