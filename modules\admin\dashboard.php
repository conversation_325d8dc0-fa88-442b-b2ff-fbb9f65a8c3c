<?php
// لوحة تحكم الإداريين
session_start();
header('Content-Type: text/html; charset=utf-8');
ini_set('default_charset', 'utf-8');
mb_internal_encoding('UTF-8');

require_once '../../config/config.php';
require_once '../../config/database.php';

// التحقق من تسجيل الدخول
if (!isset($_SESSION['admin_id'])) {
    redirect('login.php');
}

$admin_id = $_SESSION['admin_id'];
$admin_name = $_SESSION['admin_name'];
$admin_role = $_SESSION['admin_role'];
$admin_region_id = $_SESSION['admin_region_id'];

// جلب إحصائيات الإداري
$today = date('Y-m-d');
$week_start = date('Y-m-d', strtotime('monday this week'));
$month_start = date('Y-m-01');

// إحصائيات المؤيدين
$my_supporters_today = fetchOne("SELECT COUNT(*) as count FROM supporters WHERE added_by = ? AND DATE(created_at) = ?", [$admin_id, $today])['count'];
$my_supporters_week = fetchOne("SELECT COUNT(*) as count FROM supporters WHERE added_by = ? AND DATE(created_at) >= ?", [$admin_id, $week_start])['count'];
$my_supporters_month = fetchOne("SELECT COUNT(*) as count FROM supporters WHERE added_by = ? AND DATE(created_at) >= ?", [$admin_id, $month_start])['count'];
$my_supporters_total = fetchOne("SELECT COUNT(*) as count FROM supporters WHERE added_by = ?", [$admin_id])['count'];

// إحصائيات الرسائل
$unread_messages = fetchOne("SELECT COUNT(*) as count FROM messages WHERE receiver_type = 'admin' AND receiver_id = ? AND status = 'unread'", [$admin_id])['count'];
$total_messages = fetchOne("SELECT COUNT(*) as count FROM messages WHERE receiver_type = 'admin' AND receiver_id = ?", [$admin_id])['count'];

// إحصائيات المطالب
$pending_requests = fetchOne("SELECT COUNT(*) as count FROM supporter_requests WHERE admin_id = ? AND status = 'pending'", [$admin_id])['count'];
$total_requests = fetchOne("SELECT COUNT(*) as count FROM supporter_requests WHERE admin_id = ?", [$admin_id])['count'];

// إحصائيات التقارير
$pending_reports = fetchOne("SELECT COUNT(*) as count FROM weekly_reports WHERE admin_id = ? AND status = 'draft'", [$admin_id])['count'];

// جلب آخر الرسائل
$recent_messages = fetchAll("SELECT * FROM messages WHERE receiver_type = 'admin' AND receiver_id = ? ORDER BY created_at DESC LIMIT 5", [$admin_id]);

// جلب آخر المطالب
$recent_requests = fetchAll("SELECT * FROM supporter_requests WHERE admin_id = ? ORDER BY created_at DESC LIMIT 5", [$admin_id]);
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>لوحة تحكم الإداريين - <?php echo SITE_NAME; ?></title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: #f8f9fa;
        }
        .main-content {
            margin-top: 80px;
        }
        .sidebar {
            background: #343a40;
            min-height: calc(100vh - 80px);
            padding-top: 20px;
        }
        .sidebar .nav-link {
            color: #adb5bd;
            padding: 12px 20px;
            border-radius: 0;
            transition: all 0.3s;
        }
        .sidebar .nav-link:hover,
        .sidebar .nav-link.active {
            color: white;
            background: #495057;
        }
        .stat-card {
            border-radius: 15px;
            border: none;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
            transition: transform 0.3s;
        }
        .stat-card:hover {
            transform: translateY(-5px);
        }
        .stat-icon {
            font-size: 2.5rem;
            opacity: 0.8;
        }
        .welcome-card {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border-radius: 15px;
            padding: 2rem;
            margin-bottom: 2rem;
        }
        .quick-action-btn {
            border-radius: 10px;
            padding: 15px;
            margin-bottom: 10px;
            transition: all 0.3s;
        }
        .quick-action-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0,0,0,0.2);
        }
    </style>
</head>
<body>
    <!-- شريط التنقل العلوي -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-dark fixed-top">
        <div class="container-fluid">
            <a class="navbar-brand" href="dashboard.php">
                <i class="fas fa-user-shield me-2"></i>
                لوحة تحكم الإداريين
            </a>

            <div class="navbar-nav ms-auto">
                <div class="nav-item dropdown">
                    <a class="nav-link dropdown-toggle" href="#" id="userDropdown" role="button" data-bs-toggle="dropdown">
                        <i class="fas fa-user-circle me-1"></i>
                        مرحباً، <?php echo htmlspecialchars($admin_name); ?>
                    </a>
                    <ul class="dropdown-menu dropdown-menu-end">
                        <li><a class="dropdown-item" href="profile.php"><i class="fas fa-user me-2"></i>الملف الشخصي</a></li>
                        <li><a class="dropdown-item" href="settings.php"><i class="fas fa-cog me-2"></i>الإعدادات</a></li>
                        <li><hr class="dropdown-divider"></li>
                        <li><a class="dropdown-item" href="logout.php"><i class="fas fa-sign-out-alt me-2"></i>تسجيل الخروج</a></li>
                    </ul>
                </div>
            </div>
        </div>
    </nav>

    <!-- المحتوى الرئيسي -->
    <div class="container-fluid main-content">
        <div class="row">
            <!-- الشريط الجانبي -->
            <nav class="col-md-3 col-lg-2 d-md-block sidebar">
                <div class="position-sticky pt-3">
                    <ul class="nav flex-column">
                        <li class="nav-item">
                            <a class="nav-link active" href="dashboard.php">
                                <i class="fas fa-tachometer-alt me-2"></i>
                                لوحة التحكم
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="supporters.php">
                                <i class="fas fa-users me-2"></i>
                                المؤيدين
                                <span class="badge bg-primary ms-2"><?php echo $my_supporters_total; ?></span>
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="messages.php">
                                <i class="fas fa-envelope me-2"></i>
                                الرسائل
                                <?php if ($unread_messages > 0): ?>
                                <span class="badge bg-danger ms-2"><?php echo $unread_messages; ?></span>
                                <?php endif; ?>
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="requests.php">
                                <i class="fas fa-hand-holding-heart me-2"></i>
                                مطالب المؤيدين
                                <?php if ($pending_requests > 0): ?>
                                <span class="badge bg-warning ms-2"><?php echo $pending_requests; ?></span>
                                <?php endif; ?>
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="reports.php">
                                <i class="fas fa-file-alt me-2"></i>
                                التقارير الأسبوعية
                                <?php if ($pending_reports > 0): ?>
                                <span class="badge bg-info ms-2"><?php echo $pending_reports; ?></span>
                                <?php endif; ?>
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="statistics.php">
                                <i class="fas fa-chart-bar me-2"></i>
                                الإحصائيات
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="../../dashboard.php">
                                <i class="fas fa-home me-2"></i>
                                الصفحة الرئيسية
                            </a>
                        </li>
                    </ul>
                </div>
            </nav>

            <!-- المحتوى الرئيسي -->
            <main class="col-md-9 ms-sm-auto col-lg-10 px-md-4">
                <!-- ترحيب -->
                <div class="welcome-card">
                    <div class="row align-items-center">
                        <div class="col-md-8">
                            <h2>
                                <i class="fas fa-sun me-2"></i>
                                مرحباً بك، <?php echo htmlspecialchars($admin_name); ?>!
                            </h2>
                            <p class="mb-0">
                                <i class="fas fa-calendar me-2"></i>
                                اليوم هو <?php echo date('l, F j, Y'); ?>
                            </p>
                            <p class="mb-0">
                                <i class="fas fa-clock me-2"></i>
                                آخر تسجيل دخول: <?php echo date('Y-m-d H:i'); ?>
                            </p>
                        </div>
                        <div class="col-md-4 text-center">
                            <i class="fas fa-user-shield fa-5x opacity-50"></i>
                        </div>
                    </div>
                </div>

                <!-- الإحصائيات -->
                <div class="row mb-4">
                    <div class="col-md-3">
                        <div class="card stat-card bg-primary text-white">
                            <div class="card-body">
                                <div class="d-flex justify-content-between">
                                    <div>
                                        <h3><?php echo number_format($my_supporters_today); ?></h3>
                                        <p class="mb-0">مؤيدين اليوم</p>
                                    </div>
                                    <div class="align-self-center">
                                        <i class="fas fa-user-plus stat-icon"></i>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="col-md-3">
                        <div class="card stat-card bg-success text-white">
                            <div class="card-body">
                                <div class="d-flex justify-content-between">
                                    <div>
                                        <h3><?php echo number_format($my_supporters_week); ?></h3>
                                        <p class="mb-0">مؤيدين هذا الأسبوع</p>
                                    </div>
                                    <div class="align-self-center">
                                        <i class="fas fa-users stat-icon"></i>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="col-md-3">
                        <div class="card stat-card bg-warning text-white">
                            <div class="card-body">
                                <div class="d-flex justify-content-between">
                                    <div>
                                        <h3><?php echo number_format($unread_messages); ?></h3>
                                        <p class="mb-0">رسائل غير مقروءة</p>
                                    </div>
                                    <div class="align-self-center">
                                        <i class="fas fa-envelope stat-icon"></i>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="col-md-3">
                        <div class="card stat-card bg-info text-white">
                            <div class="card-body">
                                <div class="d-flex justify-content-between">
                                    <div>
                                        <h3><?php echo number_format($pending_requests); ?></h3>
                                        <p class="mb-0">مطالب معلقة</p>
                                    </div>
                                    <div class="align-self-center">
                                        <i class="fas fa-hand-holding-heart stat-icon"></i>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- الإجراءات السريعة -->
                <div class="row mb-4">
                    <div class="col-md-6">
                        <div class="card">
                            <div class="card-header">
                                <h5><i class="fas fa-bolt me-2"></i>الإجراءات السريعة</h5>
                            </div>
                            <div class="card-body">
                                <a href="add_supporter.php" class="btn btn-primary quick-action-btn w-100">
                                    <i class="fas fa-user-plus me-2"></i>إضافة مؤيد جديد
                                </a>
                                <a href="requests.php?action=add" class="btn btn-warning quick-action-btn w-100">
                                    <i class="fas fa-hand-holding-heart me-2"></i>إضافة مطلب مؤيد
                                </a>
                                <a href="messages.php?action=compose" class="btn btn-info quick-action-btn w-100">
                                    <i class="fas fa-pen me-2"></i>إرسال رسالة للإدارة
                                </a>
                                <a href="reports.php?action=create" class="btn btn-success quick-action-btn w-100">
                                    <i class="fas fa-file-alt me-2"></i>كتابة تقرير أسبوعي
                                </a>
                            </div>
                        </div>
                    </div>

                    <div class="col-md-6">
                        <div class="card">
                            <div class="card-header">
                                <h5><i class="fas fa-chart-pie me-2"></i>إحصائيات شاملة</h5>
                            </div>
                            <div class="card-body">
                                <div class="row text-center">
                                    <div class="col-6">
                                        <h4 class="text-primary"><?php echo number_format($my_supporters_total); ?></h4>
                                        <small>إجمالي المؤيدين</small>
                                    </div>
                                    <div class="col-6">
                                        <h4 class="text-success"><?php echo number_format($my_supporters_month); ?></h4>
                                        <small>مؤيدين هذا الشهر</small>
                                    </div>
                                    <div class="col-6 mt-3">
                                        <h4 class="text-warning"><?php echo number_format($total_requests); ?></h4>
                                        <small>إجمالي المطالب</small>
                                    </div>
                                    <div class="col-6 mt-3">
                                        <h4 class="text-info"><?php echo number_format($total_messages); ?></h4>
                                        <small>إجمالي الرسائل</small>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- آخر الأنشطة -->
                <div class="row">
                    <div class="col-md-6">
                        <div class="card">
                            <div class="card-header">
                                <h5><i class="fas fa-envelope me-2"></i>آخر الرسائل</h5>
                            </div>
                            <div class="card-body">
                                <?php if (empty($recent_messages)): ?>
                                    <p class="text-muted text-center">لا توجد رسائل</p>
                                <?php else: ?>
                                    <?php foreach ($recent_messages as $message): ?>
                                    <div class="d-flex justify-content-between align-items-center border-bottom py-2">
                                        <div>
                                            <strong><?php echo htmlspecialchars($message['subject']); ?></strong>
                                            <br>
                                            <small class="text-muted"><?php echo date('Y-m-d H:i', strtotime($message['created_at'])); ?></small>
                                        </div>
                                        <span class="badge <?php echo $message['status'] == 'unread' ? 'bg-danger' : 'bg-secondary'; ?>">
                                            <?php echo $message['status'] == 'unread' ? 'جديد' : 'مقروء'; ?>
                                        </span>
                                    </div>
                                    <?php endforeach; ?>
                                    <div class="text-center mt-3">
                                        <a href="messages.php" class="btn btn-outline-primary btn-sm">عرض جميع الرسائل</a>
                                    </div>
                                <?php endif; ?>
                            </div>
                        </div>
                    </div>

                    <div class="col-md-6">
                        <div class="card">
                            <div class="card-header">
                                <h5><i class="fas fa-hand-holding-heart me-2"></i>آخر المطالب</h5>
                            </div>
                            <div class="card-body">
                                <?php if (empty($recent_requests)): ?>
                                    <p class="text-muted text-center">لا توجد مطالب</p>
                                <?php else: ?>
                                    <?php foreach ($recent_requests as $request): ?>
                                    <div class="d-flex justify-content-between align-items-center border-bottom py-2">
                                        <div>
                                            <strong><?php echo htmlspecialchars($request['title']); ?></strong>
                                            <br>
                                            <small class="text-muted"><?php echo htmlspecialchars($request['supporter_name']); ?></small>
                                        </div>
                                        <span class="badge bg-<?php
                                            echo $request['status'] == 'pending' ? 'warning' :
                                                ($request['status'] == 'completed' ? 'success' : 'info');
                                        ?>">
                                            <?php
                                            $status_map = [
                                                'pending' => 'قيد الانتظار',
                                                'received' => 'تم الاستلام',
                                                'in_progress' => 'جاري العمل',
                                                'completed' => 'مكتمل',
                                                'rejected' => 'مرفوض'
                                            ];
                                            echo $status_map[$request['status']] ?? $request['status'];
                                            ?>
                                        </span>
                                    </div>
                                    <?php endforeach; ?>
                                    <div class="text-center mt-3">
                                        <a href="requests.php" class="btn btn-outline-warning btn-sm">عرض جميع المطالب</a>
                                    </div>
                                <?php endif; ?>
                            </div>
                        </div>
                    </div>
                </div>
            </main>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
