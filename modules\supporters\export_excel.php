<?php
// تصدير المؤيدين إلى Excel - للصفحة الأصلية
require_once '../../config/config.php';
require_once '../../config/database.php';

// التحقق من تسجيل الدخول
if (!isLoggedIn()) {
    redirect('../../login.php');
}

// بناء الاستعلام مع التصفية
$where_conditions = [];
$params = [];

// تصفية حسب المنطقة للإداريين
if (isAdmin() && $_SESSION['region_id']) {
    $where_conditions[] = "s.region_id = ?";
    $params[] = $_SESSION['region_id'];
}

// تطبيق فلاتر البحث إذا وجدت
if (!empty($_GET['search'])) {
    $search = '%' . $_GET['search'] . '%';
    $where_conditions[] = "(s.full_name LIKE ? OR s.phone LIKE ? OR s.voter_number LIKE ?)";
    $params = array_merge($params, [$search, $search, $search]);
}

if (!empty($_GET['region_id'])) {
    $where_conditions[] = "s.region_id = ?";
    $params[] = $_GET['region_id'];
}

if (!empty($_GET['gender'])) {
    $where_conditions[] = "s.gender = ?";
    $params[] = $_GET['gender'];
}

$where_clause = !empty($where_conditions) ? 'WHERE ' . implode(' AND ', $where_conditions) : '';

// جلب البيانات
$sql = "SELECT s.full_name, 
               CASE s.gender WHEN 'male' THEN 'ذكر' ELSE 'أنثى' END as gender,
               CASE s.marital_status 
                   WHEN 'single' THEN 'أعزب'
                   WHEN 'married' THEN 'متزوج'
                   WHEN 'divorced' THEN 'مطلق'
                   WHEN 'widowed' THEN 'أرمل'
                   ELSE 'أعزب'
               END as marital_status,
               s.birth_date,
               YEAR(CURDATE()) - YEAR(s.birth_date) as age,
               s.education,
               s.profession,
               s.address,
               s.phone,
               s.voter_number,
               s.voting_center,
               r.name as region_name,
               s.notes,
               DATE_FORMAT(s.created_at, '%Y-%m-%d') as created_date
        FROM supporters s 
        LEFT JOIN regions r ON s.region_id = r.id 
        $where_clause 
        ORDER BY s.created_at DESC";

$supporters = fetchAll($sql, $params);

if (empty($supporters)) {
    showMessage('لا توجد بيانات للتصدير', 'warning');
    redirect('supporters.php');
}

// تصدير كـ HTML يمكن فتحه في Excel
$filename = 'المؤيدين_' . date('Y-m-d_H-i-s') . '.xls';

header('Content-Type: application/vnd.ms-excel; charset=utf-8');
header('Content-Disposition: attachment; filename="' . $filename . '"');
header('Cache-Control: max-age=0');

echo "\xEF\xBB\xBF"; // BOM for UTF-8

echo '<table border="1" style="border-collapse: collapse;">';
echo '<tr style="background-color: #4472C4; color: white; font-weight: bold; text-align: center;">';
echo '<td style="padding: 8px;">الاسم الكامل</td>';
echo '<td style="padding: 8px;">الجنس</td>';
echo '<td style="padding: 8px;">الحالة الاجتماعية</td>';
echo '<td style="padding: 8px;">تاريخ الميلاد</td>';
echo '<td style="padding: 8px;">العمر</td>';
echo '<td style="padding: 8px;">التحصيل الدراسي</td>';
echo '<td style="padding: 8px;">المهنة</td>';
echo '<td style="padding: 8px;">العنوان</td>';
echo '<td style="padding: 8px;">رقم الهاتف</td>';
echo '<td style="padding: 8px;">رقم الناخب</td>';
echo '<td style="padding: 8px;">المركز الانتخابي</td>';
echo '<td style="padding: 8px;">المنطقة</td>';
echo '<td style="padding: 8px;">ملاحظات</td>';
echo '<td style="padding: 8px;">تاريخ الإضافة</td>';
echo '</tr>';

$row_color = true;
foreach ($supporters as $row) {
    $bg_color = $row_color ? '#F2F2F2' : '#FFFFFF';
    echo '<tr style="background-color: ' . $bg_color . ';">';
    echo '<td style="padding: 5px;">' . htmlspecialchars($row['full_name']) . '</td>';
    echo '<td style="padding: 5px; text-align: center;">' . htmlspecialchars($row['gender']) . '</td>';
    echo '<td style="padding: 5px; text-align: center;">' . htmlspecialchars($row['marital_status']) . '</td>';
    echo '<td style="padding: 5px; text-align: center;">' . htmlspecialchars($row['birth_date']) . '</td>';
    echo '<td style="padding: 5px; text-align: center;">' . htmlspecialchars($row['age']) . ' سنة</td>';
    echo '<td style="padding: 5px;">' . htmlspecialchars($row['education'] ?: 'غير محدد') . '</td>';
    echo '<td style="padding: 5px;">' . htmlspecialchars($row['profession'] ?: 'غير محدد') . '</td>';
    echo '<td style="padding: 5px;">' . htmlspecialchars($row['address']) . '</td>';
    echo '<td style="padding: 5px; text-align: center;">' . htmlspecialchars($row['phone']) . '</td>';
    echo '<td style="padding: 5px; text-align: center;">' . htmlspecialchars($row['voter_number'] ?: 'غير محدد') . '</td>';
    echo '<td style="padding: 5px;">' . htmlspecialchars($row['voting_center'] ?: 'غير محدد') . '</td>';
    echo '<td style="padding: 5px;">' . htmlspecialchars($row['region_name']) . '</td>';
    echo '<td style="padding: 5px;">' . htmlspecialchars($row['notes'] ?: 'لا توجد') . '</td>';
    echo '<td style="padding: 5px; text-align: center;">' . htmlspecialchars($row['created_date']) . '</td>';
    echo '</tr>';
    $row_color = !$row_color;
}

echo '</table>';

// إضافة إحصائيات في النهاية
echo '<br><br>';
echo '<table border="1" style="border-collapse: collapse; margin-top: 20px;">';
echo '<tr style="background-color: #70AD47; color: white; font-weight: bold;">';
echo '<td style="padding: 8px;">الإحصائيات</td>';
echo '<td style="padding: 8px;">العدد</td>';
echo '</tr>';

$total = count($supporters);
$males = count(array_filter($supporters, function($s) { return $s['gender'] == 'ذكر'; }));
$females = count(array_filter($supporters, function($s) { return $s['gender'] == 'أنثى'; }));

echo '<tr><td style="padding: 5px;">إجمالي المؤيدين</td><td style="padding: 5px; text-align: center;">' . $total . '</td></tr>';
echo '<tr><td style="padding: 5px;">الذكور</td><td style="padding: 5px; text-align: center;">' . $males . '</td></tr>';
echo '<tr><td style="padding: 5px;">الإناث</td><td style="padding: 5px; text-align: center;">' . $females . '</td></tr>';
echo '<tr><td style="padding: 5px;">نسبة الذكور</td><td style="padding: 5px; text-align: center;">' . ($total > 0 ? round(($males / $total) * 100, 1) : 0) . '%</td></tr>';
echo '</table>';

echo '<br><p style="font-size: 12px; color: #666;">تم التصدير في: ' . date('Y-m-d H:i:s') . ' - نظام إدارة الحملة الانتخابية</p>';

exit;
?>
