<?php
require_once '../../config/config.php';
require_once '../../config/database.php';

// التحقق من تسجيل الدخول
if (!isLoggedIn()) {
    http_response_code(401);
    echo json_encode(['success' => false, 'message' => 'غير مصرح']);
    exit;
}

// التحقق من الصلاحيات - الإداريين للمرشح فقط
if (!isCandidate()) {
    http_response_code(403);
    echo json_encode(['success' => false, 'message' => 'ليس لديك صلاحية']);
    exit;
}

// التحقق من وجود معرف الإداري
if (!isset($_GET['id']) || !is_numeric($_GET['id'])) {
    http_response_code(400);
    echo json_encode(['success' => false, 'message' => 'معرف الإداري غير صحيح']);
    exit;
}

$admin_id = (int)$_GET['id'];

// جلب بيانات الإداري مع إحصائيات المؤيدين
$sql = "SELECT u.*, r.name as region_name,
               COUNT(s.id) as supporters_count
        FROM users u 
        LEFT JOIN regions r ON u.region_id = r.id 
        LEFT JOIN supporters s ON u.region_id = s.region_id
        WHERE u.id = ? AND u.user_type = 'admin'
        GROUP BY u.id";

$admin = fetchOne($sql, [$admin_id]);

if (!$admin) {
    http_response_code(404);
    echo json_encode(['success' => false, 'message' => 'الإداري غير موجود']);
    exit;
}

// إرجاع البيانات
header('Content-Type: application/json; charset=utf-8');
echo json_encode([
    'success' => true,
    'admin' => $admin
]);
?>
