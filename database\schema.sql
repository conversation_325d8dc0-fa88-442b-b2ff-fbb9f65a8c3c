-- إن<PERSON>ا<PERSON> قاعدة البيانات
CREATE DATABASE IF NOT EXISTS irjnpfzw_mr CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
USE irjnpfzw_mr;

-- جد<PERSON><PERSON> المستخدمين (المرشح والإداريين)
CREATE TABLE users (
    id INT AUTO_INCREMENT PRIMARY KEY,
    username VA<PERSON>HA<PERSON>(50) UNIQUE NOT NULL,
    password VARCHAR(255) NOT NULL,
    phone VARCHAR(15) NOT NULL,
    full_name VARCHAR(100) NOT NULL,
    user_type ENUM('candidate', 'admin') DEFAULT 'admin',
    permissions JSON,
    region_id INT,
    status ENUM('active', 'inactive') DEFAULT 'active',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- جدول المناطق
CREATE TABLE regions (
    id INT AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(100) NOT NULL,
    description TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- جدول المؤيدين
CREATE TABLE supporters (
    id INT AUTO_INCREMENT PRIMARY KEY,
    full_name VARCHAR(100) NOT NULL,
    gender ENUM('male', 'female') NOT NULL,
    marital_status ENUM('single', 'married', 'divorced', 'widowed') NOT NULL,
    birth_date DATE NOT NULL,
    education VARCHAR(50),
    profession VARCHAR(100),
    address TEXT NOT NULL,
    phone VARCHAR(15) NOT NULL,
    voter_number VARCHAR(50) UNIQUE,
    voting_center VARCHAR(100),
    region_id INT NOT NULL,
    photo VARCHAR(255),
    notes TEXT,
    added_by INT NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (region_id) REFERENCES regions(id) ON DELETE CASCADE,
    FOREIGN KEY (added_by) REFERENCES users(id) ON DELETE CASCADE
);

-- جدول المصروفات
CREATE TABLE expenses (
    id INT AUTO_INCREMENT PRIMARY KEY,
    date DATE NOT NULL,
    type VARCHAR(100) NOT NULL,
    amount DECIMAL(10,2) NOT NULL,
    description TEXT NOT NULL,
    attachment VARCHAR(255),
    added_by INT NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (added_by) REFERENCES users(id) ON DELETE CASCADE
);

-- جدول الفعاليات
CREATE TABLE events (
    id INT AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(200) NOT NULL,
    description TEXT,
    event_date DATE NOT NULL,
    event_time TIME NOT NULL,
    location VARCHAR(200) NOT NULL,
    photos JSON,
    status ENUM('planned', 'ongoing', 'completed', 'cancelled') DEFAULT 'planned',
    added_by INT NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (added_by) REFERENCES users(id) ON DELETE CASCADE
);

-- جدول المنافسين
CREATE TABLE competitors (
    id INT AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(100) NOT NULL,
    party VARCHAR(100),
    region_id INT,
    strengths TEXT,
    weaknesses TEXT,
    photos JSON,
    notes TEXT,
    added_by INT NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (region_id) REFERENCES regions(id) ON DELETE SET NULL,
    FOREIGN KEY (added_by) REFERENCES users(id) ON DELETE CASCADE
);

-- جدول التقارير
CREATE TABLE reports (
    id INT AUTO_INCREMENT PRIMARY KEY,
    title VARCHAR(200) NOT NULL,
    content TEXT NOT NULL,
    report_type ENUM('daily', 'weekly', 'monthly', 'event', 'general') NOT NULL,
    report_date DATE NOT NULL,
    region_id INT,
    submitted_by INT NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (region_id) REFERENCES regions(id) ON DELETE SET NULL,
    FOREIGN KEY (submitted_by) REFERENCES users(id) ON DELETE CASCADE
);

-- جدول الإشعارات
CREATE TABLE notifications (
    id INT AUTO_INCREMENT PRIMARY KEY,
    title VARCHAR(200) NOT NULL,
    message TEXT NOT NULL,
    type ENUM('info', 'success', 'warning', 'error') DEFAULT 'info',
    user_id INT,
    is_read BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
);

-- جدول إعدادات النظام
CREATE TABLE system_settings (
    id INT AUTO_INCREMENT PRIMARY KEY,
    setting_key VARCHAR(100) UNIQUE NOT NULL,
    setting_value TEXT,
    description TEXT,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- إدراج البيانات الأساسية

-- إدراج المرشح الرئيسي
INSERT INTO users (username, password, phone, full_name, user_type) 
VALUES ('abd', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', '07719992716', 'المرشح الرئيسي', 'candidate');

-- إدراج مناطق افتراضية
INSERT INTO regions (name, description) VALUES 
('المنطقة الأولى', 'وصف المنطقة الأولى'),
('المنطقة الثانية', 'وصف المنطقة الثانية'),
('المنطقة الثالثة', 'وصف المنطقة الثالثة');

-- إدراج إعدادات النظام الافتراضية
INSERT INTO system_settings (setting_key, setting_value, description) VALUES 
('site_name', 'نظام إدارة الحملة الانتخابية', 'اسم الموقع'),
('candidate_name', 'المرشح', 'اسم المرشح'),
('site_logo', '', 'شعار الموقع'),
('primary_color', '#007bff', 'اللون الأساسي'),
('secondary_color', '#6c757d', 'اللون الثانوي'),
('enable_notifications', '1', 'تفعيل الإشعارات'),
('backup_frequency', 'weekly', 'تكرار النسخ الاحتياطي');

-- إنشاء فهارس لتحسين الأداء
CREATE INDEX idx_supporters_region ON supporters(region_id);
CREATE INDEX idx_supporters_phone ON supporters(phone);
CREATE INDEX idx_supporters_voter_number ON supporters(voter_number);
CREATE INDEX idx_expenses_date ON expenses(date);
CREATE INDEX idx_events_date ON events(event_date);
CREATE INDEX idx_reports_date ON reports(report_date);
CREATE INDEX idx_notifications_user ON notifications(user_id);
