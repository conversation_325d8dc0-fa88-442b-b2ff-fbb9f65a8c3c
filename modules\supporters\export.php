<?php
require_once '../../config/config.php';
require_once '../../config/database.php';

// التحقق من تسجيل الدخول
if (!isLoggedIn()) {
    redirect('../../login.php');
}

// التحقق من نوع التصدير
$export_type = $_GET['type'] ?? 'excel';
$allowed_types = ['excel', 'pdf', 'csv'];

if (!in_array($export_type, $allowed_types)) {
    showMessage('نوع التصدير غير مدعوم', 'error');
    redirect('supporters.php');
}

// بناء الاستعلام مع التصفية
$where_conditions = [];
$params = [];

// تصفية حسب المنطقة للإداريين
if (isAdmin() && $_SESSION['region_id']) {
    $where_conditions[] = "s.region_id = ?";
    $params[] = $_SESSION['region_id'];
}

// تطبيق فلاتر البحث إذا وجدت
if (!empty($_GET['search'])) {
    $search = '%' . $_GET['search'] . '%';
    $where_conditions[] = "(s.full_name LIKE ? OR s.phone LIKE ? OR s.voter_number LIKE ?)";
    $params = array_merge($params, [$search, $search, $search]);
}

if (!empty($_GET['region_id'])) {
    $where_conditions[] = "s.region_id = ?";
    $params[] = $_GET['region_id'];
}

if (!empty($_GET['gender'])) {
    $where_conditions[] = "s.gender = ?";
    $params[] = $_GET['gender'];
}

$where_clause = !empty($where_conditions) ? 'WHERE ' . implode(' AND ', $where_conditions) : '';

// جلب البيانات
$sql = "SELECT s.full_name, 
               CASE s.gender WHEN 'male' THEN 'ذكر' ELSE 'أنثى' END as gender,
               CASE s.marital_status 
                   WHEN 'single' THEN 'أعزب'
                   WHEN 'married' THEN 'متزوج'
                   WHEN 'divorced' THEN 'مطلق'
                   WHEN 'widowed' THEN 'أرمل'
               END as marital_status,
               s.birth_date,
               YEAR(CURDATE()) - YEAR(s.birth_date) as age,
               s.education,
               s.profession,
               s.address,
               s.phone,
               s.voter_number,
               s.voting_center,
               r.name as region_name,
               s.notes,
               s.created_at
        FROM supporters s 
        LEFT JOIN regions r ON s.region_id = r.id 
        $where_clause 
        ORDER BY s.created_at DESC";

$supporters = fetchAll($sql, $params);

if (empty($supporters)) {
    showMessage('لا توجد بيانات للتصدير', 'warning');
    redirect('supporters.php');
}

// العناوين
$headers = [
    'الاسم الكامل',
    'الجنس',
    'الحالة الاجتماعية',
    'تاريخ الميلاد',
    'العمر',
    'التحصيل الدراسي',
    'المهنة',
    'العنوان',
    'رقم الهاتف',
    'رقم الناخب',
    'المركز الانتخابي',
    'المنطقة',
    'ملاحظات',
    'تاريخ الإضافة'
];

switch ($export_type) {
    case 'excel':
    case 'csv':
        exportToCSV($supporters, $headers);
        break;
    case 'pdf':
        exportToPDF($supporters, $headers);
        break;
}

function exportToCSV($data, $headers) {
    $filename = 'المؤيدين_' . date('Y-m-d_H-i-s') . '.csv';
    
    header('Content-Type: text/csv; charset=utf-8');
    header('Content-Disposition: attachment; filename="' . $filename . '"');
    header('Cache-Control: max-age=0');
    
    $output = fopen('php://output', 'w');
    
    // كتابة BOM للدعم العربي
    fprintf($output, chr(0xEF).chr(0xBB).chr(0xBF));
    
    // كتابة العناوين
    fputcsv($output, $headers);
    
    // كتابة البيانات
    foreach ($data as $row) {
        $csv_row = [
            $row['full_name'],
            $row['gender'],
            $row['marital_status'],
            $row['birth_date'],
            $row['age'] . ' سنة',
            $row['education'] ?: 'غير محدد',
            $row['profession'] ?: 'غير محدد',
            $row['address'],
            $row['phone'],
            $row['voter_number'] ?: 'غير محدد',
            $row['voting_center'] ?: 'غير محدد',
            $row['region_name'],
            $row['notes'] ?: 'لا توجد',
            formatArabicDate($row['created_at'])
        ];
        fputcsv($output, $csv_row);
    }
    
    fclose($output);
    exit;
}

function exportToPDF($data, $headers) {
    // استخدام مكتبة TCPDF أو FPDF
    require_once '../../vendor/tcpdf/tcpdf.php';
    
    $pdf = new TCPDF(PDF_PAGE_ORIENTATION, PDF_UNIT, PDF_PAGE_FORMAT, true, 'UTF-8', false);
    
    // إعدادات PDF
    $pdf->SetCreator('نظام إدارة الحملة الانتخابية');
    $pdf->SetAuthor('نظام إدارة الحملة الانتخابية');
    $pdf->SetTitle('قائمة المؤيدين');
    $pdf->SetSubject('تقرير المؤيدين');
    
    // إعدادات الخط العربي
    $pdf->SetFont('aealarabiya', '', 12);
    $pdf->SetRTL(true);
    
    // إضافة صفحة
    $pdf->AddPage();
    
    // العنوان
    $pdf->SetFont('aealarabiya', 'B', 16);
    $pdf->Cell(0, 10, 'قائمة المؤيدين', 0, 1, 'C');
    $pdf->Cell(0, 10, 'تاريخ التقرير: ' . date('Y-m-d H:i:s'), 0, 1, 'C');
    $pdf->Ln(10);
    
    // جدول البيانات
    $pdf->SetFont('aealarabiya', '', 8);
    
    // عرض الأعمدة
    $col_widths = [30, 15, 20, 20, 15, 20, 20, 40, 25, 25, 30, 20];
    
    // رأس الجدول
    $pdf->SetFillColor(200, 220, 255);
    for ($i = 0; $i < count($headers) - 2; $i++) { // استبعاد الملاحظات وتاريخ الإضافة للمساحة
        $pdf->Cell($col_widths[$i], 8, $headers[$i], 1, 0, 'C', true);
    }
    $pdf->Ln();
    
    // بيانات الجدول
    $pdf->SetFillColor(245, 245, 245);
    $fill = false;
    
    foreach ($data as $row) {
        $pdf->Cell($col_widths[0], 6, $row['full_name'], 1, 0, 'C', $fill);
        $pdf->Cell($col_widths[1], 6, $row['gender'], 1, 0, 'C', $fill);
        $pdf->Cell($col_widths[2], 6, $row['marital_status'], 1, 0, 'C', $fill);
        $pdf->Cell($col_widths[3], 6, $row['birth_date'], 1, 0, 'C', $fill);
        $pdf->Cell($col_widths[4], 6, $row['age'] . ' سنة', 1, 0, 'C', $fill);
        $pdf->Cell($col_widths[5], 6, $row['education'] ?: 'غير محدد', 1, 0, 'C', $fill);
        $pdf->Cell($col_widths[6], 6, $row['profession'] ?: 'غير محدد', 1, 0, 'C', $fill);
        $pdf->Cell($col_widths[7], 6, substr($row['address'], 0, 30) . '...', 1, 0, 'C', $fill);
        $pdf->Cell($col_widths[8], 6, $row['phone'], 1, 0, 'C', $fill);
        $pdf->Cell($col_widths[9], 6, $row['voter_number'] ?: 'غير محدد', 1, 0, 'C', $fill);
        $pdf->Cell($col_widths[10], 6, $row['voting_center'] ?: 'غير محدد', 1, 0, 'C', $fill);
        $pdf->Cell($col_widths[11], 6, $row['region_name'], 1, 0, 'C', $fill);
        $pdf->Ln();
        
        $fill = !$fill;
        
        // إضافة صفحة جديدة إذا امتلأت الصفحة
        if ($pdf->GetY() > 250) {
            $pdf->AddPage();
            
            // إعادة رأس الجدول
            $pdf->SetFillColor(200, 220, 255);
            for ($i = 0; $i < count($headers) - 2; $i++) {
                $pdf->Cell($col_widths[$i], 8, $headers[$i], 1, 0, 'C', true);
            }
            $pdf->Ln();
            $pdf->SetFillColor(245, 245, 245);
        }
    }
    
    // إحصائيات
    $pdf->Ln(10);
    $pdf->SetFont('aealarabiya', 'B', 12);
    $pdf->Cell(0, 10, 'إجمالي المؤيدين: ' . count($data), 0, 1, 'C');
    
    // إخراج PDF
    $filename = 'المؤيدين_' . date('Y-m-d_H-i-s') . '.pdf';
    $pdf->Output($filename, 'D');
    exit;
}
?>
