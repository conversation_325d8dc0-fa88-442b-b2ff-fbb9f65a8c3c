<?php
// إعدادات قاعدة البيانات - محدثة
define('DB_HOST', 'localhost');
define('DB_NAME', 'irjnpfzw_mr');
define('DB_USER', 'irjnpfzw_mr');
define('DB_PASS', 'irjnpfzw_mr');
define('DB_CHARSET', 'utf8mb4');

class Database {
    private $host = DB_HOST;
    private $db_name = DB_NAME;
    private $username = DB_USER;
    private $password = DB_PASS;
    private $charset = DB_CHARSET;
    public $conn;

    public function getConnection() {
        $this->conn = null;
        try {
            $dsn = "mysql:host=" . $this->host . ";dbname=" . $this->db_name . ";charset=utf8mb4";
            $this->conn = new PDO($dsn, $this->username, $this->password, array(
                PDO::MYSQL_ATTR_INIT_COMMAND => "SET NAMES utf8mb4 COLLATE utf8mb4_unicode_ci",
                PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
                PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
                PDO::ATTR_EMULATE_PREPARES => false,
                PDO::MYSQL_ATTR_USE_BUFFERED_QUERY => true
            ));

            // تأكيد إعدادات الترميز
            $this->conn->exec("SET NAMES utf8mb4 COLLATE utf8mb4_unicode_ci");
            $this->conn->exec("SET CHARACTER SET utf8mb4");
            $this->conn->exec("SET character_set_connection=utf8mb4");
            $this->conn->exec("SET character_set_client=utf8mb4");
            $this->conn->exec("SET character_set_results=utf8mb4");

        } catch(PDOException $exception) {
            echo "خطأ في الاتصال: " . $exception->getMessage();
        }
        return $this->conn;
    }
}

// دالة للحصول على اتصال قاعدة البيانات
function getDBConnection() {
    $database = new Database();
    return $database->getConnection();
}

// دالة لتنفيذ استعلام آمن
function executeQuery($sql, $params = []) {
    try {
        $conn = getDBConnection();
        $stmt = $conn->prepare($sql);
        $result = $stmt->execute($params);
        return $result;
    } catch(PDOException $e) {
        error_log("Database Error: " . $e->getMessage());
        showMessage("خطأ في قاعدة البيانات: " . $e->getMessage(), 'error');
        return false;
    }
}

// دالة للحصول على صف واحد
function fetchOne($sql, $params = []) {
    try {
        $conn = getDBConnection();
        if (!$conn) {
            return false;
        }
        $stmt = $conn->prepare($sql);
        $stmt->execute($params);
        return $stmt->fetch(PDO::FETCH_ASSOC);
    } catch(PDOException $e) {
        error_log("Database Error: " . $e->getMessage());
        return false;
    }
}

// دالة للحصول على عدة صفوف
function fetchAll($sql, $params = []) {
    try {
        $conn = getDBConnection();
        if (!$conn) {
            return [];
        }
        $stmt = $conn->prepare($sql);
        $stmt->execute($params);
        return $stmt->fetchAll(PDO::FETCH_ASSOC);
    } catch(PDOException $e) {
        error_log("Database Error: " . $e->getMessage());
        return [];
    }
}

// دالة للحصول على آخر ID مدرج
function getLastInsertId() {
    $conn = getDBConnection();
    return $conn->lastInsertId();
}

// دالة للحصول على عدد الصفوف
function getRowCount($sql, $params = []) {
    $stmt = executeQuery($sql, $params);
    return $stmt ? $stmt->rowCount() : 0;
}

// دالة لعرض الرسائل
function showMessage($message, $type = 'info') {
    // يمكن تطوير هذه الدالة لاحقاً لعرض الرسائل بشكل أفضل
    if ($type === 'error') {
        error_log($message);
    }
}

// متغير عام لاتصال قاعدة البيانات
$GLOBALS['pdo'] = getDBConnection();
?>
