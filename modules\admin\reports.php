<?php
// صفحة التقارير الأسبوعية للإداريين
session_start();
header('Content-Type: text/html; charset=utf-8');
ini_set('default_charset', 'utf-8');
mb_internal_encoding('UTF-8');

require_once '../../config/config.php';
require_once '../../config/database.php';

// التحقق من تسجيل الدخول
if (!isset($_SESSION['admin_id'])) {
    redirect('login.php');
}

$admin_id = $_SESSION['admin_id'];
$admin_name = $_SESSION['admin_name'];

// معالجة إنشاء تقرير جديد
if ($_SERVER['REQUEST_METHOD'] == 'POST' && isset($_POST['action']) && $_POST['action'] == 'create') {
    $week_start = sanitize($_POST['week_start']);
    $week_end = sanitize($_POST['week_end']);
    $supporters_added = (int)$_POST['supporters_added'];
    $events_attended = (int)$_POST['events_attended'];
    $calls_made = (int)$_POST['calls_made'];
    $meetings_held = (int)$_POST['meetings_held'];
    $challenges = sanitize($_POST['challenges']);
    $achievements = sanitize($_POST['achievements']);
    $next_week_plans = sanitize($_POST['next_week_plans']);
    $notes = sanitize($_POST['notes']);
    
    if (!empty($week_start) && !empty($week_end)) {
        // التحقق من عدم وجود تقرير لنفس الأسبوع
        $existing = fetchOne("SELECT id FROM weekly_reports WHERE admin_id = ? AND week_start = ?", [$admin_id, $week_start]);
        
        if ($existing) {
            showMessage('يوجد تقرير لهذا الأسبوع مسبقاً', 'error');
        } else {
            $sql = "INSERT INTO weekly_reports (admin_id, week_start, week_end, supporters_added, events_attended, calls_made, meetings_held, challenges, achievements, next_week_plans, notes, status, created_at) 
                    VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, 'draft', NOW())";
            
            $result = executeQuery($sql, [$admin_id, $week_start, $week_end, $supporters_added, $events_attended, $calls_made, $meetings_held, $challenges, $achievements, $next_week_plans, $notes]);
            
            if ($result) {
                showMessage('تم إنشاء التقرير بنجاح', 'success');
            } else {
                showMessage('حدث خطأ أثناء إنشاء التقرير', 'error');
            }
        }
    } else {
        showMessage('يرجى تحديد تواريخ الأسبوع', 'error');
    }
}

// معالجة تقديم التقرير
if (isset($_GET['submit']) && is_numeric($_GET['submit'])) {
    $report_id = (int)$_GET['submit'];
    $result = executeQuery("UPDATE weekly_reports SET status = 'submitted', submitted_at = NOW() WHERE id = ? AND admin_id = ?", [$report_id, $admin_id]);
    
    if ($result) {
        showMessage('تم تقديم التقرير للإدارة', 'success');
    } else {
        showMessage('حدث خطأ أثناء تقديم التقرير', 'error');
    }
}

// جلب التقارير
$reports = fetchAll("SELECT * FROM weekly_reports WHERE admin_id = ? ORDER BY week_start DESC", [$admin_id]);

// حساب الأسبوع الحالي
$current_week_start = date('Y-m-d', strtotime('monday this week'));
$current_week_end = date('Y-m-d', strtotime('sunday this week'));
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>التقارير الأسبوعية - لوحة تحكم الإداريين</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: #f8f9fa;
        }
        .main-content {
            margin-top: 80px;
        }
        .report-card {
            border-radius: 10px;
            border: none;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 15px;
            transition: transform 0.2s;
        }
        .report-card:hover {
            transform: translateY(-2px);
        }
        .status-draft { border-left: 4px solid #6c757d; }
        .status-submitted { border-left: 4px solid #007bff; }
        .status-reviewed { border-left: 4px solid #28a745; }
    </style>
</head>
<body>
    <!-- شريط التنقل العلوي -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-dark fixed-top">
        <div class="container-fluid">
            <a class="navbar-brand" href="dashboard.php">
                <i class="fas fa-user-shield me-2"></i>
                لوحة تحكم الإداريين
            </a>
            
            <div class="navbar-nav ms-auto">
                <div class="nav-item dropdown">
                    <a class="nav-link dropdown-toggle" href="#" id="userDropdown" role="button" data-bs-toggle="dropdown">
                        <i class="fas fa-user-circle me-1"></i>
                        <?php echo htmlspecialchars($admin_name); ?>
                    </a>
                    <ul class="dropdown-menu dropdown-menu-end">
                        <li><a class="dropdown-item" href="dashboard.php"><i class="fas fa-tachometer-alt me-2"></i>لوحة التحكم</a></li>
                        <li><a class="dropdown-item" href="logout.php"><i class="fas fa-sign-out-alt me-2"></i>تسجيل الخروج</a></li>
                    </ul>
                </div>
            </div>
        </div>
    </nav>

    <!-- المحتوى الرئيسي -->
    <div class="container-fluid main-content">
        <div class="row">
            <div class="col-md-12">
                <div class="d-flex justify-content-between align-items-center mb-4">
                    <h2><i class="fas fa-file-alt me-2"></i>التقارير الأسبوعية</h2>
                    <div>
                        <button type="button" class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#createReportModal">
                            <i class="fas fa-plus me-2"></i>تقرير جديد
                        </button>
                        <a href="dashboard.php" class="btn btn-secondary">
                            <i class="fas fa-arrow-left me-2"></i>العودة
                        </a>
                    </div>
                </div>

                <?php displayMessage(); ?>

                <!-- معلومات الأسبوع الحالي -->
                <div class="alert alert-info">
                    <h6><i class="fas fa-calendar-week me-2"></i>الأسبوع الحالي</h6>
                    <p class="mb-0">من <?php echo $current_week_start; ?> إلى <?php echo $current_week_end; ?></p>
                </div>

                <!-- قائمة التقارير -->
                <div class="row">
                    <?php if (empty($reports)): ?>
                        <div class="col-12">
                            <div class="alert alert-info text-center">
                                <i class="fas fa-file-alt fa-3x mb-3"></i>
                                <h5>لا توجد تقارير</h5>
                                <p>لم تقم بإنشاء أي تقارير أسبوعية بعد</p>
                                <button type="button" class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#createReportModal">
                                    <i class="fas fa-plus me-2"></i>إنشاء تقرير جديد
                                </button>
                            </div>
                        </div>
                    <?php else: ?>
                        <?php foreach ($reports as $report): ?>
                        <div class="col-md-6">
                            <div class="card report-card status-<?php echo $report['status']; ?>">
                                <div class="card-body">
                                    <div class="d-flex justify-content-between align-items-start mb-2">
                                        <h6 class="card-title">
                                            تقرير الأسبوع: <?php echo date('Y-m-d', strtotime($report['week_start'])); ?> - <?php echo date('Y-m-d', strtotime($report['week_end'])); ?>
                                        </h6>
                                        <div>
                                            <span class="badge bg-<?php 
                                                echo $report['status'] == 'draft' ? 'secondary' : 
                                                    ($report['status'] == 'submitted' ? 'primary' : 'success'); 
                                            ?>">
                                                <?php 
                                                $status_map = [
                                                    'draft' => 'مسودة',
                                                    'submitted' => 'مقدم',
                                                    'reviewed' => 'تمت المراجعة'
                                                ];
                                                echo $status_map[$report['status']] ?? $report['status'];
                                                ?>
                                            </span>
                                            <?php if ($report['status'] == 'draft'): ?>
                                            <a href="?submit=<?php echo $report['id']; ?>" class="btn btn-outline-primary btn-sm ms-2" onclick="return confirm('هل تريد تقديم هذا التقرير للإدارة؟')">
                                                <i class="fas fa-paper-plane"></i> تقديم
                                            </a>
                                            <?php endif; ?>
                                        </div>
                                    </div>
                                    
                                    <div class="row text-center mb-3">
                                        <div class="col-3">
                                            <h5 class="text-primary"><?php echo $report['supporters_added']; ?></h5>
                                            <small>مؤيدين جدد</small>
                                        </div>
                                        <div class="col-3">
                                            <h5 class="text-success"><?php echo $report['events_attended']; ?></h5>
                                            <small>فعاليات</small>
                                        </div>
                                        <div class="col-3">
                                            <h5 class="text-info"><?php echo $report['calls_made']; ?></h5>
                                            <small>مكالمات</small>
                                        </div>
                                        <div class="col-3">
                                            <h5 class="text-warning"><?php echo $report['meetings_held']; ?></h5>
                                            <small>اجتماعات</small>
                                        </div>
                                    </div>
                                    
                                    <?php if ($report['achievements']): ?>
                                    <div class="mb-2">
                                        <strong>الإنجازات:</strong>
                                        <p class="small"><?php echo nl2br(htmlspecialchars($report['achievements'])); ?></p>
                                    </div>
                                    <?php endif; ?>
                                    
                                    <?php if ($report['challenges']): ?>
                                    <div class="mb-2">
                                        <strong>التحديات:</strong>
                                        <p class="small"><?php echo nl2br(htmlspecialchars($report['challenges'])); ?></p>
                                    </div>
                                    <?php endif; ?>
                                    
                                    <?php if ($report['next_week_plans']): ?>
                                    <div class="mb-2">
                                        <strong>خطط الأسبوع القادم:</strong>
                                        <p class="small"><?php echo nl2br(htmlspecialchars($report['next_week_plans'])); ?></p>
                                    </div>
                                    <?php endif; ?>
                                    
                                    <small class="text-muted">
                                        <i class="fas fa-clock me-1"></i>
                                        تم الإنشاء: <?php echo date('Y-m-d H:i', strtotime($report['created_at'])); ?>
                                        <?php if ($report['submitted_at']): ?>
                                        <br><i class="fas fa-paper-plane me-1"></i>
                                        تم التقديم: <?php echo date('Y-m-d H:i', strtotime($report['submitted_at'])); ?>
                                        <?php endif; ?>
                                    </small>
                                </div>
                            </div>
                        </div>
                        <?php endforeach; ?>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>

    <!-- نافذة إنشاء تقرير -->
    <div class="modal fade" id="createReportModal" tabindex="-1">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">إنشاء تقرير أسبوعي</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <form method="POST" action="">
                    <input type="hidden" name="action" value="create">
                    <div class="modal-body">
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="week_start" class="form-label">بداية الأسبوع *</label>
                                    <input type="date" class="form-control" id="week_start" name="week_start" value="<?php echo $current_week_start; ?>" required>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="week_end" class="form-label">نهاية الأسبوع *</label>
                                    <input type="date" class="form-control" id="week_end" name="week_end" value="<?php echo $current_week_end; ?>" required>
                                </div>
                            </div>
                        </div>
                        
                        <div class="row">
                            <div class="col-md-3">
                                <div class="mb-3">
                                    <label for="supporters_added" class="form-label">مؤيدين جدد</label>
                                    <input type="number" class="form-control" id="supporters_added" name="supporters_added" value="0" min="0">
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="mb-3">
                                    <label for="events_attended" class="form-label">فعاليات حضرتها</label>
                                    <input type="number" class="form-control" id="events_attended" name="events_attended" value="0" min="0">
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="mb-3">
                                    <label for="calls_made" class="form-label">مكالمات أجريتها</label>
                                    <input type="number" class="form-control" id="calls_made" name="calls_made" value="0" min="0">
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="mb-3">
                                    <label for="meetings_held" class="form-label">اجتماعات عقدتها</label>
                                    <input type="number" class="form-control" id="meetings_held" name="meetings_held" value="0" min="0">
                                </div>
                            </div>
                        </div>
                        
                        <div class="mb-3">
                            <label for="achievements" class="form-label">الإنجازات</label>
                            <textarea class="form-control" id="achievements" name="achievements" rows="3" placeholder="اذكر أهم الإنجازات التي حققتها هذا الأسبوع"></textarea>
                        </div>
                        
                        <div class="mb-3">
                            <label for="challenges" class="form-label">التحديات</label>
                            <textarea class="form-control" id="challenges" name="challenges" rows="3" placeholder="اذكر التحديات التي واجهتها هذا الأسبوع"></textarea>
                        </div>
                        
                        <div class="mb-3">
                            <label for="next_week_plans" class="form-label">خطط الأسبوع القادم</label>
                            <textarea class="form-control" id="next_week_plans" name="next_week_plans" rows="3" placeholder="اذكر خططك للأسبوع القادم"></textarea>
                        </div>
                        
                        <div class="mb-3">
                            <label for="notes" class="form-label">ملاحظات إضافية</label>
                            <textarea class="form-control" id="notes" name="notes" rows="2" placeholder="أي ملاحظات أخرى تريد إضافتها"></textarea>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-save me-2"></i>إنشاء التقرير
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
