<?php
// تسجيل خروج الإداريين
session_start();
require_once '../../config/config.php';
require_once '../../config/database.php';

// إلغاء الجلسة من قاعدة البيانات
if (isset($_SESSION['admin_id']) && isset($_SESSION['session_token'])) {
    executeQuery("UPDATE admin_sessions SET is_active = FALSE WHERE admin_id = ? AND session_token = ?", 
                [$_SESSION['admin_id'], $_SESSION['session_token']]);
}

// مسح جميع متغيرات الجلسة
session_unset();
session_destroy();

// إعادة توجيه لصفحة تسجيل الدخول
redirect('login.php');
?>
