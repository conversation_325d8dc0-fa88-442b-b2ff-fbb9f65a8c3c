<?php
// اختبار الصفحة الأصلية للمؤيدين

header('Content-Type: text/html; charset=utf-8');
ini_set('default_charset', 'utf-8');
mb_internal_encoding('UTF-8');

require_once 'config/config.php';
require_once 'config/database.php';

echo "<!DOCTYPE html>";
echo "<html lang='ar' dir='rtl'>";
echo "<head>";
echo "<meta charset='UTF-8'>";
echo "<meta name='viewport' content='width=device-width, initial-scale=1.0'>";
echo "<title>اختبار الصفحة الأصلية للمؤيدين</title>";
echo "<link href='https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css' rel='stylesheet'>";
echo "<link href='https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css' rel='stylesheet'>";
echo "<style>";
echo "body { font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); min-height: 100vh; padding: 2rem; }";
echo ".test-card { background: white; border-radius: 15px; box-shadow: 0 10px 30px rgba(0,0,0,0.1); padding: 2rem; margin: 0 auto; max-width: 900px; }";
echo ".success { color: #28a745; }";
echo ".error { color: #dc3545; }";
echo ".warning { color: #ffc107; }";
echo ".test-item { border: 1px solid #e9ecef; border-radius: 8px; padding: 15px; margin-bottom: 15px; }";
echo "</style>";
echo "</head>";
echo "<body>";

echo "<div class='test-card'>";
echo "<h1 class='text-center mb-4'><i class='fas fa-vial'></i> اختبار الصفحة الأصلية للمؤيدين</h1>";

// اختبار 1: فحص الملفات المطلوبة
echo "<div class='test-item'>";
echo "<h5><i class='fas fa-file-check'></i> اختبار 1: فحص الملفات المطلوبة</h5>";

$required_files = [
    'modules/supporters/supporters.php' => 'الصفحة الأصلية',
    'modules/supporters/import.php' => 'ملف الاستيراد',
    'modules/supporters/export_excel.php' => 'تصدير Excel',
    'modules/supporters/export_pdf.php' => 'تصدير PDF',
    'modules/supporters/template_csv.php' => 'نموذج CSV',
    'modules/supporters/modals.php' => 'النوافذ المنبثقة',
    'assets/js/supporters.js' => 'ملف الجافا سكريبت'
];

$files_ok = true;
foreach ($required_files as $file => $description) {
    $exists = file_exists($file);
    if ($exists) {
        echo "<p class='success'><i class='fas fa-check'></i> $description: موجود</p>";
    } else {
        echo "<p class='error'><i class='fas fa-times'></i> $description: مفقود</p>";
        $files_ok = false;
    }
}

if ($files_ok) {
    echo "<div class='alert alert-success'>✅ جميع الملفات موجودة</div>";
} else {
    echo "<div class='alert alert-danger'>❌ بعض الملفات مفقودة</div>";
}
echo "</div>";

// اختبار 2: فحص قاعدة البيانات
echo "<div class='test-item'>";
echo "<h5><i class='fas fa-database'></i> اختبار 2: فحص قاعدة البيانات</h5>";

try {
    $supporters_count = fetchOne("SELECT COUNT(*) as count FROM supporters")['count'];
    $regions_count = fetchOne("SELECT COUNT(*) as count FROM regions")['count'];
    
    echo "<p class='success'><i class='fas fa-check'></i> الاتصال بقاعدة البيانات: نجح</p>";
    echo "<p class='success'><i class='fas fa-check'></i> عدد المؤيدين: " . number_format($supporters_count) . "</p>";
    echo "<p class='success'><i class='fas fa-check'></i> عدد المناطق: " . number_format($regions_count) . "</p>";
    
    if ($regions_count == 0) {
        echo "<div class='alert alert-warning'>⚠️ لا توجد مناطق - يجب إضافة مناطق أولاً</div>";
    }
    
} catch (Exception $e) {
    echo "<p class='error'><i class='fas fa-times'></i> خطأ في قاعدة البيانات: " . htmlspecialchars($e->getMessage()) . "</p>";
}
echo "</div>";

// اختبار 3: فحص روابط التصدير
echo "<div class='test-item'>";
echo "<h5><i class='fas fa-download'></i> اختبار 3: فحص روابط التصدير</h5>";

$export_links = [
    'modules/supporters/export_excel.php' => 'تصدير Excel',
    'modules/supporters/export_pdf.php' => 'تصدير PDF',
    'modules/supporters/template_csv.php' => 'نموذج CSV'
];

foreach ($export_links as $link => $description) {
    if (file_exists($link)) {
        echo "<p class='success'><i class='fas fa-check'></i> $description: <a href='$link' target='_blank' class='btn btn-sm btn-outline-primary'>اختبار</a></p>";
    } else {
        echo "<p class='error'><i class='fas fa-times'></i> $description: غير متوفر</p>";
    }
}
echo "</div>";

// اختبار 4: فحص نموذج CSV
echo "<div class='test-item'>";
echo "<h5><i class='fas fa-file-csv'></i> اختبار 4: نموذج CSV للاستيراد</h5>";

echo "<div class='alert alert-info'>";
echo "<h6>تنسيق ملف CSV المطلوب:</h6>";
echo "<code style='font-size: 12px;'>";
echo "الاسم الكامل,الجنس,الحالة الاجتماعية,تاريخ الميلاد,التحصيل الدراسي,المهنة,العنوان,رقم الهاتف,رقم الناخب,المركز الانتخابي,المنطقة,ملاحظات<br>";
echo "أحمد محمد علي,ذكر,متزوج,1990-01-01,بكالوريوس,مهندس,بغداد - الكرادة,07701234567,123456789,مدرسة الكرادة,الكرادة,مؤيد نشط";
echo "</code>";
echo "</div>";

if (file_exists('modules/supporters/template_csv.php')) {
    echo "<a href='modules/supporters/template_csv.php' class='btn btn-primary'>";
    echo "<i class='fas fa-download'></i> تحميل نموذج CSV";
    echo "</a>";
}
echo "</div>";

// اختبار 5: فحص الأذونات
echo "<div class='test-item'>";
echo "<h5><i class='fas fa-shield-alt'></i> اختبار 5: فحص الأذونات</h5>";

$upload_dir = 'uploads/';
if (!file_exists($upload_dir)) {
    mkdir($upload_dir, 0755, true);
    echo "<p class='success'><i class='fas fa-check'></i> تم إنشاء مجلد الرفع: $upload_dir</p>";
} else {
    echo "<p class='success'><i class='fas fa-check'></i> مجلد الرفع موجود: $upload_dir</p>";
}

if (is_writable($upload_dir)) {
    echo "<p class='success'><i class='fas fa-check'></i> مجلد الرفع قابل للكتابة</p>";
} else {
    echo "<p class='error'><i class='fas fa-times'></i> مجلد الرفع غير قابل للكتابة</p>";
}
echo "</div>";

// النتيجة النهائية
echo "<div class='alert alert-primary text-center'>";
echo "<h4><i class='fas fa-clipboard-check'></i> النتيجة النهائية</h4>";

if ($files_ok && isset($supporters_count)) {
    echo "<div class='alert alert-success'>";
    echo "<h5>✅ النظام جاهز للاستخدام!</h5>";
    echo "<p>يمكنك الآن استخدام الصفحة الأصلية مع جميع الميزات:</p>";
    echo "<ul class='text-start'>";
    echo "<li>✅ إضافة وتعديل وحذف المؤيدين</li>";
    echo "<li>✅ استيراد ملفات CSV</li>";
    echo "<li>✅ تصدير Excel و PDF</li>";
    echo "<li>✅ البحث والفلترة</li>";
    echo "</ul>";
    echo "</div>";
} else {
    echo "<div class='alert alert-warning'>";
    echo "<h5>⚠️ النظام يحتاج إصلاح</h5>";
    echo "<p>يرجى تشغيل أداة الإصلاح أولاً</p>";
    echo "</div>";
}
echo "</div>";

// أزرار التنقل
echo "<div class='text-center mt-4'>";
echo "<a href='modules/supporters/supporters.php' class='btn btn-primary btn-lg me-2'>";
echo "<i class='fas fa-users'></i> الصفحة الأصلية";
echo "</a>";

echo "<a href='fix_original_supporters.php' class='btn btn-success btn-lg me-2'>";
echo "<i class='fas fa-tools'></i> أداة الإصلاح";
echo "</a>";

echo "<a href='modules/supporters/supporters_working.php' class='btn btn-info btn-lg'>";
echo "<i class='fas fa-users'></i> الصفحة المُصلحة";
echo "</a>";
echo "</div>";

// معلومات إضافية
echo "<div class='mt-4'>";
echo "<h6>معلومات تقنية:</h6>";
echo "<ul>";
echo "<li><strong>إصدار PHP:</strong> " . PHP_VERSION . "</li>";
echo "<li><strong>ترميز الصفحة:</strong> UTF-8</li>";
echo "<li><strong>ترميز PHP:</strong> " . mb_internal_encoding() . "</li>";
echo "<li><strong>PDO MySQL:</strong> " . (extension_loaded('pdo_mysql') ? 'متوفر' : 'غير متوفر') . "</li>";
echo "<li><strong>وقت الاختبار:</strong> " . date('Y-m-d H:i:s') . "</li>";
echo "</ul>";
echo "</div>";

echo "</div>";
echo "</body>";
echo "</html>";
?>
