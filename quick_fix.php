<?php
// حل سريع للمشكلة
error_reporting(E_ALL);
ini_set('display_errors', 1);
header('Content-Type: text/html; charset=utf-8');

echo "<!DOCTYPE html>";
echo "<html lang='ar' dir='rtl'>";
echo "<head>";
echo "<meta charset='UTF-8'>";
echo "<meta name='viewport' content='width=device-width, initial-scale=1.0'>";
echo "<title>حل سريع للمشكلة</title>";
echo "<link href='https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css' rel='stylesheet'>";
echo "<style>body { font-family: Arial, sans-serif; padding: 20px; background: #f8f9fa; }</style>";
echo "</head>";
echo "<body>";

echo "<div class='container'>";
echo "<div class='row justify-content-center'>";
echo "<div class='col-md-8'>";

echo "<div class='card'>";
echo "<div class='card-header bg-danger text-white'>";
echo "<h3>🚨 حل سريع للمشكلة</h3>";
echo "</div>";
echo "<div class='card-body'>";

echo "<div class='alert alert-warning'>";
echo "<h5>المشكلة: معلومات قاعدة البيانات غير صحيحة</h5>";
echo "<p>اسم المستخدم 'abd' غير صحيح لقاعدة البيانات 'irjnpfzw_mr'</p>";
echo "</div>";

// جرب المعلومات الصحيحة المحتملة
$possible_configs = [
    ['irjnpfzw_mr', 'irjnpfzw_mr', 'كلمة المرور الأصلية'],
    ['irjnpfzw_mr', 'irjnpfzw_abd', 'كلمة المرور الأصلية'],
    ['irjnpfzw_mr', 'irjnpfzw_user', 'كلمة المرور الأصلية'],
    ['irjnpfzw_mr', 'irjnpfzw_admin', 'كلمة المرور الأصلية']
];

echo "<h5>جرب هذه المعلومات:</h5>";

foreach ($possible_configs as $index => $config) {
    echo "<div class='card mb-3'>";
    echo "<div class='card-header'>";
    echo "<h6>خيار " . ($index + 1) . "</h6>";
    echo "</div>";
    echo "<div class='card-body'>";
    echo "<form method='POST' action='fix_database_config.php'>";
    echo "<div class='row'>";
    echo "<div class='col-md-3'>";
    echo "<label>Host:</label>";
    echo "<input type='text' class='form-control' name='host' value='localhost' readonly>";
    echo "</div>";
    echo "<div class='col-md-3'>";
    echo "<label>Database:</label>";
    echo "<input type='text' class='form-control' name='dbname' value='{$config[0]}' readonly>";
    echo "</div>";
    echo "<div class='col-md-3'>";
    echo "<label>Username:</label>";
    echo "<input type='text' class='form-control' name='username' value='{$config[1]}' readonly>";
    echo "</div>";
    echo "<div class='col-md-3'>";
    echo "<label>Password:</label>";
    echo "<input type='password' class='form-control' name='password' placeholder='أدخل كلمة المرور' required>";
    echo "</div>";
    echo "</div>";
    echo "<div class='text-center mt-2'>";
    echo "<button type='submit' class='btn btn-primary btn-sm'>جرب هذا الخيار</button>";
    echo "</div>";
    echo "</form>";
    echo "</div>";
    echo "</div>";
}

echo "<hr>";

echo "<div class='alert alert-info'>";
echo "<h5>🔧 الحل البديل: تشغيل النظام بدون قاعدة بيانات</h5>";
echo "<p>يمكنك تشغيل النظام مؤقتاً بدون قاعدة بيانات لاختبار الواجهات:</p>";
echo "<div class='text-center'>";
echo "<a href='simple_dashboard.php' class='btn btn-success me-2'>لوحة تحكم بسيطة</a>";
echo "<a href='demo_system.php' class='btn btn-info me-2'>نظام تجريبي</a>";
echo "<a href='offline_dashboard.php' class='btn btn-warning'>لوحة تحكم بدون قاعدة بيانات</a>";
echo "</div>";
echo "</div>";

echo "</div>";
echo "</div>";

echo "</div>";
echo "</div>";
echo "</div>";

echo "</body>";
echo "</html>";
?>
