<?php
require_once '../../config/config.php';
require_once '../../config/database.php';

// التحقق من تسجيل الدخول
if (!isLoggedIn()) {
    http_response_code(401);
    echo json_encode(['success' => false, 'message' => 'غير مصرح']);
    exit;
}

// التحقق من وجود معرف الفعالية
if (!isset($_GET['id']) || !is_numeric($_GET['id'])) {
    http_response_code(400);
    echo json_encode(['success' => false, 'message' => 'معرف الفعالية غير صحيح']);
    exit;
}

$event_id = (int)$_GET['id'];

// جلب بيانات الفعالية
$sql = "SELECT e.*, u.full_name as added_by_name
        FROM events e 
        LEFT JOIN users u ON e.added_by = u.id 
        WHERE e.id = ?";

$event = fetchOne($sql, [$event_id]);

if (!$event) {
    http_response_code(404);
    echo json_encode(['success' => false, 'message' => 'الفعالية غير موجودة']);
    exit;
}

// إرجاع البيانات
header('Content-Type: application/json; charset=utf-8');
echo json_encode([
    'success' => true,
    'event' => $event
]);
?>
